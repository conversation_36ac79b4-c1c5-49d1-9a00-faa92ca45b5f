<template>
  <div class="marketing-personne-card-box">
    <div class="card-title">营销人员区域配置</div>
    <div class="card-content">
      <Bar @change="handleChange" :series="series" :y-data="yData" height="100%" />
    </div>

    <el-dialog v-model="dialogVisible" title="区域配置" destroy-on-close width="40%">
      <el-row>
        <el-col :span="24">
          <el-form-item label="区域选择：">
            <el-tree-select clearable v-model="formInline.areaId" check-strictly multiple style="width: 100%"
              :props="{ children: 'children', label: 'name', value: 'id' }" placeholder="请选择" :data="treeData"
              :render-after-expand="false" />
          </el-form-item>
        </el-col>
      </el-row>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submit"> 确定 </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "MarketingPersonnelManagement"
});
import { ref, onMounted } from "vue";
import { getCityTreeApi } from "@/api/sys/city";
import { ElMessage } from "element-plus";

import Bar from "./components/Bar.vue";
import {
  getSaleUserCountApi,
  getSaleUserByIdApi,
  saveSaleUserApi
} from "@/api/customer-management/index";
const dialogVisible = ref<boolean>(false);
const yData = ref([]);
const series = ref([
  {
    name: "区域数量",
    type: "bar",
    markPoint: {
      symbolRotate: -90,
      data: [
        { type: "max", name: "Max" },
        { type: "min", name: "Min" }
      ]
    },
    markLine: {
      data: [{ type: "average", name: "Avg" }]
    },
    data: []
  }
]);
const list = ref([]);
const treeData = ref([]);

const formInline = ref({
  userId: "",
  areaId: []
});
async function getList() {
  const res = await getSaleUserCountApi();
  if (res.data) {
    list.value = res.data;
    yData.value = res.data?.map(i => i.userName);
    series.value[0].data = res.data?.map(i => i.areaCount);
  }
}
async function getCityTreeData() {
  const tree = await getCityTreeApi();
  treeData.value = tree.data;
}
async function submit() {
  const submitData = {
    areaIds: formInline.value.areaId.length
      ? formInline.value.areaId
      : null,
    userId: formInline.value.userId
  };
  const res = await saveSaleUserApi(submitData);
  if (res.code === "200") {
    ElMessage({
      message: "操作成功",
      type: "success"
    });
    getList();
    dialogVisible.value = false;
  } else {
    ElMessage({
      message: res.message,
      type: "error"
    });
  }
}
async function handleChange(data) {
  dialogVisible.value = true;
  if (data !== undefined) {
    formInline.value.userId = list.value[data].userId;
    const res = await getSaleUserByIdApi(formInline.value.userId);
    formInline.value.areaId = res.data.areaIds;
  }
}
onMounted(() => {
  getList();
  getCityTreeData();
});
</script>

<style lang="scss" scoped>
.marketing-personne-card-box {
  padding: 20px;
  box-sizing: border-box;
  border-radius: 6px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 2px 22px rgba(0, 0, 0, 0.06);
  // width: 100%;
  height: 100%;
  height: calc(100vh - 210px);

  .card-title {
    font-size: 18px;
    padding-bottom: 5px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }

  .card-content {
    height: 100%;
  }
}
</style>
