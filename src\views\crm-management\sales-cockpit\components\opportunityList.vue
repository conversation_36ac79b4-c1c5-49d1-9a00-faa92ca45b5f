<template>
  <div>
    <pure-table
      border
      stripe
      :columns="columns"
      :loading="loading"
      :data="tableData"
      :pagination="pagination"
      @page-size-change="onSizeChange"
      @page-current-change="onCurrentChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from "vue";
import { columns } from "./data";
import type { PaginationProps } from "@pureadmin/table";
import { delay } from "@pureadmin/utils";
import { getOpportunityListApi } from "@/api/customer-management/index";
const props = defineProps({
  queryType: {
    type: String as PropType<string>,
    default: "0"
  },
  time: {
    type: String,
    default: ""
  }
});
const loading = ref(false);
const tableData = ref([]);
const searchInfo = ref({
  time:"",
  title: undefined,
  customName: undefined,
  followerName: undefined,
  followStage: 1,
  pageNo: 1,
  pageSize: 10
});
/** 分页配置 */
const pagination = reactive<PaginationProps>({
  pageSize: 10,
  currentPage: 1,
  pageSizes: [10, 20, 40, 50],
  total: 0,
  align: "right",
  background: true,
  small: false
});
function onSizeChange(val) {
  searchInfo.value.pageSize = val;
  getList();
}
function onCurrentChange(val) {
  searchInfo.value.pageNo = val;
  getList();
}
async function getList() {
  loading.value = true;
  const { data } = await getOpportunityListApi(searchInfo.value);
  pagination.total = data ? Number(data.totalCount) : 0;
  tableData.value = data ? data.data : [];
  console.log(tableData.value)
  delay(300).then(() => {
    loading.value = false;
  });
}
onMounted(() => {
  getList();
});
watch(
  () => props.queryType,
  newVal => {
    searchInfo.value.followStage = newVal;
    getList();
  },
  { immediate: true }
);
</script>

<style scoped></style>
