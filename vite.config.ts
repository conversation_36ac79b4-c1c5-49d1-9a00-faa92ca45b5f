import dayjs from "dayjs";
import { resolve } from "path";
import pkg from "./package.json";
import { warpperEnv } from "./build";
import { getPluginsList } from "./build/plugins";
import { include, exclude } from "./build/optimize";
import { UserConfigExport, ConfigEnv, loadEnv } from "vite";

/** 当前执行node命令时文件夹的地址（工作目录） */
const root: string = process.cwd();

/** 路径查找 */
const pathResolve = (dir: string): string => {
  return resolve(__dirname, ".", dir);
};

/** 设置别名 */
const alias: Record<string, string> = {
  "@": pathResolve("src"),
  "@build": pathResolve("build")
};

const { dependencies, devDependencies, name, version } = pkg;
const __APP_INFO__ = {
  pkg: { dependencies, devDependencies, name, version },
  lastBuildTime: dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss")
};

export default ({ command, mode }: ConfigEnv): UserConfigExport => {
  const { VITE_CDN, VITE_PORT, VITE_COMPRESSION, VITE_PUBLIC_PATH } =
    warpperEnv(loadEnv(mode, root));
  return {
    base: VITE_PUBLIC_PATH,
    root,
    resolve: {
      alias
    },
    // 服务端渲染
    server: {
      // 是否开启 https
      https: false,
      // 端口号
      port: VITE_PORT,
      host: "0.0.0.0",
      // 添加允许iframe嵌入的头部
      headers: {
        'X-Frame-Options': 'ALLOWALL',
        'Content-Security-Policy': 'frame-ancestors http://***********:9291 https://***********:9291 http://localhost:* https://localhost:* http://127.0.0.1:* https://127.0.0.1:* *',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Origin, X-Requested-With, Content-Type, Accept, Authorization'
      },
      // 本地跨域代理 https://cn.vitejs.dev/config/server-options.html#server-proxy
      proxy: {
        '/api/auth-center/': {
          // 测试的时候记得删除upload前缀
          target: 'http://***********:8187/selling', // 测试通用
          // target: 'http://************:8187/selling', //正式通用
          // target: 'http://**************:9292/',
          // target: 'http://*************:9081',
          // target: 'http://************:8000/',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ''),
          // rewrite: (path) => path.replace(/^\/api\/auth-center/, ''),
        },
        "/api": {
          // 这里填写后端地址
          // target: "http://172.18.0.46:8187/selling",
          target: "http://***********:8187/selling", // 测试
          // target: "http://************:9882", // l./
          // target: "http://************:8187/selling", //正式
          // target: "http://************:9882", // whq
          // target: "http://***********:9882",
          // target: "http://172.30.25.58:9882",//fc
          changeOrigin: true,
          rewrite: path => path.replace(/^\/api/, "")
        },
        "/selling": {
          // 这里填写后端地址(一般是上传文件的路径)
          target: "http://***********:8187/selling",
          // target: "http://************:9882", // whq
          // target: "http://*************:9882",
          // target: "http://************:9882",
          // target: "http://***********:9882",
          changeOrigin: true,
          rewrite: path => path.replace(/^\/selling/, "")
        },
      }
    },
    // 预览模式配置
    preview: {
      headers: {
        'X-Frame-Options': 'ALLOWALL',
        'Content-Security-Policy': 'frame-ancestors http://***********:9291 https://***********:9291 http://localhost:* https://localhost:* http://127.0.0.1:* https://127.0.0.1:* *',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Origin, X-Requested-With, Content-Type, Accept, Authorization'
      }
    },
    plugins: getPluginsList(command, VITE_CDN, VITE_COMPRESSION),
    // https://cn.vitejs.dev/config/dep-optimization-options.html#dep-optimization-options
    optimizeDeps: {
      include,
      exclude
    },
    build: {
      sourcemap: false,
      // 消除打包大小超过500kb警告
      chunkSizeWarningLimit: 4000,
      rollupOptions: {
        input: {
          index: pathResolve("index.html"),
          "static-screen": pathResolve("static-screen.html")
        },
        // 静态资源分类打包
        output: {
          chunkFileNames: "static/js/[name]-[hash].js",
          entryFileNames: "static/js/[name]-[hash].js",
          assetFileNames: "static/[ext]/[name]-[hash].[ext]"
        }
      }
    },
    define: {
      __INTLIFY_PROD_DEVTOOLS__: false,
      __APP_INFO__: JSON.stringify(__APP_INFO__)
    }
  };
};
