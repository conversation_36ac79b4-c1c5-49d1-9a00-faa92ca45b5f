<script setup lang="ts">
import {
  ref,
  computed,
  onMounted,
  watch,
  type Ref,
  nextTick,
  PropType,
  onActivated
} from "vue";
import { useAppStoreHook } from "@/store/modules/app";
import {
  delay,
  useDark,
  useECharts,
  type EchartOptions
} from "@pureadmin/utils";
import type { EChartsOption } from "echarts";
import * as echarts from "echarts/core";
import { triggerWindowResize } from "@/utils/event";
const props = defineProps({
  height: {
    type: String as PropType<string>,
    default: "100%"
  },
  width: {
    type: String as PropType<string>,
    default: "100%"
  },
  series: {
    type: Array as PropType<Array<object>>,
    default: () => []
  },
    time: {
    type: String,
    default: ""
  }
});
const { isDark } = useDark();

const theme: EchartOptions["theme"] = computed(() => {
  return isDark.value ? "dark" : "light";
});

const chartRef = ref<HTMLDivElement | null>(null);
const { setOptions, resize } = useECharts(chartRef as Ref<HTMLDivElement>, {
  theme
});
const emit = defineEmits(["update"]);
const getOption = (): EChartsOption => {
  return {
    tooltip: {
      trigger: "item"
    },
    legend: {
      bottom: "5%",
      right: "center"
    },
    series: props.series
  };
};

watch(
  () => useAppStoreHook().getSidebarStatus,
  () => {
    delay(600).then(() => resize());
  }
);
watch(
  () => props,
  () => setOptions(getOption() as EChartsOption, false, { notMerge: true }),
  {
    immediate: true,
    deep: true
  }
);
onMounted(() => {
  nextTick(() => {
    var myChart = echarts.init(chartRef.value!);
    myChart.on("click", params => {
      emit("update", params.data.id);
    });
  });
  delay(300).then(() => resize());
});
onActivated(() => triggerWindowResize());
</script>

<template>
  <div ref="chartRef" :style="{ height, width }" />
</template>
