import { request } from '@/utils/request'
// 获取列表
export const queryPageSalePackage = (data: any) => {
    const res = request.post<any>({
      url: '/salePackage/queryPage',
      data,
    })
    return res
  }
// 售电套餐新增
export const addSalePackage = (data: any) => {
    const res = request.post<any>({
      url: '/salePackage/addSalePackage',
      data,
    })
    return res
  }
// 批量删除套餐
export const deleteBatch = (data: any) => {
    const res = request.post<any>({
      url: '/salePackage/deleteBatch',
      data,
    })
    return res
  }
  

// 批量导出
export const exportBatch = (data: any) => {
    const res = request.post<any>({
      url: '/salePackage/exportBatch',
      responseType: 'blob',
      data,
    },
    { isTransformResponse: false },
  )
    return res
  }
  
// 套餐批量导入
export const importBatch = (data: any) => {
    return request.post<any>({
      url: '/salePackage/importBatch',
      data,
      headers: {
        "Content-Type": "multipart/form-data"
      }
    })
  }
// 套餐上-下架
export const onOrDownShelf = (data: any) => {
    const res = request.post<any>({
      url: '/salePackage/onOrDownShelf',
      data,
    })
    return res
  }
  
// 修改套餐
export const updateSalePackage = (data: any) => {
    const res = request.post<any>({
      url: '/salePackage/updateSalePackage',
      data,
    })
    return res
  }
  
  // 根据id查询信息 /salePackage/selectById
  export const selectById = (data: any) => {
    const res = request.get<any>({
      url: `/salePackage/selectById?id=${data}`,

    })
    return res
  }
