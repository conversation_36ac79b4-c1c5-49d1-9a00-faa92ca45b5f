import {request} from '@/utils/request'

// const preBaseUrl = '/spotPricePred' // 原先接口
const preBaseUrl = '/spotTradPricePred' //本地
// const preBaseUrl = '' //本地
// const preBaseUrl = '/web/spotTradPricePred' //线上
// 一.获取节点下拉：
export const nodeList = () => {
  const res = request.get<any>({
    url: `${preBaseUrl}/nodeList`,
  })
  return res
}
// 二.分时电价查询(折线图)：
export const hourPrice = (data: any) => {
  const res = request.post<any>({
    url: `${preBaseUrl}/hourPrice`,
    data,
  })
  return res
}
// 三.分时数据查询(蓝色折线图)：
export const hourData = (data: any) => {
  const res = request.post<any>({
    url: `${preBaseUrl}/hourData`,
    data,
  })
  return res
}
// 四.分日查询：
export const dailyData = (data: any) => {
  const res = request.post<any>({
    // url: `${preBaseUrl}/spotPricePred/dailyData`,
    url: `${preBaseUrl}/dailyData`,
    data,
  })
  return res
}
// 五.下载模板：
export const downloadTemplate = () => {
  const res = request.get<any>(
    {
      url: `${preBaseUrl}/downloadTemplate`,
      responseType: 'blob',
    },
    { isTransformResponse: false },
  )
  return res
}
// 六.上传文件：
export const uploadTemplate = (formData: any, data: any) => {
  const { dateDate, nodeId, timeType } = data
  return request.post({
    url: `${preBaseUrl}/upload?dateDate=${dateDate}&nodeId=${nodeId}&timeType=${timeType}`,
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}
// 七.更新修正数据：
export const updateCorrectionData = (data: any) => {
  return request.post({
    url: `${preBaseUrl}/updateCorrectionData`,
    data,
  })
}
// 八.重置修正数据：
export const resetCorrectionData = (data: any) => {
  return request.post({
    url: `${preBaseUrl}/resetCorrectionData`,
    data,
  })
}

// 预测按钮
export const forecastButtonAPI = (data: any) => {
  return request.post({
    url: `${preBaseUrl}/pred`,
    data,
  })
}

// 统一出清点电价导出
export const exportMarketPriceAPI = (data: any) => {
  const res = request.get<any>(
    {
      url: `/tsso/market-boundary/exportSettlementData?startTime=${data[0]}&endTime=${data[1]}`,
      responseType: 'blob',
    },
    { isTransformResponse: false },
  )
  return res
}