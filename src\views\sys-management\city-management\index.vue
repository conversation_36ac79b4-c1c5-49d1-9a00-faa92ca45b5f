<template>
  <section class="flex overflow-auto">
    <div class="w-[15%] tree-wrapper mt-[20px]">
      <el-tree
        :data="treeData"
        :props="{ children: 'children', label: 'name' }"
        @node-click="handleNodeClick"
      />
    </div>
    <div class="w-[85%]">
      <div class="app-content-container container-h">
        <div class="flex justify-between mb-[20px]">
          <div class="flex">
            <div class="app-form-group">
              <div>
                <span>名称：</span>
                <el-input
                  v-model="searchInfo.name"
                  clearable
                  placeholder="请输入名称"
                  class="filter-item"
                />
              </div>
            </div>
            <div class="app-btn-group">
              <el-button class="filter-item" type="primary" @click="getList"
                >查询</el-button
              >
              <el-button class="filter-item" @click="handleReset"
                >重置</el-button
              >
            </div>
          </div>
          <el-button class="filter-item" @click="handleCreate" type="primary"
            >新增</el-button
          >
        </div>
        <pure-table
          :columns="columns"
          border
          stripe
          :loading="loading"
          :data="tableData"
          :pagination="pagination"
          @page-size-change="onSizeChange"
          @page-current-change="onCurrentChange"
        >
          <template #name="{ row }">
            <a @click="getDetail(row.id)" style="color: #007bf7">{{
              row.name
            }}</a>
          </template>
          <template #operation="{ row }">
            <el-button
              link
              type="primary"
              size="small"
              @click="getDetail(row.id)"
              >编辑</el-button
            >
            <el-popconfirm
              width="220"
              confirm-button-text="确定"
              cancel-button-text="取消"
              :icon="InfoFilled"
              icon-color="#626AEF"
              title="确认删除？"
              @confirm="handleDel(row.id)"
            >
              <template #reference>
                <el-button link type="danger" size="small">删除</el-button>
              </template>
            </el-popconfirm>
          </template>
        </pure-table>
      </div>

      <el-dialog v-model="dialogVisible" :title="title" width="40%">
        <el-form
          ref="ruleFormRef"
          :model="formInline"
          :rules="dataFormRules"
          label-width="160px"
        >
          <el-row>
            <el-col :span="24">
              <el-form-item label="父級：" prop="parentId">
                <el-tree-select
                  clearable
                  v-model="formInline.parentId"
                  check-strictly
                  style="width: 100%"
                  :props="{ children: 'children', label: 'name', value: 'id' }"
                  placeholder="请选择"
                  :data="treeData"
                  :render-after-expand="false"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="名称：" prop="name">
                <el-input
                  maxlength="10"
                  show-word-limit
                  v-model="formInline.name"
                  placeholder="请输入"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="排序：" prop="serialNumber">
                <el-input
                  maxlength="10"
                  show-word-limit
                  v-model="formInline.serialNumber"
                  placeholder="请输入"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" @click="submit(ruleFormRef)">
              确定
            </el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </section>
</template>

<script lang="ts">
import { ref, reactive, onMounted } from "vue";
import { columns } from "./components/data";
import type { PaginationProps } from "@pureadmin/table";
import { delay } from "@pureadmin/utils";
import { InfoFilled } from "@element-plus/icons-vue";
import {
  getCityTreeApi,
  saveSysCityDataApi,
  getSysCityListApi,
  getSysCityDetailApi,
  delSysCityByIdApi
} from "@/api/sys/city";
import {
  CityTreeListModel,
  CityResultModel,
  CityPageModel
} from "@/model/cityModel";
import type { FormInstance } from "element-plus";
import { ElMessage } from "element-plus";
export default {
  name: "CityManagement",
  setup() {
    onMounted(async () => {
      getCityTreeData();
      getList();
    });

    const ruleFormRef = ref<FormInstance>();
    const title = ref<string>("新增");
    const loading = ref(false);
    const dialogVisible = ref<boolean>(false);
    const tableData = ref([]);
    const dataFormRules = {
      name: [
        {
          required: true,
          message: "名称是必填项",
          trigger: "blur"
        }
      ]
    };
    const treeData = ref<CityTreeListModel[]>([]);
    function handleNodeClick(node) {
      // console.log(node,'node');
      searchInfo.parentId = node.id;
      getList();
    }
    async function getCityTreeData() {
      const tree = await getCityTreeApi();
      treeData.value = tree.data;
    }
    /** 分页配置 */
    const pagination = reactive<PaginationProps>({
      pageSize: 10,
      currentPage: 1,
      pageSizes: [10, 20, 40, 50],
      total: 0,
      align: "right",
      background: true,
      small: false
    });
    const searchInfo = reactive<CityPageModel>({
      parentId: undefined,
      name: undefined,
      pageNo: 1,
      pageSize: 10
    });
    const formInline = reactive<CityResultModel>({
      id: undefined,
      parentId: "",
      name: "",
      serialNumber: 999
    });
    async function getList() {
      loading.value = true;
      const { data } = await getSysCityListApi(searchInfo);
      pagination.total = Number(data.totalCount);
      tableData.value = data.data;
      delay(600).then(() => {
        loading.value = false;
      });
    }
    function handleReset() {
      searchInfo.name = undefined;
      getList();
    }

    function onSizeChange(val) {
      searchInfo.pageSize = val;
      getList();
    }

    function onCurrentChange(val) {
      searchInfo.pageNo = val;
      getList();
    }

    async function getDetail(id: number) {
      title.value = "编辑";
      dialogVisible.value = true;
      const res = await getSysCityDetailApi(id);
      formInline.id = res.data.id;
      formInline.name = res.data.name;
      formInline.parentId = res.data.parentId;
      formInline.serialNumber = res.data.serialNumber;
    }

    async function handleDel(id: number) {
      await delSysCityByIdApi(id);
      ElMessage({
        message: "操作成功",
        type: "success"
      });
      getList();
      getCityTreeData()
    }

    async function submit(formEl: FormInstance | undefined) {
      if (!formEl) return;
      await formEl.validate(async (valid, fields) => {
        if (valid) {
          if (formInline.parentId === undefined) {
            formInline.parentId = "0";
          }
          const res = await saveSysCityDataApi(formInline);
          if (res.code === "200") {
            ElMessage({
              message: "操作成功",
              type: "success"
            });
            getList();
            getCityTreeData();
            dialogVisible.value = false;
          } else {
            ElMessage({
              message: res.message,
              type: "error"
            });
          }
        } else {
          console.log("error submit!", fields);
        }
      });
    }

    function handleCreate() {
      title.value = "新增";
      formInline.id = undefined;
      formInline.serialNumber = 999;
      formInline.parentId = undefined;
      formInline.name = "";
      dialogVisible.value = true;
    }
    return {
      treeData,
      InfoFilled,
      submit,
      getList,
      getDetail,
      handleDel,
      handleReset,
      loading,
      dialogVisible,
      pagination,
      searchInfo,
      onSizeChange,
      onCurrentChange,
      columns,
      tableData,
      title,
      handleCreate,
      formInline,
      dataFormRules,
      ruleFormRef,
      handleNodeClick
    };
  }
};
</script>

<style lang="scss" scoped>
.tree-wrapper {
  padding: 20px;
  border-radius: 6px;
  background-color: #fff;
  box-shadow: 0px 2px 22px rgba(0, 0, 0, 0.06);
  margin-right: 20px;
  overflow-y: auto;
}
</style>
