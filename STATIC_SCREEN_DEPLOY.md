# 静态大屏部署方案

## 方案概述

本方案创建了一个独立的静态数据大屏，具有以下特点：

- ✅ 不请求接口，使用静态数据
- ✅ 不被路由拦截，独立访问
- ✅ 不需要登录验证
- ✅ 不需要 layout 布局
- ✅ 通过 nginx 配置独立端口访问
- ✅ 支持全屏显示

## 文件结构

```
├── src/views/static-big-screen/index.vue    # 静态大屏页面
├── src/static-screen-main.ts                # 独立入口文件
├── static-screen.html                       # 独立HTML入口
├── nginx-static-screen.conf                 # Nginx配置
├── Dockerfile.static-screen                 # Docker配置
├── build-static-screen.sh                   # 构建脚本
└── STATIC_SCREEN_DEPLOY.md                  # 部署说明
```

## 本地开发

### 1. 安装依赖

```bash
pnpm install
```

### 2. 开发模式运行

```bash
# 运行主应用（包含静态大屏）
pnpm dev

# 访问静态大屏
http://localhost:8080/static-screen.html
```

### 3. 验证功能

访问静态大屏后，应该能看到：

- ✅ 完整的大屏布局和背景
- ✅ 实时时间显示
- ✅ 公司简介滚动文本
- ✅ ECharts 收益概览图表
- ✅ ECharts 结算管理图表
- ✅ 江西省地图（带数据点，支持点击弹窗）
- ✅ ECharts 柱状图（地区统计）
- ✅ 代理大用户滚动列表
- ✅ ECharts 饼图（用户画像）
- ✅ 销售漏斗图
- ✅ 全屏功能（点击标题）
- ✅ 响应式布局（支持不同屏幕尺寸）
- ✅ 非全屏状态下可滚动

### 4. 响应式测试

访问测试页面验证响应式功能：

```
http://localhost:8080/test-screen-responsive.html
```

可以测试不同分辨率下的显示效果。

### 5. 全屏状态测试

访问全屏测试页面验证全屏功能：

```
http://localhost:8080/test-fullscreen.html
```

可以测试全屏状态下的高度适配和调试信息。

## nginx 部署配置说明

### 端口访问策略

项目提供了两种 nginx 配置方案：

#### 1. 纯静态大屏模式（推荐）

- **配置文件**: `nginx-pure-static-screen.conf`
- **端口**: 8081
- **特点**: 只能访问静态大屏，所有路由都指向静态大屏页面
- **适用场景**: 专门的大屏展示，不需要访问其他页面

#### 2. 混合模式

- **配置文件**: `nginx-static-screen.conf`
- **端口**: 8081（纯静态）+ 8082（混合）
- **特点**:
  - 8081 端口：纯静态大屏
  - 8082 端口：默认显示静态大屏，但可以通过路由访问其他页面
- **适用场景**: 需要在同一端口下访问其他功能页面

### 路由访问说明

**问题**: 部署到 8081 端口后能否通过路由访问其他页面？

**回答**:

- 使用 `nginx-pure-static-screen.conf`：**不能**，所有请求都会返回静态大屏
- 使用 `nginx-static-screen.conf` 的 8082 端口：**可以**，支持 Vue 路由访问其他页面

## 生产部署

### 方式一：传统部署

#### 1. 构建项目

```bash
# 使用构建脚本
chmod +x build-static-screen.sh
./build-static-screen.sh

# 或手动构建
pnpm build:static-screen
```

#### 2. 配置 Nginx

```bash
# 复制nginx配置
sudo cp nginx-static-screen.conf /etc/nginx/sites-available/static-screen
sudo ln -s /etc/nginx/sites-available/static-screen /etc/nginx/sites-enabled/

# 复制构建文件到nginx目录
sudo cp -r dist/* /usr/share/nginx/html/

# 重启nginx
sudo systemctl restart nginx
```

#### 3. 访问地址

- 主应用：http://localhost/
- 静态大屏：http://localhost:8081/

### 方式二：Docker 部署

#### 1. 构建 Docker 镜像

```bash
docker build -f Dockerfile.static-screen -t static-screen-app .
```

#### 2. 运行容器

```bash
docker run -d \
  --name static-screen \
  -p 80:80 \
  -p 8081:8081 \
  static-screen-app
```

#### 3. 访问地址

- 主应用：http://localhost/
- 静态大屏：http://localhost:8081/

### 方式三：Docker Compose 部署

创建 `docker-compose.yml`：

```yaml
version: "3.8"
services:
  static-screen-app:
    build:
      context: .
      dockerfile: Dockerfile.static-screen
    ports:
      - "80:80"
      - "8081:8081"
    restart: unless-stopped
```

运行：

```bash
docker-compose up -d
```

## 自定义配置

### 修改静态数据

编辑 `src/views/static-big-screen/index.vue` 中的 `staticData` 对象：

```typescript
const staticData = ref({
  companyIntro: ["公司简介内容..."],
  contractUser: "1,256",
  targetPower: "85,420"
  // ... 其他数据
});
```

### 修改端口

编辑 `nginx-static-screen.conf` 中的端口配置：

```nginx
server {
    listen 8082;  # 修改为你需要的端口
    # ...
}
```

### 修改样式

静态大屏使用与原大屏相同的样式，如需自定义，可修改 `src/views/static-big-screen/index.vue` 中的样式部分。

## 注意事项

1. **资源路径**：确保所有图片资源路径正确，建议使用绝对路径
2. **浏览器兼容性**：建议使用现代浏览器访问
3. **全屏显示**：点击标题可进入全屏模式
4. **数据更新**：如需更新数据，修改静态数据后重新构建部署
5. **端口冲突**：确保 8081 端口未被占用

## 故障排除

### 1. ECharts 相关错误

**错误信息**: `useECharts: echarts未绑定到globalProperties`
**解决方案**:

- 确保在 `src/static-screen-main.ts` 中正确导入和使用了 `useEcharts`
- 检查 `src/plugins/echarts/index.ts` 文件是否存在

**错误信息**: `Renderer 'undefined' is not imported`
**解决方案**:

- 确保正确导入 ECharts 渲染器
- 检查 ECharts 插件配置是否正确

### 1.1. 地图相关错误

**错误信息**: `registerMap doesn't exists`
**解决方案**:

- 确保在 HTML 文件中加载了 `/resource/map/mapJson.js`
- 检查 `window.mapJSON` 是否正确定义

**错误信息**: 地图点击弹窗无响应
**解决方案**:

- 已修复地图组件的点击事件处理
- 确保 `window.handleMapDetail` 函数正确绑定
- 静态版本使用 `window.handleStaticMapDetail` 函数

### 2. 组件导入错误

**错误信息**: `Module has no default export`
**解决方案**:

- 当前版本已移除对复杂图表组件的依赖
- 使用 CSS 和 HTML 实现的简化图表展示

### 3. 滚动组件错误

**错误信息**: `Cannot find module 'vue3-scroll-seamless'`
**解决方案**:

- 已在代码中添加 `@ts-ignore` 注释
- 如果仍有问题，可以安装类型定义: `pnpm add -D @types/vue3-scroll-seamless`

### 4. 页面无法访问

- 检查 nginx 配置是否正确
- 检查端口是否被占用: `netstat -tulpn | grep :8081`
- 检查防火墙设置: `sudo ufw status`

### 5. 样式显示异常

- 检查静态资源路径
- 确认 CSS 文件是否正确加载
- 检查浏览器控制台是否有 404 错误

### 6. 响应式布局问题

**问题**: 非全屏状态下内容被截断
**解决方案**:

- 已优化大屏高度自适应，支持滚动
- 使用 `min-height: 1080px` 和 `height: auto`
- 小屏幕下自动调整组件尺寸

**问题**: 不同分辨率下显示异常
**解决方案**:

- 使用响应式测试页面验证: `http://localhost:8080/test-screen-responsive.html`
- 检查媒体查询是否正确应用
- 确保背景图片使用 `background-attachment: fixed`

### 7. 全屏状态问题

**问题**: 全屏状态下高度没有 100%，下方出现空白
**解决方案**:

- 已添加强制性的 `:fullscreen` CSS 选择器和 `!important` 声明
- 使用 `FullscreenManager` 类进行全屏状态管理
- 全屏时自动设置 `height: 100vh !important` 和 `overflow: hidden !important`
- 隐藏底部装饰元素避免影响高度计算
- 使用 `test-fullscreen.html` 页面进行调试

**问题**: 全屏切换不生效
**解决方案**:

- 检查浏览器是否支持全屏 API
- 确保用户手势触发（点击事件）
- 查看控制台是否有全屏相关错误
- 使用调试页面检查元素高度和样式

**问题**: 全屏状态下内容被截断
**解决方案**:

- 内容区域使用 `calc(100vh - 100px)` 计算高度
- 移除底部 padding，改为 `padding: 100px 45px 0px 45px`
- 确保所有容器都使用 `box-sizing: border-box`

### 8. 开发环境测试

使用提供的测试脚本:

```bash
chmod +x test-static-screen.sh
./test-static-screen.sh
```

## 技术栈

- Vue 3 + TypeScript
- Element Plus
- ECharts
- Vite
- Nginx
- Docker
