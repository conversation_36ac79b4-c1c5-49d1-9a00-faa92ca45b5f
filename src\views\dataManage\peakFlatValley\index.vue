<template>
  <div class="container-main">
    <!--查询-->
    <el-card>
      <div class="title">查询条件</div>
      <div class="searchForm">
        <el-row style="margin-top: 20px">
          <el-col :span="6">
            <span>年份：</span>
            <el-select v-model="searchForm.yearTime">
              <el-option v-for="(item, index) in yearList" :key="index" :label="item" :value="item"></el-option>
            </el-select>
          </el-col>
          <el-col :span="6">
            <span>季节：</span>
            <el-select v-model="searchForm.season" :disabled="!!configForm.valleyPeakId" placeholder="全部">
              <el-option v-for="item in seasons" :key="item[0]" :label="item[1].name" :value="item[0]" />
            </el-select>
          </el-col>
        </el-row>
        <div class="operation" style="display: flex; margin-left: 20px; margin-top: 20px">
          <el-button class="no-border" type="success" @click="clickQueryBtn">
            <img :src="getAssetURL('Search')" style="margin-right: 6px;" />
            查询
          </el-button>
          <el-button class="no-border" type="info" @click="clickResetBtn">
            <img :src="getAssetURL('Refresh')" style="margin-right: 6px;" />
            重置
          </el-button>
        </div>
      </div>
    </el-card>
    <el-card style="margin-top: 20px">
      <div class="button" @click="onAdd">
        <el-icon>
          <CirclePlus />
        </el-icon>
        新增峰谷配置
      </div>
      <div class="table-content">
        <el-table :data="tableData" :header-cell-style="{
          background: '#f2f3f5',
          textAlign: 'center',
        }" :cell-style="{ textAlign: 'center' }">
          <el-table-column prop="yearTime" label="年份" align="center" />
          <el-table-column label="季节" align="center" prop="seasonType"></el-table-column>
          <el-table-column prop="months" label="月份" align="center" />
          <el-table-column prop="peakStartTime" label="尖峰-时段" align="center" width="220" />
          <el-table-column prop="summitStartTime" label="高峰-时段" align="center" width="220" />
          <el-table-column prop="flatSegmentStartTime" label="平段-时段" align="center" width="220" />
          <el-table-column prop="lowEbbStartTime" label="低谷-时段" align="center" width="220" />
          <el-table-column fixed="right" label="操作" align="center" width="200px">
            <template #default="scope">
              <div style="display: flex">
                <div @click="onEdit(scope.row)" style="margin-right: 15px; display: flex;
                  align-items: center; color: #165dff; cursor: pointer">
                  <img :src="getAssetURL('edit')" style="margin-right: 2px" />
                  编辑
                </div>
                <div @click="onRemove(scope.row)" style="margin-right: 15px;display: flex;
                  align-items: center; color: #f53f3f; cursor: pointer">
                  <img :src="getAssetURL('delete')" style="margin-right: 2px" />
                  删除
                </div>
                <div @click="onDetail(scope.row)" style="color: #165dff; cursor: pointer">
                  详情
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-container">
          <el-pagination :current-page="params.pageNo" :page-sizes="[5, 10, 20, 30, 50, 100]" :page-size="params.pageSize"
            :total="params.total" layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
        </div>
      </div>
    </el-card>
    <el-dialog v-model="dialogVisible" :title="`${labelDialogStatus}峰谷配置信息`" width="45%" draggable>
      <el-form ref="ruleFormRef" :model="configForm" :rules="configFormRules" label-width="120px"
        :disabled="labelDialogStatus === DialogStatus.DETAIL">
        <el-row>
          <el-col :span="10">
            <el-form-item label="年份" prop="yearTime">
              <el-date-picker v-model="configForm.yearTime" type="year" :disabled="!!configForm.valleyPeakId" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="季节" prop="seasonTypeCode">
              <el-select v-model="configForm.seasonTypeCode" :disabled="!!configForm.valleyPeakId" placeholder="请选择季节"
                @change="seasonChange">
                <el-option v-for="item in seasons" :key="item[0]" :label="item[1].name" :value="item[0]" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="20">
            <el-form-item label="月份" prop="months">
              <el-input v-model="configForm.months" type="text" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="1">
            <el-button class="choose-button" @click="
              (monthSelectDialog = true),
              (tempParam = configForm.months),
              console.log(configForm.months)
              ">
              选择
            </el-button>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="20">
            <el-form-item label="尖端-时段" prop="peakStartTime">
              <el-input v-model="configForm.peakStartTime" type="text" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="1">
            <el-button class="choose-button" @click="
              (timeSelectDialog = true),
              (timeSelectNumber = 1),
              console.log(tempParam),
              (tempParam = configForm.peakStartTime)
              ">
              选择
            </el-button>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="20">
            <el-form-item label="高峰-时段" prop="summitStartTime">
              <el-input disabled v-model="configForm.summitStartTime" type="text"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="1">
            <el-button class="choose-button" @click="
              (timeSelectDialog = true),
              (timeSelectNumber = 2),
              (tempParam = configForm.summitStartTime)
              ">
              选择
            </el-button>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="20">
            <el-form-item label="平段-时段" prop="flatSegmentStartTime">
              <el-input disabled v-model="configForm.flatSegmentStartTime" type="text"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="1">
            <el-button class="choose-button" @click="
              (timeSelectDialog = true),
              (timeSelectNumber = 3),
              (tempParam = configForm.flatSegmentStartTime)
              ">
              选择
            </el-button>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="20">
            <el-form-item label="低谷-时段" prop="lowEbbStartTime">
              <el-input v-model="configForm.lowEbbStartTime" type="text" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="1">
            <el-button class="choose-button" @click="
              (timeSelectDialog = true),
              (timeSelectNumber = 4),
              (tempParam = configForm.lowEbbStartTime)
              ">
              选择
            </el-button>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button class="cancel-button" @click="dialogVisible = false">
            取消
          </el-button>
          <el-button type="primary" class="save-button" @click="onConfirm(ruleFormRef)">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog v-model="monthSelectDialog" title="选择月份" width="25%" :append-to-body="true" @close="() => {
      tempParam = []
    }
      ">
      <div class="dialog-body" style="margin-left: 4rem">
        <div class="yearTime">
          年份：{{
            configForm.yearTime
            ? dayjs(configForm.yearTime).format('YYYY')
            : '请选择年份'
          }}
        </div>
        <div class="monthTime">
          <div class="month-title">月份：</div>
          <div class="check">
            <!-- <el-checkbox-group v-model="configForm.months"> -->
            <el-checkbox-group v-model="tempParam">
              <el-row>
                <el-col :span="6" v-for="(item, index) in months" :key="index">
                  <el-checkbox :label="item.name" :value="item.value" />
                </el-col>
              </el-row>
            </el-checkbox-group>
          </div>
        </div>
        <div class="operation-button" style="
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 2rem;
          ">
          <el-button class="save-button" @click="
            (configForm.months = tempParam), (monthSelectDialog = false)
            ">
            保存
          </el-button>
          <el-button class="cancel-button" @click="monthSelectDialog = false">
            取消
          </el-button>
        </div>
      </div>
    </el-dialog>
    <el-dialog v-model="timeSelectDialog" title="选择时刻" width="30%" :append-to-body="true" @close="() => {
      tempParam = []
    }
      ">
      <div class="dialog-body" style="margin-left: 4rem">
        <div class="time-select">
          <div class="time-title">时刻选择：</div>
          <div class="check">
            <el-checkbox-group v-model="tempParam">
              <el-row>
                <el-col :span="6" v-if="timeSelectNumber == 1" v-for="(item, index) in hours1" :key="index">
                  <el-checkbox :label="item.name" :value="item.value" />
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="6" v-if="timeSelectNumber == 2" v-for="(item, index) in hours2" :key="index">
                  <el-checkbox :label="item.name" :value="item.value" />
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="6" v-if="timeSelectNumber == 3" v-for="(item, index) in hours3" :key="index">
                  <el-checkbox :label="item.name" :value="item.value" />
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="6" v-if="timeSelectNumber == 4" v-for="(item, index) in hours4" :key="index">
                  <el-checkbox :label="item.name" :value="item.value" />
                </el-col>
              </el-row>
            </el-checkbox-group>
          </div>
        </div>
        <div class="operation-button" style="
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 2rem;
          ">
          <el-button class="save-button" @click="
            (timeSelectDialog = false),
            (configForm[timeSelectDialogTitle[timeSelectNumber]] =
              tempParam),
            console.log(tempParam)
            ">
            保存
          </el-button>
          <el-button class="cancel-button" @click="timeSelectDialog = false">
            取消
          </el-button>
        </div>
      </div>
    </el-dialog>
    <el-card style="margin-top: 20px">
      <template #header>
        <div class="title">
          <span>分时电价机制</span>
        </div>
        <div class="card-header" style="margin-top: 1rem">
          <div class="search">
            <span style="margin-right: 10px;">年份:</span>
            <el-select v-model="timeYearTime" @change="changeYearTime">
              <el-option v-for="item in yearList" :key="item" :label="item" :value="item"></el-option>
            </el-select>
          </div>
          <div class="card-header-content">
            <div v-for="(item, index) in colorArr.slice(-4)" :key="item" class="card-header-content-item">
              <div style="margin-left: 8px">
                {{ ['尖端', '高峰', '平段', '低谷'][index] }}:
              </div>
              <div :style="{ backgroundColor: item }" style="width: 48px; height: 24px; margin-left: 12px"></div>
            </div>
          </div>
        </div>
      </template>
      <div class="table-area">
        <el-table :data="tableData1" border :header-cell-style="{
          background: '#f2f3f5',
          textAlign: 'center',
        }" :cell-style="{ height: '58px', padding: '0px' }">
          <el-table-column prop="month" label="月份" align="center" width="58px">
            <template #default="scope">
              <div class="month-col">{{ scope.row.month }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="season" label="季节" align="center" width="58px">
            <template #default="scope">
              <div class="month-col">{{ scope.row.season }}</div>
            </template>
          </el-table-column>
          <template v-for="item in columns" :key="item">
            <el-table-column width="58px" :label="item.label" :prop="item.prop" align="center">
              <template #default="{ row }">
                <div class="table-content-cell" :style="{ backgroundColor: colorArr[row[item.prop]] }">
                  &nbsp;
                </div>
              </template>
            </el-table-column>
          </template>
        </el-table>
      </div>
    </el-card>
  </div>
</template>
<script lang="ts" setup>
import { reactive, ref, computed, onMounted } from 'vue'
import { CirclePlus } from '@element-plus/icons-vue'
import { FormRules, FormInstance, ElMessage } from 'element-plus'
import dayjs from 'dayjs'
import { cloneDeep } from 'lodash'
import {
  selectPeakValley,
  queryYearTimes,
  queryLastPeakValley,
  deletePeakValley,
  updatePeakValley,
  savePeakValley,
  queryPageListByTime,
} from '@/api'

const getAssetURL = (image: any) => {
  return new URL(`../../../assets/svg/${image}.svg`, import.meta.url).href
}

const tableData = ref<any[]>([])

const originData = Array.from({ length: 12 }, (_: any, index: number) => ({
  month: `${index + 1}月`,
  ...Array.from({ length: 24 }, (_: any, subIndex: number) => ({
    [`${subIndex}`]: subIndex,
  })),
}))
console.log(originData)
const tableData1 = ref<any[]>(cloneDeep(originData))

const searchForm = reactive({
  yearTime: null,
  season: null,
})
const ruleFormRef = ref<FormInstance>()
const yearList = ref(<any>[])
const tempParam = ref(<any>[])
const timeYearTime = ref()
const params = reactive({
  pageSize: 5,
  total: 0,
  pageNo: 1,
})

const seasons = new Map([
  ['1', { name: '春季', value: '1' }],
  ['2', { name: '夏季', value: '2' }],
  ['3', { name: '秋季', value: '3' }],
  ['4', { name: '冬季', value: '4' }],
  ['5', { name: '其他', value: '5' }],
])
const months = [
  { name: '1月', value: '1' },
  { name: '2月', value: '2' },
  { name: '3月', value: '3' },
  { name: '4月', value: '4' },
  { name: '5月', value: '5' },
  { name: '6月', value: '6' },
  { name: '7月', value: '7' },
  { name: '8月', value: '8' },
  { name: '9月', value: '9' },
  { name: '10月', value: '10' },
  { name: '11月', value: '11' },
  { name: '12月', value: '12' },
]
interface hourInfo {
  value: string
  name: string
}
const hours: hourInfo[] = Array.from({ length: 24 }, (_, index) => ({
  value: `${index}`,
  name: `${index}:00`,
}))
const hours1 = computed(() => {
  return hours.filter(
    (item: hourInfo) =>
      !configForm.value.summitStartTime.includes(item.name) &&
      !configForm.value.flatSegmentStartTime.includes(item.name) &&
      !configForm.value.lowEbbStartTime.includes(item.name),
  )
})
const hours2 = computed(() => {
  return hours.filter(
    (item: hourInfo) =>
      !configForm.value.peakStartTime.includes(item.name) &&
      !configForm.value.flatSegmentStartTime.includes(item.name) &&
      !configForm.value.lowEbbStartTime.includes(item.name),
  )
})
const hours3 = computed(() => {
  return hours.filter(
    (item: hourInfo) =>
      !configForm.value.peakStartTime.includes(item.name) &&
      !configForm.value.summitStartTime.includes(item.name) &&
      !configForm.value.lowEbbStartTime.includes(item.name),
  )
})
const hours4 = computed(() => {
  return hours.filter(
    (item: hourInfo) =>
      !configForm.value.peakStartTime.includes(item.name) &&
      !configForm.value.summitStartTime.includes(item.name) &&
      !configForm.value.flatSegmentStartTime.includes(item.name),
  )
})
const labelDialogStatus = ref<DialogStatus>()

const formData = {
  yearTime: null,
  seasonTypeCode: null,
  months: [],
  peakStartTime: [],
  summitStartTime: [],
  flatSegmentStartTime: [],
  lowEbbStartTime: [],
}

const configForm = ref<any>({})

const dialogVisible = ref(false)
const monthSelectDialog = ref(false)
const timeSelectDialog = ref(false)
const timeSelectDialogTitle = [
  '',
  'peakStartTime',
  'summitStartTime',
  'flatSegmentStartTime',
  'lowEbbStartTime',
]
const timeSelectNumber = ref(0)
enum DialogStatus {
  CREATE = '新增',
  UPDATE = '修改',
  DETAIL = '详情',
}

// const monthList = reactive(
//   Array.from({ length: 12 }, (_, index) => ({
//     value: index + 1,
//     name: `${index + 1}月`,
//   })),
// )

// 表单校验要求
const configFormRules = reactive<FormRules>({
  yearTime: [{ required: true, message: '请输入年份', trigger: 'blur' }],
  seasonTypeCode: [{ required: true, message: '请输入季节', trigger: 'blur' }],
  months: [{ required: true, message: '请输入月份', trigger: 'blur' }],
})

const colorArr = ['#fff', '#f00', '#ffc000', '#00b050', '#00b0f0']

// 初始化展示表格信息
const columns = Array.from({ length: 24 }, (_, index) => ({
  prop: `${index}:00`,
  label: `${index + 1}:00`,
}))
const seasonChange = () => {
  configForm.value.months = []
}
const clickQueryBtn = () => {
  initData()
}
const clickResetBtn = () => {
  searchForm.season = null
  searchForm.yearTime = null
  params.pageNo = 1
  params.pageSize = 5
  initData()
}

/**
 * 移除配置
 */
const onRemove = async (row: any) => {
  deletePeakValley(row.id).then(() => {
    ElMessage.success('删除成功')
    initData()
  })
}

/**
 * 确认新增或修改
 */
const onConfirm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      if (
        !checkArray([
          ...configForm.value.peakStartTime,
          ...configForm.value.summitStartTime,
          ...configForm.value.flatSegmentStartTime,
          ...configForm.value.lowEbbStartTime,
        ])
      ) {
        return ElMessage.warning('时间段重复或没有覆盖24小时')
      }
      const tempData = {
        ...configForm.value,
        months: configForm.value.months.join(','),
        peakStartTime: configForm.value.peakStartTime.join(','),
        summitStartTime: configForm.value.summitStartTime.join(','),
        flatSegmentStartTime: configForm.value.flatSegmentStartTime.join(','),
        lowEbbStartTime: configForm.value.lowEbbStartTime.join(','),
        yearTime: dayjs(configForm.value.yearTime).format('YYYY'),
      }
      // 修改逻辑

      if (configForm.value.id) {
        updatePeakValley(tempData).then(() => {
          ElMessage.success('修改成功')
          dialogVisible.value = false
          initData()
        })
      }
      // 添加逻辑
      else {
        console.log('添加')
        savePeakValley(tempData).then(() => {
          ElMessage.success('添加成功')
          dialogVisible.value = false
          initData()
        })
      }

      function checkArray(arr: string[]) {
        // 检查是否包含0到23之间的所有数字
        console.log(arr)

        for (let i = 0; i <= 23; i++) {
          if (!arr.includes(`${i.toString()}:00`)) {
            return false
          }
        }

        // 检查是否有重复的数字
        const set = new Set(arr)
        if (set.size !== arr.length) {
          return false
        }

        return true
      }
    } else {
      console.log('error submit!', fields)
    }
  })
}

/**
 * 点击添加初始化信息
 */
const onAdd = () => {
  configForm.value = cloneDeep(formData)
  queryLastPeakValley().then((res) => {
    res.months = res.months.split(',')
    res.peakStartTime =
      res.peakStartTime === '' ? [] : res.peakStartTime.split(',')
    res.summitStartTime =
      res.summitStartTime === '' ? [] : res.summitStartTime.split(',')
    res.flatSegmentStartTime =
      res.flatSegmentStartTime === '' ? [] : res.flatSegmentStartTime.split(',')
    res.lowEbbStartTime =
      res.lowEbbStartTime === '' ? [] : res.lowEbbStartTime.split(',')
    res.id = null

    configForm.value = cloneDeep(res)
  })

  labelDialogStatus.value = DialogStatus.CREATE
  dialogVisible.value = true
}
/**
 * 初始化数据
 */
const initData = async () => {
  const res = await queryYearTimes()
  yearList.value = res
  timeYearTime.value = res[0]
  selectPeakValley({
    ...params,
    yearTime: searchForm.yearTime,
    seasonTypeCode: searchForm.season,
  }).then((res: any) => {
    console.log(res)
    tableData.value = res.data
    params.total = res.totalCount
  })
}
/**
 * 编辑配置
 * @param row 行记录
 */
const onEdit = (row: any) => {
  configForm.value = {
    id: row.id,
    yearTime: row.yearTime.toString(),
    seasonTypeCode: row.seasonTypeCode,
    months: row.months.split(','),
    peakStartTime: row.peakStartTime.split(','),
    peakEndTime: row.peakEndTime,
    summitStartTime: row.summitStartTime.split(','),
    summitEndTime: row.summitEndTime,
    flatSegmentStartTime: row.flatSegmentStartTime.split(','),
    flatSegmentEndTime: row.flatSegmentEndTime,
    lowEbbStartTime: row.lowEbbStartTime.split(','),
    lowEbbEndTime: row.lowEbbEndTime,
  }
  labelDialogStatus.value = DialogStatus.UPDATE
  dialogVisible.value = true
}
const onDetail = (row: any) => {
  configForm.value = {
    valleyPeakId: row.valleyPeakId,
    yearTime: row.yearTime.toString(),
    seasonTypeCode: row.seasonTypeCode,
    months: row.months.split(','),
    peakStartTime: row.peakStartTime.split(','),
    peakEndTime: row.peakEndTime,
    summitStartTime: row.summitStartTime.split(','),
    summitEndTime: row.summitEndTime,
    flatSegmentStartTime: row.flatSegmentStartTime.split(','),
    flatSegmentEndTime: row.flatSegmentEndTime,
    lowEbbStartTime: row.lowEbbStartTime.split(','),
    lowEbbEndTime: row.lowEbbEndTime,
  }
  labelDialogStatus.value = DialogStatus.DETAIL
  dialogVisible.value = true
}
const handleSizeChange = (val: number) => {
  params.pageSize = val
  initData()
}

const handleCurrentChange = (val: number) => {
  params.pageNo = val
  initData()
}
const changeYearTime = () => {
  initTimePriceData()
}
const initTimePriceData = () => {
  queryPageListByTime({
    yearTime: timeYearTime.value,
    pageNo: 1,
    pageSize: 12,
  }).then((res) => {
    console.log(res)
    tableData1.value = cloneDeep(originData)
    res.forEach((item: any) => {
      item.month = item.month.substring(0, item.month.length - 1)
      tableData1.value[Number(item.month) - 1].season = item.seasonType
      item.peakStartTime.forEach((time: any) => {
        tableData1.value[Number(item.month) - 1][time] = 1
      })
      item.summitStartTime.forEach((time: any) => {
        tableData1.value[Number(item.month) - 1][time] = 2
      })
      item.flatSegmentStartTime.forEach((time: any) => {
        tableData1.value[Number(item.month) - 1][time] = 3
      })
      item.lowEbbStartTime.forEach((time: any) => {
        tableData1.value[Number(item.month) - 1][time] = 4
      })
    })
  })
}
onMounted(async () => {
  await initData()
  initTimePriceData()
})
</script>
<style lang="scss" scoped>
.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding-top: 20px;
}

.table-area {
  &:deep(.el-table .cell) {
    padding: 4px;
    height: 100%;
  }

  &:deep(.month-col) {
    margin-top: 13px;
  }
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;

  &-content {
    display: flex;
    align-items: center;

    &-item {
      display: flex;
      align-items: center;
    }
  }
}

.title {
  display: flex;
  align-items: center;
  height: 32px;
  font-size: 16px;
  font-weight: 600;
  border-left: 3px solid #c3d7f0;
  padding-left: 6px;
}

.button {
  display: flex;
  height: 32px;
  width: 160px;
  padding: 0 16px;
  align-items: center;
  gap: 8px;
  background-color: #254f7a;
  color: white;
  border-radius: 2px;
  margin-bottom: 10px;
  cursor: pointer;
}

.choose-button {
  margin-left: 5px;
  margin-top: 4px;
  width: 70;
  background-color: #254f7a;
  color: white;
}

.el-form {
  .el-form-item {
    margin-bottom: 1.3rem;
  }
}

.table-row {
  height: 200px;
}

.table-content-cell {
  height: 100%;
  border-radius: 2px;
}
</style>
