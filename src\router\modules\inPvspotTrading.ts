export default {
  path: '/inPvspotTrading',
  name: 'inPvspotTrading',
  meta: {
    title: '现货交易',
    icon: "material-symbols:supervisor-account-rounded",
    rank: 5
  },
  redirect: '/inPvspotTrading/tradStrategy',
  children: [
    {
      path: '/inPvspotTrading/tradStrategy',
      component: () => import('@/views/inPvspotTrading/tradStrategy/index.vue'),
      name: 'tradStrategy',
      meta: {
        title: '现货交易策略',
        icon: '',
      },
    },
    {
      path: '/spotTrading/priceForecasting',
      component: () =>
        import('@/views/priceForecasting/index.vue'),
      name: 'priceForecasting',
      meta: {
        title: '价格预测',
        icon: '',
      },
    },
  ],
};