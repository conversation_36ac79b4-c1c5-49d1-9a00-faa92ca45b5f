<template>
  <section>
    <div class="app-search-card">
      <div class="app-form-group">
        <!-- <div class="ml-[20px]">
          <span>签约年份：</span>
          <el-date-picker
            style="width: 160px"
            v-model="searchInfo.signYear"
            type="year"
            placeholder="请选择"
            format="YYYY"
            value-format="YYYY"
          />
        </div> -->
        <div class="ml-[20px]">
          <span>客户分级：</span>
          <DictSelect style="width: 160px" v-model="searchInfo.customGrade" :clearable="true" dict-code="customGrade" />
        </div>
        <div class="ml-[20px]">
          <span>所属地区：</span>
          <el-tree-select style="width: 160px" default-expand-all v-model="searchInfo.areaId" check-strictly
            :props="{ children: 'children', label: 'name', value: 'id' }" placeholder="请选择" :data="treeData"
            :render-after-expand="false" />
        </div>
        <div class="ml-[20px]">
          <span>行业分类：</span>
          <DictSelect style="width: 160px" v-model="searchInfo.industryId" :clearable="true" dict-code="industry" />
        </div>
      </div>
      <div class="app-btn-group">
        <el-button class="filter-item" type="primary" @click="getList">查询</el-button>
        <el-button class="filter-item" @click="handleReset">重置</el-button>
      </div>
    </div>
    <div class="app-card mt-[20px] p-[20px] pt-[10px]">
      <div class="mb-[10px]">
        <el-button type="primary" @click="handleCreate">新增</el-button>
        <el-button type="primary" @click="handleExport">导出excel</el-button>
      </div>
      <pure-table :columns="columns" border stripe :loading="loading" :data="tableData" :pagination="pagination"
        @page-size-change="onSizeChange" @page-current-change="onCurrentChange"
        @selection-change="handleSelectionChange">
        <template #nameHeader>
          <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate" :column="columns[1]" />
        </template>
        <template #monthlyAverageElectricity>
          <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate" :column="columns[2]" />
        </template>
        <template #effectiveDateHeader>
          <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate" :column="columns[3]" />
        </template>
        <template #terminationDateHeader>
          <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate" :column="columns[4]" />
        </template>
        <template #electricityTypeHeader>
          <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate"
            :filter-options="electricityTypeOptions" :column="columns[5]" />
        </template>
        <template #followerNameHeader>
          <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate" :filter-options="optionsList"
            :column="columns[6]" />
        </template>
        <template #industryHeader>
          <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate" :filter-options="industryOptions"
            :column="columns[7]" />
        </template>
        <template #areaIdHeader>
          <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate" :filter-options="treeData"
            :column="columns[8]" />
        </template>
        <template #customerSourceHeader>
          <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate"
            :filter-options="customerSourceOptions" :column="columns[9]" />
        </template>
        <template #customTypeHeader>
          <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate" :filter-options="agentTypeOptions"
            :column="columns[10]" />
        </template>
        <template #statusHeader>
          <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate" :filter-options="statusOptions"
            :column="columns[11]" />
        </template>
        <template #name="{ row }">
          <a @click="handleDetail(row)" style="color: #007bf7">{{
            row.name
          }}</a>
        </template>
        <template #agentType="{ row }">
          <div>
            {{ filterDictText(row.agentType, agentTypeOptions) }}
          </div>
        </template>
        <template #status="{ row }">
          <div>
            {{ filterDictText(row.status, statusOptions) }}
          </div>
        </template>
        <template #customGrade="{ row }">
          <div>
            {{ filterDictText(row.customGrade, customGradeOptions) }}
          </div>
        </template>
        <template #customerSource="{ row }">
          <div>
            {{ filterDictText(row.customerSource, customerSourceOptions) }}
          </div>
        </template>
        <template #operation="{ row }">
          <el-button link type="primary" size="small" @click="handleEdit(row.id, row.followerId)">编辑</el-button>
          <el-button link type="primary" size="small" @click="handleDetail(row)">详情</el-button>
          <el-popconfirm width="220" confirm-button-text="确定" cancel-button-text="取消" :icon="InfoFilled"
            icon-color="#626AEF" title="确认删除？" @confirm="handleDel(row.id)">
            <template #reference>
              <el-button link type="danger" size="small">删除</el-button>
            </template>
          </el-popconfirm>
        </template>
      </pure-table>
    </div>

    <el-dialog width="60%" append-to-body destroy-on-close v-model="selectVisible" title="标签选择">
      <tag-select @change="handleChange" />
    </el-dialog>
  </section>
</template>

<script lang="ts" setup>
import { onMounted,onActivated } from "vue";
import { usePowerCustomer } from "@/hooks/customer/usePowerCustomer";
import { columns } from "../column";
import TagSelect from "@/components/Core/TagSelect/index.vue";
import TableHeadPopover from "@/components/Core/TableHeadPopover/index.vue";
defineOptions({
  name: "CustomerOpenSea"
});
const {
  handleExport,
  handleTableUpdate,
  handleChange,
  selectVisible,
  timeSlot,
  InfoFilled,
  handleReset,
  handleDel,
  filterDictText,
  customerSourceOptions,
  treeData,
  handleCreate,
  handleEdit,
  tableData,
  loading,
  pagination,
  onSizeChange,
  onCurrentChange,
  handleSelectionChange,
  handleDetail,
  agentTypeOptions,
  statusOptions,
  searchInfo,
  getList,
  getCityTreeData,
  getUserList,
  customGradeOptions,
  electricityTypeOptions,
  industryOptions,
  optionsList
} = usePowerCustomer(true);
onMounted(() => {
  getCityTreeData();
  getUserList();
  getList();
});
onActivated(() => {
   getList();
})
</script>

<style lang="scss" scoped></style>
