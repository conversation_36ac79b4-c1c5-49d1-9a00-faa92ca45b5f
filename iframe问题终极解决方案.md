# iframe嵌入问题终极解决方案

## 🎯 问题分析

您遇到的错误：
```
Refused to frame 'http://***********:8187/' because an ancestor violates the following Content Security Policy directive: "frame-ancestors http://***********:9291".
```

这表明：
1. 系统只允许 `http://***********:9291` 作为父页面
2. 您当前的父页面不是这个地址
3. 需要扩展允许的父域名列表

## 🔧 已完成的修改

### 1. 更新了 vite.config.ts
```javascript
'Content-Security-Policy': 'frame-ancestors http://***********:9291 https://***********:9291 http://localhost:* https://localhost:* http://127.0.0.1:* https://127.0.0.1:* *'
```

### 2. 更新了 index.html
```html
<meta http-equiv="Content-Security-Policy" content="frame-ancestors http://***********:9291 https://***********:9291 http://localhost:* https://localhost:* http://127.0.0.1:* https://127.0.0.1:* *;" />
```

## 🚀 立即测试

### 1. 重启开发服务器
```bash
# 停止当前服务器 (Ctrl+C)
npm run dev
```

### 2. 测试不同的父页面
现在应该支持从以下地址嵌入：
- ✅ `http://***********:9291` (原始允许的地址)
- ✅ `http://localhost:任意端口`
- ✅ `https://localhost:任意端口`
- ✅ `http://127.0.0.1:任意端口`
- ✅ `https://127.0.0.1:任意端口`
- ✅ 任何其他域名 (通过最后的 `*`)

## 🔥 如果仍然有问题，使用这个终极方案

### 方案A：完全移除CSP限制

#### 修改 vite.config.ts：
```javascript
headers: {
  'X-Frame-Options': 'ALLOWALL',
  // 完全移除CSP限制
  // 'Content-Security-Policy': '...',
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Origin, X-Requested-With, Content-Type, Accept, Authorization'
}
```

#### 修改 index.html：
```html
<!-- 只保留X-Frame-Options -->
<meta http-equiv="X-Frame-Options" content="ALLOWALL" />
<!-- 移除或注释掉CSP -->
<!-- <meta http-equiv="Content-Security-Policy" content="..."> -->
```

### 方案B：动态移除CSP限制

在 `src/main.ts` 中添加：
```javascript
// 动态移除CSP限制
if (typeof document !== 'undefined') {
  // 移除所有CSP相关的meta标签
  const cspMetas = document.querySelectorAll('meta[http-equiv="Content-Security-Policy"]');
  cspMetas.forEach(meta => meta.remove());
  
  // 移除X-Frame-Options限制
  const frameMetas = document.querySelectorAll('meta[http-equiv="X-Frame-Options"]');
  frameMetas.forEach(meta => meta.remove());
  
  // 重新添加允许所有的配置
  const allowAllFrame = document.createElement('meta');
  allowAllFrame.httpEquiv = 'X-Frame-Options';
  allowAllFrame.content = 'ALLOWALL';
  document.head.appendChild(allowAllFrame);
  
  console.log('已移除所有iframe限制');
}
```

### 方案C：检查您的父页面地址

请告诉我您的父页面实际地址是什么？比如：
- `http://localhost:3000`
- `http://*************:8080`
- 其他地址？

我可以为您定制精确的CSP策略。

## 🧪 测试方法

### 1. 创建测试页面
```html
<!DOCTYPE html>
<html>
<head>
    <title>iframe测试</title>
</head>
<body>
    <h1>当前页面地址：<span id="currentUrl"></span></h1>
    <iframe 
        src="http://***********:8187/?hide=true" 
        width="100%" 
        height="600px"
        frameborder="0"
        onload="console.log('✅ iframe加载成功')"
        onerror="console.log('❌ iframe加载失败')">
    </iframe>
    
    <script>
        document.getElementById('currentUrl').textContent = window.location.href;
        console.log('父页面地址:', window.location.href);
    </script>
</body>
</html>
```

### 2. 检查浏览器控制台
- 如果看到 "✅ iframe加载成功" - 问题解决
- 如果仍有错误 - 使用方案A完全移除CSP限制

## 🎯 快速解决步骤

如果您想立即解决问题，最简单的方法：

1. **注释掉所有CSP配置**：
   ```javascript
   // vite.config.ts 中
   headers: {
     'X-Frame-Options': 'ALLOWALL',
     // 'Content-Security-Policy': '...',  // 注释掉这行
     'Access-Control-Allow-Origin': '*',
     // ... 其他配置
   }
   ```

2. **重启服务器**：
   ```bash
   npm run dev
   ```

3. **测试iframe嵌入**

这样就完全移除了CSP限制，iframe应该可以正常工作了！

---

*如果您需要我根据您的具体父页面地址定制CSP策略，请告诉我父页面的完整URL。*
