import dayjs from "dayjs";
export const columns: TableColumnList = [
  {
    type: "selection",
    align: "center"
  },
  {
    label: "序号",
    align: "center",
    width: 80,
    type: "index"
  },
  {
    label: "客户名称",
    prop: "customName",
    slot: "accountName"
  },
  {
    label:"峰段电量",
    prop:"summitStartPower",
    sortable: true,
    formatter: ({ summitStartPower }) => {
      return summitStartPower !== null ? summitStartPower.toFixed(2) : "-";
    }
  },
  {
    label:"平段电量",
    prop:"flatSegmentStartPower",
    sortable: true,
    formatter: ({ flatSegmentStartPower }) => {
      return flatSegmentStartPower !== null ? flatSegmentStartPower.toFixed(2) : "-";
    }
  },
  {
    label:"低谷电量",
    prop:"lowEbbStartPower",
    sortable: true,
    formatter: ({ lowEbbStartPower }) => {
      return lowEbbStartPower !== null ? lowEbbStartPower.toFixed(2) : "-";
    }
  },
  {
    label:"尖峰电量",
    prop:"peakStartPower",
    sortable: true,
    formatter: ({ peakStartPower }) => {
      return peakStartPower !== null ? peakStartPower.toFixed(2) : "-";
    }
  },
  {
    label:"总电量",
    prop:"totalPower",
    sortable: true,
    formatter: ({ totalPower }) => {
      return totalPower !== null ? totalPower.toFixed(2) : "-";
    }
  },
  // {
  //   label: "数据完整度",
  //   sortable: true,
  //   prop: "dataIntegrity",
  //   formatter: ({ dataIntegrity }) => {
  //     return dataIntegrity !== null ? dataIntegrity + "%" : "-";
  //   }
  // },
  // {
  //   label: "申报电量最近更新时间",
  //   prop: "declareMaxDate",
  //   formatter: ({ declareMaxDate }) => {
  //     return declareMaxDate !== null
  //       ? dayjs(Number(declareMaxDate)).format("YYYY-MM-DD")
  //       : "-";
  //   }
  // },
  // {
  //   label: "实际电量最近更新时间",
  //   prop: "powerMaxDate",
  //   formatter: ({ powerMaxDate }) => {
  //     return powerMaxDate !== null
  //       ? dayjs(Number(powerMaxDate)).format("YYYY-MM-DD")
  //       : "-";
  //   }
  // },
  {
    label: "操作",
    width: "120",
    fixed: "right",
    slot: "operation"
  }
];

export const detailColumns: TableColumnList = [
  {
    label: "数据项",
    prop: "type",
    align: "center",
    headerAlign: "center"
  }
];
