<template>
  <div class="radio-wrap">
    <div @click="modelChange(item)" :class="{ 'is-active': modelValue == item, 'radio-button': true }"
      v-for="item in tabs" :key="item">
      {{ item }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { watch } from 'vue'
const props = defineProps(['modelValue', 'tabs'])
const emits = defineEmits(['update:modelValue', 'change'])
let modelValue: any
watch(
  () => props.modelValue,
  () => {
    modelValue = props.modelValue
  },
  {
    immediate: true,
  },
)

function modelChange(value: any) {
  emits('change', value)
  emits('update:modelValue', value)
}
</script>

<style scoped lang="scss">
.radio-wrap {
  display: flex;
  width: fit-content;
  padding: 3px;
  background-color: #f2f3f5;
  border-radius: 2px;

  .radio-button {
    padding: 0 12px !important;
    height: 32px;
    line-height: 32px;
    margin-right: 0;
    border-radius: 2px;
    font-size: 14px;
    font-weight: 400;
    color: #4e5969;
    border: none;
    cursor: pointer;

    &.is-active {
      background-color: #254f7a;
      color: #fff;
    }
  }
}
</style>
