import { defineStore } from "pinia";
import { store } from "@/store";
import { cacheType } from "./types";
import {constantMenus, router} from "@/router";
import { useMultiTagsStoreHook } from "./multiTags";
import { debounce, getKeyList } from "@pureadmin/utils";
import { ascending, filterTree, filterNoPermissionTree } from "@/router/utils";
import {queryUserFunction} from "@/api";
import {useUserStore} from "@/store/modules/user";
let env = import.meta.env.MODE || 'development'
// TODO 部署生产环境时注释下面这行代码
env = 'development'
/**
 * 判断用户是否有权限访问单个路由
 */
export const hasPermission = (roles: any, route: any) => {
  const viewCodes = roles.map((item: any) => item.functionName)
  // console.log('hasPermission', roles, route, viewCodes)
  if (route.meta.title) {
    return viewCodes.some((item: any) => {
      return item == route.meta.title
    })
  } else {
    return false
  }
}

/**
 * 筛选可访问的动态路由
 */
export const filterAsyncRoutes = (routes: any, roles: any) => {
  const res: any = []
  routes.forEach((route: any) => {
    const r = { ...route }
    if (hasPermission(roles, r)) {
      if (r.children) {
        r.children = filterAsyncRoutes(r.children, roles)
      }
      res.push(r)
    }
  })
  return res
}

export const usePermissionStore = defineStore({
  id: "pure-permission",
  state: () => ({
    // 静态路由生成的菜单
    constantMenus,
    // 整体路由生成的菜单（静态、动态）
    wholeMenus: [],
    // 缓存页面keepAlive
    cachePageList: []
  }),
  actions: {
    /** 组装整体路由生成的菜单 */ async handleWholeMenus(routes: any[]) {
      // const env = 'development'
      if (env == 'production') {
        const userStore = useUserStore(store)
        const res = await queryUserFunction({
          token: userStore.token,
          systemCode: 'shengchandaping',
        })
        const accessedRoutes = filterAsyncRoutes(this.constantMenus, res)
        // console.log(accessedRoutes)
        // accessedRoutes.forEach((route: any) => {
        //   router.addRoute(route)
        // })
        this.wholeMenus = filterNoPermissionTree(
            // filterTree(ascending(this.constantMenus.concat(routes)))
            filterTree(ascending(accessedRoutes.concat(routes)))
        );
        // console.log(this.wholeMenus, 'wholeMenus')
      } else {
        this.wholeMenus = filterNoPermissionTree(
            filterTree(ascending(this.constantMenus.concat(routes)))
        );
        // console.log(this.wholeMenus, 'wholeMenus')
      }
    },
    cacheOperate({ mode, name }: cacheType) {
      const delIndex = this.cachePageList.findIndex(v => v === name);
      switch (mode) {
        case "refresh":
          this.cachePageList = this.cachePageList.filter(v => v !== name);
          break;
        case "add":
          this.cachePageList.push(name);
          break;
        case "delete":
          delIndex !== -1 && this.cachePageList.splice(delIndex, 1);
          break;
      }
      /** 监听缓存页面是否存在于标签页，不存在则删除 */
      debounce(() => {
        let cacheLength = this.cachePageList.length;
        const nameList = getKeyList(useMultiTagsStoreHook().multiTags, "name");
        while (cacheLength > 0) {
          nameList.findIndex(v => v === this.cachePageList[cacheLength - 1]) ===
            -1 &&
            this.cachePageList.splice(
              this.cachePageList.indexOf(this.cachePageList[cacheLength - 1]),
              1
            );
          cacheLength--;
        }
      })();
    },
    /** 清空缓存页面 */
    clearAllCachePage() {
      this.wholeMenus = [];
      this.cachePageList = [];
    }
  }
});

export function usePermissionStoreHook() {
  return usePermissionStore(store);
}
