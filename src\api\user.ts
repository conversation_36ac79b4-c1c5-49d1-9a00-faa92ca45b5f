import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";
import { ResponseDetailModel, BasicResponseParams } from "@/model/baseModel";
export type UserResult = {
  code: string;
  message: string;
  data: {
    handleResult: boolean;
    code: string;
    userId?: string,
    name?: string,
    userName?:string
  };
};

export type RefreshTokenResult = {
  success: boolean;
  data: {
    /** `token` */
    accessToken: string;
    /** 用于调用刷新`accessToken`的接口时所需的`token` */
    refreshToken: string;
    /** `accessToken`的过期时间（格式'xxxx/xx/xx xx:xx:xx'） */
    expires: Date;
  };
};

export type GetTokenResult = {
  code: string;
  message: string;
  data: {
    token: string;
    name: string;
  };
};

/** 登录 */
export const getLogin = (data?: object) => {
  return http.request<UserResult>("post", "/login", { data });
};

/** 登出 */
export const loginOutApi = authorization => {
  return http.request<any>(
    "get",
    baseUrlApi("loginout"),
    {},
    {
      headers: {
        Authorization: authorization
      }
    }
  );
};

// 登录接口
export const getVerificationCodeApi = (data: object, authorization) => {
  return http.request<UserResult>(
    "post",
    baseUrlApi("getVerificationCode"),
    { data },
    {
      headers: {
        Authorization: authorization
      }
    }
  );
};
// 登录接口
export const getTokenApi = (params, authorization) => {
  return http.request<GetTokenResult>(
    "get",
    baseUrlApi("exchange"),
    {
      params
    },
    {
      headers: {
        Authorization: authorization
      }
    }
  );
};

/** 刷新token */
export const refreshTokenApi = (data?: object) => {
  return http.request<RefreshTokenResult>("post", "/refreshToken", { data });
};

// 获取登录用户信息
export const getLoginInfoApi = authorization => {
  return http.request<UserResult>(
    "get",
    baseUrlApi("/loginInfo"),
    {},
    {
      headers: {
        Authorization: authorization
      }
    }
  );
};

// 查询所有用户信息
export const getAllUserListApi = () => {
  return http.request<any>("get", baseUrlApi("system/user/getAll"));
};

// 生成唯一id
export const getUnqueIdApi = () => {
  return http.request<ResponseDetailModel<number>>(
    "get",
    baseUrlApi("system/annex/nextId")
  );
};

// 查询附件
export const getFileListApi = (type: string, id: number) => {
  return http.request<any>(
    "get",
    baseUrlApi(`system/annex/list/${type}/${id}`)
  );
};

// 删除文件
export const delFileApi = (id: string|number) => {
  return http.request<BasicResponseParams>(
    "get",
    baseUrlApi(`system/annex/delete/${id}`)
  );
};
// 删除文件
export const deleteImage = (id: string|number) => {
  return http.request<BasicResponseParams>(
    "get",
    baseUrlApi(`/system/annex/deleteImage/${id}`)
  );
};