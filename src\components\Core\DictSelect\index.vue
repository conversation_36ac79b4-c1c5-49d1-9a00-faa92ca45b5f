<template>
  <el-select style="width:100%" v-model="currentVal" :placeholder="title" :clearable="clearable" :size="size"
    :disabled="disabled" @change="handleChange" :multiple="multiple" collapse-tags>
    <el-option v-for="item in dictData" :key="item.no" :label="item.label" :value="item.value" />
  </el-select>
</template>

<script lang='ts'>
import { ref, computed, watch, onMounted } from 'vue'
import { useUserStore } from "@/store/modules/user";
export default {
  name: 'DictSelect',
  props: {
    value: {
      type: [String, Number],
      default: undefined
    },
    title: {
      type: String,
      default: '请选择'
    },
    size: {
      // 按钮尺寸 medium/small/mini
      type: String,
      default: 'default'
    },
    clearable: {
      // 是否可以清空数据
      type: Boolean,
      default: true
    },
    disabled: {
      // 按钮是否禁用
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: false
    },
    filterable: {
      type: Boolean,
      default: true
    },
    dictCode: {
      type: String,
      default: undefined
    },
    identification: {
      type: String,
      default: 'value'
    }
  },
  emits: ['input', 'change'],

  setup(props, { emit }) {
    const userStore = useUserStore();
    const dictData = computed(() => userStore.getDictList.find(i => i.code == props.dictCode)?.items.map(item => {
      return {
        label: item.label,
        value: Number(item.value)
      }
    }))
    console.log("aaaa",dictData.value)
    const currentVal = ref<string>('')
    
    function handleChange(val) {
      console.log(val)
      emit('input', val)
      // emit('change', val)
      emit('change', dictData.value.find((i) => i.value === val))
    }
    watch(() => props.value, (newVal) => {
      if ([undefined, null, ''].includes(newVal)) {
        currentVal.value = undefined
      } else {
        currentVal.value = newVal + ''
      }
    }, { immediate: true })
    return {
      currentVal,
      dictData,
      handleChange
    }
  }
}
</script>

<style lang='scss' scoped></style>
