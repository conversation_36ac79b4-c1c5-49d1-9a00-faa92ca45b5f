import dayjs from "dayjs";
import { useUserStore } from "@/store/modules/user";
const userStore = useUserStore();
const followTypeOptions = userStore.getDictList.find(
  i => i.code === "followType"
).items;
const followStageOptions = userStore.getDictList.find(
  i => i.code === "followUpSelect"
).items;
export const followColumns: TableColumnList = [
  {
    label: "序号",
    align: "center",
    width: 80,
    type: "index"
  },
  {
    label: "跟进内容",
    prop: "title",
    width: 220
  },
  {
    label: "跟进形式",
    prop: "type",
    formatter: ({ type }) =>
      type !== null
        ? followTypeOptions.find(item => item.value == type).label
        : ""
  },
  {
    label: "绿电需求",
    sortable: true,
    prop: "electricityDemand"
  },
  {
    label: "其它意向需求",
    prop: "otherDemand"
  },
  {
    label: "跟进人",
    prop: "followerName"
  },
  {
    label: "陪同人员",
    prop: "entourage"
  },
  {
    label: "跟进时间",
    sortable: true,
    prop: "followTime",
    width: 160,
    formatter: ({ followTime }) =>
      followTime ? dayjs(Number(followTime)).format("YYYY-MM-DD") : ""
  },
  {
    label: "创建时间",
    sortable: true,
    prop: "createTime",
    width: 160,
    formatter: ({ createTime }) =>
      createTime ? dayjs(Number(createTime)).format("YYYY-MM-DD HH:mm:ss") : ""
  }
];
export const bussinesColumns: TableColumnList = [
  {
    label: "序号",
    width: 80,
    align: "center",
    type: "index"
  },
  {
    label: "机会名称",
    prop: "title",
    width: 220
  },
  {
    label: "所在区域",
    prop: "areaName"
  },
  {
    label: "商机描述",
    prop: "content"
  },
  {
    label: "跟进阶段",
    prop: "followStage",
    formatter: ({ followStage }) =>
      followStage !== null
        ? followStageOptions.find(item => item.value == followStage).label
        : ""
  },
  {
    label: "跟进人",
    prop: "followerName"
  },
  // {
  //   label: "年用电量",
  //   sortable: true,
  //   prop: "yearlyElectricityQty"
  // },
  {
    label: "合同电量",
    sortable: true,
    prop: "contractElectricityQty"
  },
  // {
  //   label: "营销区域",
  //   prop: "marketingAreaName"
  // },
  {
    label: "预计交易日期",
    sortable: true,
    prop: "predictTradeDate",
    width: 130,
    formatter: ({ predictTradeDate }) =>
      predictTradeDate ? dayjs(predictTradeDate).format("YYYY-MM-DD") : ""
  },
  {
    label: "已有合同电量",
    sortable: true,
    prop: "existElectricityQty",
    width: 130,
  }
];
