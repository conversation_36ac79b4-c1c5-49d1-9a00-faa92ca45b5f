<script setup lang="ts">
import {
  ref,
  computed,
  onMounted,
  watch,
  type Ref,
  PropType,
  onActivated
} from "vue";
import { useAppStoreHook } from "@/store/modules/app";
import {
  delay,
  useDark,
  useECharts,
  type EchartOptions
} from "@pureadmin/utils";
import * as echarts from "echarts/core";
import type { EChartsOption } from "echarts";
import { triggerWindowResize } from "@/utils/event";
const emit = defineEmits(["change"]);

const props = defineProps({
  height: {
    type: String as PropType<string>,
    default: "100%"
  },
  width: {
    type: String as PropType<string>,
    default: "100%"
  },
  xData: {
    type: Array as PropType<string[] | number[]>,
    default: () => []
  },
  yData: {
    type: Array as PropType<string[] | number[]>,
    default: () => []
  },
  series: {
    type: Array as PropType<Array<object>>,
    default: () => []
  },
  totalSeries: {
    type: Array as PropType<Array<object>>,
    default: () => []
  },
  legendData: {
    type: Array as PropType<string[]>,
    default: () => []
  }
});
const { isDark } = useDark();

const theme: EchartOptions["theme"] = computed(() => {
  return isDark.value ? "dark" : "light";
});

const chartRef = ref<HTMLDivElement | null>(null);
const { setOptions, resize } = useECharts(chartRef as Ref<HTMLDivElement>, {
  theme
});
const getOption = (): EChartsOption => {
  return {
    tooltip: {
      show: false,
      trigger: "item",
      formatter: "{b} : {c}"
    },
    grid: {
      top: "0%",
      bottom: "8%"
    },
    legend: {
      show: false,
      bottom: "4%",
      textStyle: {
        color: "#fff"
      },
      data: props.legendData
    },

    series: [
      {
        name: "Expected",
        type: "funnel",
        left: "10%",
        right: "8%",
        width: "70%",
        label: {
          show: false,
          position: "right",
          formatter: "{b}\n({c})",
          color: "#fff"
        },
        tooltip: {
          show: false,
          valueFormatter: () => ""
        },
        labelLine: {
          length: 20,
          lineStyle: {
            width: 1,
            type: "solid"
          }
        },
        itemStyle: {
          borderColor: "transparent",
          opacity: 0.8,
          borderWidth: 0
        },
        emphasis: {
          disabled: true,
          label: {
            show: false,
            fontSize: 20
          }
        },
        data: props.totalSeries
      },
      {
        name: "Actual",
        type: "funnel",
        left: "10%",
        right: "8%",
        width: "70%",
        maxSize: "80%",
        label: {
          show: true,
          position: "right",
          formatter: "{b}\n({c})",
          color: "#fff"
        },
        labelLine: {
          length: 80,
          lineStyle: {
            width: 1,
            type: "solid"
          }
        },
        itemStyle: {
          borderColor: "transparent",
          borderWidth: 0
        },
        emphasis: {
          label: {
            fontSize: 20
          }
        },
        data: props.series
      }
    ]
  };
};

watch(
  () => useAppStoreHook().getSidebarStatus,
  () => {
    delay(600).then(() => resize());
  }
);
watch(
  () => props,
  () => setOptions(getOption() as EChartsOption, false, { notMerge: true }),
  {
    immediate: true,
    deep: true
  }
);
onMounted(() => {
  delay(300).then(() => resize());
});
onActivated(() => triggerWindowResize());
</script>

<template>
  <div ref="chartRef" :style="{ height, width }" />
</template>
