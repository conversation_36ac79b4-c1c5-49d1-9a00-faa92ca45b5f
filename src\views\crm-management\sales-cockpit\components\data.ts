import dayjs from "dayjs";
import { useUserStore } from "@/store/modules/user";
const userStore = useUserStore();
const followStageOptions = userStore.getDictList.find(
  i => i.code === "fiveCustomStatus"
)?.items;
const contractStatusOptions = userStore.getDictList.find(
  i => i.code === "contractStatus"
)?.items;
export const columns: TableColumnList = [
  {
    label: "序号",
    width: 80,
    align: "center",
    type: "index"
  },
  {
    label: "机会名称",
    prop: "title",
    width: 220,
    slot: "title"
  },
  {
    label: "客户",
    prop: "customName"
  },
  {
    label: "所在区域",
    prop: "areaName"
  },
  {
    label: "商机描述",
    prop: "content"
  },
  {
    label: "跟进阶段",
    prop: "followStage",
    formatter: ({ followStage }) =>
      followStage !== null
        ? followStageOptions.find(item => item.value == followStage).label
        : ""
  },
  {
    label: "营销人员",
    prop: "followerName"
  },
  {
    label: "年用电量",
    prop: "yearlyElectricityQty"
  },
  {
    label: "合同电量",
    prop: "contractElectricityQty"
  },
  {
    label: "营销区域",
    prop: "marketingAreaName"
  },
  {
    label: "预计交易日期",
    prop: "predictTradeDate",
    width: 130,
    formatter: ({ predictTradeDate }) =>
      predictTradeDate ? dayjs(predictTradeDate).format("YYYY-MM-DD") : ""
  },
  {
    label: "已有合同电量",
    prop: "existElectricityQty",
    width: 130,
  }
];

export const constractColumns: TableColumnList = [
  {
    label: "序号",
    width: 80,
    type: "index"
  },
  {
    label: "零售用户",
    prop: "retailUser",
    width: 220,
    // slot: "name"
  },
  {
    label: "合同编号",
    prop: "contractNum"
  },
  {
    label: "合同电量（MWh）",
    width: 160,
    prop: "contractElectricity"
  },
  // {
  //   label: "合同电价（MWh）",
  //   width: 160,
  //   prop: "electricityPrice"
  // },
  {
    label: "合同开始时间",
    prop: "startTime",
    // formatter: ({ startTime }) =>
    //   startTime !== null ? dayjs(Number(startTime)).format("YYYY-MM") : ""
  },
  {
    label: "合同结束时间",
    prop: "endTime",
    // formatter: ({ endTime }) =>
    //   endTime !== null ? dayjs(Number(endTime)).format("YYYY-MM") : ""
  },

  // {
  //   label: "合同状态",
  //   prop: "contractStatus",
  //   formatter: ({ contractStatus }) =>
  //     contractStatus !== null
  //       ? contractStatusOptions.find(item => item.value == contractStatus).label
  //       : ""
  // }
];
