import { useUserStore } from "@/store/modules/user";
export function useDictOptions() {
  const userStore = useUserStore();
  // 代理类型
  const agentTypeOptions = userStore.getDictList.find(
    i => i.code === "agentType"
  )?.items;
  // 用电性质
  const electricityTypeOptions = userStore.getDictList.find(
    i => i.code === "electricityNature"
  )?.items;
  // 行业类型
  const industryOptions = userStore.getDictList.find(
    i => i.code === "industry"
  )?.items;
  // 客户状态
  const statusOptions = userStore.getDictList.find(
    i => i.code === "customStatus"
  )?.items;

  // 客户来源
  const customerSourceOptions = userStore.getDictList.find(
    i => i.code === "customerSource"
  )?.items;
  // 客户分级
  const customGradeOptions = userStore.getDictList.find(
    i => i.code === "customGrade"
  )?.items;
  // 用电规模
  const electricityConsumptionOptions = userStore.getDictList.find(
    i => i.code === "electricityConsumptionScale"
  )?.items;
  // 生产类型
  const productTypeOptions = userStore.getDictList.find(
    i => i.code === "productType"
  )?.items;
  // 用电特征;
  const electricityCharacteristicsOptions = userStore.getDictList.find(
    i => i.code === "electricityConsumptionCharacteristics"
  )?.items;
  // 合同期限;
  const contractPeriodOptions = userStore.getDictList.find(
    i => i.code === "contractPeriod"
  )?.items;
  // 套餐类型;
  const packageTypeOptions = userStore.getDictList.find(
    i => i.code === "packageType"
  )?.items;
  // 套餐名称;
  const packageNameOptions = userStore.getDictList.find(
    i => i.code === "packageName"
  )?.items;
  // 偏差特点;
  const deviationCharacteristicsOptions = userStore.getDictList.find(
    i => i.code === "deviationCharacteristics"
  )?.items;
  // 跟进类型
  const followTypeOptions = userStore.getDictList.find(
    i => i.code === "followType"
  )?.items;
  // 合同状态
  const contractStatusOptions = userStore.getDictList.find(
    i => i.code === "contractStatus"
  )?.items;
  // 	跟进阶段
  const followStageOptions = userStore.getDictList.find(
    i => i.code === "followUpSelect"
  )?.items;
  // 	企业性质
  const ownershipOptions = userStore.getDictList.find(
    i => i.code === "ownership"
  )?.items;
  // 	登记状态
  const registrationStatusOptions = userStore.getDictList.find(
    i => i.code === "registrationStatus"
  )?.items;
  // 用户画像
  const userPortraitOptions = userStore.getDictList.find(
    i => i.code === "userPortrait"
  )?.items;
  return {
    electricityConsumptionOptions,
    productTypeOptions,
    electricityCharacteristicsOptions,
    contractPeriodOptions,
    packageTypeOptions,
    packageNameOptions,
    deviationCharacteristicsOptions,
    agentTypeOptions,
    statusOptions,
    customerSourceOptions,
    customGradeOptions,
    followTypeOptions,
    contractStatusOptions,
    followStageOptions,
    industryOptions,
    electricityTypeOptions,
    registrationStatusOptions,
    ownershipOptions,
    userPortraitOptions
  };
}
