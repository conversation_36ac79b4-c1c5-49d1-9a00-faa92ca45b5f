<script setup lang="ts">
import extraIcon from "./extraIcon.vue";
import { isAllEmpty } from "@pureadmin/utils";
import { useNav } from "@/layout/hooks/useNav";
import { ref, toRaw, watch, onMounted, nextTick } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { getParentPaths, findRouteByPath } from "@/router/utils";
import { usePermissionStoreHook } from "@/store/modules/permission";
import LogoutCircleRLine from "@iconify-icons/ri/logout-circle-r-line";
// import Setting from "@iconify-icons/ri/settings-3-line";


const menuRef = ref();
const defaultActive = ref(null);

const {
  route,
  device,
  logout,
  // onPanel,
  resolvePath,
  username,
  userAvatar,
  getDivStyle,
  avatarsStyle,
  title
} = useNav();

function getDefaultActive(routePath) {
  const wholeMenus = usePermissionStoreHook().wholeMenus;
  /** 当前路由的父级路径 */
  const parentRoutes = getParentPaths(routePath, wholeMenus)[0];
  defaultActive.value = !isAllEmpty(route.meta?.activePath)
    ? route.meta.activePath
    : findRouteByPath(parentRoutes, wholeMenus)?.children[0]?.path;
}

onMounted(() => {
  getDefaultActive(route.path);
});

nextTick(() => {
  menuRef.value?.handleResize();
});

watch(
  () => [route.path, usePermissionStoreHook().wholeMenus],
  () => {
    getDefaultActive(route.path);
  }
);
</script>

<template>
  <div
    v-if="device !== 'mobile'"
    class="horizontal-header"
    v-loading="usePermissionStoreHook().wholeMenus.length === 0"
  >
    <div class="header-logo-wrapper">
      <img
        src="/src/assets/logo.png"
        alt="logo"
      />
      <span class="logo-title"> {{ title }}</span>
    </div>
    <div class="header-right-wrapper">
      <el-menu
        router
        ref="menuRef"
        mode="horizontal"
        :ellipsis="false"
        class="horizontal-header-menu"
        :default-active="defaultActive"
      >
        <el-menu-item
          v-for="route in usePermissionStoreHook().wholeMenus"
          :key="route.path"
          :index="resolvePath(route) || route.redirect"
        >
          <template #title>
            <div
              v-if="toRaw(route.meta.icon)"
              :class="['sub-menu-icon', route.meta.icon]"
            >
              <component
                :is="useRenderIcon(route.meta && toRaw(route.meta.icon))"
              />
            </div>
            <div :style="getDivStyle">
              <span class="select-none">
                {{ route.meta.title }}
              </span>
              <extraIcon :extraIcon="route.meta.extraIcon" />
            </div>
          </template>
        </el-menu-item>
      </el-menu>
      <div class="horizontal-header-right">
        <!-- 退出登录 -->
        <el-dropdown trigger="click">
          <span class="el-dropdown-link navbar-bg-hover select-none">
            <img :src="userAvatar" :style="avatarsStyle" />
            <p v-if="username" class="dark:text-white">{{ username }}</p>
          </span>
          <template #dropdown>
            <el-dropdown-menu class="logout">
              <el-dropdown-item @click="logout">
                <IconifyIconOffline
                  :icon="LogoutCircleRLine"
                  style="margin: 5px"
                />
                退出系统
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <!-- <span
        class="set-icon navbar-bg-hover"
        title="打开项目配置"
        @click="onPanel"
      >
        <IconifyIconOffline :icon="Setting" />
      </span> -->
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-loading-mask) {
  opacity: 0.45;
}

.logout {
  max-width: 120px;

  ::v-deep(.el-dropdown-menu__item) {
    display: inline-flex;
    flex-wrap: wrap;
    min-width: 100%;
  }
}

.header-logo-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin-left: 34px;

  img {
    width: 32px;
    height: 32px;
  }

  .logo-title {
    font-size: 20px;
    margin-left: 16px;
  }
}

.header-right-wrapper {
  height: 100%;
  display: flex;
  align-items: center;
}
</style>
