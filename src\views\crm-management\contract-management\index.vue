<template>
  <section class="p-20px">
    <div class="contractManagement-header">
      <div class="header-card">
        <div>
          待执行合同数
          <span class="value">{{ headerData.notStartedCount }}</span>
          <span class="unit">个</span>
        </div>
      </div>
      <div class="header-card">
        <div>
          执行中合同数
          <span class="value">{{ headerData.executingCount }}</span>
          <span class="unit">个</span>
        </div>
      </div>
      <div class="header-card">
        <div>
          已终止合同数
          <span class="value">{{ headerData.expiredCount }}</span>
          <span class="unit">个</span>
        </div>
      </div>
      <div class="header-card">
        <div class="li">
          <div>
            <span class="value">{{ headerData.due30Count }}</span>
            <span class="unit">个</span>
          </div>
          <div>30天到期</div>
        </div>
        <div class="li">
          <div>
            <span class="value">{{ headerData.due60Count }}</span>
            <span class="unit">个</span>
          </div>
          <div>60天到期</div>
        </div>
        <div class="li">
          <div>
            <span class="value">{{ headerData.due90Count }}</span>
            <span class="unit">个</span>
          </div>
          <div>90天到期</div>
        </div>
      </div>
    </div>
    <div class="app-content-container">
      <div class="flex justify-between">
        <div class="flex">
          <div class="app-form-group">
            <div>
              <span>零售用户：</span>
              <el-input
                style="width: 140px"
                v-model="searchInfo1.retailUser"
                clearable
                placeholder="请输入合同名称"
                class="filter-item"
              />
            </div>
            <div class="ml-[20px]">
              <span>合同执行时间：</span>
              <el-date-picker
                style="width: 240px"
                v-model="searchInfo1.timeList"
                type="daterange"
                placeholder="请选择"
                format="YYYY-MM"
                value-format="YYYY-MM"
              />
            </div>
            <!-- <div class="ml-[20px]">
              <span>合同终止时间：</span>
              <el-date-picker
                style="width: 140px"
                v-model="searchInfo.endTime"
                type="date"
                placeholder="请选择"
                format="YYYY-MM-DD"
                value-format="x"
              />
            </div> -->
          </div>
          <div class="app-btn-group" style="margin-top: 8px">
            <el-button
              class="filter-item"
              type="primary"
              @click="getContractList"
              >查询</el-button
            >
            <el-button class="filter-item" @click="handleReset">重置</el-button>
          </div>
        </div>
        <div style="display: flex">
          <el-button @click="handleCreate" type="primary">新增合同</el-button>
          <el-upload
            class="daoru"
            style="margin-left: 10px"
            action=""
            :http-request="detailUploadFunction"
            :on-change="detailguideInto"
            :show-file-list="false"
          >
            <el-button :icon="Upload" type="primary">导入</el-button>
          </el-upload>
          <el-button
            style="margin-left: 10px"
            :icon="Download"
            type="primary"
            @click="handleExportContractList"
          >
            导出
          </el-button>
          <el-button
            style="margin-left: 10px"
            :icon="Document"
            type="primary"
            @click="handleDownloadTemplate"
          >
            下载模板
          </el-button>
        </div>
      </div>
      <pure-table
        class="mt-[20px]"
        border
        stripe
        :columns="columns"
        :loading="loading"
        :data="tableData"
        :pagination="pagination"
        @page-size-change="onSizeChange"
        @page-current-change="onCurrentChange"
        @sort-change="handleSortChange"
      >
        <template #retailUserHeader>
          <table-head-popover
            :time-slot="timeSlot"
            @tableUpdate="handleTableUpdate"
            :column="columns[1]"
          />
        </template>
        <template #packageNameHeader>
          <table-head-popover
            :time-slot="timeSlot"
            @tableUpdate="handleTableUpdate"
            :column="columns[2]"
          />
        </template>

        <template #contractNumHeader>
          <table-head-popover
            :time-slot="timeSlot"
            @tableUpdate="handleTableUpdate"
            :column="columns[3]"
          />
        </template>

        <template #name="{ row }">
          <a @click="handleDetail(row.id, row)" style="color: #007bf7">{{
            row.retailUser
          }}</a>
        </template>
        <template #operation="{ row }">
          <el-button
            link
            type="primary"
            size="small"
            @click="handleDetail(row.id, row)"
            >详情</el-button
          >
          <el-button
            v-if="row.contractStatus != '3'"
            link
            type="primary"
            size="small"
            @click="handleDel(row.id)"
            >删除</el-button
          >
          <!-- <el-button
            v-if="row.contractStatus != '3'"
            link
            type="danger"
            size="small"
            @click="handleStop(row.id)"
            >终止</el-button
          > -->
        </template>
      </pure-table>
    </div>
    <!-- 新增合同表单 -->
    <add-form ref="addFormRef" @update="getContractList" />
    <!-- 变更合同表单 -->
    <!-- <change-form ref="changeFormRef" @update="getList" /> -->
    <!-- 终止合同表单 -->
    <!-- <stop-form ref="stopFormRef" @update="getList" /> -->
    <!-- 详情 -->
    <detail-list ref="detailListRef" :inofr="infor" @update="handlerUpdata" />
  </section>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from "vue";
import addForm from "./components/addForm.vue";
import changeForm from "./components/changeForm.vue";
import stopForm from "./components/stopForm.vue";
import detailList from "./components/detailList.vue";
import { columns, addColumns } from "./components/data";
import type { PaginationProps } from "@pureadmin/table";
import { delay } from "@pureadmin/utils";
import TableHeadPopover from "@/components/Core/TableHeadPopover/index.vue";
import {
  getContractPageList,
  getContractListApi,
  getContractNewTotal,
  getPortraitMeterList,
  importContractData,
  exportContractInfoList,
  exportContractTemplate
} from "@/api/customer-management/index";
import dayjs from "dayjs";
import { GETContractTotalModel } from "@/model/customerModel";
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";
import { Upload, Download, Document } from "@element-plus/icons-vue";
import { useUserStore } from "@/store/modules/user";
let useStore = useUserStore();
defineOptions({
  name: "ContractManagement"
});
// 头部数据
const headerData = ref<GETContractTotalModel>({
  due30Count: 1,
  due60Count: 0,
  due90Count: 0,
  executingCount: 1,
  expiredCount: 0,
  notStartedCount: 0
});

const infor = ref({});
const timeSlot = ref<number>(dayjs().valueOf());
const addFormRef = ref(null);
const changeFormRef = ref(null);
const stopFormRef = ref(null);
const detailListRef = ref(null);
const tableData = ref([]);
const loading = ref(false);
// const stopFormId = ref<string>("");
/** 分页配置 */
const pagination = reactive<PaginationProps>({
  pageSize: 10,
  currentPage: 1,
  pageSizes: [10, 20, 40, 50],
  total: 0,
  align: "right",
  background: true,
  small: false
});

function detailUploadFunction() {}
let detailfileList = ref<any>();
function detailguideInto(file: any) {
  const fileExtension = file.name.split(".").pop();
  const size = file.size / 1024 / 1024;
  if (fileExtension !== "xlsx" && fileExtension !== "xls") {
    ElMessage.warning("只能上传excel文件");
    detailfileList.value = [];
    return;
  }
  if (size > 10) {
    ElMessage.warning("文件大小不得超过10M");
    detailfileList.value = [];
    return;
  }
  detailfileList.value = [file];
  detailhandleUpload();
}

const detailhandleUpload = () => {
  if (detailfileList.value.length === 0) {
    ElMessage.warning("未选择文件");
  } else {
    const formData = new FormData();
    formData.append("file", detailfileList.value[0].raw);
    importContractData(formData)
      .then(() => {
        ElMessage.success("上传成功");
        getContractList();
      })
      .catch(e => {
        console.log(e);
      });
  }
};
const searchInfo = reactive({
  code: undefined,
  customName: undefined,
  name: undefined,
  startTime: undefined,
  endTime: undefined,
  pageNo: 1,
  pageSize: 10
});
const searchInfo1 = reactive({
  packageName: "",
  retailUser: "",
  timeList: [],
  pageNo: 1,
  sort: [],
  pageSize: 10
});

// 获取头部数据
async function getHeaderData() {
  const res = await getContractNewTotal();
  if (res.data) {
    headerData.value = { ...res.data };
  }
}
// 表格筛选
function handleTableUpdate(data) {
  searchInfo1[data.propKey] = data.value;
  getContractList();
}
async function getList() {
  // loading.value = true;
  // const { data } = await getContractListApi(searchInfo);
  // pagination.total = Number(data.totalCount);
  // tableData.value = data.data;
  // getHeaderData();
  // delay(600).then(() => {
  //   loading.value = false;
  // });
}

const getContractList = async () => {
  loading.value = true;
  getHeaderData();
  // searchInfo1.timeList === "" ?  searchInfo1.timeList : []

  const { data } = await getContractPageList({
    ...searchInfo1
  });

  pagination.total = Number(data.totalCount);
  tableData.value = data.data;
  delay(600).then(() => {
    loading.value = false;
  });
};

const handlerUpdata = async () => {
  loading.value = true;
  getHeaderData();
  // searchInfo1.timeList === "" ?  searchInfo1.timeList : []

  const { data } = await getContractPageList({
    ...searchInfo1
  });

  pagination.total = Number(data.totalCount);
  tableData.value = data.data;
  delay(600).then(() => {
    loading.value = false;
  });
};
function handleReset() {
  searchInfo1.retailUser = "";
  searchInfo1.timeList = [];

  // timeSlot.value = dayjs().valueOf();
  getContractList();
}
function onSizeChange(val) {
  searchInfo1.pageSize = val;
  getContractList();
}

function onCurrentChange(val) {
  searchInfo1.pageNo = val;
  getContractList();
}
// 打开子组件弹窗
function handleCreate() {
  addFormRef.value.handleCreate();
}
async function handleDel(id) {
  // changeFormRef.value.handleCreate(id);
  // getPortraitMeterList
  // ElMessage.warning("确定删除该数据？");
  //
  // 显示确认框
  try {
    // 显示确认框
    await ElMessageBox.confirm("确定要删除吗?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    });

    // 执行删除操作
    await getPortraitMeterList(id); // 假设id是你要删除的项目的ID，你可能需要从props或某处获取它
    getContractList();
    ElMessage.success("删除成功");
  } catch (error) {
    // ElMessage.warning("删除失败");
    // console.error(error); // 在开发过程中，打印错误到控制台可以帮助调试
  }

  // getPortraitMeterList( id ).then(() => {
  //   getContractList();
  //   ElMessage.success("删除成功");
  // }).catch(() => {
  //   ElMessage.warning("删除失败");
  // });
}
function handleStop(id) {
  stopFormRef.value.handleCreate(id);
}
const dialogVisible = ref(false);
const dataForm = ref<any>({
  id: undefined,
  retailUser: "",
  contractNum: "",
  contractElectricity: "",
  startTime: "",
  endTime: "",
  orderTime: "",
  packageName: "",
  agentSaleModel: "",
  saleMan: "",
  saleManId: "",
  entryName: "",
  remark: "",
  fileId: ""
});
function handleDetail(id, row: any) {
  // dialogVisible.value = true
  // console.log("qqssadas",row,row.endTime )
  // dataForm.value.retailUser =row.retailUser
  // dataForm.value.contractNum =row.contractNum
  // dataForm.value.contractElectricity =row.contractElectricity
  // dataForm.value.startTime =row.startTime
  // dataForm.value.endTime =row.endTime
  // dataForm.value.orderTime =row.orderTime
  // dataForm.value.packageName =row.packageName
  // dataForm.value.agentSaleModel =row.agentSaleModel
  // dataForm.value.saleMan =row.saleMan
  // dataForm.value.entryName =row.entryName
  // dataForm.value.remark =row.remark
  // dataForm.value.fileId =row.fileId
  // // dataForm.value.saleManId =row.retailUser.saleManId

  detailListRef.value.handleCreate(row);
}
const cancel = () => {
  dialogVisible.value = false;
};

const handleSortChange = (item: any) => {
  console.log(item, "排序");
  searchInfo1.sort = [];

  let order;
  if (item.order !== null) {
    if (item.order.includes("desc")) {
      order = "desc";
    } else {
      order = "asc";
    }
    searchInfo1.sort.push(item.prop, order);

    getContractList();
  } else {
    getContractList();
  }
};

// 导出合同数据
const handleExportContractList = () => {
  const loading = ElLoading.service({ text: "正在导出..." });
  exportContractInfoList(searchInfo1)
    .then(data => {
      const blob = new Blob([data]);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `合同数据_${dayjs().format("YYYY-MM-DD_HH-mm-ss")}.xlsx`;
      link.click();
      window.URL.revokeObjectURL(url);
      loading.close();
      ElMessage.success("导出成功");
    })
    .catch(() => {
      ElMessage.error("导出失败");
      loading.close();
    });
};

// 下载合同管理模板
const handleDownloadTemplate = () => {
  const loading = ElLoading.service({ text: "正在下载..." });
  exportContractTemplate()
    .then(data => {
      const blob = new Blob([data]);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = "合同管理_导入模板.xlsx";
      link.click();
      window.URL.revokeObjectURL(url);
      loading.close();
      ElMessage.success("下载成功");
    })
    .catch(() => {
      ElMessage.error("下载失败");
      loading.close();
    });
};
// const handleTimeChange = (item:any) => {
//    console.log(item,'时间',searchInfo1.time)
// }

onMounted(() => {
  // getList();
  getContractList();
});
</script>

<style lang="scss" scoped>
.contractManagement-header {
  display: flex;

  .header-card {
    padding: 10px 15px;
    border-radius: 6px;
    box-shadow: 0px 2px 22px rgba(0, 0, 0, 0.06);
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    // align-items: baseline;
    margin-right: 20px;

    .value {
      font-size: 34px;
      color: #10507e;
      font-weight: 700;
      margin: 0 5px;
    }

    .unit {
      color: #10507e;
    }

    &:last-child {
      flex: 2;
      margin-right: 0;

      .li {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        flex: 1;
      }
    }
  }
}

.form-title {
  padding: 10px;
  border-bottom: 1px solid #ccc;
  font-size: 18px;
  margin-bottom: 20px;
}
</style>
