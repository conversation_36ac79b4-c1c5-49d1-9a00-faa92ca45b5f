import dayjs from "dayjs";
import { useUserStore } from "@/store/modules/user";
const userStore = useUserStore();
const followTypeOptions = userStore.getDictList.find(
  i => i.code === "followType"
)?.items;
export const columns: TableColumnList = [
  {
    label: "序号",
    align: "center",
    width: 80,
    type: "index"
  },
  {
    label: "客户名称",
    width: 180,
    slot: "customName",
    prop: "customName",
    headerSlot: "nameHeader",
    searchType: "text"
  },
  {
    label: "跟进内容",
    prop: "title",
    headerSlot: "titleHeader",
    searchType: "text",
    width: 180
  },
  {
    label: "跟进形式",
    prop: "type",
    headerSlot: "typeHeader",
    searchType: "select",
    formatter: ({ type }) =>
      type !== null
        ? followTypeOptions.find(item => item.value == type).label
        : ""
  },
  {
    label: "绿电需求",
    // sortable: true,
    prop: "electricityDemand"
  },
  {
    label: "其它意向需求",
    prop: "otherDemand"
  },
  {
    label: "跟进人",
    prop: "followerName"
  },
  {
    label: "陪同人员",
    prop: "entourage"
  },
  {
    label: "跟进时间",
    sortable: true,
    prop: "followTime",
    width: 160,
    formatter: ({ followTime }) =>
      !["0", 0, undefined, null].includes(followTime)
        ? dayjs(Number(followTime)).format("YYYY-MM-DD")
        : ""
  },
  // {
  //   label: "创建时间",
  //   sortable: true,
  //   prop: "createTime",
  //   width: 160,
  //   formatter: ({ createTime }) =>
  //     createTime ? dayjs(Number(createTime)).format("YYYY-MM-DD HH:mm:ss") : ""
  // },
  {
    label: "操作",
    width: "120",
    fixed: "right",
    slot: "operation"
  }
];
