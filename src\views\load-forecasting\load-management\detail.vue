<template>
  <div class="app-card" v-loading="loading">
    <div class="mr-[20px] ml-[15px] mt-[20px]">
      <el-button
        @click="
          () => {
            router.back();
          }
        "
        >返回</el-button
      >
    </div>
    <div class="card-header">
      <div class="flex items-center">
        <span class="mr-[4px]">客户名称：</span>
        <el-input style="width: 250px" readonly v-model:value="customName" />
        <span class="ml-[20px]">用电户号：</span>
        <el-select
          style="width: 160px"
          v-model="accountVal"
          @change="handleAccounChange"
        >
          <el-option
            v-for="item in accountList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <span class="ml-[20px]">表计名称：</span>
        <el-select
          style="width: 300px"
          v-model="meterVal"
          @change="handleMeterChange"
        >
          <el-option
            v-for="item in meterList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>
    </div>
    <div class="p-[20px]">
      <!--      <div class="flex items-center">-->
      <!--        <span class="mr-[4px]">曲线查询日期选择：</span>-->
      <!--        <el-date-picker-->
      <!--          :clearable="false"-->
      <!--          style="width: 160px"-->
      <!--          @change="getCustomMeterPowerLine"-->
      <!--          v-model="searchInfo.start"-->
      <!--          type="date"-->
      <!--          placeholder="请选择"-->
      <!--          format="YYYY-MM-DD"-->
      <!--          value-format="x"-->
      <!--        />-->
      <!--        <span class="mx-[5px]">-</span>-->
      <!--        <el-date-picker-->
      <!--          :clearable="false"-->
      <!--          style="width: 160px"-->
      <!--          @change="getCustomMeterPowerLine"-->
      <!--          v-model="searchInfo.end"-->
      <!--          type="date"-->
      <!--          placeholder="请选择"-->
      <!--          format="YYYY-MM-DD"-->
      <!--          value-format="x"-->
      <!--        />-->
      <!--      </div>-->

      <div class="flex items-center">
        <span class="mr-[4px]">查询日期选择：</span>
        <el-date-picker
          :clearable="false"
          style="width: 160px"
          @change="getCustomMeterPowerLine"
          v-model="searchInfo.start"
          type="month"
          placeholder="请选择"
          format="YYYY-MM"
          value-format="YYYY-MM"
        />
        <!--        <span class="mx-[5px]">-</span>-->
        <!--        <el-date-picker-->
        <!--            :clearable="false"-->
        <!--            style="width: 160px"-->
        <!--            @change="getCustomMeterPowerLine"-->
        <!--            v-model="searchInfo.end"-->
        <!--            type="date"-->
        <!--            placeholder="请选择"-->
        <!--            format="YYYY-MM-DD"-->
        <!--            value-format="x"-->
        <!--        />-->
      </div>

      <div class="mt-[20px]">
        <el-row>
          <el-col :span="8">
            <H4 style="margin-top: 40px"> 1.本期电量 </H4>
            <p>
              &nbsp&nbsp&nbsp&nbsp本月您的电量为{{
                powerTotal || 0
              }}兆瓦时，较上期环比增长{{ growthRate || 0 }}%
            </p>
            <H4 style="margin-top: 20px">2.峰谷比例</H4>
            <p>
              &nbsp&nbsp&nbsp&nbsp本期您用电的峰谷分时比例为：{{
                peakValleyRatio?.peakStartPowerRatio || 0
              }}%, {{ peakValleyRatio?.summitStartPowerRatio || 0 }}%,
              {{ peakValleyRatio?.flatSegmentStartPowerRatio || 0 }}%,
              {{ peakValleyRatio?.lowEbbStartPowerRatio || 0 }}%,
              上期峰谷分时比例为:
              {{ prevMonthPeakValleyRatio?.peakStartPowerRatio || 0 }}%,
              {{ prevMonthPeakValleyRatio?.summitStartPowerRatio || 0 }}%,
              {{ prevMonthPeakValleyRatio?.flatSegmentStartPowerRatio || 0 }}%,
              {{ prevMonthPeakValleyRatio?.lowEbbStartPowerRatio || 0 }}%
            </p>
          </el-col>
          <el-col :span="16">
            <Echarts
              :echartsData="analyseOptionDeal"
              EWidth="100%"
              EHeight="300px"
              echartId="analyse"
            ></Echarts>
            <!--            <ChartsLine height="250px" :series="series" :x-data="xData" />-->
            <!--            <ChartsBar height="350px"  :series="series1" :x-data="xData1" />-->
          </el-col>
        </el-row>
      </div>
      <!--      <div class="flex items-center mt-[20px]">-->
      <!--        <span class="mr-[4px]">数据查询日期选择：</span>-->
      <!--        <el-date-picker-->
      <!--          :clearable="false"-->
      <!--          style="width: 160px"-->
      <!--          @change="handleSelectChange"-->
      <!--          v-model="dateValue"-->
      <!--          type="date"-->
      <!--          placeholder="请选择"-->
      <!--          format="YYYY-MM-DD"-->
      <!--          value-format="x"-->
      <!--        />-->
      <!--      </div>-->
      <div class="mt-[20px]">
        <pure-table border stripe :columns="columns" :data="tableData">
          <!--          <template-->
          <!--            v-for="(item, index) in timeSlot(60)"-->
          <!--            :key="item"-->
          <!--            #[`hour${index}`]="{ row }"-->
          <!--          >-->
          <!--            <el-input-number-->
          <!--              @change="handleInputChange"-->
          <!--              style="width: 90px"-->
          <!--              size="small"-->
          <!--              v-if="row.type === '修正值'"-->
          <!--              v-model="row[item]"-->
          <!--            />-->
          <!--            <span v-else>-->
          <!--              {{ row[item] }}-->
          <!--            </span>-->
          <!--          </template>-->
        </pure-table>
      </div>
      <!--      <div class="mt-[20px]">-->
      <!--        <el-button @click="updateLine">修改</el-button>-->
      <!--      </div>-->
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, reactive, onActivated } from "vue";
import dayjs from "dayjs";
import {
  getForecastDetailApi,
  queryCustomAccountApi,
  saveDayPointsApi
} from "@/api/load-forecasting/index";
import { getCustomPowerApi } from "@/api/customer-management/index";
import { ElMessage } from "element-plus";
import { cloneDeep } from "lodash-es";
import ChartsLine from "./components/ChartsLine.vue";
defineOptions({
  name: "LoadForecastingLoadManagementDetail"
});
const loading = ref<boolean>(false);
import { useRoute, useRouter } from "vue-router";
import ChartsBar from "@/views/big-screen/components/charts-bar.vue";
import { echartsConfigBottom } from "@/utils/echartsConfig";
const route = useRoute();
const router = useRouter();
// const props = defineProps({
//   searchDate: {
//     type: Object,
//     default: () => {}
//   },
//   id: {
//     type: String,
//     default: ""
//   },
//   name: {
//     type: String,
//     default: ""
//   }
// });

const props = reactive({
  searchDate: "",
  id: "",
  name: ""
});

// 本期同期电量
const analyseOption = ref({
  confine: true,
  tooltip: {
    trigger: "axis",
    backgroundColor: "rgba(255,255,255,0.90)",
    borderRadius: "4px",
    boxShadow: " 0px 2px 10px 0px rgba(0, 0, 0, 0.16)",
    formatter: function (params: any) {
      let context;
      let content = params.map((item: any) => {
        return `${item.marker}<span>${
          item.seriesName
        }</span>:<span style="margin-left: 10px;float: right">${
          item.value ? item.value : "--"
        }MWh</span><br>`;
      });
      let newContent = "";
      content.forEach((item: any) => {
        newContent = newContent + item;
      });
      context = `<div>${params[0].name}</div><div>${newContent}</div>`;
      return context;
    }
  },
  // legend: {},
  grid: {
    top: "18%",
    left: "5%",
    right: "5%",
    bottom: "15%"
  },
  xAxis: {
    type: "category",
    data: ["尖峰", "高峰", "平", "谷", "深谷"],
    axisTick: {
      show: false // 将此选项设置为 false 来隐藏 X 轴刻度
    }
  },
  yAxis: [
    {
      position: "left",
      type: "value",
      name: "电量(MWh)",
      nameTextStyle: {
        // y轴name的样式调整
        fontSize: 14
      },
      splitLine: {
        show: false //想要不显示网格线，改为false
      }
    }
  ],
  series: [
    {
      name: "上期",
      data: [],
      type: "bar",
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      showSymbol: false, // 不显示折线上的圆点
      symbol: "circle", //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        color: "#254f7a"
      }
    },
    {
      name: "本期",
      data: [],
      type: "bar",
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      showSymbol: false, // 不显示折线上的圆点
      symbol: "circle", //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        color: "#029CD4"
      }
    }
  ]
});
const analyseOptionDeal = echartsConfigBottom(analyseOption.value);

const xData = ref<string[]>(["尖峰", "高峰", "平", "谷"]);
const series = ref([
  {
    type: "bar",
    name: "上期",
    // markPoint: {
    //   data: [
    //     { type: "max", name: "Max" },
    //     { type: "min", name: "Min" }
    //   ]
    // },
    data: []
  },
  {
    type: "bar",
    name: "本期",
    // markPoint: {
    //   data: [
    //     { type: "max", name: "Max" },
    //     { type: "min", name: "Min" }
    //   ]
    // },
    data: []
  }
]);
const xData1 = ref<string[]>(["尖峰", "高峰", "平", "谷"]);
const series1 = ref([
  {
    type: "bar",
    name: "上期",
    data: []
  },
  {
    type: "bar",
    name: "本期",
    data: []
  }
]);
const dateValue = ref(dayjs().valueOf());
const accountVal = ref<string>("");
const meterVal = ref<string>("");
// 户信息列表
const accountInfoList = ref([]);
// 户号列表
const accountList = ref([]);
//表记列表
const meterList = ref([]);
const searchInfo = ref({
  start: "",
  end: ""
});
const customName = ref("");
const timesColumns = timeSlot(60).map((item, index) => {
  return {
    label: item,
    prop: `hour${index + 1}`,
    align: "center",
    width: 120,
    slot: `hour${index}`
  };
});
const columns = [
  {
    label: "读数日期",
    prop: "readDateStr",
    align: "center",
    headerAlign: "center",
    width: 120
  },
  {
    label: "日电量合计",
    prop: "dayTotal",
    align: "center",
    headerAlign: "center",
    width: 120
  }
].concat(timesColumns);
const tableData = ref([]);
const customId = ref<string>("");
const CustomAccountData = ref();
async function queryCustomAccount() {
  loading.value = true;
  try {
    const res = await queryCustomAccountApi(customName.value);
    // console.log(res.data)
    accountInfoList.value = res.data.accountInfoList;
    if (res.data.accountInfoList) {
      accountList.value = res.data.accountInfoList.map(item => {
        return {
          label: item.accountNo,
          value: item.accountNo
        };
      });
      accountVal.value = accountList.value[0].value;
    }
    if (res.data.accountInfoList[0].meterInfoList) {
      meterList.value = res.data.accountInfoList[0].meterInfoList.map(item => {
        return {
          label: item.meterName,
          value: item.meterCode
        };
      });
      meterVal.value = meterList.value[0].value;
      await getForecastDetail();
      // await getCustomMeterPowerLine();
      // await getTableData();
    }
    setTimeout(() => {
      loading.value = false;
    }, 300);
  } catch {
    setTimeout(() => {
      loading.value = false;
    }, 300);
  }
}
// 当月出电量
const powerTotal = ref();
// 较上期环比增长率
const growthRate = ref();
// 当月峰谷比例
const peakValleyRatio = ref({
  peakStartPowerRatio: "",
  summitStartPowerRatio: "",
  flatSegmentStartPowerRatio: "",
  lowEbbStartPowerRatio: ""
});
// 上月峰谷比例
const prevMonthPeakValleyRatio = ref({
  peakStartPowerRatio: "",
  summitStartPowerRatio: "",
  flatSegmentStartPowerRatio: "",
  lowEbbStartPowerRatio: ""
});
// 峰平谷数据 0本期 1上期
const peakValleyData0 = ref();
const peakValleyData1 = ref();

const getForecastDetail = async () => {
  await getForecastDetailApi({
    meterCode: meterVal.value,
    date: searchInfo.value.start
  }).then((res: any) => {
    // console.log(res.data)
    tableData.value = res.data.customPowerConsumptionDaily;
    powerTotal.value = res.data.powerTotal;
    growthRate.value = res.data.growthRate;
    peakValleyRatio.value = res.data.peakValleyRatio;
    prevMonthPeakValleyRatio.value = res.data.prevMonthPeakValleyRatio;
    peakValleyData0.value = [
      res.data.peakValley[0]?.peakStartPower,
      res.data.peakValley[0]?.summitStartPower,
      res.data.peakValley[0]?.flatSegmentStartPower,
      res.data.peakValley[0]?.lowEbbStartPower
    ];
    peakValleyData1.value = [
      res.data.peakValley[1]?.peakStartPower,
      res.data.peakValley[1]?.summitStartPower,
      res.data.peakValley[1]?.flatSegmentStartPower,
      res.data.peakValley[1]?.lowEbbStartPower
    ];
    analyseOption.value.series[1].data = peakValleyData0.value;
    analyseOption.value.series[0].data = peakValleyData1.value;
    // console.log(peakValleyData0.value,peakValleyData1.value)
  });
};

async function handleAccounChange(data) {
  // const res = await queryCustomAccountApi({
  //   customId: customId.value,
  //   accountId: data
  // });
  if (accountInfoList.value) {
    accountInfoList.value.forEach((item, index) => {
      console.log(item, index, data);
      if (item.accountNo === data) {
        meterList.value = item.meterInfoList.map(item => {
          return {
            label: item.meterName,
            value: item.meterCode
          };
        });
        meterVal.value = meterList.value[0].value;
        getForecastDetail();
      }
    });
    // await getCustomMeterPowerLine();
    // await getTableData();
  }
}
// 获取电量曲线
async function getCustomMeterPowerLine() {
  // if (
  //   dayjs(searchInfo.value.start).valueOf() > dayjs(searchInfo.value.end).valueOf()
  // ) {
  //   ElMessage({
  //     message: "查询起始时间不能晚于截止时间",
  //     type: "error"
  //   });
  //   return;
  // }
  loading.value = true;
  // const res = await getCustomPowerApi({
  //   parameter: {
  //     meterId: meterVal.value,
  //     startReadDate: searchInfo.value.start,
  //     endReadDate: searchInfo.value.end
  //   }
  // });
  await getForecastDetail();
  // if (res.data.points) {
  //   xData.value = res.data.points.map(i => i.hour);
  //   series.value[0].data = res.data.points.map(i => i.value);
  // } else {
  //   series.value[0].data = [];
  // }
  setTimeout(() => {
    loading.value = false;
  }, 300);
}
function handleSelectChange(data) {
  dateValue.value = data;
  getTableData();
}
// 获取表格数据
async function getTableData() {
  const res = await getCustomPowerApi({
    parameter: {
      meterId: meterVal.value,
      startReadDate: dateValue.value,
      endReadDate: dateValue.value
    }
  });
  let obj = {};
  if (res.data.points) {
    const times = res.data.points.map(i => i.hour);
    const values = res.data.points.map(i => i.value);
    // series.value[1].data = filerTimeVal(res.data.points, xData.value);
    obj = times.reduce((pre, current, index) => {
      pre[current] = values[index];
      return pre;
    }, {});
    const obj1 = cloneDeep(obj);
    Reflect.set(obj, "type", "原始值");
    Reflect.set(obj1, "type", "修正值");
    tableData.value = [obj, obj1];
  } else {
    tableData.value = [];
    // series.value[1].data = [];
  }
}
// 输入框改变事件
function handleInputChange() {
  // 修改曲线值
  const obj = tableData.value[1];
  const data = [];
  // 对象转数组
  for (const key in obj) {
    if (key !== "type") {
      data.push({
        hour: `${dayjs(dateValue.value).format("MM-DD")} ${key}`,
        value: obj[key]
      });
    }
  }
  const seriesValue = Array.from(data).fill("0");
  const xDataValue = timeSlot(60).map(
    item => `${dayjs(dateValue.value).format("MM-DD")} ${item}`
  );
  for (let index = 0; index < (data && data.length); index++) {
    const element = data[index];
    const subIndex = xDataValue.findIndex(i => i === element.hour);
    seriesValue[subIndex] =
      Number(seriesValue[subIndex]) + Number(element.value);
  }
  const array = [];
  timeSlot(60).forEach((item, index) => {
    array.push({
      hour: item,
      value: seriesValue[index]
    });
  });
  series.value[1].data = filerTimeVal(array, xData.value);
}
async function updateLine() {
  const obj = tableData.value[1];
  const data = [];
  // 对象转数组
  for (const key in obj) {
    if (key !== "type") {
      data.push({
        hour: key,
        value: obj[key]
      });
    }
  }
  const res = await saveDayPointsApi({
    points: data,
    parameter: {
      meterId: meterVal.value,
      startReadDate: dateValue.value,
      endReadDate: dateValue.value
    }
  });
  if (res.code === "200") {
    ElMessage({
      message: "操作成功",
      type: "success"
    });
    getCustomMeterPowerLine();
    getTableData();
  } else {
    ElMessage({
      message: res.message,
      type: "error"
    });
  }
}
async function handleMeterChange(data) {
  meterVal.value = data;
  await getCustomMeterPowerLine();
  getTableData();
}
// 对齐时间轴
function filerTimeVal(array, time) {
  const timeArr: any = [];
  const arrVal: any = [];
  time.forEach(item => {
    timeArr.push({
      time: item
    });
  });
  timeArr.forEach(item => {
    let index = null;
    index = array.findIndex(i => {
      return (
        `${dayjs(dateValue.value).format("MM-DD")} ${i.hour}` === item.time
      );
    });
    if (index !== -1 && index !== null && array[index] !== undefined) {
      arrVal.push(array[index]);
    } else {
      arrVal.push({
        timeFormat: item.time,
        value: "-"
      });
    }
    index = null;
  });
  return arrVal;
}
// 获取时间轴
function timeSlot(step) {
  //  step = 间隔的分钟
  var date = new Date();
  date.setHours(0); // 时分秒设置从零点开始
  date.setSeconds(0);
  date.setUTCMinutes(0);
  var timeArr: any = [];
  var slotNum = (24 * 60) / step; // 算出多少个间隔
  for (var f = 0; f < slotNum; f++) {
    var time = new Date(Number(date.getTime()) + Number(step * 60 * 1000 * f)); // 获取：零点的时间 + 每次递增的时间
    var hour = "";
    var sec: any = "";
    time.getHours() < 10
      ? (hour = "0" + time.getHours())
      : (hour = time.getHours() as any); // 获取小时
    time.getMinutes() < 10
      ? (sec = "0" + time.getMinutes())
      : (sec = time.getMinutes()); // 获取分钟
    timeArr.push(hour + ":" + sec);
  }
  return timeArr;
}
watch(
  () => props.searchDate,
  (newVal: any) => {
    newVal = {
      startDate: dayjs(newVal.startDate).format("YYYY-MM-DD"),
      endDate: dayjs(newVal.endDate).format("YYYY-MM-DD")
    };
    searchInfo.value.start = newVal.startDate;
    dateValue.value = newVal.startDate;
    searchInfo.value.end = newVal.endDate;
  },
  { deep: true, immediate: true }
);
watch(
  () => props.id,
  async newVal => {
    console.log(newVal, "newVal");

    customId.value = newVal;
    // await queryCustomAccount();
    // getCustomMeterPowerLine();
    // getTableData();
  },
  { immediate: true }
);
watch(
  () => props.name,
  newVal => {
    customName.value = newVal;
  },
  { immediate: true }
);
onMounted(async () => {
  const newVal = {
    startDate: dayjs(Date()).format("YYYY-MM"),
    endDate: dayjs(Date()).format("YYYY-MM")
  };
  // const newVal = {
  //   startDate: dayjs(Date()).valueOf(),
  //   endDate: dayjs(Date()).valueOf(),
  // }
  searchInfo.value.start = String(newVal.startDate);
  dateValue.value = Number(newVal.startDate);
  searchInfo.value.end = String(newVal.endDate);
  customName.value = <string>route.query.customName;
  customId.value = <string>route.query.detailId;
  // console.log(route.query.startDate)
  searchInfo.value.start = <string>route.query.startDate;
  // console.log(route.query.customName, route.query,customName.value, customId.value)
  await queryCustomAccount();
});
onActivated(() => {
  console.log("111");
});
</script>

<style lang="scss" scoped></style>
