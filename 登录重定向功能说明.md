# 登录重定向功能说明

## 功能概述

实现了登录后自动跳转到原始页面的功能。当用户访问需要登录的页面时，系统会自动保存当前页面路径，在用户登录成功后跳转回原始页面，提升用户体验。

## 实现原理

### 1. 路径保存机制
- 当用户未登录访问受保护页面时，系统自动保存当前页面路径到 `sessionStorage`
- 存储键名：`login-redirect-path`
- 保存完整路径：包括路由路径、查询参数和hash参数

### 2. 登录后跳转
- 登录成功后检查是否有保存的重定向路径
- 如果有，跳转到原始页面并清除保存的路径
- 如果没有，跳转到默认页面（`/big-screen`）

### 3. 多场景支持
支持以下场景的重定向：
- 路由守卫拦截（未登录访问受保护页面）
- 用户信息获取失败（token过期等）
- 其他需要重新登录的情况

## 技术实现

### 1. 工具函数 (`src/utils/loginRedirect.ts`)

```javascript
// 保存重定向路径
export function saveRedirectPath(path?: string): void

// 获取保存的重定向路径
export function getRedirectPath(): string | null

// 清除保存的重定向路径
export function clearRedirectPath(): void

// 执行登录后跳转
export async function executeLoginRedirect(router: any, defaultPath: string = '/big-screen'): Promise<void>

// 处理需要登录的路由跳转
export function handleLoginRequired(router: any, currentPath?: string): void
```

### 2. 路由守卫修改 (`src/router/index.ts`)

```javascript
// 未登录用户访问受保护页面时
if (whiteList.indexOf(to.path) !== -1) {
  next();
} else {
  // 使用工具函数保存重定向路径
  const { saveRedirectPath } = await import("@/utils/loginRedirect");
  saveRedirectPath(to.fullPath);
  next({ path: "/login" });
}
```

### 3. 用户Store修改 (`src/store/modules/user.ts`)

```javascript
// 用户信息获取失败时
} catch (error) {
  this.$reset()
  // 使用工具函数处理登录重定向
  const { handleLoginRequired } = await import("@/utils/loginRedirect");
  handleLoginRequired(router);
}
```

### 4. 登录页面修改 (`src/views/login/index.vue`)

```javascript
// 登录成功后
// 使用工具函数执行登录后跳转
const { executeLoginRedirect } = await import("@/utils/loginRedirect");
await executeLoginRedirect($router, "/big-screen");
```

## 使用场景

### 场景1：直接访问受保护页面
1. 用户在浏览器地址栏输入：`http://localhost:8080/#/sys-management/dict-management/index`
2. 系统检测到用户未登录，保存路径并跳转到登录页
3. 用户登录成功后，自动跳转到字典管理页面

### 场景2：Token过期
1. 用户正在使用系统，token过期
2. 系统尝试获取用户信息失败，保存当前页面路径
3. 跳转到登录页面
4. 用户重新登录后，回到之前的页面

### 场景3：嵌入模式下的重定向
1. 外部系统嵌入页面：`http://localhost:8080/#/customer-record/index?hide=true`
2. 如果需要登录，保存完整路径（包括hide参数）
3. 登录后跳转回嵌入模式的原始页面

## 测试方法

### 测试1：基本重定向功能
1. 确保未登录状态（清除token）
2. 直接访问：`http://localhost:8080/#/sys-management/dict-management/index`
3. 验证：自动跳转到登录页
4. 登录成功后验证：自动跳转到字典管理页面

### 测试2：带参数的重定向
1. 访问带参数的页面：`http://localhost:8080/#/customer-record/index?hide=true`
2. 登录后验证：跳转到带参数的原始页面

### 测试3：Token过期重定向
1. 登录系统并导航到某个页面
2. 手动清除token或等待token过期
3. 刷新页面或执行需要认证的操作
4. 验证：跳转到登录页，登录后回到原页面

### 测试4：默认跳转
1. 直接访问登录页面并登录
2. 验证：跳转到默认页面（/big-screen）

## 调试功能

### 1. 控制台日志
系统会在以下时机输出调试日志：
- 保存重定向路径时
- 登录成功跳转时
- 清除重定向路径时

### 2. 测试页面调试
在测试页面 (`src/views/test-embedded-mode.vue`) 中添加了重定向路径的显示：
- 显示当前是否有保存的重定向路径
- 显示具体的重定向路径内容

### 3. SessionStorage检查
可以在浏览器开发者工具中查看：
- Application → Storage → Session Storage
- 查找键名：`login-redirect-path`

## 注意事项

### 1. 路径过滤
- 不会保存登录页面路径（避免循环重定向）
- 自动处理hash路由和history路由模式

### 2. 存储方式
- 使用 `sessionStorage` 而不是 `localStorage`
- 会话结束后自动清除，避免跨会话的错误重定向

### 3. 错误处理
- 如果重定向路径无效，会跳转到默认页面
- 包含完整的错误处理和降级机制

### 4. 兼容性
- 与现有的单点登录功能兼容
- 与嵌入模式功能兼容
- 不影响正常的登录流程

## 故障排除

### 问题1：重定向不生效
- 检查 sessionStorage 中是否有保存路径
- 检查控制台是否有相关日志
- 确认登录成功后的跳转逻辑

### 问题2：重定向到错误页面
- 检查保存的路径是否正确
- 确认路由配置是否有效
- 检查权限设置

### 问题3：循环重定向
- 确认没有保存登录页面路径
- 检查路由守卫逻辑
- 确认白名单配置正确

---

*功能实现时间: 2025-01-13*  
*版本: v1.0*
