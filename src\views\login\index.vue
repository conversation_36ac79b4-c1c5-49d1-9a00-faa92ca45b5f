<template>
  <div class="login_container">
    <div class="right_container">
      <div class="lt-logo">
        <!-- <img src="@/assets/images/BigScreen/stateGrid.png" alt="logo" style="width: 180px; height: 180px" /> -->
        <!--        <img src="@/assets/images/powerlttitle.png" alt="text" style="width: 220px; height: 40px; margin-left: 5px" />-->
      </div>
      <div class="form_left"></div>
      <div class="form_right">
        <h1 class="form_right_title">售电交易辅助决策</h1>
        <!-- 登录的表单 -->
        <el-form
          class="form_right_form"
          :model="loginForm"
          :rules="rules"
          ref="loginForms"
          label-position="left"
          label-width="60px"
        >
          <el-form-item prop="username" label="账号">
            <el-input
              :prefix-icon="User"
              v-model="loginForm.username"
              size="large"
            ></el-input>
          </el-form-item>
          <el-form-item prop="password" label="密码">
            <el-input
              type="password"
              :prefix-icon="Lock"
              v-model="loginForm.password"
              show-password
              size="large"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button
              :loading="loading"
              style="width: 100%"
              type="primary"
              size="large"
              @click="onLogin"
              color="#0052D9"
            >
              登录
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 弹窗 -->
    <div class="select-model" v-if="dialogVerifyVisibleSelect">
      <el-dialog
        :close-on-click-modal="false"
        v-model="dialogVerifyVisibleSelect"
        center
      >
        <template #header>
          <div style="font-size: 24px; font-weight: 700; color: #fff">
            选择登录的企业
          </div>
        </template>
        <div class="login-select-body">
          <div class="login-select-body-content">
            <el-radio-group v-model="loginRadioSelect" class="ml-4">
              <div v-for="(item, index) in loginRequestList" :key="index">
                <el-radio :label="index" size="large" fill="#088990;">
                  {{ item.tenantName }}
                </el-radio>
                <div
                  class="hr-line-div"
                  v-if="loginRadioSelect === index"
                ></div>
                <div v-else style="height: 12px"></div>
              </div>
            </el-radio-group>
          </div>
        </div>

        <template #footer>
          <el-button
            type="primary"
            class="cancel-button"
            @click="dialogVerifyVisibleSelect = false"
          >
            取消
          </el-button>
          <el-button
            type="primary"
            class="save-button"
            @click="
              (dialogVerifyVisibleSelect = false), reqGetVerificationCodeFn()
            "
          >
            确认
          </el-button>
        </template>
      </el-dialog>
    </div>
    <!-- 验证滑块 -->
    <el-dialog
      v-model="dialogVerifyVisible"
      width="600px"
      :close-on-click-modal="false"
      title="请验证是否真人操作"
    >
      <slide-verify
        style="margin-left: 30px"
        ref="blockRef"
        :l="60"
        :r="10"
        :w="500"
        :h="350"
        :accuracy="accuracy"
        :imgs="images"
        @success="onSuccess"
        @fail="onFail"
        slider-text="向右滑动"
      ></slide-verify>
    </el-dialog>

    <el-dialog v-model="dialogidCardVisible" title="输入信息" width="60%">
      <el-form
        ref="mainFormRef"
        label-suffix=":"
        :model="formData"
        label-width="125px"
        :rules="mainFormRules"
      >
        <el-row>
          <el-col style="margin-top: 10px">
            <h2 style="text-align: center">之后请使用联系电话登录</h2>
          </el-col>
        </el-row>
        <el-row>
          <el-col style="margin-top: 10px">
            <el-form-item prop="phone" label="联系电话">
              <el-input v-model="formData.phone"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="footer" style="margin-top: 15px">
        <el-button class="save-button" @click="saveClick">确认 </el-button>
        <el-button class="cancel-button" @click="cancleClick">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { User, Lock } from "@element-plus/icons-vue";
import { reactive, ref } from "vue";
import { useRouter } from "vue-router";
import { ElNotification, FormRules } from "element-plus";
//引入获取当前时间的函数
import { getTime } from "@/utils/time";
//引入用户相关的小仓库
import { useUserStore } from "@/store/modules/user";
import { validatorUserName, validatorPassword } from "@/utils/require";
import md5 from "js-md5";

import {
  CheckPhone,
  getUserTenant,
  reqExchangeToken,
  reqGetVerificationCode,
  reqLogin,
  updateUserInfo
} from "@/api";
import SlideVerify from "vue3-slide-verify";
import "vue3-slide-verify/dist/style.css";
import verify1 from "@/assets/images/verity/verify1.png";
import verify2 from "@/assets/images/verity/verify2.png";
import verify3 from "@/assets/images/verity/verify3.png";
import { usePermissionStoreHook } from "@/store/modules/permission";
import { addPathMatch } from "@/router/utils";
import { setToken } from "@/utils/auth";

//登录验证滑块回调
const dialogVerifyVisible = ref(false);
//@ts-ignore
const blockRef = ref<any>();
const msg = ref("");
const accuracy = ref(5);
const images = [verify1, verify2, verify3];
const onSuccess = () => {
  setTimeout(() => {
    login();
    dialogVerifyVisible.value = false;
    blockRef?.value?.refresh();
  }, 1000);
};
const onLogin = async () => {
  await loginForms.value.validate();
  // dialogVerifyVisible.value = true
  login();
};
const onFail = () => {
  msg.value = "验证失败，请重试";
};

let useStore = useUserStore();
//获取el-form组件
let loginForms = ref();
//获取路由器
let $router = useRouter();
//路由对象
// let $route = useRoute()
//定义变量控制按钮加载效果
let loading = ref(false);
//收集账号与密码的数据
let loginForm = reactive({ username: "", password: "" });
let loginFormCopy = reactive({ username: "", password: "" });

//登录按钮回调
const login = async () => {
  //保证全部表单相校验通过再发请求
  await loginRequest();
};
//自定义校验规则函数

//定义表单校验需要配置对象
const rules = {
  username: [{ trigger: "change", validator: validatorUserName }],
  password: [{ trigger: "change", validator: validatorPassword }]
};
// 获取地址中的token

// #region 弹窗
let loginRadioSelect = ref(0); //定义变量控制选择登录企业的弹框
let dialogVerifyVisibleSelect = ref(false); //定义变量控制选择登录企业的弹框变量
// #endregion

//登录请求列表
const loginRequestList = ref<any>([]);
// const loginRequest = async () => {
//   try {
//     //对密码进行md5加密
//     loginFormCopy.username = loginForm.username
//     //@ts-ignore
//     loginFormCopy.password = md5(loginForm.password)
//     await useStore.userLogin(loginFormCopy, (resList: any) => {
//       loginRequestList.value = resList
//       if (loginRequestList.value.length === 0) {
//         throw new Error('该账号没有企业')
//       }
//       else if (loginRequestList.value.length === 1) {
//         reqGetVerificationCodeFn()
//       } else {
//         dialogVerifyVisibleSelect.value = true
//       }
//     })
//   } catch (error) {
//     //登录失败的提示信息
//     ElNotification({
//       title: '请检查账号与密码是否正确',
//       type: 'error',
//     })
//   }
// }

let loginObj = ref<any>();
const dialogidCardVisible = ref<any>(false);
let formData = ref<any>({
  phone: "",
  idCard: ""
});

const checkConfirmPhoneNumber = async (
  rule: any,
  value: string,
  callback: any
) => {
  if (value) {
    const res = await CheckPhone({
      userId: loginObj.value.busId,
      phone: value
    });
    if (res) {
      callback(new Error("该电话号码已被使用。"));
    } else {
      callback();
    }
  } else {
    callback();
  }
};

const mainFormRef = ref<any>();
const mainFormRules = reactive({
  phone: [
    {
      required: true,
      pattern: /^1[345789]\d{9}$/,
      trigger: "blur",
      message: "请输入正确联系电话"
    },
    { validator: checkConfirmPhoneNumber, trigger: "blur" }
  ]
  // idCard: [
  //   {
  //     required: true,
  //     pattern: /^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dxX]$/,
  //     trigger: 'blur',
  //     message: '请输入正确身份证号',
  //   },
  // ]
});

const saveClick = () => {
  mainFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      console.log(`output->`, formData.value);
      const res = await updateUserInfo({
        ...loginObj.value,
        ...formData.value
      });
      console.log(`output->res`, res);
      try {
        const res1 = await getUserTenant(res);
        useStore.setLoginUserTenantList(res1.loginUserTenantList);
        useStore.setUserId(res1.busId);
        loginRequestList.value = res1.loginUserTenantList;
        if (loginRequestList.value.length === 0)
          throw new Error("该账号没有企业");
        else if (loginRequestList.value.length === 1) {
          await reqGetVerificationCodeFn();
        } else {
          dialogVerifyVisibleSelect.value = true;
        }
      } catch (error) {
        //登录失败的提示信息
        ElNotification({
          title: "请检查账号与密码是否正确",
          type: "error"
        });
      }
    }
  });
};

const loginRequest = async () => {
  try {
    //对密码进行md5加密
    loginFormCopy.username = loginForm.username;
    loginFormCopy.password = loginForm.password;
    // loginFormCopy.password = md5(loginForm.password)

    const res = await reqLogin(loginFormCopy);
    loginObj.value = res;
    if (!res.phone) {
      dialogidCardVisible.value = true;
    } else {
      const res1 = await getUserTenant(res);
      useStore.setLoginUserTenantList(res1.loginUserTenantList);
      useStore.setUserId(res1.busId);
      loginRequestList.value = res1.loginUserTenantList;
      if (loginRequestList.value.length === 0)
        throw new Error("该账号没有企业");
      else if (loginRequestList.value.length === 1) {
        await reqGetVerificationCodeFn();
      } else {
        dialogVerifyVisibleSelect.value = true;
      }
    }
  } catch (error) {
    //登录失败的提示信息
    ElNotification({
      title: "请检查账号与密码是否正确",
      type: "error"
    });
  }
};

const cancleClick = () => {
  mainFormRef.value?.resetFields();
  dialogidCardVisible.value = false;
};

// #region 获取验证码
const verificationData = reactive({
  tenantId: "",
  username: "",
  password: "",
  userId: ""
});
const tokenCode = ref("");
const reqGetVerificationCodeFn = async () => {
  try {
    verificationData.username = loginFormCopy.username;
    verificationData.password = loginFormCopy.password;
    verificationData.tenantId =
      loginRequestList.value[loginRadioSelect.value].tenantId;
    verificationData.userId = useStore.userId;
    let res = await reqGetVerificationCode(verificationData); // 获取验证码
    tokenCode.value = res.code;
    let result = await reqExchangeToken({ code: tokenCode.value }); // 获取token
    //将token存储到本地
    useStore.userSetToken(result.token);
    useStore.userSetLongToken(result.longToken);
    await useStore.stateReqUserInfo(); // 获取用户信息
    dialogVerifyVisibleSelect.value = false;
    // 全部采取静态路由模式
    await usePermissionStoreHook().handleWholeMenus([]);
    addPathMatch();
    setToken({
      username: loginFormCopy.username,
      roles: ["admin"],
      accessToken: result.token
    } as any);
    // 使用工具函数执行登录后跳转
    const { executeLoginRedirect } = await import("@/utils/loginRedirect");
    await executeLoginRedirect($router, "/big-screen");

    ElNotification({
      type: "success",
      message: "欢迎回来",
      title: `HI,${getTime()}好`,
      duration: 800
    });
  } catch (error) {
    console.log(error);
  }
};
// #endregion
</script>

<style scoped lang="scss">
.login_container {
  width: 100%;
  height: 100vh;
  background: url("@/assets/images/powerbg1.png") no-repeat;
  background-size: 100% 100%;

  display: flex;
  justify-content: center;
  align-items: center;

  .right_container {
    height: 60%;
    width: 65%;
    border-radius: 10px;
    position: relative;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    background-color: #fff;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    -moz-background-size: 100% 100%;

    .form_left {
      width: 420px;
      height: 420px;
      background: url("@/assets/images/powerLogo.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      -moz-background-size: 100% 100%;
    }

    .form_right {
      width: 550px;
      height: 500px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-around;

      &_title {
        text-align: center;
        font-family: Microsoft YaHei;
        font-size: 42px;
        font-style: normal;
        font-weight: 700;
        line-height: 42px;
      }

      &_form {
        background-color: #fff;
        border-radius: 8px;
        width: 100%;
        height: 300px;
        padding: 24px 24px 0 24px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }
    }
  }
}

.select-model {
  :deep(.el-dialog) {
    border-radius: 4px;
    width: 70%;
    height: 70%;
    padding: 0;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .el-dialog__body {
      padding-left: 5px;
      height: 70%;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .el-dialog__header {
      padding: 0;
      height: 10%;
      margin-right: 0;
      /* background: linear-gradient(360deg, #01689c 0%, #0fa982 100%); */
      background-color: #0052d9;
      border-radius: 4px 4px 0 0;
      display: flex;
      align-items: center;
      justify-content: center;

      .el-dialog__title {
        color: #ffffff;
        font-size: 24px;
        font-weight: 700;
        line-height: 33px;
      }

      .el-dialog__close {
        font-size: 32px;
        color: #ffffff;
      }
    }

    .el-dialog__footer {
      height: 15%;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 20px;
      border-top: 1px solid #0052d9;
    }
  }

  :deep(.el-radio-group) {
    margin: 0 !important;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: flex-start;

    .el-radio {
      margin: 0;
      font-size: 32px;
    }

    .el-radio__input.is-checked + .el-radio__label {
      color: #0052d9 !important;
    }

    .el-radio__input.is-checked .el-radio__inner {
      background: #0052d9 !important;
      border-color: #0052d9 !important;
    }
  }

  :deep(.el-radio__label) {
    font-size: 20px;
  }

  .save-button {
    margin-left: 8px;
    width: 120px;
    border-radius: 0;
    border: none;
    /* background: linear-gradient(317deg, #01689c 0%, #0fa982 100%); */
    background-color: #0052d9;

    /* 文字/14 */
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
  }

  .cancel-button {
    width: 120px;
    border-radius: 2px;
    border: none;
    color: #000;
    background-color: #f2f3f5;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
  }

  .login-select-body {
    height: 100%;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    overflow: auto;

    .login-select-body-content {
      height: 100%;
      display: inline-block;
      margin: 0;
      overflow: auto;
    }
  }

  .hr-line-div {
    margin: 5px auto;
    height: 2px;
    width: 100%;
    /* background: radial-gradient(#165DFF 1%, white 100%); */
    background: linear-gradient(
      244deg,
      rgba(255, 255, 255, 0) 0%,
      #0052d9,
      rgba(255, 255, 255, 0) 100%
    );
  }
}

.lt-logo {
  width: 300px;
  height: 40px;
  position: absolute;
  top: 80px;
  left: -50px;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
}

:deep(.el-form-item) {
  position: relative;
  display: flex;
  align-items: center;
}

:deep(.el-form-item__label) {
  height: 40px;
  line-height: 40px;
  font-size: 22px;
}

.footer {
  display: flex;
  justify-content: center;
}

.save-button {
  margin-left: 8px;
  width: 120px;
  border-radius: 0;
  border: none;
  /* background: linear-gradient(317deg, #01689c 0%, #0fa982 100%); */
  background-color: rgba(0, 82, 217, 0.8);

  color: white;
  /* 文字/14 */
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  /* 157.143% */
}

.cancel-button {
  width: 120px;
  border-radius: 2px;
  border: none;
  color: #000;
  background-color: #d8d9dc;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  /* 157.143% */
}
</style>
