// 全屏状态管理工具
export class FullscreenManager {
  private element: HTMLElement | null = null;
  private callbacks: {
    onEnter?: () => void;
    onExit?: () => void;
    onChange?: (isFullscreen: boolean) => void;
  } = {};

  constructor(elementId?: string) {
    if (elementId) {
      this.element = document.getElementById(elementId);
    }
    this.bindEvents();
  }

  private bindEvents() {
    // 监听全屏状态变化
    document.addEventListener('fullscreenchange', this.handleFullscreenChange.bind(this));
    document.addEventListener('webkitfullscreenchange', this.handleFullscreenChange.bind(this));
    document.addEventListener('mozfullscreenchange', this.handleFullscreenChange.bind(this));
    document.addEventListener('MSFullscreenChange', this.handleFullscreenChange.bind(this));
  }

  private handleFullscreenChange() {
    const isFullscreen = this.isFullscreen();
    
    if (isFullscreen) {
      this.callbacks.onEnter?.();
      this.optimizeForFullscreen();
    } else {
      this.callbacks.onExit?.();
      this.optimizeForWindow();
    }
    
    this.callbacks.onChange?.(isFullscreen);
  }

  private optimizeForFullscreen() {
    // 全屏状态下的强制优化
    if (this.element) {
      // 强制设置容器样式
      this.element.style.setProperty('height', '100vh', 'important');
      this.element.style.setProperty('min-height', '100vh', 'important');
      this.element.style.setProperty('max-height', '100vh', 'important');
      this.element.style.setProperty('overflow', 'hidden', 'important');
      this.element.style.setProperty('margin', '0', 'important');
      this.element.style.setProperty('padding', '0', 'important');
      this.element.style.setProperty('box-sizing', 'border-box', 'important');

      // 调整内容区域
      const mainContent = this.element.querySelector('.main-content') as HTMLElement;
      if (mainContent) {
        mainContent.style.setProperty('height', 'calc(100vh - 100px)', 'important');
        mainContent.style.setProperty('max-height', 'calc(100vh - 100px)', 'important');
        mainContent.style.setProperty('overflow', 'hidden', 'important');
        mainContent.style.setProperty('padding', '100px 45px 0px 45px', 'important');
        mainContent.style.setProperty('margin', '0', 'important');
        mainContent.style.setProperty('box-sizing', 'border-box', 'important');
      }

      // 添加全屏标识类
      this.element.classList.add('fullscreen-active');
    }
  }

  private optimizeForWindow() {
    // 窗口状态下的优化 - 清除全屏样式
    if (this.element) {
      // 清除强制样式
      this.element.style.removeProperty('height');
      this.element.style.removeProperty('min-height');
      this.element.style.removeProperty('max-height');
      this.element.style.removeProperty('overflow');
      this.element.style.removeProperty('margin');
      this.element.style.removeProperty('padding');

      // 恢复默认样式
      this.element.style.minHeight = '1080px';
      this.element.style.height = 'auto';
      this.element.style.overflow = 'auto';

      // 调整内容区域
      const mainContent = this.element.querySelector('.main-content') as HTMLElement;
      if (mainContent) {
        mainContent.style.removeProperty('height');
        mainContent.style.removeProperty('max-height');
        mainContent.style.removeProperty('overflow');
        mainContent.style.removeProperty('padding');
        mainContent.style.removeProperty('margin');

        // 恢复默认样式
        mainContent.style.height = 'auto';
        mainContent.style.overflow = 'visible';
      }

      // 移除全屏标识类
      this.element.classList.remove('fullscreen-active');
    }
  }

  public isFullscreen(): boolean {
    return !!(
      document.fullscreenElement ||
      (document as any).webkitFullscreenElement ||
      (document as any).mozFullScreenElement ||
      (document as any).msFullscreenElement
    );
  }

  public async enterFullscreen(element?: HTMLElement): Promise<void> {
    const targetElement = element || this.element || document.documentElement;
    
    try {
      if (targetElement.requestFullscreen) {
        await targetElement.requestFullscreen();
      } else if ((targetElement as any).webkitRequestFullscreen) {
        await (targetElement as any).webkitRequestFullscreen();
      } else if ((targetElement as any).mozRequestFullScreen) {
        await (targetElement as any).mozRequestFullScreen();
      } else if ((targetElement as any).msRequestFullscreen) {
        await (targetElement as any).msRequestFullscreen();
      }
    } catch (error) {
      console.error('Failed to enter fullscreen:', error);
    }
  }

  public async exitFullscreen(): Promise<void> {
    try {
      if (document.exitFullscreen) {
        await document.exitFullscreen();
      } else if ((document as any).webkitExitFullscreen) {
        await (document as any).webkitExitFullscreen();
      } else if ((document as any).mozCancelFullScreen) {
        await (document as any).mozCancelFullScreen();
      } else if ((document as any).msExitFullscreen) {
        await (document as any).msExitFullscreen();
      }
    } catch (error) {
      console.error('Failed to exit fullscreen:', error);
    }
  }

  public async toggleFullscreen(element?: HTMLElement): Promise<void> {
    if (this.isFullscreen()) {
      await this.exitFullscreen();
    } else {
      await this.enterFullscreen(element);
    }
  }

  public onFullscreenChange(callbacks: {
    onEnter?: () => void;
    onExit?: () => void;
    onChange?: (isFullscreen: boolean) => void;
  }) {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  public destroy() {
    document.removeEventListener('fullscreenchange', this.handleFullscreenChange.bind(this));
    document.removeEventListener('webkitfullscreenchange', this.handleFullscreenChange.bind(this));
    document.removeEventListener('mozfullscreenchange', this.handleFullscreenChange.bind(this));
    document.removeEventListener('MSFullscreenChange', this.handleFullscreenChange.bind(this));
  }
}
