<template>
  <div class="mask">
    <div class="content-box" v-if="isShow">
      <div class="title"> <img src="./lock.png" alt="" style="margin-right: 5px;">提示</div>
      <div class="content">暂无权限，如有需求可联系本单位管理员</div>
      <el-button class="back" @click="back">返回</el-button>
    </div>
  </div>
</template>

<script setup lang='ts'>
import { ref } from 'vue'
const isShow = ref<any>(true)
function back() {
  isShow.value = false
}

</script>

<style scoped lang="scss">
.mask {
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
  position: absolute;
  top: 59px;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, .4);
  backdrop-filter: blur(4px);
  background: url('./bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;

  .content-box {
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 464px;
    height: 180px;
    background-color: #fff;

    .title {
      display: flex;
      align-items: center;
      font-size: 16px;
      margin: 10px;
    }

    .content {
      font-size: 14px;
      margin: 15px;
    }
  }
}
</style>