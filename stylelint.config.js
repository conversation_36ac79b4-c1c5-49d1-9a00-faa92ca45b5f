module.exports = {
  defaultSeverity: "error",
  root: true,
  extends: [
    "stylelint-config-standard",
    "stylelint-config-prettier",
    "stylelint-config-html/vue",
    "stylelint-config-recommended-vue/scss",
    "stylelint-config-recommended-scss"
  ],
  plugins: ["stylelint-order", "stylelint-prettier", "stylelint-scss"],
  overrides: [
    {
      files: ["**/*.(css|html|vue)"],
      customSyntax: "postcss-html"
    },
    {
      files: ["*.scss", "**/*.scss"],
      customSyntax: "postcss-scss",
      extends: [
        "stylelint-config-standard-scss",
        "stylelint-config-recommended-vue/scss"
      ]
    }
  ],
  rules: {
    indentation: 2,
    "selector-pseudo-class-no-unknown": null,
    "selector-class-pattern": null,
    "color-function-notation": null
  },
  ignoreFiles: ["**/*.js", "**/*.jsx", "**/*.tsx", "**/*.ts", "**/*.json"]
};
