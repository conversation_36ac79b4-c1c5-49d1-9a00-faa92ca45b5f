import dayjs from "dayjs";
export const columns: TableColumnList = [
  {
    label: "序号",
    width: 80,
    align:"center",
    type: "index"
  },
  {
    label: "字典名称",
    prop: "name",
    slot:'name'
  },
  {
    label: "字典编码",
    prop: "code"
  },
  // {
  //   label: "创建时间",
  //   prop: "createTime",
  //   formatter: ({ createTime }) =>
  //     dayjs(createTime).format("YYYY-MM-DD HH:mm:ss")
  // },

  {
    label: "操作",
    width: "120",
    fixed: "right",
    slot: "operation"
  }
];

export const formColums: TableColumnList = [
  {
    width: 55,
    type: "selection"
  },
  {
    label: "序号",
    width: 80,
    type: "index"
  },
  {
    label: "字典标签",
    prop: "label",
    slot: "label"
  },
  {
    label: "字典编号",
    prop: "no",
    slot: "no"
  },
  {
    label: "字典值",
    prop: "value",
    slot: "dictValue"
  },
  {
    label: "排序",
    prop: "serialNumber",
    slot: "serialNumber"
  }
];

