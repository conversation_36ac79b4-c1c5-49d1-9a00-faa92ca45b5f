<template>
    <div>
        <el-dialog width="35%" v-model="dialogVisible" title="通过哪种方式进行标签的创建？" center>
            <div class="flex-container ">
                    <div class="box" @click="clickLabel">
                        <img :src="getAssetURL('createRules')" alt="" style="margin: 0 20px;" />
                        <!-- <img :src="getAssetURL('createRules')"  alt=""/> -->
                        <div class="contant">
                            <p style="font-weight: bold; margin-bottom: 8px;">规则创建</p>
                            <p>
使用用户属性及行为数据，筛选出符合条件的用户。如：将“过去 7 天有登录行为”的用户，划分为“近期登录用户”用户分群</p>
                        </div>
                    </div>

                </div>
                <!-- <el-col :span="12">
                    <div class="box" @click="handleStatistics">
                        <img src="/src/assets/svg/u14.png" alt="">
                        <div class="contant">
                            <p style="font-weight: bold; margin-bottom: 8px;">统计类</p>   
                            <p>将用户数据统计的计算结果作为标签值</p>

                                
                        </div>
                    </div>

                </el-col> -->
           
            <!-- <el-row v-if="createtype === 'createLable'">
                <el-col :span="8">
                    <div class="box">
                        <img src="/src/assets/svg/u19.png" alt="">
                        <div class="contant">
                            <p style="font-weight: bold; margin-bottom: 8px;">挖掘类</p>
                            <p>根据数据挖掘模型指定标签值</p>


                        </div>
                    </div>

                </el-col>

            </el-row> -->
        </el-dialog>

    </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useRouter } from "vue-router";
const dialogVisible = ref<boolean>(false);
const { push } = useRouter();
const createtype = ref<string>("");
const handleCreateLabel = (type:any) => {

    createtype.value = type;
    dialogVisible.value = true;
}

// 点击统计
const  handleStatistics = (type:any) => {
    createtype.value = type;
    dialogVisible.value = false; 
}
const clickLabel = () => {
    if(createtype.value === "createLable"){
        
      push(`/userPortrait/userGrouping/ruleCreation`);
      dialogVisible.value = false;
    }else{

        dialogVisible.value = false; 
    }
   
}

const getAssetURL = (image: any) => {
    return new URL(`../../../../assets/svg/${image}.svg`, import.meta.url).href
}

defineExpose({
    handleCreateLabel
});
</script>

<style lang="scss" scoped>

.flex-container {
  display: flex;
  flex-direction: column; /* 或 row，取决于你的布局需求 */
  align-items: center; /* 水平居中 */
  justify-content: center; /* 垂直居中（如果你需要的话） */
  height: 100%; /* 如果需要垂直居中，确保容器有高度 */
}
.box {
    width: 90%;

    border: 1px solid #ccc;
    display: flex;
    cursor: pointer;

    img {
        width: 50px;
        height: 55px;
        margin: auto 15px;
    }
    .contant{
        margin: 15px 0;
    }
}
.box:hover{
    border: 1px solid #009688;
}

.el-row {
    margin-bottom: 20px;
}

.el-row:last-child {
    margin-bottom: 0;
}

.el-col {
    border-radius: 4px;
}

.grid-content {
    border-radius: 4px;
    min-height: 36px;
}
</style>