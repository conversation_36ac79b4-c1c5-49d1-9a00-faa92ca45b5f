node启动报错：'NODE_OPTIONS' 不是内部或外部命令，也不是可运行的程序

```
解决： pnpm install -g win-node-env. 重新启动

调用服务路径在/public/serverConfig.json的文件下
"BaseUrl":"http://172.18.0.46:9882"

1.运行提示o.upload.addEventListener is not a function
解决方案:(此方法不是根本解决办法，问题3的解决办法是最终解决方案)
```	            // 创建原生 XHR 对象，调用原生 open()，监听所有原生事件
	            var xhr = createNativeXMLHttpRequest()
```	            this.custom.xhr = xhr
找到node_modules/mockjs/dist/mock.js 第8308行
找到node_modules/mockjs/src/xhr/xhr.js 第216行(确保在生产模式下打包出来的代码正常使用。)
添加代码： MockXMLHttpRequest.prototype.upload = xhr.upload;

2.上传文件链接返回提示 no such bucket
这是后端给你的token有问题，让后端去设置一下正确的bucket再生成token给你

3…上传文件过程报错cannot read property ‘total’ of null（文件能传成功，无法获取执行回调）
这是由于vue-cli中的mockjs模块全局使用了MockXMLHttpRequest，而七牛sdk本身使用的是XMLHttpRequest按照标准来的，但是vue-cli中的mockjs模块硬是把XMLHttpRequest自己封装一下导致冲突，会导致对象属性访问不到，删除mockjs模块即可。
解决方法：npm uninstall mockjs 删除该模块依赖