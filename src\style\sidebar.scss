/* $sideBarWidth: vertical 模式下主体内容距离网页文档左侧的距离 */
@mixin merge-style($sideBarWidth) {
  $menuActiveText: #7a80b4;

  @media screen and (width >=150px) and (width <=420px) {
    .app-main-nofixed-header {
      overflow-y: hidden;
    }
  }

  @media screen and (width >=420px) {
    .app-main-nofixed-header {
      overflow: hidden;
    }
  }

  .sub-menu-icon {
    margin-right: 5px;
    font-size: 24px;

    svg {
      width: 24px;
      height: 24px;
    }
  }

  .set-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 48px;
    cursor: pointer;
  }

  .main-container {
    position: relative;
    height: 100vh;
    min-height: 100%;
    margin-left: $sideBarWidth;
    background: #f7f7f7;
    /* main-content 属性动画 */
    transition: margin-left var(--pure-transition-duration);

    .el-scrollbar__wrap {
      // height: calc(100% - 24px);
      overflow: auto;
      // background: #fff;
      // border-radius: 0 10px 10px 10px;
    }
  }


  .fixed-header {
    position: fixed;
    top: 0;
    right: 0;
    z-index: 998;
    width: 100%;
    // width: calc(100% - 210px);

    /* fixed-header 属性左上角动画 */
    transition: width var(--pure-transition-duration);
  }

  .main-hidden {
    margin-left: 0 !important;

    .fixed-header {
      width: 100% !important;

      +.app-main {
        padding-top: 37px !important;
      }
    }
  }

  .sidebar-container {
    position: fixed;
    top: 64px;
    bottom: 0;
    left: 0;
    z-index: 1001;
    width: $sideBarWidth !important;
    height: calc(100% - 64px);
    overflow: hidden;
    font-size: 0;
    // background: $menuBg;
    // box-shadow: 0 0 1px #888;

    /* 展开动画 */
    transition: width var(--pure-transition-duration);

    .scrollbar-wrapper {
      overflow-x: hidden !important;
    }

    .el-scrollbar__bar.is-vertical {
      right: 0;
    }

    .el-scrollbar {
      height: calc(100% - 44px);
    }

    &.has-logo {
      .el-scrollbar.pc {
        /* logo: 48px、leftCollapse: 40px、leftCollapse-shadow: 4px */
        height: calc(100% - 92px);
      }

      .el-scrollbar.mobile {
        height: 100%;
      }
    }

    .is-horizontal {
      display: none;
    }

    a {
      display: inline-block;
      display: flex;
      flex-wrap: wrap;
      width: 100%;
      padding-left: 10px;
    }

    .el-menu {
      height: 100%;
      background-color: transparent !important;
      border: none;
    }

    .el-menu-item,
    .el-sub-menu__title {
      height: 50px;
      color: $menuText;
      background-color: transparent !important;

      &:hover {
        // sub一级菜单
      }

      div,
      span {
        height: 50px;
        line-height: 50px;
        font-size: 16px;
        // margin-left: 5px;
      }
    }

    .submenu-title-noDropdown,
    .el-sub-menu__title {
      font-weight: 800;
      color: #000 !important;

      &:hover {
        background-color: transparent;
      }
    }

    .is-active>.el-sub-menu__title,
    .is-active.submenu-title-noDropdown {
      color: $subMenuActiveText !important;

      i {
        color: $subMenuActiveText !important;
      }
    }

    .is-active {
      color: $subMenuActiveText !important;
      transition: color 0.3s;
    }

    .el-menu-item.is-active.nest-menu>* {
      z-index: 1;
      font-weight: 800;
    }

    .el-menu-item.is-active.nest-menu::before {
      position: absolute;
      inset: 0 8px;
      margin: 4px 0;
      clear: both;
      content: "";
      //background: var(--el-color-primary);
      background: #254f7a;
      opacity: 1;
      border-radius: 4px;
      :deep(.el--tooltip__trigger) {
          color: #fff;
      }
    }

    .el-menu-item.is-active.nest-menu > * {
      z-index: 1;
      color: #222;
    }
    .el-menu-item.is-active.nest-menu > div > span {
      z-index: 1;
      color: white;
    }

    .el-menu .el-menu--inline .el-sub-menu__title,
    & .el-sub-menu .el-menu-item {
      min-width: $sideBarWidth !important;
      font-size: 12px;
      color: #4f4f4f !important;
      // background-color: $subMenuBg !important;
    }

    /* 有子集的激活菜单左侧小竖条 */
    .el-menu--collapse .is-active.outer-most.el-sub-menu>.el-sub-menu__title::before {
      position: absolute;
      top: 0;
      left: 0;
      width: 2px;
      height: 100%;
      clear: both;
      content: "";
      background-color: $menuActiveBefore;
      transition: all var(--pure-transition-duration) ease-in-out;
      transform: translateY(0);
    }

    .el-menu--collapse .outer-most.el-sub-menu>.el-sub-menu__title::before {
      position: absolute;
      top: 50%;
      display: block;
      width: 3px;
      height: 0;
      content: "";
      transform: translateY(-50%);
    }

    /* 无子集的激活菜单背景 */
    .is-active.submenu-title-noDropdown.outer-most>* {
      z-index: 1;
      // color: var(--el-color-primary);
      // background: var(--el-color-primary) !important;
      color: #222;
      //color: #214494;
    }

    .is-active.submenu-title-noDropdown.outer-most > div > span {
      z-index: 1;
      color: white;
      font-weight: normal;
    }

    .is-active.submenu-title-noDropdown.outer-most::before {
      position: absolute;
      inset: 0 8px;
      margin: 4px 0;
      clear: both;
      content: "";
      opacity: 0.9;
      //background: var(--el-color-primary);
      background: #254f7a;
      border-radius: 3px;
      :deep(.sub-menu-icon) {
        color: white;
      }
    }
  }

  /* vertical 菜单折叠 */
  .el-menu--vertical {
    .el-menu--popup {
      background-color: $subMenuBg !important;

      .el-menu-item {
        span {
          font-size: 16px;
        }
      }
    }

    &>.el-menu {
      i {
        margin-right: 20px;
      }
    }

    .is-active>.el-sub-menu__title,
    .is-active.submenu-title-noDropdown {
      color: $subMenuActiveText !important;

      i {
        color: $subMenuActiveText !important;
      }
    }

    /* 子菜单中还有子菜单 */
    .el-menu .el-sub-menu__title {
      min-width: $sideBarWidth !important;
      font-size: 12px;
      // background-color: $subMenuBg !important;
    }

    .el-menu-item,
    .el-sub-menu__title {
      height: 50px;
      line-height: 50px;
      background-color: $subMenuBg;

      &:hover {
        color: $menuTitleHover !important;
      }
    }

    .is-active {
      color: $subMenuActiveText !important;
      transition: color 0.3s;
    }

    .el-menu-item.is-active.nest-menu>* {
      z-index: 1;
      // color: #fff;
    }

    .el-menu-item.is-active.nest-menu::before {
      position: absolute;
      inset: 0 8px;
      clear: both;
      content: "";
      // background: var(--el-color-primary) !important;
      border-radius: 3px;
    }

    .el-menu-item,
    .el-sub-menu {
      .iconfont {
        font-size: 18px;
      }

      .el-menu-tooltip__trigger {
        width: 48px;
        padding: 0;
      }
    }
  }

  /* horizontal 菜单 */
  .el-menu--horizontal {
    &>.el-sub-menu .el-sub-menu__icon-arrow {
      position: static !important;
      margin-top: 0;
    }

    .el-menu--popup {
      background-color: $subMenuBg !important;

      .el-menu-item {
        color: $menuText;
        background-color: $subMenuBg;

        span {
          font-size: 16px;
        }
      }

      .el-sub-menu__title {
        color: $menuText;
      }
    }

    /* 无子菜单时激活 border-bottom */
    .router-link-exact-active>.submenu-title-noDropdown {
      height: 60px;
      border-bottom: 2px solid var(--el-menu-active-color);
    }

    /* 子菜单中还有子菜单 */
    .el-menu .el-sub-menu__title {
      min-width: $sideBarWidth !important;
      font-size: 12px;
      // background-color: $subMenuBg !important;

      &:hover {
        color: $menuTitleHover !important;
      }
    }

    .is-active>.el-sub-menu__title,
    .is-active.submenu-title-noDropdown {
      color: $subMenuActiveText !important;

      i {
        color: $subMenuActiveText !important;
      }
    }

    //.nest-menu .el-sub-menu>.el-sub-menu__title,
    .el-menu-item.nest-menu {
      font-size: 16px;

      &:hover {
        color: $menuTitleHover !important;
        background-color: #254f7a !important;
        border-radius: 4px;
        //width: 192px;
        //height: 42px;
      }
    }

    .el-menu-item.is-active {
      color: $subMenuActiveText !important;
      transition: color 0.3s;
    }

    .el-menu-item.is-active.nest-menu>* {
      z-index: 1;
      // color: #fff;
    }

    .el-menu-item.is-active.nest-menu::before {
      position: absolute;
      inset: 0 5px;
      clear: both;
      content: "";
      // background: var(--el-color-primary) !important;
      border-radius: 3px;
    }
  }

  .horizontal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 64px;
    background: $menuBg;

    .horizontal-header-left {
      display: flex;
      align-items: center;
      width: auto;
      min-width: 200px;
      height: 100%;
      padding-left: 10px;
      cursor: pointer;
      transition: all var(--pure-transition-duration) ease;

      img {
        display: inline-block;
        height: 32px;
      }

      span {
        display: inline-block;
        height: 32px;
        margin: 2px 0 0 12px;
        overflow: hidden;
        font-size: 18px;
        font-weight: 600;
        line-height: 32px;
        color: $subMenuActiveText;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .horizontal-header-menu {
      flex: 1;
      align-items: center;
      min-width: 0;
      width: 100%;
      height: 100%;
    }

    .horizontal-header-right {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      margin-right: 10px;
      // flex: 1;
      // min-width: 340px;
      color: $subMenuActiveText;

      .dropdown-badge {
        height: 48px;
        color: $subMenuActiveText;
      }

      .el-dropdown-link {
        display: flex;
        align-items: center;
        justify-content: space-around;
        height: 48px;
        padding: 10px;
        // color: $subMenuActiveText;
        cursor: pointer;

        p {
          font-size: 14px;
        }

        img {
          width: 22px;
          height: 22px;
          border-radius: 50%;
        }
      }
    }

    .el-menu {
      width: 100% !important;
      height: 100%;
      background-color: transparent;
      border: none;
    }

    .el-menu-item,
    .el-sub-menu__title {
      padding-right: var(--el-menu-base-level-padding);
      color: $menuText;

      &:hover {
        color: $menuTitleHover !important;
      }
    }

    .submenu-title-noDropdown,
    .el-sub-menu__title {
      height: 48px;
      line-height: 48px;
      background: $menuBg;

      svg {
        position: static !important;
      }
    }

    .is-active>.el-sub-menu__title,
    .is-active.submenu-title-noDropdown {
      color: $subMenuActiveText !important;

      i {
        color: $subMenuActiveText !important;
      }
    }

    .is-active {
      color: $subMenuActiveText !important;
      transition: color 0.3s;
    }
  }

  .el-menu--collapse .el-menu .el-sub-menu {
    min-width: $sideBarWidth !important;
  }

  /* 手机端 */
  .mobile {
    .fixed-header {
      width: 100% !important;
      transition: width var(--pure-transition-duration);
    }

    .main-container {
      margin-left: 0 !important;
    }

    .sidebar-container {
      width: $sideBarWidth;
      transition: transform var(--pure-transition-duration);
    }

    &.hideSidebar {
      .sidebar-container {
        pointer-events: none;
        transition-duration: 0.3s;
        transform: translate3d(-$sideBarWidth, 0, 0);
      }
    }
  }
}

body[layout="vertical"] {
  $sideBarWidth: 210px;

  @include merge-style($sideBarWidth);

  .el-menu--collapse {
    width: 48px;
  }

  .sidebar-logo-container {
    background: $sidebarLogo;
  }

  .hideSidebar {
    .fixed-header {
      width: 100%;
      transition: width var(--pure-transition-duration);
    }

    .sidebar-container {
      width: 48px !important;
      transition: width var(--pure-transition-duration);

      .is-active.submenu-title-noDropdown.outer-most {
        .sub-menu-icon {
          color: #222222 !important;
        }
        background: transparent !important;
      }
    }

    .main-container {
      margin-left: 49px;
    }

    /* 菜单折叠 */
    .el-menu--collapse {
      .el-sub-menu {
        &>.el-sub-menu__title {
          &>span {
            width: 100%;
            height: 100%;
            text-align: center;
            visibility: visible;
          }
        }
      }

      .submenu-title-noDropdown {
        background: transparent !important;
      }

      .el-sub-menu__title {
        padding: 0;
      }
    }

    .sub-menu-icon {
      margin-right: 0;
    }
  }

  /* 搜索 */
  .search-container,
  /* 告警 */
  .dropdown-badge,
  /* 用户名 */
  .el-dropdown-link,
  /* 设置 */
  .set-icon {
    &:hover {
      background: #f6f6f6;
    }
  }
}

body[layout="horizontal"] {
  $sideBarWidth: 0;

  @include merge-style($sideBarWidth);

  .fixed-header,
  .main-container {
    transition: none !important;
  }

  .fixed-header {
    width: 100%;
  }
}

body[layout="mix"] {
  $sideBarWidth: 220px;

  @include merge-style($sideBarWidth);

  .el-menu--collapse {
    width: 48px;
  }

  .el-menu {
    --el-menu-hover-bg-color: transparent !important;
  }

  .hideSidebar {
    .fixed-header {
      width: 100%;
      transition: width var(--pure-transition-duration);
    }

    .sidebar-container {
      width: 48px !important;
      transition: width var(--pure-transition-duration);

      .is-active.submenu-title-noDropdown.outer-most {
        .sub-menu-icon {
          color: #222222 !important;
        }
        background: transparent !important;
      }
    }

    .main-container {
      margin-left: 49px;
    }

    /* 菜单折叠 */
    .el-menu--collapse {
      .el-sub-menu {
        &>.el-sub-menu__title {
          padding: 0;

          &>span {
            width: 100%;
            height: 100%;
            text-align: center;
            visibility: visible;
          }
        }
      }
    }
  }
}