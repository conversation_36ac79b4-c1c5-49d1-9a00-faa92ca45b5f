<template>
  <!-- 每个table表头的popover -->
  <!-- 注意：逻辑部分尽量不好写到这个组件内，因为这个组件是根据外面table循环创建的，在这里写逻辑会非常影响性能 -->
  <div class="customHeader" style="display: inline-block">
    <el-popover width="240" ref="popover" placement="top" :visible="visible">
      <div class="flex justify-end mb-[4px] cursor-pointer">
        <IconifyIconOnline
          @click="visible = false"
          icon="ep:circle-close"
          width="12px"
          height="12px"
        />
      </div>
      <!-- table表头文字显示-->
      <template #reference>
        <div
          style="width: 100%"
          class="flex items-center cursor-pointer flex-nowrap"
          @click="headerClick"
        >
          {{ column.label }} &nbsp;
          <IconifyIconOnline
            :style="filterForm.value ? 'color:blue' : ''"
            icon="ep:search"
          />
        </div>
      </template>
      <!-- text 文本 -->
      <div v-if="column.searchType == 'text'">
        <el-input
          style="width: 100%"
          v-model.trim="filterForm.value"
          clearable
          placeholder="请输入查询内容"
          @keyup.native.enter="confirm()"
        />
      </div>
      <!-- select 下拉选择-->
      <div v-else-if="column.searchType == 'select'">
        <el-select
          v-model="filterForm.value"
          placeholder="请选择"
          style="width: 100%"
          @change="confirm"
          clearable
        >
          <el-option
            v-for="(item, index) in filterOptions"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>
      <div v-else-if="column.searchType == 'treeSelect'">
        <el-tree-select
          style="width: 100%"
          default-expand-all
          v-model="filterForm.value"
          check-strictly
          :props="{ children: 'children', label: 'name', value: 'id' }"
          placeholder="请选择"
          :data="filterOptions"
           @change="confirm"
          :render-after-expand="false"
        />
      </div>
      <!-- number 数字框 -->
      <div v-else-if="column.searchType == 'number'">
        <el-input
          v-model="filterForm.value"
          clearable
          placeholder="请输入数字"
          @keyup.native.enter="confirm()"
        />
      </div>
      <!-- number_range 数字范围-->
      <div
        class="flex items-center"
        v-else-if="column.searchType == 'number_range'"
      >
        <el-input
          v-model.trim="filterForm.value"
          style="width: 120px"
          clearable
          placeholder="输入起始值"
        />
        <span class="mx-[4px]">-</span>
        <el-input
          v-model.trim="spareValue"
          style="width: 120px"
          clearable
          placeholder="输入结束值"
        />
      </div>
      <!-- date 单个日期-->
      <div v-else-if="column.searchType == 'date'">
        <el-date-picker
          style="width: 100%"
          v-model="filterForm.value"
          type="date"
          clearable
          placeholder="选择日期"
          @change="confirm"
          value-format="x"
        />
      </div>
      <!-- datetime 日期时间-->
      <div v-else-if="column.searchType == 'datetime'">
        <el-date-picker
          v-model="filterForm.value"
          type="datetime"
          placeholder="选择日期时间"
          value-format="x"
        />
      </div>
      <!-- date_range 日期范围-->
      <div v-else-if="column.searchType == 'date_range'">
        <el-date-picker
          style="width: 100%"
          v-model="filterForm.value"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="x"
        />
      </div>
      <!-- datetime_range 日期时分秒范围-->
      <div v-else-if="column.searchType == 'datetime_range'">
        <el-date-picker
          v-model="filterForm.value"
          clearable
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
        />
      </div>
      <!-- radio 单选-->
      <div v-else-if="column.searchType == 'radio'">
        <el-radio-group v-model="filterForm.value">
          <el-radio
            v-for="(item, index) in filterOptions"
            :key="index"
            :label="item.value"
            :value="item.value"
          >
            {{ item.label }}
          </el-radio>
        </el-radio-group>
      </div>
      <!-- checkBox 多选-->
      <div v-else-if="column.searchType == 'checkBox'">
        <el-checkbox-group v-model="checkboxList">
          <el-checkbox
            v-for="(item, index) in filterOptions"
            :key="index"
            :label="item.value"
            :value="item.value"
          >
            {{ item.label }}
          </el-checkbox>
        </el-checkbox-group>
      </div>

      <!-- confirm 确定框-->
      <div style="text-align: right">
        <el-button
          type="primary"
          size="small"
          class="confirm"
          plain
          @click="reset()"
        >
          重置
        </el-button>
        <el-button
          type="primary"
          size="small"
          class="confirm"
          @click="confirm()"
        >
          确定
        </el-button>
      </div>
    </el-popover>
  </div>
</template>

<script lang="ts" setup>
import { nextTick, ref, watch } from "vue";
import { ElMessage } from "element-plus";

defineOptions({
  name: "TableHeadPopover"
});
type FilterOptionType = {
  label: string;
  value: number | string;
};
// column 当前列数据，filterOptions 多选/单选/下拉/数据
// props: ['column', 'filterOptions', 'columnIndex'],
const emit = defineEmits(["tableUpdate"]);
const popover = ref();
const props = defineProps({
  columnIndex: {
    type: Number,
    default: 0
  },
  filterOptions: {
    type: Array<FilterOptionType>,
    default: () => []
  },
  column: {
    type: Object,
    default: () => ({})
  },
  timeSlot: {
    type: Number,
    default: 0
  }
});
// 监测父组件重置按钮点击事件
watch(
  () => props.timeSlot,
  () => {
    filterForm.value.value = "";
    filterForm.value.tagValue = undefined;
    spareValue.value = undefined;
  }
);
const filterForm = ref({
  tagLabel: props.column.label, // 筛选tag label(tag用)
  tagValue: undefined, // 筛选tag value(tag用)
  value: undefined, // 所筛选的数据(后端接收用)
  propKey: undefined,
  fieldName: props.column.name // 当前表头字段(后端接收用)
});
function handleDateChange(status) {
  if (!status) {
    confirm();
  }
}
function handleBlur() {
  visible.value = false;
}
const visible = ref<boolean>(false);
const spareValue = ref<string>("");
const checkboxList = ref([]);
// 解决点击表头和排序的冲突
function headerClick(e) {
  visible.value = true;
  e.cancelBubble = true;
}

function reset() {
  filterForm.value.value = "";
  filterForm.value.tagValue = undefined;
  spareValue.value = undefined;
  visible.value = false;
  // popover.value.hide();
  emit("tableUpdate", filterForm.value); // 传递的是对象
}
function confirm() {
  let minValue = filterForm.value.value; // 数值双向绑定  做个闭环赋值
  let type = props.column.searchType;
  filterForm.value.propKey = props.column.propKey
    ? props.column.propKey
    : props.column.prop;
  // 跟后端商定 , 多个值存在时进行判断 , 以filterForm.value一个值为字符串的形式传递
  // 根据需求做了处理
  // checkBox和radio和select由于value值的原因需要处理
  if (type === "checkBox" || type === "radio" || type === "select") {
    if (type === "checkBox") {
      filterForm.value.value = checkboxList.value.join(",");
    }
    if (props.column.param && props.column.param.length > 0) {
      let str = "";
      props.column.param.forEach((i, t) => {
        if (type === "checkBox" && i.value === Number(checkboxList.value[t])) {
          str = str + i.label;
        }
        if (type === "radio" && i.value === Number(filterForm.value.value)) {
          str = str + i.label;
        }
        if (type === "select" && i.value === Number(filterForm.value.value)) {
          str = str + i.label;
        }
      });
      filterForm.value.tagValue = str;
    }
  } else if (type === "number_range") {
    filterForm.value.tagValue = [filterForm.value.value, spareValue.value];
    // filterForm.value.value = [filterForm.value.value, spareValue.value];
  } else {
    filterForm.value.tagValue = filterForm.value.value; // 其他类型的赋值给tag用
  }
  visible.value = false;
  emit("tableUpdate", filterForm.value); // 传递的是对象
  filterForm.value.value = minValue; // 数值双向绑定  做个闭环赋值
  // popover.value.hide(); // 关闭popover
}
</script>

<style scoped>
.customHeader {
  position: relative;
}
.confirm {
  margin-top: 10px;
}
/* 禁止双击选中文字 */
.label {
  -moz-user-select: none; /*火狐*/
  -webkit-user-select: none !important; /*webkit浏览器*/
  -ms-user-select: none; /*IE10*/
  -khtml-user-select: none; /*早期浏览器*/
  user-select: none;
  cursor: pointer;
}
.labelColor {
  color: #409eff;
}

.el-checkbox-group {
  display: flex;
  justify-content: flex-start;
  flex-direction: column;
  max-height: 150px;
  overflow-y: auto;
}

.el-table th div {
  line-height: 12px;
  display: flex;
  justify-content: center;
  width: 100%;
}
</style>
