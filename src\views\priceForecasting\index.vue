<template>
  <section>
    <!-- 顶部选择框及按钮 -->
    <el-card class="jt-card">
      <div class="header">
        <div style="display: flex; align-items: center">
          <!--        <MyTab-->
          <!--          @change="timeTypeActiveChange"-->
          <!--          v-model="timeTypeActive"-->
          <!--          :tabs="['分时', '分日']"-->
          <!--        ></MyTab>-->
          <el-row align="middle">
            <el-form-item label="预测目标日:">
              <el-date-picker
                  @change="rangePickerChange"
                  style="width: 228px"
                  range-separator="至"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  v-model="dayDate"
                  type="daterange"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  @calendar-change="getStartTime"
                  :disabled-date="disableFutureDates"
              />
            </el-form-item>
            <el-form-item label="数据展示:" style="margin-left: 20px">
              <el-select
                  v-model="forecastOrRealtime"
                  @change="forecastOrRealtimeChange"
              >
                <el-option label="全部" :value="0"></el-option>
                <el-option label="日前实际" :value="1"></el-option>
                <!-- <el-option label="实时" :value="2"></el-option> -->
                <el-option label="D-2" :value="3"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="算法:" style="margin-left: 20px">
              <el-select v-model="algorithmType" @change="algorithmChange">
                <!--              <el-option label="算法A" :value="1"></el-option>-->
                <el-option
                    v-for="item in algorithmTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                ></el-option>
                <!--              <el-option label="全部" :value="0"></el-option>-->
              </el-select>
            </el-form-item>
            <el-form-item label="数据维度:" style="margin-left: 20px">
              <el-select
                  @change="timeParticleChange"
                  class="singleSelect"
                  v-model="timeParticle"
                  placeholder="请选择"
              >
                <el-option
                    v-for="item in timeParticleOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-button
                type="primary"
                style="margin-bottom: 20px; margin-left: 30px"
                size="default"
                @click="forecastButton"
                v-if="!forecastLoading"
            >
              预测
            </el-button>
            <el-button
                type="primary"
                style="margin-bottom: 20px; margin-left: 30px"
                v-else
                icon="ChromeFilled"
            >
              预测中...
            </el-button>
          </el-row>

          <!--        <el-date-picker-->
          <!--          @change="singlePickerChange"-->
          <!--          v-if="timeTypeActive === '分时'"-->
          <!--          format="YYYY-MM-DD"-->
          <!--          value-format="YYYY-MM-DD"-->
          <!--          v-model="timeDate"-->
          <!--          style="margin-left: 15px"-->
          <!--          :clearable="false"-->
          <!--        />-->
        </div>
      </div>
      <div class="header" style="margin-top: 20px">
        <div style="display: flex; align-items: center">
          <!--        <MyTab-->
          <!--          v-model="priceTypeActive"-->
          <!--          @change="priceTypeActiveChange"-->
          <!--          :tabs="['统一出清点电价', '节点电价']"-->
          <!--        ></MyTab>-->
          <!--        <el-select-->
          <!--          v-if="priceTypeActive === '节点电价'"-->
          <!--          @change="marketPriceChange"-->
          <!--          style="margin-left: 20px; width: 260px"-->
          <!--          class="singleSelect"-->
          <!--          v-model="marketPriceValue"-->
          <!--          placeholder="请选择节点"-->
          <!--        >-->
          <!--          <el-option-->
          <!--            v-for="item in marketPriceOptions"-->
          <!--            :key="item.value"-->
          <!--            :label="item.label"-->
          <!--            :value="item.value"-->
          <!--          />-->
          <!--        </el-select>-->
          <!--        <el-radio-group-->
          <!--          style="margin-left: 20px"-->
          <!--          v-model="forecastOrRealtime"-->
          <!--          @change="forecastOrRealtimeChange"-->
          <!--        >-->
          <!--          <el-radio label="1" size="large">日前</el-radio>-->
          <!--          <el-radio label="2" size="large">实时</el-radio>-->
          <!--        </el-radio-group>-->
        </div>
        <div>
          <!-- <el-button type="primary" icon="Refresh">重新预测</el-button>
										<el-button type="primary" icon="DocumentChecked">保存</el-button> -->
        </div>
      </div>
    </el-card>

    <!-- 价格预测结果及右边三个 -->
    <div v-if="timeTypeActive === ''">
      <div style="display: flex; margin-top: 20px">
        <el-card class="jt-card" style="width: 100%">
          <div class="header">
            <div class="header-title">
              <img src="@/assets/svg/title-arrow.svg" alt="" />
              价格预测结果
            </div>
            <!--          <MyTab-->
            <!--            v-model="forecastOutcomeActive"-->
            <!--            @change="forecastOutcomChange"-->
            <!--            :tabs="[-->
            <!--              '统调负荷',-->
            <!--              '火电竞价空间',-->
            <!--              '火电负荷率',-->
            <!--              '新能源负荷',-->
            <!--              '联络线计划',-->
            <!--              '非市场化出力',-->
            <!--              '阻塞频次',-->
            <!--            ]"-->
            <!--          ></MyTab>-->
          </div>
          <div class="header" style="margin-top: 20px">
            <div>
              {{ forecastOrRealtime == 1 ? '日前实际' : '实时' }}电价（预测）均价为：
              <span style="color: #4eca6b">{{ predAvg || '--' }}元/MWh</span>
              <span style="margin-left: 20px">
              {{
                  forecastOrRealtime == 1 ? '日前实际' : '实时'
                }}电价（实际）均价为：
            </span>
              <span style="color: #4eca6b">{{ realAvg || '--' }}元/MWh</span>
            </div>
          </div>

          <Echarts
              :echartsData="forecastOutcomeOption"
              EWidth="100%"
              EHeight="550px"
              echartId="forecastOutcome"
          ></Echarts>
        </el-card>
        <!-- <div style="width: 33%">
										<el-card class="jt-card" style="margin-bottom: 10px">
												<div class="header">
														<div class="header-title"><img src="@/assets/svg/title-arrow.svg" alt="">自相关时间序列模型</div>
														<div>
																历史7日准确率：
																<span style="color: #4eca6b">95.33%</span>
														</div>
												</div>
												<Echarts :echartsData="timeSeriesOption" EWidth="100%" EHeight="155px" echartId="timeSeries"></Echarts>
										</el-card>
										<el-card class="jt-card" style="margin-bottom: 10px">
												<div class="header">
														<div class="header-title"><img src="@/assets/svg/title-arrow.svg" alt="">三段拟合法价格模型</div>
														<div>
																历史7日准确率：
																<span style="color: #4eca6b">95.33%</span>
														</div>
												</div>
												<Echarts :echartsData="legalPriceOption" EWidth="100%" EHeight="155px" echartId="legalPrice"></Echarts>
										</el-card>
										<el-card class="jt-card">
												<div class="header">
														<div class="header-title"><img src="@/assets/svg/title-arrow.svg" alt="">随机森林模型</div>
														<div>
																历史7日准确率：
																<span style="color: #4eca6b">95.33%</span>
														</div>
												</div>
												<Echarts :echartsData="forestOption" EWidth="100%" EHeight="155px" echartId="forest"></Echarts>
										</el-card>
								</div> -->
      </div>

      <el-card class="jt-card" style="margin-top: 20px">
        <div class="header">
          <div class="header-title">
            <img src="@/assets/svg/title-arrow.svg" alt="" />
            预测信息
          </div>
          <div>
            <el-button type="info" @click="resetClick">
              <el-icon>
                <Refresh />
              </el-icon>
              <b>重置</b>
            </el-button>
            <el-button type="primary" @click="DalogVisible = true">
              导入数据
            </el-button>
          </div>
        </div>

        <el-dialog
            @close="dialogClose"
            v-model="DalogVisible"
            title="导入数据"
            width="50%"
        >
          <div>
            模板下载：
            <el-button type="primary" @click="downloadTemplateClick">
              下载模板
            </el-button>
          </div>
          <div style="margin-top: 20px">
            上传数据：
            <el-upload
                class="upload"
                action=""
                :http-request="UploadFunction"
                :on-change="uploadFile"
            >
              <el-button :icon="Upload" type="primary">点击上传</el-button>
            </el-upload>
          </div>
          <div style="margin-top: 20px">预览文件：</div>
          <div
              style="
            width: 100%;
            height: 250px;
            line-height: 250px;
            text-align: center;
          "
          >
            <el-table
                v-if="uploadTableData.length"
                :cell-style="columnbackgroundStyle"
                class="table"
                :header-cell-style="{
              borderColor: '#DCDFE6',
              color: '#1D2129',
              backgroundColor: '#F2F3F5',
            }"
                :data="uploadTableData"
                border
                style="width: 100%; margin-top: 20px"
                height="250"
            >
              <el-table-column
                  :key="value"
                  v-for="(value, key) in uploadBackData"
                  :label="key"
                  :prop="key"
              ></el-table-column>
            </el-table>
            <span v-else>暂无数据</span>
          </div>
          <template #footer></template>
        </el-dialog>
        <div style="height: 300px">
          <el-table
              stripe
              :cell-style="columnbackgroundStyle"
              class="table"
              :header-cell-style="{
            borderColor: '#DCDFE6',
            color: '#1D2129',
            backgroundColor: '#F2F3F5',
          }"
              :data="tableData"
              border
              style="width: 100%; height: 100%; margin-top: 20px"
          >
            <el-table-column prop="time" label="时间" width="180" />
            <el-table-column
                prop="pricePred"
                :label="`${forecastOrRealtime == 1 ? '日前实际' : '实时'}电价(预测)`"
                width="180"
            />
            <el-table-column
                prop="priceReal"
                :label="`${forecastOrRealtime == 1 ? '日前实际' : '实时'}电价(实际)`"
            />
            <el-table-column prop="threeDaysAvg" label="3日均价" />
            <el-table-column prop="sevenDaysAvg" label="7日均价" />
            <el-table-column prop="fifteenDaysAvg" label="15日均价" />
            <el-table-column
                prop="priceCorrection"
                :label="`${forecastOrRealtime == 1 ? '日前实际' : '实时'}电价(修正)`"
            >
              <template #default="scope">
                <el-input
                    @input="
                  (v: any) =>
                    (scope.row.priceCorrection = v.replace(
                      /^([0-9-]\d*\.?\d{0,2})?.*$/,
                      '$1',
                    ))
                "
                    @change="tableChange(scope.row)"
                    v-model="scope.row.priceCorrection"
                ></el-input>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>

    <div>
      <el-card class="jt-card" style="margin-top: 20px">
        <div class="header" style="width: 100%; display: block">
          <el-row justify="space-between">
            <el-col :span="22">
              <div class="header-title">
                <img src="@/assets/svg/title-arrow.svg" alt="" />
                价格预测
              </div>
            </el-col>
            <el-col :span="2">
              <div style="width: 100px">
                <el-button @click="exportMarketPrice" type="primary" :icon="Edit">
                  导出
                </el-button>
              </div>
            </el-col>
          </el-row>
          <div>
            <!--          <el-select-->
            <!--            @change="timeParticleChange"-->
            <!--            class="singleSelect"-->
            <!--            v-model="timeParticle"-->
            <!--            placeholder="请选择"-->
            <!--          >-->
            <!--            <el-option-->
            <!--              v-for="item in timeParticleOptions"-->
            <!--              :key="item.value"-->
            <!--              :label="item.label"-->
            <!--              :value="item.value"-->
            <!--            />-->
            <!--          </el-select>-->
          </div>
        </div>
        <Echarts
            ref="Echarts1"
            :echartsData="priceForecastingOption"
            EWidth="100%"
            EHeight="300px"
            echartId="priceForecasting"
        ></Echarts>
      </el-card>
      <el-card class="jt-card" style="margin-top: 20px">
        <div class="header">
          <div class="header-title">
            <img src="@/assets/svg/title-arrow.svg" alt="" />
            偏差
          </div>
        </div>
        <div>
          <el-select
              v-model="select1"
              @change="selectChange"
              placeholder="请选择"
              disabled
          >
            <el-option label="日前实际电价" :value="1"></el-option>
            <!-- <el-option label="实时电价" :value="2"></el-option> -->
          </el-select>
          VS
          <el-select
              v-model="select2"
              @change="selectChange"
              placeholder="请选择"
              disabled
          >
            <el-option
                v-for="(item, index) in algorithmTypeComOptions"
                :label="item.label"
                :value="item.value"
                :key="index"
            ></el-option>
          </el-select>
          准确率: {{ precision }}%
          <el-form-item></el-form-item>
        </div>
        <Echarts
            ref="Echarts2"
            :echartsData="offsetOption"
            EWidth="100%"
            EHeight="300px"
            echartId="offset"
        ></Echarts>
      </el-card>
      <el-card class="jt-card" style="margin-top: 20px">
        <el-form-item label="日期选择">
          <el-date-picker
              @change="searchDateChange"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              v-model="searchDate"
              style="margin-left: 15px"
              :clearable="false"
              :disabled-date="disabledDateRange"
          />
        </el-form-item>
        <div style="height: 200px">
          <el-table
              stripe
              :cell-style="columnbackgroundStyle"
              class="table"
              :header-cell-style="{
            borderColor: '#DCDFE6',
            color: '#1D2129',
            backgroundColor: '#F2F3F5',
          }"
              :data="tableData"
              border
              style="width: 100%; height: 100%; margin-top: 20px"
          >
            <el-table-column prop="date" label="日期" width="180" />
            <el-table-column prop="type" label="类型" />
            <el-table-column prop="dayPrice" :label="tab1" />
            <el-table-column prop="dayPriceA" :label="tab2" />
            <el-table-column prop="deviationValue" label="偏差值" />
          </el-table>
        </div>
        <div style="height: 300px">
          <el-table
              stripe
              :cell-style="columnbackgroundStyle"
              class="table"
              :header-cell-style="{
            borderColor: '#DCDFE6',
            color: '#1D2129',
            backgroundColor: '#F2F3F5',
          }"
              :data="tableData1"
              border
              style="width: 100%; height: 100%; margin-top: 20px"
          >
            <el-table-column prop="date" label="日期" width="180">
              <template #default="{ row }">
                {{ dayjs(row.date).format('YYYY-MM-DD') }}
              </template>
            </el-table-column>
            <el-table-column prop="time" label="时段">
              <template #default="{ row }">
                {{ dayjs(row.date).format('HH:mm') }}
              </template>
            </el-table-column>
            <el-table-column prop="dayPrice" :label="tab1" />
            <el-table-column prop="dayPriceA" :label="tab2" />
            <el-table-column prop="deviationValue" label="偏差值" />
          </el-table>
        </div>
      </el-card>
    </div>
  </section>
</template>

<script setup lang="ts">
import * as echarts from 'echarts'
import dayjs from 'dayjs' // 引入dayjs
import { nextTick, onMounted, ref } from 'vue'
import {
  dailyData,
  downloadTemplate,
  exportMarketPriceAPI,
  forecastButtonAPI,
  hourData,
  hourPrice,
  nodeList,
  resetCorrectionData,
  updateCorrectionData,
  uploadTemplate,
} from '@/api/priceForecasting/index'
import {Edit, Upload} from '@element-plus/icons-vue'
import { ElLoading, ElMessage } from 'element-plus'

// 统一出清点电价导出
const exportMarketPrice = () => {
  const loading = ElLoading.service({ text: '正在下载...' })
  exportMarketPriceAPI(dayDate.value[0])
      .then((data) => {
        const blob = new Blob([data])
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `实际电价导出表${dayDate.value[0]}.xlsx`
        link.click()
        window.URL.revokeObjectURL(url)
        loading.close()
      })
      .catch(() => {
        ElMessage.error('下载失败')
        loading.close()
      })
}

const forecastLoading = ref(false)

const forecastButton = () => {
  forecastLoading.value = true
  const data = {
    start_date: dayDate.value[0],
    end_date: dayDate.value[1],
    term: forecastOrRealtime.value == '1' ? '1' : forecastOrRealtime.value == '3' ? '3' : forecastOrRealtime.value == '0' ? null : '',
    algorithm_name: algorithmType.value == 'xgboost' ? 'xgboost' : algorithmType.value == 'tcn' ? 'tcn' : null,
  }

  forecastButtonAPI(data).then((res) => {
    // ElMessage.success('预测完成')
    setTimeout(() => {
      getDailyData()
    }, 3000)
    forecastLoading.value = false
  }).catch(() => {
    forecastLoading.value = false
  })
}

const Echarts1 = ref<any>(null)
const Echarts2 = ref<any>(null)

// 关闭弹窗函数
function dialogClose() {
  uploadTableData.value = []
}

// 上传的表格数据
const uploadTableData = ref<any>([])

function timeTypeActiveChange() {
  nextTick(() => {
    if (timeTypeActive.value == '分时') {
      getHourPrice()
      getHourData()
    } else {
      getDailyData()
    }
  })
}

// 多选项
const typeValue = ref<any>([1, 2])
const typeOptions = ref<any>([
  {
    value: 1,
    label: '统调负荷',
  },
  {
    value: 2,
    label: '火电竞价空间',
  },
  {
    value: 3,
    label: '火电负荷率',
  },
  {
    value: 4,
    label: '新能源负荷',
  },
  {
    value: 5,
    label: '联络线计划',
  },
  {
    value: 6,
    label: '非市场化出力',
  },
  {
    value: 7,
    label: '阻塞频次',
  },
])
const typeChange = () => {
  console.log('多个类型', typeValue.value)
  getHourPrice()
  getHourData()
}

// 年月，时刻修改
function timeParticleChange() {
  getDailyData()
}

/**
 * 当算法发生变化时的处理函数
 * 此函数没有参数和返回值
 *
 * 该函数的主要作用是响应算法类型的变更，包括以下两个步骤：
 * 1. 调用 getDailyData 函数获取每日数据，该函数可能负责从服务器获取最新的数据信息
 * 2. 将 select2.value 设置为 algorithmType.value，以确保界面选择与当前算法类型保持一致
 */
const algorithmChange = () => {
  getDailyData()
  if (forecastOrRealtime.value == 1 && algorithmType.value != '') {
    select2.value = algorithmType.value == 'xgboost' ? 1 : algorithmType.value == 'tcn' ? 3 : 999
    selectChange()
  }
}


// 分日查询
async function getDailyData() {
  // 日期选择
  searchDate.value = <string>dayDate?.value[0]
  await searchDateChange()
  priceForecastingOption.value.xAxis.data = []
  offsetOption.value.xAxis.data = []
  // priceForecastingOption.value.series[0].data = []
  // priceForecastingOption.value.series[1].data = []
  priceForecastingOption.value.series = []
  offsetOption.value.series[0].data = []

  let algorithmsToUse = []
  if (algorithmType.value === '') {
    algorithmsToUse = algorithmTypeOptions.value.filter(
        (item) => item.label !== '全部',
    )
  } else {
    algorithmsToUse = []
  }

  console.log('算法', algorithmsToUse)

  if (forecastOrRealtime.value == 0) {
    priceForecastingOption.value.series = []
    // ;[1, 2, 3].forEach((item: any) => {
    ;[1, 3].forEach((item: any) => {
      dailyData({
        startDate: dayDate.value[0],
        endDate: dayDate.value[1],
        nodeId:
            priceTypeActive.value === '节点电价' ? marketPriceValue.value : '-1',
        timeType: item,
        // timeType: 3,
        algorithmName: algorithmType.value,
        unfold: timeParticle.value,
      }).then((res: any) => {
        priceForecastingOption.value.xAxis.data = res.dateList
        offsetOption.value.xAxis.data = res.dateList
        if (item == 3 || item == 2) {
          if (algorithmType.value !== '') {
            priceForecastingOption.value.series.push({
              name: `${item == 3 ? `D-2${algorithmType.value}` : '实时'}电价`,
              data: item == 3 ? res?.longTimePred : res?.priceReal,
              type: 'line',
              // itemStyle: {
              //   color: '#029CD4',
              // },
            })
          } else {
            algorithmsToUse.forEach((algorithm) => {
              // console.log(algorithm)
              dailyData({
                startDate: dayDate.value[0],
                endDate: dayDate.value[1],
                nodeId:
                    priceTypeActive.value === '节点电价'
                        ? marketPriceValue.value
                        : '-1',
                timeType: item,
                // timeType: 3,
                algorithmName: algorithm.value,
                unfold: timeParticle.value,
              }).then((res: any) => {
                // console.log('算法', algorithm.value, res)
                priceForecastingOption.value.series.push({
                  name: `${item == 3 ? `D-2` : '日前实际'}${
                      algorithm.label
                  }`,
                  data: res?.longTimePred,
                  type: 'line',
                  // itemStyle: {
                  //   color: '#0052D9',
                  // },
                })
              })
            })
          }
        } else {
          if (algorithmType.value === '') {
            console.log('全部')
            console.log(algorithmsToUse)
            algorithmsToUse.forEach((algorithm) => {
              // console.log(algorithm)
              dailyData({
                startDate: dayDate.value[0],
                endDate: dayDate.value[1],
                nodeId:
                    priceTypeActive.value === '节点电价'
                        ? marketPriceValue.value
                        : '-1',
                timeType: item,
                // timeType: 3,
                algorithmName: algorithm.value,
                unfold: timeParticle.value,
              }).then((res: any) => {
                console.log('算法', algorithm.value, res)
                priceForecastingOption.value.series.push({
                  name: `${item == 1 ? '日前实际' : 'D-2'}${algorithm.label}`,
                  data: item == 1 ? res?.pricePred : res?.longTimePred,
                  type: 'line',
                  // itemStyle: {
                  //   color: '#0052D9',
                  // },
                })
              })
            })
            priceForecastingOption.value.series.push({
              name: `${item == 1 ? '日前实际' : '实时'}电价`,
              data: res?.priceReal,
              type: 'line',
            })
          } else {
            priceForecastingOption.value.series.push(
                {
                  name: `${item == 1 ? '日前实际' : '实时'}${
                      algorithmType.value
                  }算法`,
                  data: res?.pricePred,
                  type: 'line',
                  // itemStyle: {
                  //   color: '#0052D9',
                  // },
                },
                {
                  name: `${item == 1 ? '日前实际' : '实时'}电价`,
                  data: res?.priceReal,
                  type: 'line',
                  // itemStyle: {
                  //   color: '#029CD4',
                  // },
                },
            )
          }
        }

        if (item == 1) {
          offsetOption.value.series[0].data = res.bias
          precision.value = (res.precision * 100).toFixed(2)
          // // 日前表格
          // const uniqueDates = Array.from(
          //   new Set(res.dateList.map((dateStr) => dateStr.split(' ')[0])),
          // )
        }
      })
    })
  } else {
    const res = await dailyData({
      startDate: dayDate.value[0],
      endDate: dayDate.value[1],
      nodeId:
          priceTypeActive.value === '节点电价' ? marketPriceValue.value : '-1',
      timeType: forecastOrRealtime.value,
      algorithmName: algorithmType.value,
      unfold: timeParticle.value,
    })
    priceForecastingOption.value.xAxis.data = res.dateList
    offsetOption.value.xAxis.data = res.dateList
    if (forecastOrRealtime.value == 3 || forecastOrRealtime.value == 2) {
      if (algorithmType.value !== '') {
        priceForecastingOption.value.series.push({
          name: `${forecastOrRealtime.value == 3 ? `D-2${algorithmType.value}算法` : '实时'}电价`,
          data: forecastOrRealtime.value == 3 ? res?.longTimePred : res.priceReal,
          type: 'line',
          // itemStyle: {
          //   color: '#029CD4',
          // },
        })
      } else {
        algorithmsToUse.forEach((algorithm) => {
          // console.log(algorithm)
          dailyData({
            startDate: dayDate.value[0],
            endDate: dayDate.value[1],
            nodeId:
                priceTypeActive.value === '节点电价'
                    ? marketPriceValue.value
                    : '-1',
            timeType: forecastOrRealtime.value,
            // timeType: 3,
            algorithmName: algorithm.value,
            unfold: timeParticle.value,
          }).then((res: any) => {
            // console.log('算法', algorithm.value, res)
            priceForecastingOption.value.series.push({
              name: `${forecastOrRealtime.value == 3 ? `D-2` : '实时'}${
                  algorithm.label
              }算法`,
              data: res?.longTimePred,
              type: 'line',
              // itemStyle: {
              //   color: '#0052D9',
              // },
            })
          })
        })
      }
    } else {
      if (algorithmType.value === '') {
        algorithmsToUse.forEach((algorithm) => {
          // console.log(algorithm)
          dailyData({
            startDate: dayDate.value[0],
            endDate: dayDate.value[1],
            nodeId:
                priceTypeActive.value === '节点电价'
                    ? marketPriceValue.value
                    : '-1',
            timeType: forecastOrRealtime.value,
            // timeType: 3,
            algorithmName: algorithm.value,
            unfold: timeParticle.value,
          }).then((res: any) => {
            // console.log('算法', algorithm.value, res)
            priceForecastingOption.value.series.push({
              name: `${forecastOrRealtime.value == 1 ? '日前实际' : '实时'}${
                  algorithm.label
              }算法`,
              data: res?.pricePred,
              type: 'line',
              // itemStyle: {
              //   color: '#0052D9',
              // },
            })
          })
        })
        priceForecastingOption.value.series.push({
          name: `${forecastOrRealtime.value == 1 ? '日前实际' : '实时'}电价`,
          data: res?.priceReal,
          type: 'line',
          // itemStyle: {
          //   color: '#029CD4',
          // },
        })
      } else {
        priceForecastingOption.value.series.push(
            {
              name: `${forecastOrRealtime.value == 1 ? '日前实际' : '实时'}${
                  algorithmType.value
              }算法`,
              data: res?.pricePred,
              type: 'line',
              // itemStyle: {
              //   color: '#0052D9',
              // },
            },
            {
              name: `${forecastOrRealtime.value == 1 ? '日前实际' : '实时'}电价`,
              data: res?.priceReal,
              type: 'line',
              // itemStyle: {
              //   color: '#029CD4',
              // },
            },
        )
      }
    }
    // priceForecastingOption.value.series[0].data = res?.pricePred
    // priceForecastingOption.value.series[1].data = res?.priceReal
    offsetOption.value.series[0].data = res.bias
    precision.value = (res.precision * 100).toFixed(2)
  }
}

// 文件上传
function UploadFunction() {}
const fileList = ref<any>([])
// 文件上传状态改变
function uploadFile(file: any) {
  const fileExtension = file.name.split('.').pop()
  const size = file.size / 1024 / 1024
  if (fileExtension !== 'xlsx' && fileExtension !== 'xls') {
    ElMessage.warning('只能上传excel文件')
    fileList.value = []
    return
  }
  if (size > 10) {
    ElMessage.warning('文件大小不得超过10M')
    fileList.value = []
    return
  }
  fileList.value = [file]
  handleUpload()
}

// 上传文件返回数据
let uploadBackData = ref(null)
// 上传函数
const handleUpload = () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('未选择文件')
  } else {
    const formData = new FormData()
    formData.append('file', fileList.value[0].raw)
    const data = {
      dateDate: dayDate.value[0],
      nodeId:
          priceTypeActive.value === '节点电价' ? marketPriceValue.value : '-1',
      timeType: forecastOrRealtime.value,
    }
    uploadTemplate(formData, data)
        .then((res: any) => {
          uploadBackData.value = res
          let keys = Object.keys(res)
          res[keys[0]].forEach((item1: any, index1: number) => {
            console.log(`output->item1`, item1)
            const obj: any = {}
            for (let item2 in res) {
              obj[item2] = res[item2][index1]
            }
            uploadTableData.value.push(obj)
          })

          // ElMessage.success('上传成功')
          getHourPrice()
        })
        .catch((e) => {
          console.log(e)
        })
  }
}

// 下载模板
function downloadTemplateClick() {
  const loading = ElLoading.service({ text: '正在下载...' })
  downloadTemplate()
      .then((data) => {
        const blob = new Blob([data])
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = '导入模板.xlsx'
        link.click()
        window.URL.revokeObjectURL(url)
        loading.close()
      })
      .catch(() => {
        ElMessage.error('下载失败')
        loading.close()
      })
}

// 日前实时切换
function forecastOrRealtimeChange() {
  nextTick(() => {
    // forecastOutcomeOption.value.series[0].name = `${
    //   forecastOrRealtime.value == 1 ? '日前' : '实时'
    // }电价(预测)`
    // forecastOutcomeOption.value.series[1].name = `${
    //   forecastOrRealtime.value == 1 ? '日前' : '实时'
    // }电价(实际)`
    // forecastOutcomeOption.value.series[2].name = `${
    //   forecastOrRealtime.value == 1 ? '日前' : '实时'
    // }电价(修正)`
    // priceForecastingOption.value.series[0].name = `${
    //   forecastOrRealtime.value == 1 ? '日前' : '实时'
    // }电价(预测)`
    // priceForecastingOption.value.series[1].name = `${
    //   forecastOrRealtime.value == 1 ? '日前' : '实时'
    // }电价(实际)`
    // getHourPrice()
    // getHourData()
    getDailyData()
  })
}
// 分时日期切换
function singlePickerChange() {
  nextTick(() => {
    getHourPrice()
    getHourData()
  })
}

onMounted(() => {
  rangePickerChange()
})
// 分时 分日日期切换判断
function rangePickerChange() {
  nextTick(() => {
    // console.log('分时日期切换', dayDate.value)
    // if (dayDate.value[0] == dayDate.value[1]) {
    //   console.log('分时', dayDate.value)
    //   timeTypeActive.value = '分时'
    //   getHourPrice()
    //   getHourData()
    // } else {
    //   console.log('分日')
    //   timeTypeActive.value = '分日'
    //   getDailyData()
    // }
    getDailyData()
  })
}

const startMinTime = ref()

const getStartTime = (time: any) => {
  // startMinTime.value = dayjs(time[0])
  startMinTime.value = new Date(time[0]).getTime()
}

// 定义 disabled-date 函数，限制一个月内的选择
const disableFutureDates = (time) => {
  // console.log('disabledDateRange', dayDate.value == null)
  if (dayDate.value == null) {
    dayDate.value = [
      dayjs().add(1, 'day').format('YYYY-MM-DD'),
      dayjs().add(1, 'day').format('YYYY-MM-DD'),
    ]
    // console.log(dayjs(time).format('YYYY-MM-DD'))
    startMinTime.value = null
    return false
  } else {
    if (startMinTime.value) {
      const day1 = 30 * 24 * 3600 * 1000
      let maxTime = startMinTime.value + day1
      let minTime = startMinTime.value - day1
      return time.getTime() > maxTime || time.getTime() < minTime
    }
  }
}

// 节点电价、统一出清点电价切换
function priceTypeActiveChange() {
  nextTick(() => {
    getHourPrice()
    getHourData()
    getDailyData()
  })
}

// 价格预测结果 预测与实际均价
const predAvg = ref(null)
const realAvg = ref(null)

// 导入数据
const DalogVisible = ref(false)

// 分时分日
const timeTypeActive = ref('分时')

// 分时默认日期
const timeDate = ref(dayjs().add(1, 'day').format('YYYY-MM-DD'))
// 分日默认日期
const dayDate: any = ref([
  dayjs().add(1, 'day').format('YYYY-MM-DD'),
  dayjs().add(1, 'day').format('YYYY-MM-DD'),
])
// 日期选择范围
const disabledDateRange = (time: any) => {
  // 将传入的time转换为dayjs对象（如果需要的话，因为time可能已经是Date对象）
  const dayjsTime = dayjs(time)

  // 将dayRange转换为dayjs对象，以便比较
  const startDate = dayjs(dayDate?.value[0])
  const endDate = dayjs(dayDate?.value[1])

  // 如果time不在范围内，则返回true（表示禁用）
  return dayjsTime.isBefore(startDate) || dayjsTime.isAfter(endDate)
}

// 节点电价、同一出清点电价
const priceTypeActive = ref('统一出清点电价')

const marketPriceOptions = ref<any>([])
const marketPriceValue = ref<any>(null)
function marketPriceChange() {
  nextTick(() => {
    getHourPrice()
    getHourData()
    getDailyData()
  })
}
// 获取节点
async function getNodelist() {
  // const res = await nodeList()
  // marketPriceOptions.value = res.map((item: any) => ({
  //   label: item.nodeName,
  //   value: item.nodeId,
  // }))
  // marketPriceValue.value = marketPriceOptions.value[0].value
}

// 获取分时电价查询(折线图)：
async function getHourPrice() {
  forecastOutcomeOption.value.xAxis.data = []
  tableData.value = []
  forecastOutcomeOption.value.series.forEach((item: any) => {
    item.data = []
  })
  // forecastOutcomeOption.value.series = []
  const res = await hourPrice({
    dataDate: dayDate.value[0],
    nodeId:
        priceTypeActive.value === '节点电价' ? marketPriceValue.value : '-1',
    timeType: forecastOrRealtime.value,
  })
  predAvg.value = res.predAvg
  realAvg.value = res.realAvg
  forecastOutcomeOption.value.xAxis.data = res.timeList
  forecastOutcomeOption.value.series[0].data = res?.pricePred
  forecastOutcomeOption.value.series[1].data = res?.priceReal
  forecastOutcomeOption.value.series[2].data = res?.priceCorrection
  forecastOutcomeOption.value.series[3].data = res?.threeDaysAvg
  forecastOutcomeOption.value.series[4].data = res?.sevenDaysAvg
  forecastOutcomeOption.value.series[5].data = res?.fifteenDaysAvg

  // typeValue.value.forEach((item: any) => {
  //   hourPrice({
  //     dataDate: dayDate.value[0],
  //     nodeId:
  //       priceTypeActive.value === '节点电价' ? marketPriceValue.value : '-1',
  //     timeType: item,
  //   }).then((res: any) => {
  //     forecastOutcomeOption.value.xAxis.data = res.timeList
  //     forecastOutcomeOption.value.series.push({
  //       name: `${
  //         forecastOrRealtime.value == '1'
  //           ? `${typeList[item]}日前`
  //           : `${typeList[item]}实时`
  //       }电价(预测)`,
  //       data: res.pricePred,
  //       type: 'line',
  //       smooth: true, //关键点，为true是不支持虚线，实线就用true
  //       showSymbol: false, // 不显示折线上的圆点
  //       symbol: 'circle', //设定为实心点
  //       symbolSize: 8, //设定实心点的大小
  //       itemStyle: {
  //         color: '#63CBE2',
  //       },
  //       lineStyle: {
  //         color: '#63CBE2',
  //         width: 2,
  //         type: 'solid', //'dotted'虚线 'solid'实线
  //       },
  //     })
  //     // 预测信息表格
  //     tableData.value.push(
  //       res.timeList.map((item: any, index: number) => {
  //         return {
  //           time: item,
  //           pricePred: res.pricePred[index],
  //           priceReal: res.priceReal[index],
  //           threeDaysAvg: res.threeDaysAvg[index],
  //           sevenDaysAvg: res.sevenDaysAvg[index],
  //           fifteenDaysAvg: res.fifteenDaysAvg[index],
  //           priceCorrection: res.priceCorrection[index],
  //         }
  //       }),
  //     )
  //   })
  // })

  // 预测信息表格
  tableData.value = res.timeList.map((item: any, index: number) => {
    return {
      time: item,
      pricePred: res.pricePred[index],
      priceReal: res.priceReal[index],
      threeDaysAvg: res.threeDaysAvg[index],
      sevenDaysAvg: res.sevenDaysAvg[index],
      fifteenDaysAvg: res.fifteenDaysAvg[index],
      priceCorrection: res.priceCorrection[index],
    }
  })
}
// 分时数据查询(蓝色折线图)：
async function getHourData() {
  forecastOutcomeOption.value.series[6].data = []
  forecastOutcomeOption.value.series[6].name = ''
  typeValue.value.forEach((item: any) => {
    hourData({
      date: dayDate.value[0],
      dataType: item,
      timeType: forecastOrRealtime.value,
    }).then((res: any) => {
      // forecastOutcomeOption.value.series[6].data = res.chosenDataList.map(
      //   (item: any) => (item * 100).toFixed(0),
      // )
      // if (typeList.hasOwnProperty(item)) {
      //   const value = typeList[item]
      //   console.log(value) // 输出: '统调负荷'
      // } else {
      //   console.log('无效的键')
      // }
      // console.log(typeList.value[item])
      forecastOutcomeOption.value.series.push({
        name: typeList[item],
        yAxisIndex: 1,
        data:
            typeList[item] == '火电负荷率'
                ? res.chosenDataList.map((item: any) => (item * 100).toFixed(0))
                : res.chosenDataList,
        type: 'line',
        smooth: true, //关键点，为true是不支持虚线，实线就用true
        showSymbol: false, // 不显示折线上的圆点
        symbol: 'circle', //设定为实心点
        symbolSize: 8, //设定实心点的大小
        itemStyle: {
          color: '#0052D9',
        },
        lineStyle: {
          color: '#0052D9',
          width: 2,
          type: 'solid', //'dotted'虚线 'solid'实线
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
            { offset: 1, color: 'rgba(161,195,245,1)' },
            { offset: 0, color: 'rgba(161,195,245,.1)' },
          ]),
        },
      })
    })
  })
  const res = await hourData({
    date: dayDate.value[0],
    dataType: tabList[forecastOutcomeActive.value],
    timeType: forecastOrRealtime.value,
  })
  if (forecastOutcomeActive.value === '火电负荷率') {
    forecastOutcomeOption.value.series[6].data = res.chosenDataList.map(
        (item: any) => (item * 100).toFixed(0),
    )
  } else {
    forecastOutcomeOption.value.series[6].data = res.chosenDataList
  }
  forecastOutcomeOption.value.series[6].name = forecastOutcomeActive.value
}

// 日前实时
const forecastOrRealtime = ref(1)

// 价格预测结果

const tabList: any = {
  统调负荷: 1,
  火电竞价空间: 2,
  火电负荷率: 3,
  新能源负荷: 4,
  联络线计划: 5,
  非市场化出力: 6,
  阻塞频次: 7,
}
const typeList: any = {
  1: '统调负荷',
  2: '火电竞价空间',
  3: '火电负荷率',
  4: '新能源负荷',
  5: '联络线计划',
  6: '非市场化出力',
  7: '阻塞频次',
}

const forecastOutcomeActive = ref('统调负荷')
function forecastOutcomChange() {
  nextTick(() => {
    if (forecastOutcomeActive.value === '统调负荷') {
      forecastOutcomeOption.value.yAxis[1].name = 'MW'
    }
    if (forecastOutcomeActive.value === '火电竞价空间') {
      forecastOutcomeOption.value.yAxis[1].name = 'MW'
    }
    if (forecastOutcomeActive.value === '火电负荷率') {
      forecastOutcomeOption.value.yAxis[1].name = '%'
    }
    if (forecastOutcomeActive.value === '新能源负荷') {
      forecastOutcomeOption.value.yAxis[1].name = 'MW'
    }
    if (forecastOutcomeActive.value === '联络线计划') {
      forecastOutcomeOption.value.yAxis[1].name = 'MW'
    }
    if (forecastOutcomeActive.value === '非市场化出力') {
      forecastOutcomeOption.value.yAxis[1].name = 'MW'
    }
    if (forecastOutcomeActive.value === '阻塞频次') {
      forecastOutcomeOption.value.yAxis[1].name = '次'
    }
    getHourData()
  })
}

const forecastOutcomeOption = ref({
  legend: {
    show: true,
    x: 'center',
    y: 'bottom',
    icon: 'rect',
    itemHeight: 5,
    itemWidth: 10,
    type: 'scroll',
  },
  confine: true,
  tooltip: {
    trigger: 'axis',
    backgroundColor: 'rgba(255,255,255,0.90)',
    borderRadius: '4px',
    boxShadow: ' 0px 2px 10px 0px rgba(0, 0, 0, 0.16)',
    formatter: function (params: any) {
      let context
      let content = params.map((item: any) => {
        if (item.seriesName === '火电负荷率') {
          return `${item.marker}<span>${
              item.seriesName
          }</span>:<span style="margin-left: 10px;float: right">${
              item.value ? item.value : '--'
          }%</span><br>`
        }
        if (item.seriesName === '阻塞频次') {
          return `${item.marker}<span>${
              item.seriesName
          }</span>:<span style="margin-left: 10px;float: right">${
              item.value ? item.value : '--'
          }次</span><br>`
        }
        if (
            item.seriesName === '统调负荷' ||
            item.seriesName === '火电竞价空间' ||
            item.seriesName === '新能源负荷' ||
            item.seriesName === '联络线计划' ||
            item.seriesName === '非市场化出力'
        ) {
          return `${item.marker}<span>${
              item.seriesName
          }</span>:<span style="margin-left: 10px;float: right">${
              item.value ? item.value : '--'
          }MW</span><br>`
        }
        return `${item.marker}<span>${
            item.seriesName
        }</span>:<span style="margin-left: 10px;float: right">${
            item.value ? item.value : '--'
        }元/MWh</span><br>`
      })
      let newContent = ''
      content.forEach((item: any) => {
        newContent = newContent + item
      })
      context = `<div>${params[0].name}</div><div>${newContent}</div>`
      return context
    },
  },
  grid: {
    top: '10%',
    left: '5%',
    right: '5%',
    bottom: '10%',
  },
  xAxis: {
    type: 'category',
    data: [],
    axisTick: {
      show: false, // 将此选项设置为 false 来隐藏 X 轴刻度
    },
  },
  yAxis: [
    {
      type: 'value',
      name: '元/MWh',
      nameTextStyle: {
        // y轴name的样式调整
        fontSize: 14,
      },
    },
    {
      type: 'value',
      name: 'MW',
      nameTextStyle: {
        // y轴name的样式调整
        fontSize: 14,
      },
    },
  ],
  series: [
    {
      name: `${forecastOrRealtime.value == 1 ? '日前实际' : '实时'}电价(预测)`,
      data: [],
      type: 'line',
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        color: '#63CBE2',
      },
      lineStyle: {
        color: '#63CBE2',
        width: 2,
        type: 'solid', //'dotted'虚线 'solid'实线
      },
    },
    {
      name: `${forecastOrRealtime.value == 1 ? '日前实际' : '实时'}电价(实际)`,
      data: [],
      type: 'line',
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        color: '#D54941',
      },
      lineStyle: {
        color: '#D54941',
        width: 2,
        type: 'solid', //'dotted'虚线 'solid'实线
      },
    },
    {
      name: `${forecastOrRealtime.value == 1 ? '日前实际' : '实时'}电价(修正)`,
      data: [],
      type: 'line',
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        color: '#9D51FF',
      },
      lineStyle: {
        color: '#9D51FF',
        width: 2,
        type: 'solid', //'dotted'虚线 'solid'实线
      },
    },
    {
      name: '3日均价',
      data: [],
      type: 'line',
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        color: '#FFD978',
      },
      lineStyle: {
        color: '#FFD978',
        width: 2,
        type: 'dashed', //'dotted'虚线 'solid'实线
      },
    },
    {
      name: '7日均价',
      data: [],
      type: 'line',
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        color: '#FF7D00',
      },
      lineStyle: {
        color: '#FF7D00',
        width: 2,
        type: 'dashed', //'dotted'虚线 'solid'实线
      },
    },
    {
      name: '15日均价',
      data: [],
      type: 'line',
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        color: '#00B42A',
      },
      lineStyle: {
        color: '#00B42A',
        width: 2,
        type: 'dashed', //'dotted'虚线 'solid'实线
      },
    },
    {
      name: forecastOutcomeActive.value,
      yAxisIndex: 1,
      data: [],
      type: 'line',
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        color: '#0052D9',
      },
      lineStyle: {
        color: '#0052D9',
        width: 2,
        type: 'solid', //'dotted'虚线 'solid'实线
      },
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
          { offset: 1, color: 'rgba(161,195,245,1)' },
          { offset: 0, color: 'rgba(161,195,245,.1)' },
        ]),
      },
    },
  ],
})

// // 自相关时间序列模型
// const timeSeriesOption = ref({
//   confine: true,
//   tooltip: {
//     trigger: 'axis',
//     backgroundColor: 'rgba(255,255,255,0.90)',
//     borderRadius: '4px',
//     boxShadow: ' 0px 2px 10px 0px rgba(0, 0, 0, 0.16)',
//     formatter: function (params: any) {
//       let context
//       let content = params.map((item: any) => {
//         return `${item.marker}<span>${item.seriesName
//           }</span>:<span style="margin-left: 10px;float: right">${item.value || '--'
//           }%</span><br>`
//       })
//       let newContent = ''
//       content.forEach((item: any) => {
//         newContent = newContent + item
//       })
//       context = `<div>${params[0].name}</div><div>${newContent}</div>`
//       return context
//     },
//   },
//   grid: {
//     top: '10%',
//     left: '8%',
//     right: '2%',
//     bottom: '12%',
//   },
//   xAxis: {
//     type: 'category',
//     data: [],
//     axisTick: {
//       show: false, // 将此选项设置为 false 来隐藏 X 轴刻度
//     },
//   },
//   yAxis: {
//     type: 'value',
//     axisLabel: {
//       formatter: function (value: any) {
//         // 在这里编写自定义的标签格式化逻辑
//         return value + '%' // 保留两位小数
//       },
//     },
//   },
//   series: [
//     {
//       name: `自相关时间序列模型`,
//       data: [],
//       type: 'line',
//       smooth: true, //关键点，为true是不支持虚线，实线就用true
//       showSymbol: false, // 不显示折线上的圆点
//       symbol: 'circle', //设定为实心点
//       symbolSize: 8, //设定实心点的大小
//       itemStyle: {
//         color: '#039CD4',
//       },
//       lineStyle: {
//         color: '#039CD4',
//         width: 2,
//         type: 'solid', //'dotted'虚线 'solid'实线
//       },
//     },
//   ],
// })
// // 三段拟合法价格模型
// const legalPriceOption = ref({
//   confine: true,
//   tooltip: {
//     trigger: 'axis',
//     backgroundColor: 'rgba(255,255,255,0.90)',
//     borderRadius: '4px',
//     boxShadow: ' 0px 2px 10px 0px rgba(0, 0, 0, 0.16)',
//     formatter: function (params: any) {
//       let context
//       let content = params.map((item: any) => {
//         return `${item.marker}<span>${item.seriesName
//           }</span>:<span style="margin-left: 10px;float: right">${item.value || '--'
//           }%</span><br>`
//       })
//       let newContent = ''
//       content.forEach((item: any) => {
//         newContent = newContent + item
//       })
//       context = `<div>${params[0].name}</div><div>${newContent}</div>`
//       return context
//     },
//   },
//   grid: {
//     top: '10%',
//     left: '8%',
//     right: '2%',
//     bottom: '12%',
//   },
//   xAxis: {
//     type: 'category',
//     data: [],
//     axisTick: {
//       show: false, // 将此选项设置为 false 来隐藏 X 轴刻度
//     },
//   },
//   yAxis: {
//     type: 'value',
//     axisLabel: {
//       formatter: function (value: any) {
//         // 在这里编写自定义的标签格式化逻辑
//         return value + '%' // 保留两位小数
//       },
//     },
//   },
//   series: [
//     {
//       name: `三段拟合法价格模型`,
//       data: [],
//       type: 'line',
//       smooth: true, //关键点，为true是不支持虚线，实线就用true
//       showSymbol: false, // 不显示折线上的圆点
//       symbol: 'circle', //设定为实心点
//       symbolSize: 8, //设定实心点的大小
//       itemStyle: {
//         color: '#039CD4',
//       },
//       lineStyle: {
//         color: '#039CD4',
//         width: 2,
//         type: 'solid', //'dotted'虚线 'solid'实线
//       },
//     },
//   ],
// })
// // 随机森林模型
// const forestOption = ref({
//   confine: true,
//   tooltip: {
//     trigger: 'axis',
//     backgroundColor: 'rgba(255,255,255,0.90)',
//     borderRadius: '4px',
//     boxShadow: ' 0px 2px 10px 0px rgba(0, 0, 0, 0.16)',
//     formatter: function (params: any) {
//       let context
//       let content = params.map((item: any) => {
//         return `${item.marker}<span>${item.seriesName
//           }</span>:<span style="margin-left: 10px;float: right">${item.value || '--'
//           }%</span><br>`
//       })
//       let newContent = ''
//       content.forEach((item: any) => {
//         newContent = newContent + item
//       })
//       context = `<div>${params[0].name}</div><div>${newContent}</div>`
//       return context
//     },
//   },
//   grid: {
//     top: '10%',
//     left: '8%',
//     right: '2%',
//     bottom: '12%',
//   },
//   xAxis: {
//     type: 'category',
//     data: [],
//     axisTick: {
//       show: false, // 将此选项设置为 false 来隐藏 X 轴刻度
//     },
//   },
//   yAxis: {
//     type: 'value',
//     axisLabel: {
//       formatter: function (value: any) {
//         // 在这里编写自定义的标签格式化逻辑
//         return value + '%' // 保留两位小数
//       },
//     },
//   },
//   series: [
//     {
//       name: `随机森林模型`,
//       data: [],
//       type: 'line',
//       smooth: true, //关键点，为true是不支持虚线，实线就用true
//       showSymbol: false, // 不显示折线上的圆点
//       symbol: 'circle', //设定为实心点
//       symbolSize: 8, //设定实心点的大小
//       itemStyle: {
//         color: '#039CD4',
//       },
//       lineStyle: {
//         color: '#039CD4',
//         width: 2,
//         type: 'solid', //'dotted'虚线 'solid'实线
//       },
//     },
//   ],
// })

// 预测信息
const tableData: any = ref([])
const tableData1: any = ref([])
function columnbackgroundStyle(obj: any) {
  if (obj.columnIndex == 6) {
    return { 'background-color': '#F6F6F6' }
  }
}

// 算法
const algorithmType = ref('xgboost')
const algorithmTypeOptions = ref([
  {
    label: '全部',
    value: '',
  },
  {
    label: 'xgboost算法',
    value: 'xgboost',
  },
  {
    label: 'tcn算法',
    value: 'tcn',
  },
])

const algorithmTypeComOptions = ref([
  {
    label: '日前xgboost算法',
    value: 1,
  },
  // {
  //   label: '实时xgboost算法',
  //   value: 2,
  // },
  {
    label: '日前tcn算法',
    value: 3,
  },
  // {
  //   label: '实时tcn算法',
  //   value: 4,
  // },
])

// 偏差比较
const select1 = ref(1)
const select2 = ref(1)
const tab1 = ref('日前实际电价')
const tab2 = ref(algorithmTypeComOptions.value[0].label)
const selectChange = async () => {
  offsetOption.value.xAxis.data = []
  offsetOption.value.series[0].data = []
  if (select1.value == 1 && (select2.value == 1 || select2.value == 3)) {
    // console.log(select1, select2)
    tab1.value = '日前实际电价'
    algorithmTypeComOptions.value.forEach((item) => {
      if (item.value == select2.value) {
        tab2.value = item.label
      }
    })
    realFlag.value = false
    const res = await dailyData({
      startDate: dayDate.value[0],
      endDate: dayDate.value[1],
      nodeId:
          priceTypeActive.value === '节点电价' ? marketPriceValue.value : '-1',
      timeType: 1,
      // timeType: 3,
      algorithmName: tab2.value.replace(/[^a-zA-Z]/g, ''),
      unfold: timeParticle.value,
    })
    // console.log(res)
    offsetOption.value.xAxis.data = res.dateList
    offsetOption.value.series[0].data = res.bias
    precision.value = (res.precision * 100).toFixed(2)
  } else if (select1.value == 2 && (select2.value == 2 || select2.value == 4)) {
    tab1.value = '实时电价'
    algorithmTypeComOptions.value.forEach((item) => {
      if (item.value == select2.value) {
        tab2.value = item.label
      }
    })
    realFlag.value = true
    const res = await dailyData({
      startDate: dayDate.value[0],
      endDate: dayDate.value[1],
      nodeId:
          priceTypeActive.value === '节点电价' ? marketPriceValue.value : '-1',
      timeType: 2,
      // timeType: 3,
      unfold: timeParticle.value,
      algorithmName: tab2.value.replace(/[^a-zA-Z]/g, ''),
    })
    // console.log(res)
    offsetOption.value.xAxis.data = res.dateList
    offsetOption.value.series[0].data = res.bias
    precision.value = (res.precision * 100).toFixed(2)
  }
  await searchDateChange()
}

// 准确率
const precision = ref('--')
// 表格日期时间
const searchDate = ref(dayDate.value[0])
const realFlag = ref(false)
const searchDateChange = async () => {
  // console.log('表格时间限制', searchDate.value)
  if (forecastOrRealtime.value == 0) {
    await dailyData({
      startDate: searchDate.value,
      endDate: searchDate.value,
      nodeId:
          priceTypeActive.value === '节点电价' ? marketPriceValue.value : '-1',
      timeType: 1,
      // timeType: 3,
      unfold: timeParticle.value,
      algorithmName: tab2.value.replace(/[^a-zA-Z]/g, ''),
    }).then((res: any) => {
      tableData1.value = []
      // console.log(res)
      res?.dateList?.forEach((item: any, index: any) => {
        // console.log(res, res.pricePred, res.bias)
        tableData1.value.push({
          date: item,
          dayPrice: res.priceReal[index],
          dayPriceA: res.pricePred[index],
          deviationValue: res.bias[index],
        })
      })
      // console.log(tableData1.value)
    })
    // 按天查询取出最小值最大值平均值
    await dailyData({
      startDate: searchDate.value,
      endDate: searchDate.value,
      nodeId:
          priceTypeActive.value === '节点电价' ? marketPriceValue.value : '-1',
      timeType: 1,
      // timeType: 3,
      algorithmName: tab2.value.replace(/[^a-zA-Z]/g, ''),
      unfold: 2,
    }).then((res: any) => {
      tableData.value = []
      tableData.value = [
        {
          date: searchDate.value,
          type: '最大值',
          dayPrice: res.priceRealMax,
          dayPriceA: res.pricePredMax,
          deviationValue: (res.priceRealMax - res.pricePredMax).toFixed(2),
        },
        {
          date: searchDate.value,
          type: '平均值',
          dayPrice: res.priceReal,
          dayPriceA: res.pricePred,
          deviationValue: res.bias,
        },
        {
          date: searchDate.value,
          type: '最小值',
          dayPrice: res.priceRealMin,
          dayPriceA: res.pricePredMin,
          deviationValue: (res.priceRealMin - res.pricePredMin).toFixed(2),
        },
      ]
    })
  } else if (realFlag.value == false) {
    await dailyData({
      startDate: searchDate.value,
      endDate: searchDate.value,
      nodeId:
          priceTypeActive.value === '节点电价' ? marketPriceValue.value : '-1',
      timeType: 1, // 日前
      algorithmName: tab2.value.replace(/[^a-zA-Z]/g, ''),
      unfold: timeParticle.value,
    }).then(async (res: any) => {
      tableData1.value = []
      // console.log(res)
      res?.dateList?.forEach((item: any, index: any) => {
        // console.log(res, res.pricePred, res.bias)
        tableData1.value.push({
          date: item,
          dayPrice: res.priceReal[index],
          dayPriceA: res.pricePred[index],
          deviationValue: res.bias[index],
        })
      })
      // console.log(tableData1.value)
    })
    // 按天查询取出最小值最大值平均值
    await dailyData({
      startDate: searchDate.value,
      endDate: searchDate.value,
      nodeId:
          priceTypeActive.value === '节点电价' ? marketPriceValue.value : '-1',
      timeType: 1,
      // timeType: 3,
      algorithmName: tab2.value.replace(/[^a-zA-Z]/g, ''),
      unfold: 2,
    }).then((res: any) => {
      tableData.value = []
      tableData.value = [
        {
          date: searchDate.value,
          type: '最大值',
          dayPrice: res.priceRealMax,
          dayPriceA: res.pricePredMax,
          deviationValue: (res.priceRealMax - res.pricePredMax).toFixed(2),
        },
        {
          date: searchDate.value,
          type: '平均值',
          dayPrice: res.priceReal,
          dayPriceA: res.pricePred,
          deviationValue: res.bias,
        },
        {
          date: searchDate.value,
          type: '最小值',
          dayPrice: res.priceRealMin,
          dayPriceA: res.pricePredMin,
          deviationValue: (res.priceRealMin - res.pricePredMin).toFixed(2),
        },
      ]
    })
  } else if (realFlag.value == true) {
    await dailyData({
      startDate: searchDate.value,
      endDate: searchDate.value,
      nodeId:
          priceTypeActive.value === '节点电价' ? marketPriceValue.value : '-1',
      timeType: 2, // 实时
      algorithmName: tab2.value.replace(/[^a-zA-Z]/g, ''),
      unfold: timeParticle.value,
    }).then((res: any) => {
      tableData1.value = []
      // console.log(res)
      res?.dateList?.forEach((item: any, index: any) => {
        // console.log(res, res.pricePred, res.bias)
        tableData1.value.push({
          date: item,
          dayPrice: res.priceReal[index],
          dayPriceA: res.pricePred[index],
          deviationValue: res.bias[index],
        })
      })
      // console.log(tableData1.value)
    })
    // 按天查询取出最小值最大值平均值
    await dailyData({
      startDate: searchDate.value,
      endDate: searchDate.value,
      nodeId:
          priceTypeActive.value === '节点电价' ? marketPriceValue.value : '-1',
      timeType: 1,
      // timeType: 3,
      algorithmName: tab2.value.replace(/[^a-zA-Z]/g, ''),
      unfold: 2,
    }).then((res: any) => {
      tableData.value = []
      tableData.value = [
        {
          date: searchDate.value,
          type: '最大值',
          dayPrice: res.priceRealMax,
          dayPriceA: res.pricePredMax,
          deviationValue: (res.priceRealMax - res.pricePredMax).toFixed(2),
        },
        {
          date: searchDate.value,
          type: '平均值',
          dayPrice: res.priceReal,
          dayPriceA: res.pricePred,
          deviationValue: res.bias,
        },
        {
          date: searchDate.value,
          type: '最小值',
          dayPrice: res.priceRealMin,
          dayPriceA: res.pricePredMin,
          deviationValue: (res.priceRealMin - res.pricePredMin).toFixed(2),
        },
      ]
    })
  }
}
// 长周期价格预测
const timeParticle = ref<any>(3)
const timeParticleOptions = [
  {
    value: 1,
    label: '24点',
  },
  {
    value: 2,
    label: '分日',
  },
  {
    value: 3,
    label: '96点',
  },
]
const priceForecastingOption: any = ref({
  legend: {
    show: true,
    x: 'center',
    y: 'bottom',
    icon: 'rect',
    itemHeight: 5,
    itemWidth: 10,
    type: 'scroll',
  },
  confine: true,
  tooltip: {
    trigger: 'axis',
    backgroundColor: 'rgba(255,255,255,0.90)',
    borderRadius: '4px',
    boxShadow: ' 0px 2px 10px 0px rgba(0, 0, 0, 0.16)',
    formatter: function (params: any) {
      let context
      let content = params.map((item: any) => {
        return `${item.marker}<span>${
            item.seriesName
        }</span>:<span style="margin-left: 10px;float: right">${
            item.value || '--'
        }元/MWh</span><br>`
      })
      let newContent = ''
      content.forEach((item: any) => {
        newContent = newContent + item
      })
      context = `<div>${params[0].name}</div><div>${newContent}</div>`
      return context
    },
  },
  grid: {
    top: '10%',
    left: '5%',
    right: '2%',
    bottom: '15%',
  },
  xAxis: {
    type: 'category',
    data: [],
    axisTick: {
      show: false, // 将此选项设置为 false 来隐藏 X 轴刻度
    },
  },
  yAxis: {
    type: 'value',
    name: '元/MWh',
    nameTextStyle: {
      // y轴name的样式调整
      fontSize: 14,
    },
  },
  series: [
    {
      name: `${forecastOrRealtime.value == 1 ? '日前实际' : '实时'}电价(预测)`,
      data: [],
      type: 'line',
      itemStyle: {
        color: '#0052D9',
      },
    },
    {
      name: `${forecastOrRealtime.value == 1 ? '日前实际' : '实时'}电价(实际)`,
      data: [],
      type: 'line',
      itemStyle: {
        color: '#029CD4',
      },
    },
  ],
})

// 偏差
const offsetOption = ref({
  legend: {
    show: true,
    x: 'center',
    y: 'bottom',
    icon: 'rect',
    itemHeight: 5,
    itemWidth: 10,
    type: 'scroll',
  },
  confine: true,
  tooltip: {
    trigger: 'axis',
    backgroundColor: 'rgba(255,255,255,0.90)',
    borderRadius: '4px',
    boxShadow: ' 0px 2px 10px 0px rgba(0, 0, 0, 0.16)',
    formatter: function (params: any) {
      let context
      let content = params.map((item: any) => {
        return `${item.marker}<span>${
            item.seriesName
        }</span>:<span style="margin-left: 10px;float: right">${
            item.value || '--'
        }元/MWh</span><br>`
      })
      let newContent = ''
      content.forEach((item: any) => {
        newContent = newContent + item
      })
      context = `<div>${params[0].name}</div><div>${newContent}</div>`
      return context
    },
  },
  grid: {
    top: '10%',
    left: '5%',
    right: '2%',
    bottom: '15%',
  },
  xAxis: {
    type: 'category',
    data: [],
    axisTick: {
      show: false, // 将此选项设置为 false 来隐藏 X 轴刻度
    },
  },
  yAxis: {
    type: 'value',
    name: '元/MWh',
    nameTextStyle: {
      // y轴name的样式调整
      fontSize: 14,
    },
  },
  series: [
    {
      name: `偏差`,
      data: [],
      type: 'bar',
      itemStyle: {
        color: '#E37318',
      },
    },
  ],
})

async function getData() {
  // 获取节点下拉框
  await getNodelist()
  // 获取分时电价查询(折线图)：
  // getHourPrice()
  // getHourData()
}
onMounted(async () => {
  getData()
})

// 预测信息->日前电价修正
async function tableChange(data: any) {
  await updateCorrectionData({
    dateTime: data.time,
    dateDate: dayDate.value[0],
    nodeId:
        priceTypeActive.value === '节点电价' ? marketPriceValue.value : '-1',
    timeType: forecastOrRealtime.value,
    value: data.priceCorrection,
  })
  getHourPrice()
  getHourData()
}

// 重置修正电价
async function resetClick() {
  await resetCorrectionData({
    dateDate: dayDate.value[0],
    nodeId:
        priceTypeActive.value === '节点电价' ? marketPriceValue.value : '-1',
    timeType: forecastOrRealtime.value,
  })
  getHourPrice()
  getHourData()
}
</script>

<style lang="scss" scoped>
:deep(.el-radio__input.is-checked .el-radio__inner) {
  border-color: var(--btn-color);
  background-color: var(--btn-color);
}

:deep(.el-radio__input.is-checked + .el-radio__label) {
  color: #606066;
}

:deep(.el-overlay-dialog) {
  .el-dialog {
    width: 900px;

    .el-dialog__header {
      border-bottom: 1px solid #e5e6eb;
      margin-right: 0;

      span {
        color: #1d2129;
        text-align: center;
        /* 文字/16加粗 */
        font-size: 16px;
        font-weight: 700;
        line-height: 22px;
        /* 137.5% */
      }
    }

    .el-dialog__body {
      padding: 20px;

      .el-button {
        border: 1px solid var(--btn-color);
        color: #fff;
      }

      .el-checkbox-group {
        display: inline-block;
      }
    }

    .el-dialog__footer {
      border-top: 1px solid #e5e6eb;
    }
  }
}

:deep(.el-upload-list.el-upload-list--text) {
  display: none !important;
}

.upload {
  display: inline-block;
}
</style>
