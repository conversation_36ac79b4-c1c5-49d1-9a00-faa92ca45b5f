import { http } from "@/utils/http";
import { baseUrlApi } from "../utils";
import {
  GetCityListResultModel,
  CityResultModel,
  CityPageModel,
  CityTreeListModel
} from "@/model/cityModel";
import {
  ResponseDetailModel,
  BasicResponseParams,
  BasicPageParams
} from "@/model/baseModel";
// 查询地区树
export const getCityTreeApi = () => {
  return http.request<ResponseDetailModel<CityTreeListModel[]>>(
    "get",
    baseUrlApi(`system/area/getTree`)
  );
};

export const saveSysCityDataApi = (data: CityResultModel) => {
  return http.request<BasicResponseParams>(
    "post",
    baseUrlApi("system/area/save"),
    {
      data
    }
  );
};

// 列表
export const getSysCityListApi = (data: CityPageModel) => {
  return (
    http.request <ResponseDetailModel<BasicPageParams<CityResultModel[]>>>(
      "post",
      baseUrlApi("system/area/queryPage"),
      {
        data
      }
    )
  );
};

// 根据id查询
export const getSysCityDetailApi = (id: number | string) => {
  return http.request<ResponseDetailModel<CityResultModel>>(
    "get",
    baseUrlApi(`system/area/details/${id}`)
  );
};

// 根据id删除
export const delSysCityByIdApi = (id: number | string) => {
  return http.request<BasicResponseParams>(
    "get",
    baseUrlApi(`system/area/delete/${id}`)
  );
};
