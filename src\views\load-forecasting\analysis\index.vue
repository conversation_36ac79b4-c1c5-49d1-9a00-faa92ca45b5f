<template>
  <section class="analysis-container">
    <div class="app-search-card">
      <div class="app-form-group">
        <div class="ml-[20px]">
          <span>日期范围选择：</span>
          <el-date-picker style="width: 140px" v-model="searchInfo.startDate" type="date" :clearable="false"
            placeholder="请选择" format="YYYY-MM-DD" value-format="x" />
        </div>
        <div>
          <span class="mx-[5px]">-</span>
          <el-date-picker style="width: 140px" v-model="searchInfo.endDate" type="date" :clearable="false"
            placeholder="请选择" format="YYYY-MM-DD" value-format="x" />
        </div>
        <div class="ml-[20px]">
          <span>数据范围：</span>
          <DictSelect style="width: 120px" v-model="dataRange" dict-code="dataRange" />
        </div>
        <div class="ml-[20px]" v-if="dataRange === 2">
          <el-input @click="selectVisible = true" v-model="customName" placeholder="请选择客户" />
        </div>
        <!-- <div class="ml-[20px]" v-show="searchInfo.range == 1">
          <span>客户分组：</span>
          <el-select
            v-model="searchInfo.group"
            placeholder="请选择"
            style="width: 120px"
          >
            <el-option
              v-for="item in groupOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div> -->
        <!-- <div class="ml-[20px]" v-show="searchInfo.range == 1">
          <span>客户二级分组：</span>
          <el-select
            v-model="searchInfo.group"
            placeholder="请选择"
            style="width: 120px"
          >
            <el-option
              v-for="item in groupOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div> -->
      </div>
      <div class="app-btn-group">
        <el-button class="filter-item" type="primary" @click="getList">查询</el-button>
        <el-button class="filter-item" @click="handleReset">重置</el-button>
      </div>
    </div>
    <div class="app-content-container" v-loading="loading">
      <div class="flex justify-between">
        <div class="card-item">
          <div>总电量</div>
          <div class="value">
            {{ summaryData.total !== null ? (summaryData.total).toFixed(3) : "-" }}MWh
          </div>
        </div>
        <div class="card-item">
          <div>平均值</div>
          <div class="value">
            {{ summaryData.average !== null ? summaryData.average : "-" }}MWh
          </div>
        </div>
        <div class="card-item">
          <div>最大值</div>
          <div class="value">
            {{ summaryData.max !== null ? summaryData.max : "-" }}MWh
          </div>
        </div>
        <div class="card-item">
          <div>最小值</div>
          <div class="value">
            {{ summaryData.min !== null ? summaryData.min : "-" }}MWh
          </div>
        </div>
      </div>
      <div>
        <ChartsLine :loadList="lineData.loadList" :elecList="lineData.elecList" :forecastList="lineData.forecastList"
          :temperatureList="lineData.temperatureList" :xData="xData" height="500px" />
      </div>
    </div>
    <el-dialog width="60%" append-to-body v-model="selectVisible" destroy-on-close title="客户选择">
      <customer-select @change="handleClose" />
    </el-dialog>
  </section>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import ChartsLine from "./components/ChartsLine.vue";
import dayjs from "dayjs";
import CustomerSelect from "@/components/Core/PowerConsumerSelect/powerConsumer.vue";
import {
  getForecastAnalysisDateApi,
  getForecastAnalysisApi
} from "@/api/load-forecasting/index";
defineOptions({
  name: "LoadForecastingAnalysis"
});
const loading = ref<boolean>(false);
const dataRange = ref<number>(0);
const searchInfo = ref({
  startDate: dayjs().subtract(3, "day").valueOf(),
  endDate: dayjs().valueOf(),
  customId: 0
});
const groupOptions = ref([]);
// x轴
const xData = ref<string[]>([]);
// 曲线数据
const lineData = ref({
  loadList: [],
  elecList: [],
  forecastList: [],
  temperatureList: []
});
// 顶部汇总
const summaryData = ref({
  total: 0,
  max: 0,
  min: 0,
  average: 0
});
const selectVisible = ref<boolean>(false);
const customName = ref<string>("");
// 客户确认选择弹框事件
function handleClose(row) {
  selectVisible.value = false;
  searchInfo.value.customId = row.id;
  customName.value = row.name;
}
async function getNewDate() {
  // const res = await getForecastAnalysisDateApi();
  // searchInfo.value.startDate = dayjs(
  //   dayjs(res.data.maxPowerDate).subtract(3, "day")
  // ).valueOf();
  // searchInfo.value.endDate = dayjs(res.data.maxPowerDate).valueOf();
}
async function getList() {
  loading.value = true;
  if (dataRange.value === 0) {
    searchInfo.value.customId = 0;
  }
  const res = await getForecastAnalysisApi({
    parameter: {
      ...searchInfo.value
    }
  });
  if (res.data.curveList) {
    summaryData.value = { ...res.data.summary };
    xData.value = res.data.curveList.map(i => i.time);
    lineData.value.forecastList = res.data.curveList.map(i => i.forecast);
    lineData.value.loadList = res.data.curveList.map(i => i.real);
    lineData.value.elecList = res.data.curveList.map(i =>
      i.declaration !== null ? i.declaration : null
    );
    lineData.value.temperatureList = res.data.curveList.map(i => i.temperature);
  } else {
    summaryData.value = {
      max: 0,
      min: 0,
      average: 0,
      total: 0
    };
    xData.value = [];
    lineData.value.forecastList = [];
    lineData.value.loadList = [];
    lineData.value.elecList = [];
    lineData.value.temperatureList = [];
  }
  setTimeout(() => {
    loading.value = false;
  }, 300);
}
onMounted(async () => {
  await getNewDate();
  getList();
});
async function handleReset() {
  dataRange.value = 0;
  searchInfo.value.customId = undefined;
  await getNewDate();
  getList();
}
</script>
<!-- // 10507e -->
<style lang="scss" scoped>
.analysis-container {
  .analysis-header {
    border-radius: 6px;
    box-shadow: 0px 2px 22px rgba(0, 0, 0, 0.06);
    padding: 20px 5px;
  }

  .card-item {
    width: 25%;
    padding: 10px 15px;
    background: #f5f4fb;
    border-radius: 6px;
    display: flex;
    flex-direction: column;
    justify-items: center;
    align-items: center;
    margin-right: 40px;

    .value {
      font-weight: 700;
      color: var(--el-color-primary);
    }

    &:last-child {
      margin-right: 0;
    }
  }
}
</style>
