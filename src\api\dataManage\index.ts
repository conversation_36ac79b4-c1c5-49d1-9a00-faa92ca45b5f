import { request } from '@/utils/request'
// 查询峰谷信息
export const selectPeakValley = (data: any) =>
  request.post({
    url: '/datafilling/peakValley/queryPageList',
    data: data,
  })
// 保存峰谷信息
export const savePeakValley = (data: any) =>
  request.post({
    url: '/datafilling/peakValley/savePeakValleyItem',
    data: data,
  })
// 修改峰谷信息
export const updatePeakValley = (data: any) =>
  request.post({
    url: '/datafilling/peakValley/updatePeakValley',
    data: data,
  })
// 删除峰谷信息
export const deletePeakValley = (id: any) =>
  request.delete({
    url: `/datafilling/peakValley/deletePeakValley/${id}`,
  })
// 分时查询峰谷配置信息
export const queryPageListByTime = (data: any) =>
  request.post({
    url: '/datafilling/peakValley/queryPageListByTime',
    data: data,
  })
//查询峰谷配置年份信息
export const queryYearTimes = () =>
  request.post({
    url: '/datafilling/peakValley/queryYearTimes',
  })
// 查询最近添加的峰谷配置信息
export const queryLastPeakValley = () =>
  request.post({
    url: '/datafilling/peakValley/queryLastPeakValley',
  })
