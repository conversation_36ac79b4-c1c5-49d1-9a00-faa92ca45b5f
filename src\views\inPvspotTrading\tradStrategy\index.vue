<template>
  <section>
    <el-card class="jt-card">
      <div class="header" style="display:flex;align-items:center;">
        <div style="width:25%">运行日：<el-date-picker :clearable="false" @change="pickdateChange" format="YYYY-MM-DD"
            value-format="YYYY-MM-DD" v-model="pickerDate" :disabled="stepActive !== 1"/></div>
        <div style="width:75%">
          <step :active="stepActive" :stepList="['价格场景', '模拟方案', '出清结果']">
          </step>
        </div>
      </div>
    </el-card>

    <el-card v-if="stepActive == 1" style="margin-top: 20px" class="jt-card">
      <div class="header">
        <div class="header-title">价格场景</div>
        <div>
          <el-button type="primary" @click="next">下一步</el-button>
        </div>
      </div>
      <div style="margin-top:20px;">
        <div style="display: flex; justify-content: space-between;">
          <div style="display: flex;">
            <div>
              日前：<el-select @change="dayheadScenceChange" style="margin-right: 10px; width: 226px" class="singleSelect"
                v-model="dayheadScence" placeholder="请选择">
                <el-option v-for="item in dayheadScenceOptions" :key="item.value" :label="item.label"
                  :value="item.label" />
              </el-select>
            </div>
            <el-upload class="daoru" :http-request="dayheadScenceUploadFunction" v-if="dayheadScence === '日前预测导入价格'"
              action="" :on-change="dayheadScenceUploadFile">
              <el-button :icon="Upload" type="primary">文件上传</el-button>
            </el-upload>
            <div style="margin-left: 20px;">
              实时：<el-select @change="dayinScenceChange" style="margin-right: 10px; width: 226px" class="singleSelect"
                v-model="dayinScence" placeholder="请选择">
                <el-option v-for="item in dayinScenceOptions" :key="item.value" :label="item.label" :value="item.label" />
              </el-select>
            </div>
            <el-upload class="daoru" :http-request="dayinScenceUploadFunction" v-if="dayinScence === '实时预测导入价格'" action=""
              :on-change="dayinScenceUploadFile">
              <el-button :icon="Upload" type="primary">文件上传</el-button>
            </el-upload>
          </div>
          <el-button v-if="dayinScence === '实时预测导入价格' || dayheadScence === '日前预测导入价格'"
            @click="dayinScenceDownloadContracts" type="success" :icon="Bottom">
            下载模板
          </el-button>
        </div>
        <Echarts :echartsData="priceScenceOptionDeal" EWidth="100%" EHeight="450px" echartId="priceScence"></Echarts>
        <Echarts :echartsData="offsetOptionDeal" EWidth="100%" EHeight="450px" echartId="offset"></Echarts>
        <div class="header" style="margin-top: 20px;">
          <div class="header-title">市场供需</div>
        </div>
        <Echarts :echartsData="supplyOptionDeal" EWidth="100%" EHeight="450px" echartId="supplyOption"></Echarts>
      </div>
    </el-card>

    <el-card v-if="stepActive == 2" style="margin-top: 20px" class="jt-card">
      <div class="header">
        <div class="header-title">模拟申报方案</div>
        <div>
          <el-button type="plain" @click="pre">上一步</el-button>
          <el-button type="primary" @click="next">模拟出清</el-button>
        </div>
      </div>
      <div style="margin-top:20px;">
        <div style="display: flex;justify-content: space-between;">
          <div style="display: flex;">
            <div>
              实际预测电量：<el-select @change="predictedElecChange" style="margin-right: 10px; width: 226px;"
                class="singleSelect" v-model="predictedElec" placeholder="请选择">
                <el-option v-for="item in predictedElecOptions" :key="item.value" :label="item.label"
                  :value="item.label" />
              </el-select>
            </div>
            <el-upload class="daoru" :http-request="predictedEleUploadFunction" v-if="predictedElec === '自定义'" action=""
              :on-change="predictedEleUploadFile">
              <el-button :icon="Upload" type="primary">文件上传</el-button>
            </el-upload>
            <div style="margin-left: 20px;">
              日前申报策略：<el-select @change="reportingStrategyChange" style="margin-right: 10px;width: 226px;"
                class="singleSelect" v-model="reportingStrategy" placeholder="请选择">
                <el-option v-for="item in reportingStrategyOptions" :key="item.value" :label="item.label"
                  :value="item.label" />
              </el-select>
            </div>
            <el-upload class="daoru" :http-request="reportingStrategyUploadFunction" v-if="reportingStrategy === '自定义'"
              action="" :on-change="reportingStrategyUploadFile">
              <el-button :icon="Upload" type="primary">文件上传</el-button>
            </el-upload>
          </div>
          <el-button v-if="predictedElec === '自定义' || reportingStrategy === '自定义'" @click="dayinScenceDownloadContracts"
            type="success" :icon="Bottom">
            下载模板
          </el-button>
        </div>
        <Echarts :echartsData="predictedEleOptionDeal" EWidth="100%" EHeight="450px" echartId="predictedEle"></Echarts>
        <Echarts :echartsData="priceScenceOptionDeal" EWidth="100%" EHeight="450px" echartId="priceScence"></Echarts>

      </div>
    </el-card>

    <el-card v-if="stepActive == 3" style="margin-top: 20px" class="jt-card">
      <div class="header">
        <div class="header-title">出清结果</div>
        <div>
          <el-button type="plain" @click="pre">上一步</el-button>
        </div>
      </div>
      <div style="margin-top:20px;">
        <MyTab v-model="clearResultActive" @change="clearResultChange" :tabs="['支出分析', '量价分析', '套利分析']">
        </MyTab>
        <div v-if="clearResultActive == '支出分析'">
          <div class="top-item-wrap">
            <div class="top-item" v-for="(item, index) in outputAnalyse" :key="index">
              <div class="inner-wrap">
                <div class="title">{{ item.name }}</div>
                <div class="value">{{ item.value }}</div>
              </div>
            </div>
          </div>
          <div class="header" style="margin-top: 20px;">
            <div class="header-title">支出明细</div>
          </div>
          <Echarts :echartsData="stateExpendOptionDeal" EWidth="100%" EHeight="450px" echartId="stateExpend"></Echarts>
        </div>

        <div v-if="clearResultActive == '量价分析'">
          <div class="top-item-wrap">
            <div class="top-item2">
              <div style="margin-bottom: 32px;">
                <div class="inner-wrap">
                  <div class="title">中长期电力(MW)</div>
                  <div class="value">50</div>
                </div>
                <div class="inner-wrap">
                  <div class="title">日前电力(MW)</div>
                  <div class="value">{{ dayheadEnergy }}</div>
                </div>
                <div class="inner-wrap">
                  <div class="title">实时电力(MW)</div>
                  <div class="value">50</div>
                </div>
              </div>
              <div>
                <div class="inner-wrap">
                  <div class="title">中长期均价(元/MWh)</div>
                  <div class="value">50</div>
                </div>
                <div class="inner-wrap">
                  <div class="title">日前均价(元/MWh)</div>
                  <div class="value">{{ dayheadPrice }}</div>
                </div>
                <div class="inner-wrap">
                  <div class="title">实时均价(元/MWh)</div>
                  <div class="value">50</div>
                </div>
              </div>
            </div>
            <div class="top-item2 top-item2-2">
              <div style="margin-bottom: 32px;">
                <div class="inner-wrap">
                  <div class="title">中长期比日前盈亏(元)</div>
                  <div class="value">50</div>
                </div>
              </div>
              <div>
                <div class="inner-wrap">
                  <div class="title">中长期比实时盈亏(元)</div>
                  <div class="value">50</div>
                </div>
              </div>
            </div>
            <div class="top-item2 top-item2-2">
              <div style="margin-bottom: 32px;">
                <div class="inner-wrap">
                  <div class="title">日前-中长期价差(元/MWh)</div>
                  <div class="value">50</div>
                </div>
              </div>
              <div>
                <div class="inner-wrap">
                  <div class="title">实时-日前价差(元/MWh)</div>
                  <div class="value">50</div>
                </div>
              </div>
            </div>
          </div>
          <Echarts style="margin-top: 20px;" :echartsData="volPriceOption1Deal" EWidth="100%" EHeight="450px"
            echartId="volPrice1"></Echarts>
          <div style="display: flex;justify-content: flex-end;">
            <MyTab style="margin-top: 20px;" v-model="volPriceActive" @change="volPriceChange"
              :tabs="['中长期比日前', '日前比实时']">
            </MyTab>
          </div>
          <Echarts style="margin-top: 20px;" :echartsData="volPriceOption2Deal" EWidth="100%" EHeight="450px"
            echartId="volPrice2"></Echarts>
          <Echarts style="margin-top: 20px;" :echartsData="volPriceOption3Deal" EWidth="100%" EHeight="450px"
            echartId="volPrice3"></Echarts>
        </div>

        <div v-if="clearResultActive == '套利分析'">
          <div class="top-item-wrap">
            <div class="top-item2 top-item2-2">
              <div>
                <div class="inner-wrap">
                  <div class="title">套利利益(元)</div>
                  <div class="value">50</div>
                </div>
              </div>
            </div>
            <div class="top-item2 top-item2-2">
              <div style="margin-bottom: 32px;">
                <div class="inner-wrap">
                  <div class="title">套利量差(MWh)</div>
                  <div class="value">50</div>
                </div>
              </div>
              <div>
                <div class="inner-wrap">
                  <div class="title">套利价差(元/MWh)</div>
                  <div class="value">50</div>
                </div>
              </div>
            </div>
            <div class="top-item2">
              <div style="margin-bottom: 32px;">
                <div class="inner-wrap">
                  <div class="title">日前电力(MW)</div>
                  <div class="value">50</div>
                </div>
                <div class="inner-wrap">
                  <div class="title">实时电力(MW)</div>
                  <div class="value">50</div>
                </div>
              </div>
              <div>
                <div class="inner-wrap">
                  <div class="title">日前均价(元/MWh)</div>
                  <div class="value">50</div>
                </div>
                <div class="inner-wrap">
                  <div class="title">实时均价(元/MWh)</div>
                  <div class="value">50</div>
                </div>
              </div>
            </div>
          </div>

          <Echarts style="margin-top: 20px;" :echartsData="straddleOption1Deal" EWidth="100%" EHeight="450px"
            echartId="straddle1"></Echarts>
          <Echarts style="margin-top: 20px;" :echartsData="straddleOption2Deal" EWidth="100%" EHeight="450px"
            echartId="straddle2"></Echarts>
          <Echarts style="margin-top: 20px;" :echartsData="straddleOption3Deal" EWidth="100%" EHeight="450px"
            echartId="straddle3"></Echarts>
        </div>
      </div>
    </el-card>
  </section>
</template>
  
<script setup lang='ts'>
import { ref } from 'vue'
import dayjs from 'dayjs' // 引入dayjs
import { echartsConfig } from '@/utils/echartsConfig'
import { Upload, Bottom } from '@element-plus/icons-vue'
import { ElMessage, ElLoading } from 'element-plus'

import {
  getDayAheadData, //日前价格
  getRealTimeData, //日内价格
  getDeviationData, //日前日内差
  importDayaheadForcastData, //导入日前预测价格
  importRealtimeForcastData, //导入日内预测价格
  marketSupplyDemand, //市场供需详情
  attachmentDownload, //下载附件

  importLoadPowerForcastData, //实际预测电量导入
  importLoadpowerApplyData, //预测电量申报策略导入
  getElectricalVoltageForcast, //获取实际电量预测
  getElectricalVoltageApply, //获取电量预测申报策略

  payoutStatis, //支出统计
  pyaoutDetail, //支出明细
  quantitypriceStatis, //量价统计
  quantitypriceDetail, //量价明细
} from '@/api'
import { onMounted } from 'vue';

// 导入日前预测价格文件上传
function dayheadScenceUploadFunction() { }
const fileList = ref<any>([])
// 导入日前预测价格文件上传状态改变
function dayheadScenceUploadFile(file: any) {
  const fileExtension = file.name.split('.').pop()
  const size = file.size / 1024 / 1024
  if (fileExtension !== 'xlsx' && fileExtension !== 'xls') {
    ElMessage.warning('只能上传excel文件')
    fileList.value = []
    return
  }
  if (size > 10) {
    ElMessage.warning('文件大小不得超过10M')
    fileList.value = []
    return
  }
  fileList.value = [file]
  handleUpload()
}
// 上传函数
const handleUpload = () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('未选择文件')
  } else {
    const formData = new FormData()
    formData.append('file', fileList.value[0].raw)
    importDayaheadForcastData(formData, pickerDate.value)
      .then(() => {
        ElMessage.success('上传成功')
        dayheadScenceChange()
      })
      .catch((e: any) => {
        console.log(e)
      })
  }
}

// 导入日内预测价格文件上传
function dayinScenceUploadFunction() { }
const dayinScencefileList = ref<any>([])
// 导入日内预测价格文件上传状态改变
function dayinScenceUploadFile(file: any) {
  const fileExtension = file.name.split('.').pop()
  const size = file.size / 1024 / 1024
  if (fileExtension !== 'xlsx' && fileExtension !== 'xls') {
    ElMessage.warning('只能上传excel文件')
    dayinScencefileList.value = []
    return
  }
  if (size > 10) {
    ElMessage.warning('文件大小不得超过10M')
    dayinScencefileList.value = []
    return
  }
  dayinScencefileList.value = [file]
  dayinScencehandleUpload()
}
// 上传函数
const dayinScencehandleUpload = () => {
  if (dayinScencefileList.value.length === 0) {
    ElMessage.warning('未选择文件')
  } else {
    const formData = new FormData()
    formData.append('file', dayinScencefileList.value[0].raw)
    importRealtimeForcastData(formData, pickerDate.value)
      .then(() => {
        ElMessage.success('上传成功')
        dayinScenceChange()
      })
      .catch((e: any) => {
        console.log(e)
      })
  }
}

// 导入实际预测电量文件上传
function predictedEleUploadFunction() { }
const predictedElefileList = ref<any>([])
// 导入实际预测电量文件上传状态改变
function predictedEleUploadFile(file: any) {
  const fileExtension = file.name.split('.').pop()
  const size = file.size / 1024 / 1024
  if (fileExtension !== 'xlsx' && fileExtension !== 'xls') {
    ElMessage.warning('只能上传excel文件')
    predictedElefileList.value = []
    return
  }
  if (size > 10) {
    ElMessage.warning('文件大小不得超过10M')
    predictedElefileList.value = []
    return
  }
  predictedElefileList.value = [file]
  predictedElehandleUpload()
}
// 上传函数
const predictedElehandleUpload = () => {
  if (predictedElefileList.value.length === 0) {
    ElMessage.warning('未选择文件')
  } else {
    const formData = new FormData()
    formData.append('file', predictedElefileList.value[0].raw)
    importLoadPowerForcastData(formData, pickerDate.value)
      .then(() => {
        ElMessage.success('上传成功')
        getElectricalVoltageForcastInfo()
      })
      .catch((e: any) => {
        console.log(e)
      })
  }
}

// 导入日前申报策略文件上传
function reportingStrategyUploadFunction() { }
const reportingStrategyfileList = ref<any>([])
// 导入日前申报策略文件上传状态改变
function reportingStrategyUploadFile(file: any) {
  const fileExtension = file.name.split('.').pop()
  const size = file.size / 1024 / 1024
  if (fileExtension !== 'xlsx' && fileExtension !== 'xls') {
    ElMessage.warning('只能上传excel文件')
    reportingStrategyfileList.value = []
    return
  }
  if (size > 10) {
    ElMessage.warning('文件大小不得超过10M')
    reportingStrategyfileList.value = []
    return
  }
  reportingStrategyfileList.value = [file]
  reportingStrategyUpload()
}
// 上传函数
const reportingStrategyUpload = () => {
  if (reportingStrategyfileList.value.length === 0) {
    ElMessage.warning('未选择文件')
  } else {
    const formData = new FormData()
    formData.append('file', reportingStrategyfileList.value[0].raw)
    importLoadpowerApplyData(formData, pickerDate.value)
      .then(() => {
        ElMessage.success('上传成功')
        getElectricalVoltageApplyInfo()
      })
      .catch((e: any) => {
        console.log(e)
      })
  }
}

// 下载模板
function dayinScenceDownloadContracts() {
  const loading = ElLoading.service({ text: '正在下载...' })
  attachmentDownload({
    name: '现货交易策略_导入模板.zip',
  })
    .then((data) => {
      const blob = new Blob([data])
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = '现货交易策略_导入模板.zip'
      link.click()
      window.URL.revokeObjectURL(url)
      loading.close()
    })
    .catch(() => {
      ElMessage.error('下载失败')
      loading.close()
    })
}

// 日期选择
const pickerDate = ref<any>(dayjs().format('YYYY-MM-DD'))
function pickdateChange() {
  //获取日前价格
  getDayAheadDataInfo()
  //获取日内价格
  getRealTimeDataInfo()
  //获取日前日内差
  getDeviationDataInfo()
  //获取市场供需详情
  getMarketSupplyDemand()
  //获取实际电量预测
  getElectricalVoltageForcastInfo()
  //获取日前申报策略
  getElectricalVoltageApplyInfo()
  // 获取支出统计
  getPayoutStatisInfo()
  // 获取支出明细
  getPyaoutDetailInfo()
  //获取量价统计
  getQuantitypriceStatisInfo()
  //获取量价统计
  getquantitypriceDetailInfo()
}
// 步骤条
const stepActive = ref(1)
const next = () => {
  stepActive.value++
}
const pre = () => {
  stepActive.value--
}
// 日前场景
const dayheadScence = ref<any>('日前预测价格')
const dayheadScenceOptions = ref<any>([
  {
    label: '日前预测价格',
    value: '日前预测价格'
  },
  {
    label: '日前预测导入价格',
    value: '日前预测导入价格'
  },
  {
    label: '历史日前价格',
    value: '历史日前价格'
  },
])
const dayheadScenceChange = () => {
  //获取日前价格
  getDayAheadDataInfo()
  //获取日前日内差
  getDeviationDataInfo()
  //获取市场供需详情
  getMarketSupplyDemand()
  // 获取支出统计
  getPayoutStatisInfo()
  // 获取支出明细
  getPyaoutDetailInfo()
  //获取量价统计
  getQuantitypriceStatisInfo()
  //获取量价统计
  getquantitypriceDetailInfo()
}
// 日内场景
const dayinScence = ref<any>('历史实时价格')
const dayinScenceOptions = ref<any>([
  {
    label: '历史实时价格',
    value: '历史实时价格'
  },
  {
    label: '实时预测导入价格',
    value: '实时预测导入价格'
  }
])
const dayinScenceChange = () => {
  //获取日内价格
  getRealTimeDataInfo()
  //获取日前日内差
  getDeviationDataInfo()
  //获取市场供需详情
  getMarketSupplyDemand()
  // 获取支出统计
  getPayoutStatisInfo()
  // 获取支出明细
  getPyaoutDetailInfo()
}
// 实际预测电量
const predictedElec = ref<any>('负荷预测')
const predictedElecOptions = ref<any>([
  {
    label: '负荷预测',
    value: '负荷预测',
  },
  {
    label: '自定义',
    value: '自定义',
  },
])
const predictedElecChange = () => {
  getElectricalVoltageForcastInfo()
  // 获取支出统计
  getPayoutStatisInfo()
  // 获取支出明细
  getPyaoutDetailInfo()
  //获取量价统计
  getQuantitypriceStatisInfo()
  //获取量价统计
  getquantitypriceDetailInfo()
}
// 日前申报策略
const reportingStrategy = ref<any>('等于实际预测电量')
const reportingStrategyOptions = ref<any>([
  {
    label: '等于实际预测电量',
    value: '等于实际预测电量',
  },
  {
    label: '自定义',
    value: '自定义',
  }
])
const reportingStrategyChange = () => {
  getElectricalVoltageApplyInfo()
  // 获取支出统计
  getPayoutStatisInfo()
  // 获取支出明细
  getPyaoutDetailInfo()
  //获取量价统计
  getQuantitypriceStatisInfo()
  //获取量价统计
  getquantitypriceDetailInfo()
}

// 价格场景设置图表
const priceScenceOption = ref<any>({
  tooltip: {
    trigger: 'axis',
    formatter: function (params: any) {
      let context
      let content = params.map((item: any) => {
        return `${item.marker}<span>${item.seriesName
          }</span>:<span style="margin-left: 20px;float: right">${item.value || '--'
          }元/MWh</span><br>`
      })
      let newContent = ''
      content.forEach((item: any) => {
        newContent = newContent + item
      })
      context = `<div>${params[0].name}</div><div>${newContent}</div>`
      return context
    },
  },
  legend: {
    show: true,
    width: '100%',
    x: 'right',
    y: 'top',
    type: 'scroll',
  },
  grid: {
    top: '15%',
    left: '8%',
    right: '8%',
    bottom: '5%',
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    splitLine: {
      show: false,
    },
    axisTick: {
      show: false, //坐标轴刻度线
    },
    data: [1, 1, 1, 1, 1],
  },
  yAxis: {
    type: 'value',
    name: '电价(元/MWh)',
    nameTextStyle: {
      // y轴name的样式调整
      fontSize: 14,
    },
  },
  series: [
    {
      name: '日前价格',
      type: 'line',
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      data: [1, 1, 1, 1, 1],
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        lineStyle: {
          width: 2,
          type: 'solid', //'dotted'虚线 'solid'实线
        },
      },
    },
    {
      name: '实时价格',
      type: 'line',
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      data: [1, 1, 1, 1, 1],
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        lineStyle: {
          width: 2,
          type: 'solid', //'dotted'虚线 'solid'实线
        },
      },
    },
  ],
})
const priceScenceOptionDeal = echartsConfig(priceScenceOption.value)
// 日前-日内偏差图表
const offsetOption = ref<any>({
  tooltip: {
    trigger: 'axis',
    formatter: function (params: any) {
      let context
      let content = params.map((item: any) => {
        return `${item.marker}<span>${item.seriesName
          }</span>:<span style="margin-left: 20px;float: right">${item.value || '--'
          }元/MWh</span><br>`
      })
      let newContent = ''
      content.forEach((item: any) => {
        newContent = newContent + item
      })
      context = `<div>${params[0].name}</div><div>${newContent}</div>`
      return context
    },
  },
  legend: {
    show: true,
    width: '100%',
    x: 'right',
    y: 'top',
    type: 'scroll',
  },
  grid: {
    top: '15%',
    left: '8%',
    right: '8%',
    bottom: '5%',
  },
  xAxis: {
    type: 'category',
    boundaryGap: true,
    splitLine: {
      show: false,
    },
    axisTick: {
      show: false, //坐标轴刻度线
    },
    data: [1, 1, 1, 1, 1],
  },
  yAxis: {
    type: 'value',
    name: '日前-实时偏差(元/MWh)',
    nameTextStyle: {
      // y轴name的样式调整
      fontSize: 14,
    },
  },
  series: [
    {
      name: '日前-实时偏差(正偏差)',
      type: 'bar',
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      data: [1, 1, 1, 1, 1],
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        lineStyle: {
          width: 2,
          type: 'solid', //'dotted'虚线 'solid'实线
        },
      },
    },
  ],
})
const offsetOptionDeal = echartsConfig(offsetOption.value)
// 市场供需图表
const supplyOption = ref<any>({
  tooltip: {
    trigger: 'axis',
    formatter: function (params: any) {
      let context
      let content = params.map((item: any) => {
        return `${item.marker}<span>${item.seriesName
          }</span>:<span style="margin-left: 20px;float: right">${item.value || '--'
          }MW</span><br>`
      })
      let newContent = ''
      content.forEach((item: any) => {
        newContent = newContent + item
      })
      context = `<div>${params[0].name}</div><div>${newContent}</div>`
      return context
    },
  },
  legend: {
    show: true,
    width: '100%',
    x: 'right',
    y: 'top',
    type: 'scroll',
  },
  grid: {
    top: '15%',
    left: '8%',
    right: '8%',
    bottom: '5%',
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    splitLine: {
      show: false,
    },
    axisTick: {
      show: false, //坐标轴刻度线
    },
    data: [1, 1, 1, 1, 1],
  },
  yAxis: {
    type: 'value',
    name: '出力(MW)',
    nameTextStyle: {
      // y轴name的样式调整
      fontSize: 14,
    },
  },
  series: [
    {
      name: '统调负荷-日前',
      type: 'line',
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      data: [1, 1, 1, 1, 1],
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        lineStyle: {
          width: 2,
          type: 'solid', //'dotted'虚线 'solid'实线
        },
      },
    },
    {
      name: '火电竞价空间-日前',
      type: 'line',
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      data: [1, 1, 1, 1, 1],
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        lineStyle: {
          width: 2,
          type: 'solid', //'dotted'虚线 'solid'实线
        },
      },
    },
    {
      name: '光伏出力-日前',
      type: 'line',
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      data: [1, 1, 1, 1, 1],
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        lineStyle: {
          width: 2,
          type: 'solid', //'dotted'虚线 'solid'实线
        },
      },
    },
    {
      name: '联络线计划-日前',
      type: 'line',
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      data: [1, 1, 1, 1, 1],
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        lineStyle: {
          width: 2,
          type: 'solid', //'dotted'虚线 'solid'实线
        },
      },
    },
    {
      name: '非市场机组出力-日前',
      type: 'line',
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      data: [1, 1, 1, 1, 1],
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        lineStyle: {
          width: 2,
          type: 'solid', //'dotted'虚线 'solid'实线
        },
      },
    },
    {
      name: '风电出力-日前',
      type: 'line',
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      data: [1, 1, 1, 1, 1],
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        lineStyle: {
          width: 2,
          type: 'solid', //'dotted'虚线 'solid'实线
        },
      },
    },
  ],
})
const supplyOptionDeal = echartsConfig(supplyOption.value)

// 实际预测电量：日前申报策略：
const predictedEleOption = ref<any>({
  tooltip: {
    trigger: 'axis',
    formatter: function (params: any) {
      let context
      let content = params.map((item: any) => {
        return `${item.marker}<span>${item.seriesName
          }</span>:<span style="margin-left: 20px;float: right">${item.value || '--'
          }MWh</span><br>`
      })
      let newContent = ''
      content.forEach((item: any) => {
        newContent = newContent + item
      })
      context = `<div>${params[0].name}</div><div>${newContent}</div>`
      return context
    },
  },
  legend: {
    show: true,
    width: '100%',
    x: 'right',
    y: 'top',
    type: 'scroll',
  },
  grid: {
    top: '15%',
    left: '8%',
    right: '8%',
    bottom: '5%',
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    splitLine: {
      show: false,
    },
    axisTick: {
      show: false, //坐标轴刻度线
    },
    data: [1, 1, 1, 1, 1],
  },
  yAxis: {
    type: 'value',
    name: '电量(MWh)',
    nameTextStyle: {
      // y轴name的样式调整
      fontSize: 14,
    },
  },
  series: [
    {
      name: '实际预测电量',
      type: 'line',
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      data: [1, 1, 1, 1, 1],
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        lineStyle: {
          width: 2,
          type: 'solid', //'dotted'虚线 'solid'实线
        },
      },
    },
    {
      name: '日前申报策略',
      type: 'line',
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      data: [1, 1, 1, 1, 1],
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        lineStyle: {
          width: 2,
          type: 'solid', //'dotted'虚线 'solid'实线
        },
      },
    },
  ],
})
const predictedEleOptionDeal = echartsConfig(predictedEleOption.value)
// 价格场景
const electricPrice = ref<any>({
  tooltip: {
    trigger: 'axis',
    formatter: function (params: any) {
      let context
      let content = params.map((item: any) => {
        return `${item.marker}<span>${item.seriesName
          }</span>:<span style="margin-left: 20px;float: right">${item.value || '--'
          }元/MWh</span><br>`
      })
      let newContent = ''
      content.forEach((item: any) => {
        newContent = newContent + item
      })
      context = `<div>${params[0].name}</div><div>${newContent}</div>`
      return context
    },
  },
  legend: {
    show: true,
    width: '100%',
    x: 'right',
    y: 'top',
    type: 'scroll',
  },
  grid: {
    top: '15%',
    left: '8%',
    right: '8%',
    bottom: '5%',
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    splitLine: {
      show: false,
    },
    axisTick: {
      show: false, //坐标轴刻度线
    },
    data: [1, 1, 1, 1, 1],
  },
  yAxis: {
    type: 'value',
    name: '电价(元/MWh)',
    nameTextStyle: {
      // y轴name的样式调整
      fontSize: 14,
    },
  },
  series: [
    {
      name: '日前出清电价',
      type: 'line',
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      data: [1, 1, 1, 1, 1],
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        lineStyle: {
          width: 2,
          type: 'solid', //'dotted'虚线 'solid'实线
        },
      },
    },
    {
      name: '日前出清电价',
      type: 'line',
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      data: [1, 1, 1, 1, 1],
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        lineStyle: {
          width: 2,
          type: 'solid', //'dotted'虚线 'solid'实线
        },
      },
    },
  ],
})
const electricPriceDeal = echartsConfig(electricPrice.value)

// 支出分析
const stateExpendOption = ref<any>({
  tooltip: {
    trigger: 'axis',
    formatter: function (params: any) {
      let context
      let content = params.map((item: any) => {
        if (item.seriesName === '度电支出') {
          return `${item.marker}<span>${item.seriesName
            }</span>:<span style="margin-left: 20px;float: right">${item.value || '--'
            }元/MWh</span><br>`
        } else {
          return `${item.marker}<span>${item.seriesName
            }</span>:<span style="margin-left: 20px;float: right">${item.value || '--'
            }元</span><br>`
        }
      })
      let newContent = ''
      content.forEach((item: any) => {
        newContent = newContent + item
      })
      context = `<div>${params[0].name}</div><div>${newContent}</div>`
      return context
    },
  },
  legend: {
    show: true,
    width: '100%',
    x: 'right',
    y: 'top',
    type: 'scroll',
  },
  grid: {
    top: '15%',
    left: '8%',
    right: '8%',
    bottom: '5%',
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    splitLine: {
      show: false,
    },
    axisTick: {
      show: false, //坐标轴刻度线
    },
    data: [1, 1, 1, 1, 1],
  },
  yAxis: [
    {
      type: 'value',
      name: '元',
      nameTextStyle: {
        // y轴name的样式调整
        fontSize: 14,
      },
    },
    {
      type: 'value',
      name: '元/MWh',
      nameTextStyle: {
        // y轴name的样式调整
        fontSize: 14,
      },
    }
  ],
  series: [
    {
      name: '度电支出',
      yAxisIndex: 1,
      type: 'line',
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      data: [1, 1, 1, 1, 1],
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        lineStyle: {
          width: 2,
          type: 'solid', //'dotted'虚线 'solid'实线
        },
      },
    },
  ],
})
const stateExpendOptionDeal = echartsConfig(stateExpendOption.value)
// 量价分析
const volPriceOption1 = ref<any>({
  tooltip: {
    trigger: 'axis',
    formatter: function (params: any) {
      let context
      let content = params.map((item: any) => {
        if (item.seriesName === '日前价格') {
          return `${item.marker}<span>${item.seriesName
            }</span>:<span style="margin-left: 20px;float: right">${item.value || '--'
            }元/MWh</span><br>`
        } else {
          return `${item.marker}<span>${item.seriesName
            }</span>:<span style="margin-left: 20px;float: right">${item.value || '--'
            }MW</span><br>`
        }

      })
      let newContent = ''
      content.forEach((item: any) => {
        newContent = newContent + item
      })
      context = `<div>${params[0].name}</div><div>${newContent}</div>`
      return context
    },
  },
  legend: {
    show: true,
    width: '100%',
    x: 'right',
    y: 'top',
    type: 'scroll',
  },
  grid: {
    top: '15%',
    left: '8%',
    right: '8%',
    bottom: '5%',
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    splitLine: {
      show: false,
    },
    axisTick: {
      show: false, //坐标轴刻度线
    },
    data: [1, 1, 1, 1, 1],
  },
  yAxis: [
    {
      type: 'value',
      name: 'MW',
      nameTextStyle: {
        // y轴name的样式调整
        fontSize: 14,
      },
    },
    {
      type: 'value',
      name: '(元/MWh)',
      nameTextStyle: {
        // y轴name的样式调整
        fontSize: 14,
      },
    }
  ],
  series: [
    {
      name: '日前价格',
      yAxisIndex: 1,
      type: 'line',
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      data: [1, 1, 1, 1, 1],
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        lineStyle: {
          width: 2,
          type: 'solid', //'dotted'虚线 'solid'实线
        },
      },
    },
    {
      name: '日前电力',
      type: 'line',
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      data: [1, 1, 1, 1, 1],
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        lineStyle: {
          width: 2,
          type: 'solid', //'dotted'虚线 'solid'实线
        },
      },
    }
  ],
})
const volPriceOption1Deal = echartsConfig(volPriceOption1.value)
const volPriceOption2 = ref<any>({
  tooltip: {
    trigger: 'axis',
    formatter: function (params: any) {
      let context
      let content = params.map((item: any) => {
        return `${item.marker}<span>${item.seriesName
          }</span>:<span style="margin-left: 20px;float: right">${item.value || '--'
          }元</span><br>`
      })
      let newContent = ''
      content.forEach((item: any) => {
        newContent = newContent + item
      })
      context = `<div>${params[0].name}</div><div>${newContent}</div>`
      return context
    },
  },
  legend: {
    show: true,
    width: '100%',
    x: 'right',
    y: 'top',
    type: 'scroll',
  },
  grid: {
    top: '15%',
    left: '8%',
    right: '8%',
    bottom: '5%',
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    splitLine: {
      show: false,
    },
    axisTick: {
      show: false, //坐标轴刻度线
    },
    data: [1, 1, 1, 1, 1],
  },
  yAxis: {
    type: 'value',
    name: '中长期比日前盈亏(元)',
    nameTextStyle: {
      // y轴name的样式调整
      fontSize: 14,
    },
  },
  series: [
    {
      name: '中长期比日前盈利',
      type: 'line',
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      data: [1, 1, 1, 1, 1],
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        lineStyle: {
          width: 2,
          type: 'solid', //'dotted'虚线 'solid'实线
        },
      },
    },
    {
      name: '中长期比日前亏损',
      type: 'line',
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      data: [1, 1, 1, 1, 1],
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        lineStyle: {
          width: 2,
          type: 'solid', //'dotted'虚线 'solid'实线
        },
      },
    },
  ],
})
const volPriceOption2Deal = echartsConfig(volPriceOption2.value)
const volPriceOption3 = ref<any>({
  tooltip: {
    trigger: 'axis',
    formatter: function (params: any) {
      let context
      let content = params.map((item: any) => {
        return `${item.marker}<span>${item.seriesName
          }</span>:<span style="margin-left: 20px;float: right">${item.value || '--'
          }元/MWh</span><br>`
      })
      let newContent = ''
      content.forEach((item: any) => {
        newContent = newContent + item
      })
      context = `<div>${params[0].name}</div><div>${newContent}</div>`
      return context
    },
  },
  legend: {
    show: true,
    width: '100%',
    x: 'right',
    y: 'top',
    type: 'scroll',
  },
  grid: {
    top: '15%',
    left: '8%',
    right: '8%',
    bottom: '5%',
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    splitLine: {
      show: false,
    },
    axisTick: {
      show: false, //坐标轴刻度线
    },
    data: [1, 1, 1, 1, 1],
  },
  yAxis: {
    type: 'value',
    name: '日前中长期(元/MWh)',
    nameTextStyle: {
      // y轴name的样式调整
      fontSize: 14,
    },
  },
  series: [
    {
      name: '日前中长期',
      type: 'line',
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      data: [1, 1, 1, 1, 1],
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        lineStyle: {
          width: 2,
          type: 'solid', //'dotted'虚线 'solid'实线
        },
      },
    }
  ],
})
const volPriceOption3Deal = echartsConfig(volPriceOption3.value)
// 套利分析
const straddle1 = ref<any>({
  tooltip: {
    trigger: 'axis',
    formatter: function (params: any) {
      let context
      let content = params.map((item: any) => {
        if (item.seriesName == '日前电力' || item.seriesName == '实时电力') {
          return `${item.marker}<span>${item.seriesName
            }</span>:<span style="margin-left: 20px;float: right">${item.value || '--'
            }MW</span><br>`
        } else {
          return `${item.marker}<span>${item.seriesName
            }</span>:<span style="margin-left: 20px;float: right">${item.value || '--'
            }元/MWh</span><br>`
        }
      })
      let newContent = ''
      content.forEach((item: any) => {
        newContent = newContent + item
      })
      context = `<div>${params[0].name}</div><div>${newContent}</div>`
      return context
    },
  },
  legend: {
    show: true,
    width: '100%',
    x: 'right',
    y: 'top',
    type: 'scroll',
  },
  grid: {
    top: '15%',
    left: '8%',
    right: '8%',
    bottom: '5%',
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    splitLine: {
      show: false,
    },
    axisTick: {
      show: false, //坐标轴刻度线
    },
    data: [1, 1, 1, 1, 1],
  },
  yAxis: [
    {
      type: 'value',
      name: 'MW',
      nameTextStyle: {
        // y轴name的样式调整
        fontSize: 14,
      },
    },
    {
      type: 'value',
      name: '价格(元/MWh)',
      nameTextStyle: {
        // y轴name的样式调整
        fontSize: 14,
      },
    }
  ],
  series: [
    {
      name: '日前电力',
      yAxisIndex: 1,
      type: 'line',
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      data: [1, 1, 1, 1, 1],
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        lineStyle: {
          width: 2,
          type: 'solid', //'dotted'虚线 'solid'实线
        },
      },
    },
    {
      name: '实时电力',
      yAxisIndex: 1,
      type: 'line',
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      data: [1, 1, 1, 1, 1],
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        lineStyle: {
          width: 2,
          type: 'solid', //'dotted'虚线 'solid'实线
        },
      },
    },
    {
      name: '日前出清电价',
      yAxisIndex: 1,
      type: 'line',
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      data: [1, 1, 1, 1, 1],
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        lineStyle: {
          width: 2,
          type: 'solid', //'dotted'虚线 'solid'实线
        },
      },
    },
    {
      name: '实时出清电价',
      yAxisIndex: 1,
      type: 'line',
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      data: [1, 1, 1, 1, 1],
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        lineStyle: {
          width: 2,
          type: 'solid', //'dotted'虚线 'solid'实线
        },
      },
    },
  ],
})
const straddleOption1Deal = echartsConfig(straddle1.value)
const straddle2 = ref<any>({
  tooltip: {
    trigger: 'axis',
    formatter: function (params: any) {
      let context
      let content = params.map((item: any) => {
        return `${item.marker}<span>${item.seriesName
          }</span>:<span style="margin-left: 20px;float: right">${item.value || '--'
          }元</span><br>`
      })
      let newContent = ''
      content.forEach((item: any) => {
        newContent = newContent + item
      })
      context = `<div>${params[0].name}</div><div>${newContent}</div>`
      return context
    },
  },
  legend: {
    show: true,
    width: '100%',
    x: 'right',
    y: 'top',
    type: 'scroll',
  },
  grid: {
    top: '15%',
    left: '8%',
    right: '8%',
    bottom: '5%',
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    splitLine: {
      show: false,
    },
    axisTick: {
      show: false, //坐标轴刻度线
    },
    data: [1, 1, 1, 1, 1],
  },
  yAxis: {
    type: 'value',
    name: '收益(元)',
    nameTextStyle: {
      // y轴name的样式调整
      fontSize: 14,
    },
  },
  series: [
    {
      name: '套利收益',
      type: 'line',
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      data: [1, 1, 1, 1, 1],
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        lineStyle: {
          width: 2,
          type: 'solid', //'dotted'虚线 'solid'实线
        },
      },
    }
  ],
})
const straddleOption2Deal = echartsConfig(straddle2.value)
const straddle3 = ref<any>({
  tooltip: {
    trigger: 'axis',
    formatter: function (params: any) {
      let context
      let content = params.map((item: any) => {
        if (item.seriesName === '套利量差') {
          return `${item.marker}<span>${item.seriesName
            }</span>:<span style="margin-left: 20px;float: right">${item.value || '--'
            }MWh</span><br>`
        } else {
          return `${item.marker}<span>${item.seriesName
            }</span>:<span style="margin-left: 20px;float: right">${item.value || '--'
            }元/MWh</span><br>`
        }
      })
      let newContent = ''
      content.forEach((item: any) => {
        newContent = newContent + item
      })
      context = `<div>${params[0].name}</div><div>${newContent}</div>`
      return context
    },
  },
  legend: {
    show: true,
    width: '100%',
    x: 'right',
    y: 'top',
    type: 'scroll',
  },
  grid: {
    top: '15%',
    left: '8%',
    right: '8%',
    bottom: '5%',
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    splitLine: {
      show: false,
    },
    axisTick: {
      show: false, //坐标轴刻度线
    },
    data: [1, 1, 1, 1, 1],
  },
  yAxis: [
    {
      type: 'value',
      name: 'MWh',
      nameTextStyle: {
        // y轴name的样式调整
        fontSize: 14,
      },
    },
    {
      type: 'value',
      name: '差价(元/MWh)',
      nameTextStyle: {
        // y轴name的样式调整
        fontSize: 14,
      },
    },
  ],
  series: [
    {
      name: '套利量差',
      type: 'line',
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      data: [1, 1, 1, 1, 1],
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        lineStyle: {
          width: 2,
          type: 'solid', //'dotted'虚线 'solid'实线
        },
      },
    },
    {
      name: '套利价差',
      yAxisIndex: 1,
      type: 'line',
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      data: [1, 1, 1, 1, 1],
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        lineStyle: {
          width: 2,
          type: 'solid', //'dotted'虚线 'solid'实线
        },
      },
    }
  ],
})
const straddleOption3Deal = echartsConfig(straddle3.value)

// 量价分析切换
const volPriceActive = ref<any>('中长期比日前')
function volPriceChange() { }
// 出清结果
const clearResultActive = ref<any>('支出分析')
function clearResultChange() { }
// 支出分析框
const outputAnalyse = ref<any>([
  {
    name: '总支出(万元)',
    value: 0
  },
  {
    name: '中长期支出(万元)',
    value: 0
  },
  {
    name: '日前现货支出(万元)',
    value: 0
  },
  {
    name: '实时现货支出(万元)',
    value: 0
  },
  {
    name: '度电支出(元/MWh)',
    value: 0
  },
])

//获取日前价格
async function getDayAheadDataInfo() {
  priceScenceOption.value.series[0].data = []
  priceScenceOption.value.xAxis.data = []
  const res = await getDayAheadData({
    date: pickerDate.value,
    dataType: dayheadScence.value
  })
  priceScenceOption.value.series[0].data = res.yseries
  priceScenceOption.value.xAxis.data = res.xseries
  priceScenceOption.value.series[0].name = dayheadScence.value
}
//获取日内价格
async function getRealTimeDataInfo() {
  priceScenceOption.value.series[1].data = []
  priceScenceOption.value.xAxis.data = []
  const res = await getRealTimeData({
    date: pickerDate.value,
    dataType2: dayinScence.value
  })
  priceScenceOption.value.series[1].data = res.yseries
  priceScenceOption.value.xAxis.data = res.xseries
  priceScenceOption.value.series[1].name = dayinScence.value
}
//获取日前日内差
async function getDeviationDataInfo() {
  offsetOption.value.series[0].data = []
  offsetOption.value.xAxis.data = []
  const res = await getDeviationData({
    date: pickerDate.value,
    dataType: dayheadScence.value,
    dataType2: dayinScence.value,
  })
  offsetOption.value.series[0].data = res.yseries
  offsetOption.value.xAxis.data = res.xseries
}
//获取市场供需详情
async function getMarketSupplyDemand() {
  supplyOption.value.series[0].data = []
  supplyOption.value.series[1].data = []
  supplyOption.value.series[2].data = []
  supplyOption.value.series[3].data = []
  supplyOption.value.series[4].data = []
  supplyOption.value.series[5].data = []
  supplyOption.value.xAxis.data = []
  const res = await marketSupplyDemand({
    date: pickerDate.value,
    dataType: dayheadScence.value,
    dataType2: dayinScence.value,
  })
  supplyOption.value.xAxis.data = res['时间']
  supplyOption.value.series[0].data = res['省调负荷']
  supplyOption.value.series[1].data = res['火电竞价空间']
  supplyOption.value.series[2].data = res['光伏出力']
  supplyOption.value.series[3].data = res['联络线计划']
  supplyOption.value.series[4].data = res['非市场机组出力']
  supplyOption.value.series[5].data = res['风电出力']
}

//获取实际电量预测
async function getElectricalVoltageForcastInfo() {
  predictedEleOption.value.series[0].data = []
  predictedEleOption.value.xAxis.data = []
  const res = await getElectricalVoltageForcast({
    date: pickerDate.value,
    dataType3: predictedElec.value
  })
  predictedEleOption.value.series[0].data = res.yseries
  predictedEleOption.value.xAxis.data = res.xseries
}
//获取日前申报策略
async function getElectricalVoltageApplyInfo() {
  predictedEleOption.value.series[1].data = []
  predictedEleOption.value.xAxis.data = []
  const res = await getElectricalVoltageApply({
    date: pickerDate.value,
    dataType3: predictedElec.value,
    dataType4: reportingStrategy.value
  })
  predictedEleOption.value.series[1].data = res.yseries
  predictedEleOption.value.xAxis.data = res.xseries
}
//获取支出统计
async function getPayoutStatisInfo() {
  const res = await payoutStatis({
    date: pickerDate.value,
    dataType: dayheadScence.value,
    dataType2: dayinScence.value,
    dataType3: predictedElec.value,
    dataType4: reportingStrategy.value
  })
  outputAnalyse.value[0].value = res['总支出'] == '-' ? '-' : (res['总支出'] / 10000).toFixed(2)
  outputAnalyse.value[1].value = res['中长期支出'] == '-' ? '-' : (res['中长期支出'] / 10000).toFixed(2)
  outputAnalyse.value[2].value = res['日前现货支出'] == '-' ? '-' : (res['日前现货支出'] / 10000).toFixed(2)
  outputAnalyse.value[3].value = res['实时现货支出'] == '-' ? '-' : (res['实时现货支出'] / 10000).toFixed(2)
  outputAnalyse.value[4].value = res['度电支出'] == '-' ? '-' : res['度电支出']
}
//获取支出明细
async function getPyaoutDetailInfo() {
  const res = await pyaoutDetail({
    date: pickerDate.value,
    dataType: dayheadScence.value,
    dataType2: dayinScence.value,
    dataType3: predictedElec.value,
    dataType4: reportingStrategy.value
  })
  stateExpendOption.value.xAxis.data = res.xseries
  stateExpendOption.value.series[0].data = res.yseries
}
const dayheadEnergy = ref<any>()
const dayheadPrice = ref<any>()
//获取量价统计
async function getQuantitypriceStatisInfo() {
  const res = await quantitypriceStatis({
    date: pickerDate.value,
    dataType: dayheadScence.value,
    dataType2: dayinScence.value,
    dataType3: predictedElec.value,
    dataType4: reportingStrategy.value
  })
  dayheadPrice.value = res['日前均价']
  dayheadEnergy.value = res['日前电力']
}
//获取量价明细
async function getquantitypriceDetailInfo() {
  const res = await quantitypriceDetail({
    date: pickerDate.value,
    dataType: dayheadScence.value,
    dataType2: dayinScence.value,
    dataType3: predictedElec.value,
    dataType4: reportingStrategy.value
  })
  volPriceOption1.value.xAxis.data = res['时间']
  volPriceOption1.value.series[0].data = res['日前价格']
  volPriceOption1.value.series[1].data = res['日前电力']
}
onMounted(async () => {
  //获取日前价格
  getDayAheadDataInfo()
  //获取日内价格
  getRealTimeDataInfo()
  //获取日前日内差
  getDeviationDataInfo()
  //获取市场供需详情
  getMarketSupplyDemand()
  //获取实际电量预测
  getElectricalVoltageForcastInfo()
  //获取日前申报策略
  getElectricalVoltageApplyInfo()
  //获取支出统计
  getPayoutStatisInfo()
  //获取支出明细
  getPyaoutDetailInfo()
  //获取量价统计
  getQuantitypriceStatisInfo()
  //获取量价统计
  getquantitypriceDetailInfo()
})
</script>
  
<style scoped lang="scss">
//el-card头部样式
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .header-title {
    padding: 0px 10px;
    border-left: 3px solid #c3d7f0;
    color: #303133;
    /* 文字/18加粗 */
    font-size: 17px;
    font-weight: 700;
    line-height: 22px;
  }
}

.top-item-wrap {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;

  .top-item,
  .top-item2 {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 20%;
    height: 100px;
    margin-right: 20px;
    padding: 0 32px;
    background-color: #F3F5F6;

    .title {
      color: #606266;
      font-size: 14px;
    }

    .value {
      color: #303133;
      font-size: 20px;
      font-weight: 700;
    }

    &:last-child {
      margin-right: 0px;
    }
  }

  .top-item2 {
    width: 60%;
    height: 180px;
    display: flex;
    flex-direction: column;
    justify-content: center;

    &>div {
      width: 100%;
      display: flex;
      justify-content: space-between;

      .inner-wrap {
        width: 30%;
      }
    }

    &-2 {
      width: 20%;

      .inner-wrap {
        width: 100% !important;
      }
    }
  }
}

:deep(.daoru) {
  .el-upload-list.el-upload-list--text {
    display: none;
  }
}
</style>