import dayjs from "dayjs";
import { useUserStore } from "@/store/modules/user";
const userStore = useUserStore();
const followStageOptions = userStore.getDictList.find(
  i => i.code === "fiveCustomStatus"
)?.items;
export const columns: TableColumnList = [
  {
    label: "序号",
    align: "center",
    width: 80,
    type: "index"
  },
  {
    label: "机会名称",
    prop: "title"
  },
  {
    label: "客户",
    prop: "customName"
  },
  {
    label: "所在区域",
    prop: "areaName"
  },
  {
    label: "商机描述",
    prop: "content"
  },
  {
    label: "跟进阶段",
    prop: "followStage",
    formatter: ({ followStage }) =>
      followStage !== null
        ? followStageOptions.find(item => item.value == followStage).label
        : ""
  },
  {
    label: "营销人员",
    prop: "annualElectricity"
  },
  {
    label: "年用电量",
    prop: "yearlyElectricityQty"
  },
  {
    label: "合同电量",
    prop: "contractElectricityQty"
  },
  {
    label: "营销区域",
    prop: "marketingAreaName"
  },
  {
    label: "预计交易日期",
    prop: "predictTradeDate",
    width: 130,
    formatter: ({ predictTradeDate }) =>
      predictTradeDate ? dayjs(predictTradeDate).format("YYYY-MM-DD") : ""
  },
  {
    label: "已有合同电量",
    prop: "existElectricityQty",
    width: 130,
  }
];
