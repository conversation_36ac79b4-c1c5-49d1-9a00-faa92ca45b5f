import { request } from '@/utils/request'

// 查询月度每日D-2策略信息是否存在
export const queryMonthDayD2StrategyAPI = (month: any) => {
  const res = request.get<any>({
    url: `/d2strategy/queryForMonth/${month}`,
  })
  return res
}

// 查询D-2策略信息
export const queryD2StrategyAPI = (transactionDate: any) => {
  const res = request.get<any>({
    url: `/d2strategy/query/${transactionDate}`,
  })
  return res
}

// 保存D-2策略信息
export const saveD2StrategyAPI = (data: any) => {
  const res = request.post<any>({
    url: '/d2strategy/save',
    data,
  })
  return res
}

// 获取相似日24小时
export const spotPriceForecast = (transactionDate: any) => {
  const res = request.get<any>({
    url: `
/d2strategy/queryTotalContractElectricityForSimilarDay/${transactionDate}`,
  })
  return res
}

// 获取D-2策略数据用于峰平谷
export const getD2StrategyDataForPeakValley = (transactionDate: any) => {
  const res = request.get<any>({
    url: `/d2strategy/getD2StrategyDataForPeakValley/${transactionDate}`,
  })
  return res
}

//
// //二.导出现货电价预测列表
// export const exportSpotPriceForecast = (data: any) => {
//   const res = request.post<any>(
//     {
//       url: '/web/spotTradingDecision/exportSpotPriceForecast',
//       responseType: 'blob',
//       data,
//     },
//     { isTransformResponse: false },
//   )
//   return res
// }
