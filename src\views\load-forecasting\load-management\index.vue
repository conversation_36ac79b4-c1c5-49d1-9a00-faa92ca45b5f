<template>
  <section>
    <div class="app-content-container container-h">
      <div class="flex justify-between mb-[20px]">
        <div class="flex items-center">
          <div class="app-form-group">
            <div class="ml-[20px]">
              <span>日期范围选择：</span>
              <el-date-picker style="width: 140px" v-model="searchInfo.startDate" type="date" :clearable="false"
                placeholder="请选择" format="YYYY-MM-DD" value-format="x" />
            </div>
            <div>
              <span class="mx-[5px]">-</span>
              <el-date-picker style="width: 140px" :clearable="false" v-model="searchInfo.endDate" type="date"
                placeholder="请选择" format="YYYY-MM-DD" value-format="x" />
            </div>
            <div class="ml-[20px]">
              <span>客户名称：</span>
              <el-input v-model="searchInfo.name" clearable placeholder="请输入客户名称" class="filter-item" />
            </div>
          </div>
          <div class="flex items-center app-btn-group">
            <el-button class="filter-item" type="primary" @click="getList">查询</el-button>
            <el-button class="filter-item" @click="handleReset">重置</el-button>
<!--            <div class="ml-[20px]">-->
<!--              数据完整度：<span style="color: var(&#45;&#45;el-color-primary)">{{-->
<!--                dataIntegrity-->
<!--              }}</span>%-->
<!--            </div>-->
          </div>
        </div>
        <div class="flex">
          <!-- <el-upload ref="uploadRef1" class="mr-[10px]" :limit="1" :show-file-list="false" with-credentials :headers="header" @change="handleChange1"
            :action="actionUrl" :on-success="uploadSuccess1">
            <el-button>导入申报电量</el-button>
          </el-upload> -->
          <el-upload ref="uploadRef2" :limit="1" with-credentials :show-file-list="false" :headers="header" @change="handleChange2" :action="actionUrlHour" :on-success="uploadSuccess2" :on-error="uploadError2" :disabled="uploadLoading" :before-upload="beforeHourUpload">
            <el-button v-if="!uploadLoading" type="primary">导入分时电量数据</el-button>
            <el-button v-else type="primary" loading disabled>导入分时电量数据中...</el-button>
          </el-upload>
        </div>
      </div>
      <pure-table :columns="columns" border stripe :loading="loading" :data="tableData" :pagination="pagination"
        @page-size-change="onSizeChange" @page-current-change="onCurrentChange" @sort-change="handlerSortChange">
        <template #accountName="{ row }">
          <a @click="handleDetail(row)" style="color: #007bf7">{{
            row.customName
          }}</a>
        </template>
        <template #operation="{ row }">
          <el-button link type="primary" size="small" @click="handleDetail(row)">数据明细</el-button>
        </template>
      </pure-table>
    </div>
    <el-dialog width="60%" append-to-body v-model="visible" destroy-on-close title="数据明细">
      <Detail :search-date="{
        startDate: searchInfo.startDate,
        endDate: searchInfo.endDate
      }" :id="detailId" :name="customName" />
    </el-dialog>
  </section>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance } from "vue";
import Detail from "./detail.vue";
import { columns } from "./components/data";
import type { PaginationProps } from "@pureadmin/table";
import { delay } from "@pureadmin/utils";
import { getToken } from "@/utils/auth";
import type { UploadUserFile } from "element-plus";
import {
  getForecastQueryPageApi,
  getForecastAnalysisDateApi,
  getDataIntegrityApi
} from "@/api/load-forecasting/index";
import { ElMessage } from "element-plus";
import dayjs from "dayjs";
import { useUserStore } from '@/store/modules/user'
import {useRouter} from "vue-router";
const userStore = useUserStore()
defineOptions({
  name: "LoadForecastingLoadManagement"
});
const visible = ref<boolean>(false);
const customName = ref("");
const { BaseUrl } =
  getCurrentInstance().appContext.config.globalProperties.$config;
const actionUrl = BaseUrl + "/custom/power/upload";
const actionUrlHour = BaseUrl + "/custom/power/upload";
// 上传请求头
const header = ref({
  Authorization: getToken().accessToken,
  token: userStore.token,
  longToken: userStore.longToken
});
const fileList1 = ref<UploadUserFile[]>([]);
const fileList2 = ref<UploadUserFile[]>([]);
const title = ref<string>("新增");
const loading = ref(false);
const tableData = ref([]);
const detailId = ref<string>("");
/** 分页配置 */
const pagination = reactive<PaginationProps>({
  pageSize: 10,
  currentPage: 1,
  pageSizes: [10, 20, 40, 50],
  total: 0,
  align: "right",
  background: true,
  small: false
});
const searchInfo = reactive({
  name: undefined,
  startDate: dayjs().subtract(30, "day").valueOf(),
  endDate: dayjs().valueOf(),
  pageNo: 1,
  pageSize: 10,
  // 排序字段 1 -- 峰段电量 2 -- 平段电量 3 -- 低谷电量 4 -- 尖峰电量 5 -- 总电量
  sortField: '',
  // 排序类型 1 -- 升序 2 -- 降序
  sortType: ''
});
// 数据完整度
const dataIntegrity = ref<string>("");
async function getNewDate() {
  // const res = await getForecastAnalysisDateApi();
  // searchInfo.startDate = dayjs(
  //   dayjs(res.data.maxPowerDate).subtract(30, "day")
  // ).valueOf();
  // searchInfo.endDate = dayjs(res.data.maxPowerDate).valueOf();
}
async function getList() {
  loading.value = true;
  try {
    const { data } = await getForecastQueryPageApi(searchInfo);
    pagination.total = Number(data.totalCount);
    tableData.value = data.data;
    await getDataIntegrity();
    delay(600).then(() => {
      loading.value = false;
    });
  } catch {
    delay(600).then(() => {
      loading.value = false;
    });
  }
}
async function getDataIntegrity() {
  const res = await getDataIntegrityApi(searchInfo);
  dataIntegrity.value = res.data.dataIntegrity;
}

const { push } = useRouter()

function handleDetail(row) {
  console.log(row)
  detailId.value = row.id;
  customName.value = row.customName;
  // push(`load-forecasting/load-management/detail/?detailId=${detailId.value}&customName=${customName.value}`);
  // console.log(dayjs(searchInfo.startDate).format('YYYY-MM'))
  push({name: 'LoadForecastingLoadManagementDetail', query: { detailId: detailId.value, customName: customName.value, startDate: dayjs(searchInfo.startDate).format('YYYY-MM') }});
  // visible.value = true;
}
function handleReset() {
  searchInfo.name = undefined
  getNewDate();
  getList();
}

function onSizeChange(val) {
  searchInfo.pageSize = val;
  getList();
}

function onCurrentChange(val) {
  searchInfo.pageNo = val;
  getList();
}

const handlerSortChange = (item:any) => {
  // console.log("aaaaa",item)
  if (item.order !== null) {
    searchInfo.sortField = ''
    searchInfo.sortType = ''
    switch (item.prop)
    {
      case "summitStartPower": searchInfo.sortField = '1';break;
      case "flatSegmentStartPower": searchInfo.sortField = '2';break;
      case "lowEbbStartPower": searchInfo.sortField = '3';break;
      case "peakStartPower": searchInfo.sortField = '4';break;
      case "totalPower": searchInfo.sortField = '5';break;
    }
    if(item.order.includes("desc")){
      searchInfo.sortType = "2"
    }else{
      searchInfo.sortType = "1"
    }
    // searchInfo.sort.push(item.prop,order)
    // console.log(searchInfo)
    getList()
  } else {
    searchInfo.sortField = ''
    searchInfo.sortType = ''
    getList()
  }

}
function handleChange1(data) {
    if (data.response && data.response.code && data.response.code === "200") {
        ElMessage({
            message: data.response.message,
            type: "info"
        });
        fileList1.value = [];
    }
}
const uploadLoading = ref(false)
function handleChange2(data) {
  if (data.response && data.response.code && data.response.code === "200") {
    ElMessage({
      message: data.response.message,
      type: "info"
    });
    fileList2.value = [];
  }
  uploadLoading.value = false
}

const uploadRef1 = ref()

const uploadSuccess1 = () => {
    console.log(uploadRef1.value)
    uploadRef1.value.clearFiles(); //上传成功之后清除历史记录
}
const uploadRef2 = ref()

const uploadSuccess2 = (response) => {
  console.log(response)
  if (response.code == "200") {
    uploadLoading.value = false
  }
  uploadRef2.value.clearFiles(); //上传成功之后清除历史记录
}
const uploadError2 = (response) => {
  uploadLoading.value = false
  uploadRef2.value.clearFiles(); //上传成功之后清除历史记录
}

const beforeHourUpload = () => {
  uploadLoading.value = true
}
onMounted(async () => {
  await getNewDate();
  getList();
  // getDataIntegrity();
});
</script>

<style lang="scss" scoped></style>
