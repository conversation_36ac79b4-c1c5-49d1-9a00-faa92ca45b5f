import { defineStore } from "pinia";
import { store } from "@/store";
import { setType } from "./types";
import { getConfig } from "@/config";

export const useSettingStore = defineStore({
  id: "pure-setting",
  state: (): setType => ({
    title: getConfig().Title,
    fixedHeader: getConfig().FixedHeader,
    hiddenSideBar: getConfig().HiddenSideBar,
    embeddedMode: localStorage.getItem('system-embedded-mode') === 'true'
  }),
  getters: {
    getTitle(state) {
      return state.title;
    },
    getFixedHeader(state) {
      return state.fixedHeader;
    },
    getHiddenSideBar(state) {
      return state.hiddenSideBar;
    },
    getEmbeddedMode(state) {
      return state.embeddedMode;
    },
    // 综合判断是否应该隐藏侧边栏（原有配置或嵌入模式）
    getShouldHideSidebar(state) {
      return state.hiddenSideBar || state.embeddedMode;
    }
  },
  actions: {
    CHANGE_SETTING({ key, value }) {
      if (Reflect.has(this, key)) {
        this[key] = value;
      }
    },
    changeSetting(data) {
      this.CHANGE_SETTING(data);
    },
    // 设置嵌入模式
    setEmbeddedMode(enabled: boolean) {
      this.embeddedMode = enabled;
      if (enabled) {
        localStorage.setItem('system-embedded-mode', 'true');
      } else {
        localStorage.removeItem('system-embedded-mode');
      }
    },
    // 初始化嵌入模式状态（从localStorage读取）
    initEmbeddedMode() {
      this.embeddedMode = localStorage.getItem('system-embedded-mode') === 'true';
    }
  }
});

export function useSettingStoreHook() {
  return useSettingStore(store);
}
