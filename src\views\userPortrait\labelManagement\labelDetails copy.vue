<template>
    <div>
        <div style="display: flex; justify-content: space-between;">
            <div>
                <el-button style="margin: 20px 30px 10px 20px;" @click="goBackAndClearStack">返回</el-button>
                <span>分群1</span><span> 分群用户明细</span>
            </div>
            <div>
                <!-- <el-select v-model="selectType" placeholder="不限" style="width: 130px" >
                        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select> -->
                <el-button style="margin-left: 20px" @click="handleList">
                    <b>列表显示</b>
                </el-button>
            </div>

        </div>
        <el-table :data="tableData" style="width: 100%; margin-top: 20px" row-key="id" border
            :header-cell-style="{ background: '#f2f3f5' }" align="center">
            <el-table-column prop="name" label="企业名称" />
            <el-table-column prop="monthlyAverageElectricity" label="电量" />
            <el-table-column prop="electricalNature" label="用电性质" />
            <el-table-column prop="industryId" label="用户行业" />
            <el-table-column prop="areaId" label="所在地区" />
            <el-table-column prop="customerSource" label="用户来源" />
        </el-table>
        <div class="pagenation">
            <el-pagination v-model:currentPage="condition.pageNo" v-model:page-size="condition.pageSize"
                :page-sizes="[5, 10, 15, 20]" :disabled="false" :background="false"
                layout="total, prev, pager, next, sizes" :total="condition.total" @change="handleChange"   @current-change="handleChange"/>
        </div>
        <showList ref="refShowList"></showList>
    </div>
</template>

<script setup lang="ts">
import { nextTick, onMounted, reactive, ref } from 'vue'
import showList from './components/showList.vue'
import { useRouter,useRoute } from "vue-router";
import { customLabelQueryPage } from '@/api'

const refShowList = ref(null);
const { push, go} = useRouter();
const route = useRoute();
const handleList = () => {
    refShowList.value.handleShowList()
}
let condition = reactive({
  pageNo: 1,
  pageSize: 10,
  total: 0,
});
const options = [
    {
        value: '0',
        label: '微型',
    },
    {
        value: '1',
        label: '小型',
    },
    {
        value: '2',
        label: '大型',
    }        
]

const selectType = ref('0')

function goBackAndClearStack() {
    if (window.history.length <= 1) {
        push({ path: "/userGrouping" });
        return false;
    } else {
        go(-1);
    }
    // push({ path: "/userPortrait" });
}
const tableData = ref([])

const getDetailsList = async () => {
    const sliceId = route.query.sliceId;

    console.log(sliceId)
    const res = await customLabelQueryPage({sliceId:sliceId})
    console.log(res)
    condition.total =  Number(res.totalCount) 
    tableData.value = res.data
}
const handleChange = (val:any) => {
    condition = val
    getDetailsList();

};

onMounted(() => {
    getDetailsList()
})

// defineExpose({
//     getDetailsList
// });

</script>

<style scoped lang="scss">
.pagenation {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
</style>