import { request } from '@/utils/request'
import dayjs from 'dayjs'

// const preBaseUrl = '/import'
const preBaseUrl = ''
export const reqUploadForm = (
  datas: FormData,
  url: string,
  dateUrl?: string,
  id?: string,
  schemaName?: string,
  date?: string
) => {
  if (date && schemaName == 't_daily_generation_report') {
    return request.post<boolean>({
      url: `${preBaseUrl}/${url}?date=${date}`,
      data: datas,
      headers: { 'Content-Type': 'multipart/form-data' },
    })
  }
  if (dateUrl && schemaName) {
    return request.post<boolean>({
      url: `/${url}?date=${dateUrl}&schemeName=${schemaName}`,
      data: datas,
      headers: { 'Content-Type': 'multipart/form-data' },
    })
  }
  if (id && !dateUrl) {
    const res = request.post<boolean>({
      url: `/${url}?stationId=${id}`,
      data: datas,
      headers: { 'Content-Type': 'multipart/form-data' },
    })
    return res
  }
  if (id && dateUrl) {
    const res = request.post<boolean>({
      url: `/${url}?date=${dateUrl}&&stationId=${id}`,
      data: datas,
      headers: { 'Content-Type': 'multipart/form-data' },
    })
    return res
  }
  if (dateUrl && !id) {
    const res = request.post<boolean>({
      url: `/${url}?date=${dateUrl}`,
      data: datas,
      headers: { 'Content-Type': 'multipart/form-data' },
    })
    return res
  }
  const res = request.post<boolean>({
    url: `${preBaseUrl}/${url}`,
    data: datas,
    headers: { 'Content-Type': 'multipart/form-data' },
  })
  return res
}
/**
 * 获取导入表的树形结构
 * @returns
 */
export const reqUploadTableData = (date: string) => {
  const res = request.get<any>({
    url: `${preBaseUrl}/tableInfo/importTableStructure/?date=${date}`,
    // url: `${preBaseUrl}/tableInfo/importTableStructure`,
  })
  return res
}
/**
 * 当月每天数据统计
 * @param date
 * @returns
 */
export const reqUploadMounth = (date: string) => {
  // console.log('reqUploadMounthLogDetail', dayjs(date).format('YYYY-MM-DD'))
  let _date = dayjs(date).format('YYYY-MM-DD')
  const year = _date.split('-')[0]
  const month = _date.split('-')[1]
  const res = request.get<any>({
    url: `${preBaseUrl}/importStatics/daily?year=${year}&&month=${month}`,
  })
  return res
}
/**
 * 当月当天导入数据的日志
 * @param date
 * @returns
 */
export const reqUploadMounthLog = (date: string) => {
  let _date = dayjs(date).format('YYYY-MM-DD')
  // console.log('reqUploadMounthLogDetail', _date)
  const res = request.get<any>({
    url: `${preBaseUrl}/importStatics/dailyLog?date=${_date}`,
  })
  return res
}
/**
 * 当月导入数据统计
 * @param date
 * @returns
 */
export const reqUploadMounthLogDetail = (date: string) => {
  let _date = dayjs(date).format('YYYY-MM-DD')
  // console.log('reqUploadMounthLogDetail', _date)
  const year = _date.split('-')[0]
  const month = _date.split('-')[1]
  const res = request.get<any>({
    url: `${preBaseUrl}/importStatics/monthly?year=${year}&month=${month}`,
  })
  return res
}

// 市场数据模板下载通用接口
export const downloadMarketDataTemplateAPI = (url: string) => {
  return request.post<any>(
    {
      url: `/${url}`,
      responseType: 'blob',
    },
    { isTransformResponse: false },
  )
}

// 实时出清结果数据预览
export const reqPreviewRealTimeSettlement = (date: any) => {
  return request.get<any>({
    url: `${preBaseUrl}/TRealtimeMaketclearResult/getRealtimeMarketclearResultByDate?date=${date}`,
    responseType: 'blob'
  },
      { isTransformResponse: false }
  )
}

// 实时调频出清结果数据预览
export const reqPreviewFrequencySettlement = (date: any) => {
  return request.get<any>({
    url: `${preBaseUrl}/TRealtimeFmMarketclearResult/getRealtimeFmMarketclearResultByDate?date=${date}`,
    responseType: 'blob'
  },
      { isTransformResponse: false }
  )
}

// 日前调频出清结果数据预览
export const reqPreviewFrequency24hSettlement = (date: any) => {
  return request.get<any>({
    url: `${preBaseUrl}/TDayaheadFmMarketclearResult/getDayaheadFmMarketclearResultByDate?date=${date}`,
    responseType: 'blob'
  },
      { isTransformResponse: false }
  )
}

// 日前出清结果数据预览
export const reqPreviewDailySettlement = (date: any) => {
  return request.get<any>({
    url: `${preBaseUrl}/TDayaheadMarketclearResult/getDayaheadMarketclearResultByDate?date=${date}`,
    responseType: 'blob'
  },
      { isTransformResponse: false }
  )
}

// 24时段上网电量数据预览
export const reqPreview24hDailySettlement = (date: any) => {
  return request.get<any>({
    url: `${preBaseUrl}/TDayOnlineElectricityQuantity/getDayOnlineElectricityQuantityByDate?date=${date}`,
    responseType: 'blob'
  },
      { isTransformResponse: false }
  )
}

// 现货日报数据预览
export const reqPreviewSpotDailySettlement = (date: any) => {
  return request.get<any>({
    url: `${preBaseUrl}/spotReport/getDataByDate?date=${date}`,
    responseType: 'blob'
  },
      { isTransformResponse: false }
  )
}

// 合同明细数据预览
export const reqPreviewContractDetails = (date: any) => {
  return request.get<any>({
    url: `${preBaseUrl}/contracts/getDataByDate?date=${date}`,
    responseType: 'blob'
  },
      { isTransformResponse: false }
  )
}

// 调度披露日发电情况预览
export const reqPreviewDailyGenerationReportAPI = (date: any) => {
  return request.get<any>({
    url: `${preBaseUrl}/disclose/getDailyGenerationReportByDate?date=${date}`,
    responseType: 'blob'
  },
      { isTransformResponse: false }
  )
}

// 导出excel
export const reqExportContractExcel = (data: any) => {
  return request.post<any>(
    {
      url: `/contractDetails/exportContractDetails`,
      data,
      responseType: 'blob',
    },
    { isTransformResponse: false },
  )
}
