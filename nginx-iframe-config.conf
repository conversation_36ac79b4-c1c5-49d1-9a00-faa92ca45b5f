# Nginx配置 - 支持iframe嵌入的系统配置
# 适用于Vue项目的生产环境部署

server {
    listen 80;
    server_name your-domain.com;  # 替换为您的域名
    
    # 如果需要HTTPS，取消注释以下配置
    # listen 443 ssl http2;
    # ssl_certificate /path/to/your/certificate.crt;
    # ssl_certificate_key /path/to/your/private.key;
    
    # 项目根目录
    root /var/www/html/dist;  # 替换为您的项目构建后的dist目录路径
    index index.html;
    
    # 启用gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # 处理Vue Router的history模式
    location / {
        try_files $uri $uri/ /index.html;
        
        # 关键配置：移除或修改CSP头部，允许iframe嵌入
        add_header X-Frame-Options "ALLOWALL" always;
        
        # 设置Content Security Policy，允许被任何域名嵌入
        # 注意：这会降低安全性，生产环境建议指定具体域名
        add_header Content-Security-Policy "frame-ancestors *;" always;
        
        # 或者指定特定的父域名（推荐用于生产环境）
        # add_header Content-Security-Policy "frame-ancestors http://***********:9291 https://***********:9291 http://your-parent-domain.com https://your-parent-domain.com;" always;
        
        # 允许跨域请求（如果需要）
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization" always;
        add_header Access-Control-Expose-Headers "Content-Length,Content-Range" always;
        
        # 处理预检请求
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
            add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization";
            add_header Access-Control-Max-Age 1728000;
            add_header Content-Type "text/plain; charset=utf-8";
            add_header Content-Length 0;
            return 204;
        }
    }
    
    # 静态资源缓存配置
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Frame-Options "ALLOWALL" always;
        add_header Content-Security-Policy "frame-ancestors *;" always;
    }
    
    # API代理配置（如果需要）
    location /api/ {
        # 代理到后端API服务器
        proxy_pass http://your-backend-server:port/;  # 替换为您的后端服务器地址
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 移除可能的CSP限制
        proxy_hide_header X-Frame-Options;
        proxy_hide_header Content-Security-Policy;
        
        # 添加允许iframe的头部
        add_header X-Frame-Options "ALLOWALL" always;
        add_header Content-Security-Policy "frame-ancestors *;" always;
        
        # CORS配置
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization" always;
    }
    
    # 安全配置
    location ~ /\. {
        deny all;
    }
    
    # 日志配置
    access_log /var/log/nginx/your-app-access.log;
    error_log /var/log/nginx/your-app-error.log;
}

# 如果您的系统运行在不同端口，可以添加额外的server块
server {
    listen 8187;  # 根据您的实际端口调整
    server_name ***********;  # 您的服务器IP
    
    root /var/www/html/dist;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
        
        # 关键：允许被特定域名嵌入
        add_header X-Frame-Options "ALLOWALL" always;
        add_header Content-Security-Policy "frame-ancestors http://***********:9291 https://***********:9291;" always;
        
        # 其他必要的头部
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    }
    
    # 静态资源
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Frame-Options "ALLOWALL" always;
        add_header Content-Security-Policy "frame-ancestors http://***********:9291 https://***********:9291;" always;
    }
}

# 上游服务器配置（如果使用负载均衡）
upstream backend {
    server 127.0.0.1:3000;  # 您的Node.js或其他后端服务
    # server 127.0.0.1:3001;  # 可以添加多个服务器实现负载均衡
}

# 全局配置建议
# 在nginx.conf的http块中添加以下配置：
#
# http {
#     # 隐藏nginx版本信息
#     server_tokens off;
#     
#     # 设置客户端请求体大小限制
#     client_max_body_size 10M;
#     
#     # 设置超时时间
#     client_body_timeout 12;
#     client_header_timeout 12;
#     keepalive_timeout 15;
#     send_timeout 10;
#     
#     # 包含mime类型
#     include /etc/nginx/mime.types;
#     default_type application/octet-stream;
# }
