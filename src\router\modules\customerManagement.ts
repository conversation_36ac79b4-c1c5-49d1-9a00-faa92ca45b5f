export default {
  path: "/customer-management",
  meta: {
    icon: "material-symbols:supervisor-account-rounded",
    title: "客户管理",
    rank: 2
  },
  children: [
    // {
    //   path: "/customer-open-sea/index",
    //   name: "CustomerOpenSea",
    //   component: () =>
    //     import("@/views/customer-management/customer-open-sea/index.vue"),
    //   meta: {
    //     keepAlive: true,
    //     title: "客户公海"
    //     //icon: "ep:postcard"
    //   }
    // },
    {
      path: "/customer-management/customCreate",
      name: "CustomerCreate",
      component: () => import("@/views/customer-management/customCreate.vue"),
      meta: {
        title: "新增客户",
        showLink: false
      }
    },
    {
      path: "/customer-management/customUpdate",
      name: "customUpdate",
      component: () => import("@/views/customer-management/customUpdate.vue"),
      meta: {
        title: "编辑客户",
        showLink: false
      }
    },
    {
      path: "/customer-management/customInfo",
      name: "CustomerInfomation",
      component: () => import("@/views/customer-management/customInfo.vue"),
      meta: {
        title: "客户信息",
        showLink: false
      }
    },
    {
      path: "/customer-record/index",
      name: "CustomerRecord",
      component: () =>
        import("@/views/customer-management/customer-record/index.vue"),
      meta: {
        title: "客户档案"
        //icon: "material-symbols:supervisor-account-rounded"
      }
    },
    // {
    //   path: "/customer-contacts/index",
    //   name: "CustomerContacts",
    //   component: () => import("@/views/customer-contacts/index.vue"),
    //   meta: {
    //     keepAlive: true,
    //     title: "联系人",
    //     icon: "ic:round-switch-account"
    //   }
    // },
    {
      path: "/electricity-customer/index",
      name: "ElectricityCustomer",
      component: () => import("@/views/electricity-customer/index.vue"),
      meta: {
        // keepAlive: true,
        title: "用电户号"
        //icon: "ant-design:user-outlined"
      }
    },
    // {
    //   path: "/binding-relationship/index",
    //   name: "BindingRelationship",
    //   component: () => import("@/views/binding-relationship/index.vue"),
    //   meta: {
    //     keepAlive: true,
    //     title: "绑定关系",
    //     icon: "ant-design:user-switch-outlined"
    //   }
    // },
    {
      path: "/crm-management",
      meta: {
        //icon: "ic:sharp-leaderboard",
        title: "CRM管理",
        rank: 2
      },
      children: [
        {
          path: "/crm-management/sales-cockpit/index",
          name: "SalesCockpit",
          component: () =>
            import("@/views/crm-management/sales-cockpit/index.vue"),
          meta: {
            keepAlive: false,
            title: "驾驶舱"
            //icon: "ic:sharp-table-bar"
          }
        },
        // {
        //   path: "/crm-management/customer-battle-sandtable/index",
        //   name: "CustomerBattleSandtable",
        //   component: () =>
        //     import(
        //       "@/views/crm-management/customer-battle-sandtable/index.vue"
        //     ),
        //   meta: {
        //     keepAlive: true,
        //     title: "客户作战沙盘",/crm-management/sales-cockpit/index
        //   }
        // },
        {
          path: "/crm-management/customer-follow-up-records/index",
          name: "CustomerFollowUpRecords",
          component: () =>
            import(
              "@/views/crm-management/customer-follow-up-records/index.vue"
            ),
          meta: {
            title: "跟进记录"
            //icon: "ic:round-record-voice-over"
          }
        },
        {
          path: "/crm-management/opportunity-management/index",
          name: "OpportunityManagement",
          component: () =>
            import("@/views/crm-management/opportunity-management/index.vue"),
          meta: {
            title: "商机管理"
            //icon: "ic:baseline-app-registration"
          }
        },
        {
          path: "/crm-management/contract-management/index",
          name: "ContractManagement",
          component: () =>
            import("@/views/crm-management/contract-management/index.vue"),
          meta: {
            title: "合同管理"
            //icon: "ic:baseline-library-books"
          }
        },
        
        {
          path: "/crm-management/value-added-service-records/index",
          name: "ValueAddedServiceRecords",
          component: () =>
            import(
              "@/views/crm-management/value-added-service-records/index.vue"
              ),
          meta: {
            title: "增值服务"
            //icon: "ic:baseline-attach-money"
          }
        },
        {
          path: "/salesman/index",
          name: "salesman",
          component: () =>
            import("@/views/customer-management/salesman.vue"),
          meta: {
            title: "营销人员管理"
            //icon: "ic:baseline-groups"
          }
        },
        // {
        //   path: "/crm-management/marketing-personnel-management/index",
        //   name: "MarketingPersonnelManagement",
        //   component: () =>
        //     import(
        //       "@/views/crm-management/marketing-personnel-management/index.vue"
        //     ),
        //   meta: {
        //     title: "营销人员管理",
        //     icon: "ic:baseline-groups"
        //   }
        // }
      ]
    },
    {
      path: "/userPortrait",
      meta: {
        //icon: "ic:sharp-leaderboard",
        title: "用户画像",
        rank: 2
      },
      children: [
        {
          path: '/userPortrait/labelManagement',
          name: 'labelManagement',
         
          component: () => import('@/views/userPortrait/labelManagement/index.vue'),
          meta: {
            title: '标签管理',
            icon: '',
      
          },
        },
        {
            path: '/userPortrait/labelManagement/labelUserDetails',
            component: () => import('@/views/userPortrait/labelManagement/labelUserDetails.vue'),
            name: 'labelUserDetails',
            meta: {
              title: '标签用户明细',
              icon: '',
              showLink: false
            },
          },
          {
            path: '/userPortrait/labelManagement/viewLabelRules',
            component: () => import('@/views/userPortrait/labelManagement/viewLabelRules.vue'),
            name: 'viewLabelRules',
            meta: {
              title: '查看标签规则',
              icon: '',
              showLink: false
            },
          },
          {
            path: '/userPortrait/labelManagement/editLabelRules',
            component: () => import('@/views/userPortrait/labelManagement/Edit.vue'),
            name: 'editLabelRules',
            meta: {
              title: '标签编辑',
              icon: '',
              showLink: false
            },
          },
          {
            path: '/userPortrait/labelManagement/labelDetails',
            component: () => import('@/views/userPortrait/labelManagement/labelDetails.vue'),
            name: 'labelDetails',
            meta: {
              title: '标签详情',
              icon: '',
              showLink: false
            },
          },
          {
            path: '/userPortrait/labelManagement/createUserTags',
            component: () => import('@/views/userPortrait/labelManagement/createUserTags.vue'),
            name: 'createUserTags',
            meta: {
              keepAlive: true,
              title: '创建用户标签',
              icon: '',
              showLink: false
            },
          },
        {
          path: '/userPortrait/userGrouping',
          name: 'userGrouping',
          component: () => import('@/views/userPortrait/userGrouping/index.vue'),
          meta: {
            title: '用户分群',
            icon: '',
          },
        },
        {
          path: '/userPortrait/userGrouping/groupUserDetails',
          component: () => import('@/views/userPortrait/userGrouping/groupUserDetails.vue'),
          name: 'groupUserDetails',
          meta: {
            title: '分群用户明细',
            icon: '',
            showLink: false
          },
        },
        // {
        //   path: '/userPortrait/userGrouping/clusterDetails',
        //   component: () => import('@/views/userPortrait/userGrouping/clusterDetails.vue'),
        //   name: 'clusterDetails',
        //   meta: {
        //     title: '分群详情',
        //     icon: '',
        //     showLink: false
        //   },
        // },
        // {
        //   path: '/userPortrait/userGrouping/viewRule',
        //   component: () => import('@/views/userPortrait/userGrouping/viewRule.vue'),
        //   name: 'viewRule',
        //   meta: {
        //     title: '查看规则',
        //     icon: '',
        //     showLink: false
        //   },
        // },
        {
          path: '/userPortrait/userGrouping/editRule',
          component: () => import('@/views/userPortrait/userGrouping/edit.vue'),
          name: 'editRule',
          meta: {
            title: '分群编辑',
            icon: '',
            showLink: false
          },
        },
        {
          path: '/userPortrait/userGrouping/detail',
          component: () => import('@/views/userPortrait/userGrouping/lookDetail.vue'),
          name: 'editRule',
          meta: {
            title: '查看详情',
            icon: '',
            showLink: false
          },
        },
        {
          path: '/userPortrait/userGrouping/ruleCreation',
          component: () => import('@/views/userPortrait/userGrouping/ruleCreation.vue'),
          name: 'ruleCreation',
          meta: {
            title: '规则创建',
            icon: '',
            showLink: false
          },
        },
        // {
        //   path: '/userPortrait/userGrouping/importCreation',
        //   component: () => import('@/views/userPortrait/userGrouping/importCreation.vue'),
        //   name: 'importCreation',
        //   meta: {
        //     title: '导入创建',
        //     icon: '',
        //     showLink: false
        //   },
        // }
      ],

    }
  ]
};
