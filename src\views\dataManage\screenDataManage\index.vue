<template>
		<section>
      <div class="app-card mt-[20px] p-[20px] pt-[10px]" style="margin-bottom: 20px">
          <el-col :span="18">
              <el-form-item label="公司简介：" prop="companyProfile">
                  <el-input
                      type="textarea"
                      :rows="10"
                      style="width: 100%"
                      maxlength="999"
                      show-word-limit
                      v-model="companyProfile.companyProfile"
                      placeholder="请输入"
                  />
              </el-form-item>
          </el-col>
          <el-col :span="2">
              <el-button type="primary" @click="handleSave">保存</el-button>
          </el-col>
      </div>
      <div class="app-search-card">
          <div class="app-form-group">
              <div class="ml-[20px]">
                  <span>年份选择：</span>
                  <el-date-picker style="width: 140px" v-model="searchInfo.dataYear" type="year" placeholder="请选择"
                                  :clearable="false" format="YYYY" value-format="YYYY" />
              </div>
          </div>
          <div class="app-btn-group">
              <el-button class="filter-item" type="primary" @click="getList"
              >查询</el-button
              >
              <el-button class="filter-item" @click="handleReset">重置</el-button>
          </div>
      </div>
      <div class="app-card mt-[20px] p-[20px] pt-[10px]">
          <el-row justify="space-between">
              <div class="mb-[10px]">
                  <el-button type="primary" @click="handleCreate">新增</el-button>
              </div>
              <div class="app-table-group mb-[10px]">
                  <MyTab v-model="initDataSourceData.bigScreenDataSource" @change="dataSourceChange" :tabs="['填报数据', '汇总数据']"></MyTab>
              </div>
          </el-row>
          <pure-table
                  :columns="columns"
                  border
                  stripe
                  :loading="loading"
                  :data="tableData"
          >
<!--              <template #accountNo="{ row }">-->
<!--                  <a @click="getDetail(row)" style="color: #007bf7">{{-->
<!--																						row.accountNo-->
<!--                      }}</a>-->
<!--              </template>-->
              <template #operation="{ row }">
                  <el-button link type="primary" size="small" @click="getDetail(row)"
                  >编辑</el-button
                  >
<!--                  <el-popconfirm-->
<!--                          width="220"-->
<!--                          confirm-button-text="确定"-->
<!--                          cancel-button-text="取消"-->
<!--                          :icon="InfoFilled"-->
<!--                          icon-color="#626AEF"-->
<!--                          title="确认删除？"-->
<!--                          @confirm="handleDel(row.meterId)"-->
<!--                  >-->
<!--                      <template #reference>-->
<!--                          <el-button link type="danger" size="small">删除</el-button>-->
<!--                      </template>-->
<!--                  </el-popconfirm>-->
              </template>
          </pure-table>
      </div>
						<el-dialog v-model="dialogVisible" destroy-on-close :title="title" width="60%">
          <el-form
                  ref="ruleFormRef"
                  :model="formInline"
                  :rules="dataFormRules"
                  label-width="px"
                  label-position="right"
                  :inline="true"
          >
              <el-row>
                  <el-col :span="6">
                      <el-form-item label="年份时间：" prop="dataYear">
                          <el-date-picker v-model="formInline.dataYear" style="width: 150px" type="year" placeholder="请选择"
                                          :clearable="false" format="YYYY" value-format="YYYY" />
                      </el-form-item>
                  </el-col>
<!--                  <el-col :span="8">-->
<!--                      <el-form-item label="公司简介：" prop="companyProfile">-->
<!--                          <el-input-->
<!--								                          type="textarea"-->
<!--								                          :rows="4"-->
<!--								                          style="width: 250px"-->
<!--                                  maxlength="20"-->
<!--                                  show-word-limit-->
<!--                                  v-model="formInline.companyProfile"-->
<!--                                  placeholder="请输入"-->
<!--                          />-->
<!--                      </el-form-item>-->
<!--                  </el-col>-->
				              <el-col :span="8">
                      <el-form-item label="签约用户：" prop="contractUser">
                          <el-input-number
                                  :min="0"
                                  maxlength="20"
                                  show-word-limit
                                  controls-position="right"
                                  v-model="formInline.contractUser"
                                  placeholder="请输入"
                                  :precision="0"
                                  >
                              <template #append>家</template>
                          </el-input-number>
                      </el-form-item>
				              </el-col>
              </el-row>
				          <el-row>
                  <el-col :span="6">
                      <el-form-item label="目标电量：" prop="targetPower">
                          <el-input-number
                                  :min="0"
                                  maxlength="20"
                                  show-word-limit
                                  controls-position="right"
                                  v-model="formInline.targetPower"
                                  placeholder="请输入"
                                  :precision="0"
                                  max="90000000"
                          >
                              <template #append>万kwh</template>
                          </el-input-number>
                      </el-form-item>
                  </el-col>
								          <el-col :span="6">
                  <el-form-item label="合同电量：" prop="contractElectricity">
                      <el-input-number
                              :min="0"
                              maxlength="20"
                              show-word-limit
                              controls-position="right"
                              v-model="formInline.contractElectricity"
                              placeholder="请输入"
                              :precision="0"
                              max="90000000"
                              @blur="completionRateValue(formInline.settleElectricity, formInline.contractElectricity)"
                              @change="completionRateValue(formInline.settleElectricity, formInline.contractElectricity)"
                      />
                  </el-form-item>
              </el-col>
                  <el-col :span="6">
                  <el-form-item label="结算电量：" prop="settleElectricity">
                      <el-input-number
                              :min="0"
                              maxlength="20"
                              show-word-limit
                              controls-position="right"
                              v-model="formInline.settleElectricity"
                              placeholder="请输入"
                              :precision="0"
                              max="90000000"
                              @blur="completionRateValue(formInline.settleElectricity, formInline.contractElectricity)"
                              @change="completionRateValue(formInline.settleElectricity, formInline.contractElectricity)"
                      />
                  </el-form-item>
                  </el-col>
                  <el-col :span="6">
                  <el-form-item label="完成率：" prop="completionRate">
                      <el-input
                              :min="0"
                              maxlength="20"
                              show-word-limit
                              style="width: 120px"
                              v-model="formInline.completionRate"
                              placeholder="请输入"
                              disabled
                      >
                          <template #append>%</template>
                      </el-input>
                  </el-form-item>
                  </el-col>
<!--                  <el-col :span="24">-->
<!--                  <el-form-item label="大屏数据来源" prop="bigScreenDataSource">-->
<!--                      <el-radio-group v-model="formInline.bigScreenDataSource" @change="dataSourceChange">-->
<!--                          <el-radio label="1">填报数据</el-radio>-->
<!--                          <el-radio label="2">汇总数据</el-radio>-->
<!--                      </el-radio-group>-->
<!--                  </el-form-item>-->
<!--                  </el-col>-->
				          </el-row>

          </el-form>
          <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submit(ruleFormRef)">
            确定
          </el-button>
        </span>
          </template>
						</el-dialog>
		</section>
</template>

<script setup lang="ts">
import {computed, onMounted, reactive, ref} from "vue";
import {
    editElectricityAccountApi,
    getElectricityAccountDetailApi,
    getElectricityAccountListApi,
    saveElectricityAccountApi
} from "@/api/customer-management";
import {delay} from "@pureadmin/utils";
import {CustomerAccountListModel} from "@/model/customerModel";
import {FormInstance} from "element-plus";
import { InfoFilled } from "@element-plus/icons-vue";
import { columns } from "./data";
import { ElMessage } from "element-plus";
import {
    addScreenDataInfoAPI,
    modifyScreenDataInfoAPI,
    queryScreenPageListAPI,
    switchAllDataSourceAPI
} from "@/api/bigScreenDataManage";
import dayjs from "dayjs";
import MyTab from "@/components/MyTab/index.vue";

const searchInfo = reactive({
 dataYear: ''
});

onMounted(async () => {
    queryScreenPageListAPI({
        dataYear: dayjs().format('YYYY')
    }).then((res) => {
        companyProfile.value = res[0]
    })
    await getList();
});
const ruleFormRef = ref<FormInstance>();
const title = ref<string>("新增");
const loading = ref(false);
const dialogVisible = ref<boolean>(false);
const selectVisible = ref<boolean>(false);
const tableData = ref([]);

const formInline = ref({
    id: undefined,
    settleElectricity: '',
    contractElectricity: '',
    targetPower: '',
    contractUser: '',
    companyProfile: '',
				dataYear: '',
    completionRate: '',
    bigScreenDataSource: '1'
});

const companyProfile = ref({
    id: undefined,
    settleElectricity: '',
    contractElectricity: '',
    targetPower: '',
    contractUser: '',
    companyProfile: '',
    dataYear: '',
    completionRate: ''
})

const initDataSourceData = ref({
    id: undefined,
    settleElectricity: '',
    contractElectricity: '',
    targetPower: '',
    contractUser: '',
    companyProfile: '',
    dataYear: '',
    completionRate: '',
    bigScreenDataSource: '填报数据'
})

const completionRateValue = (settleElectricity,contractElectricity) => {
    formInline.value.completionRate = (settleElectricity / contractElectricity * 100).toFixed(2);
}

const dataFormRules = {
    dataYear: [
        {
            required: true,
            message: "请选择年份",
            trigger: "change"
        }
    ],
    settleElectricity: [
        {
            required: true,
            message: "请输入结算电量",
            trigger: "blur"
        }
    ],
    contractElectricity: [
        {
            required: true,
            message: "请输入合同电量",
            trigger: "blur"
        }
    ],
    targetPower: [
        {
            required: true,
            message: "请输入目标电量",
            trigger: "blur"
        }
    ],
    contractUser: [
        {
            required: true,
            message: "请输入签约用户",
            trigger: "blur"
        }
    ],
    companyProfile: [
        {
            required: true,
            message: "请输入公司简介",
            trigger: "blur"
        }
    ]
};

async function getList() {
    loading.value = true;
    await queryScreenPageListAPI(searchInfo).then((res) => {
        tableData.value = res
				    loading.value = false
        initDataSourceData.value = res[0]
        initDataSourceData.value.bigScreenDataSource = res[0].dataSource == '0' ? '填报数据' : '汇总数据'
    })
    // pagination.total = Number(data.totalCount);
}
function handleReset() {
    searchInfo.dataYear = undefined;
    getList();
}


async function getDetail(row: any) {
    title.value = "编辑";
    dialogVisible.value = true;
    formInline.value = {...row}
    console.log(row, formInline.value)
    // const res = await getElectricityAccountDetailApi(id);
    // formInline.value = { ...res.data };
}

async function submit(formEl: FormInstance | undefined) {
    if (!formEl) return;
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            if (title.value == "新增") {
                console.log(formInline.value)
                const res = await addScreenDataInfoAPI(formInline.value);
                if (res == 'true') {
                    // ElMessage({
                    //     message: "操作成功",
                    //     type: "success"
                    // });
                    await getList();
                    dialogVisible.value = false;
                } else {
                    await getList();
                    dialogVisible.value = false;
                }
            } else if (title.value ==="编辑") {
                const res = await modifyScreenDataInfoAPI(formInline.value);
                if (res == 'true') {
                    // ElMessage({
                    //     message: "操作成功",
                    //     type: "success"
                    // });
                    await getList();
                    dialogVisible.value = false;
                } else {
                    await getList();
                    dialogVisible.value = false;
                }
            }
        } else {
            console.log("error submit!", fields);
        }
    });
}

const dataSourceChange = (val: any) => {
    // 获取数据数据源
        // initDataSourceData.value = res[0]
        switchAllDataSourceAPI({
            dataSource: val == '填报数据' ? '0' : '1',
        }).then((res) => {
            getList();
        })
}
function handleCreate() {
    title.value = "新增";
    formInline.value = {
        id: undefined,
        settleElectricity: '',
        contractElectricity: '',
        targetPower: '',
        contractUser: '',
        companyProfile: '',
        dataYear: '',
        completionRate: '',
        bigScreenDataSource: '1'
    }
    // Object.assign(formInline.value, formMap);
    dialogVisible.value = true;
}

const handleSave = () => {
    // console.log(companyProfile.value)
    modifyScreenDataInfoAPI(companyProfile.value).then((res) => {
        // ElMessage.success('保存成功')
    })
}
</script>

<style scoped lang="scss">

</style>