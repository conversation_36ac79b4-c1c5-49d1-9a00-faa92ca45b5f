<template>
  <section>
    <div>
      <!-- 第一层 -->
      <el-card
          class="jt-card"
          body-style="display:flex;justify-content:space-between;align-items:center;"
      >
        <div class="headers" style="display: flex; align-items: center">
          <MyTab v-model="active" :tabs="['日', '月']"></MyTab>
          <el-date-picker
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              v-model="tomorrow"
              style="margin-left: 15px"
              :clearable="false"
              v-if="active === '日'"
          />
          <el-date-picker
              v-model="tomorrow"
              placeholder=""
              value-format="YYYY-MM-DD"
              type="month"
              v-if="active === '月'"
          ></el-date-picker>
        </div>
        <div class="footer">
          <el-button
              type="primary"
              :icon="Refresh"
              @click="initCalendarFn(tomorrow)"
          >
            刷新
          </el-button>
          <!-- <el-button type="primary" icon="Download">批量下载</el-button> -->
        </div>
      </el-card>
      <div v-if="active === '日'">
        <!-- 第二层-1 -->
        <!--      <el-card-->
        <!--        class="jt-card"-->
        <!--        style="margin: 15px 0"-->
        <!--        body-style="display:flex;align-items:center;"-->
        <!--      >-->
        <!--        <el-tag-->
        <!--          class="primary"-->
        <!--          effect="plain"-->
        <!--          style="margin-right: 10px"-->
        <!--          size="large"-->
        <!--        >-->
        <!--          应有数据({{ secondTotalDays }})-->
        <!--        </el-tag>-->
        <!--        <el-tag-->
        <!--          type="success"-->
        <!--          effect="plain"-->
        <!--          style="margin-right: 10px"-->
        <!--          size="large"-->
        <!--        >-->
        <!--          完整数据({{ secondCompleteDays }})-->
        <!--        </el-tag>-->
        <!--        <el-tag-->
        <!--          type="warning"-->
        <!--          effect="plain"-->
        <!--          style="margin-right: 10px"-->
        <!--          size="large"-->
        <!--        >-->
        <!--          部分数据({{ secondPartDays }})-->
        <!--        </el-tag>-->
        <!--        <el-tag-->
        <!--          type="info"-->
        <!--          effect="plain"-->
        <!--          style="margin-right: 10px"-->
        <!--          size="large"-->
        <!--        >-->
        <!--          无数据({{ secondMissingDays }})-->
        <!--        </el-tag>-->
        <!--        <span style="margin-right: 10px; font-size: 16px">数据完整度</span>-->
        <!--        <div style="width: 300px; display: inline-block">-->
        <!--          <el-progress :percentage="secondCompleteness" />-->
        <!--        </div>-->
        <!--        切换火电和新能源-->
        <!--      </el-card>-->
        <!-- 第三层-1 -->
        <div
            style="display: flex; justify-content: space-between; margin-top: 15px"
        >
          <!-- 左 -->
          <el-card style="width: 45%; margin-right: 15px" class="jt-card">
            <el-calendar v-model="tomorrow">
              <template #date-cell="{ data }">
              <span
                  :class="data.isSelected ? 'is-selected' : ''"
                  style="font-size: 12px"
              >
                {{ data.day.split('-').slice(1).join('-') }}
                {{ data.isSelected ? '✔️' : '' }}
                <div style="width: 100%; margin-top: 10px">
                  <el-progress
                      :percentage="dataListLeft.shift()"
                      type="line"
                      text-inside
                      :stroke-width="13"
                      color="var(--btn-color)"
                      v-if="
                      data.day.split('-').slice(1)[0] == tomorrow.split('-')[1]
                    "
                  />
                </div>
              </span>
              </template>
            </el-calendar>
          </el-card>
          <!-- 右 -->
          <el-card
              style="width: 65%; height: 620px; overflow: auto"
              class="jt-card"
          >
            <div class="headers" style="margin-bottom: 10px">
              <el-text type="primary">全部数据({{ thirdAllcount }})</el-text>
              <el-text style="margin: 0 10px" type="success">
                已有数据({{ thirdHasCount }})
              </el-text>
              <el-text type="info">无数据({{ thirdNoCount }})</el-text>
            </div>
            <el-collapse v-model="activeKey" accordion>
              <el-collapse-item name="1" title="用户导入操作日志">
                <el-table
                    :cell-style="{ borderRight: 'none', color: '#1D2129' }"
                    :header-cell-style="{
                  borderColor: '#DCDFE6',
                  color: '#1D2129',
                  backgroundColor: '#F2F3F5',
                }"
                    :data="thirdLogList"
                    border
                    style="width: 100%"
                >
                  <el-table-column
                      label="序号"
                      type="index"
                      width="60"
                      align="center"
                  />
                  <el-table-column
                      prop="dataType"
                      label="数据类型"
                      align="center"
                  />
                  <el-table-column
                      prop="importTime"
                      label="导入时间"
                      width="160"
                      align="center"
                  />
                  <el-table-column
                      prop="dataSource"
                      label="数据来源"
                      align="center"
                  />
                  <el-table-column
                      prop="importMan"
                      label="导入人员"
                      align="center"
                  />
                </el-table>
              </el-collapse-item>
              <el-collapse-item
                  :name="upindex + 2"
                  v-for="(uplist, upindex) in treeTableData"
                  :key="upindex"
              >
                <template #title>
                  <el-badge
                      v-show="importSuccessCount(uplist) !== 0"
                      :value="importSuccessCount(uplist)"
                      style="margin-left: 20px;display: block"
                  >
                    {{ uplist.categoryChName }}
                    <el-icon
                        v-show="importSuccessCount(uplist) == 0"
                        style="color: green"
                    >
                      <Select />
                    </el-icon>
                  </el-badge>
                  <div v-show="importSuccessCount(uplist) == 0">
                    {{ uplist.categoryChName }}
<!--                    <el-icon style="color: green">-->
<!--                      <Select />-->
<!--                    </el-icon>-->
                    ✔️
                  </div>

                  <!--                <el-badge-->
                  <!--                  :value="'✔️'"-->
                  <!--                  class="item"-->
                  <!--                  :offset="[10, 5]"-->
                  <!--                ></el-badge>-->
                </template>
                <el-table
                    :cell-style="{ borderRight: 'none', color: '#1D2129' }"
                    :header-cell-style="{
                  borderColor: '#DCDFE6',
                  color: '#1D2129',
                  backgroundColor: '#F2F3F5',
                }"
                    :data="uplist.tableInfoList"
                    border
                    style="width: 100%"
                >
                  <el-table-column
                      label="序号"
                      type="index"
                      width="40"
                      align="center"
                  />
                  <el-table-column
                      prop="tableChName"
                      label="导入数据类型"
                      align="center"
                      width="150"
                  />
                  <el-table-column label="操作" align="center" min-width="400">
                    <template #default="{ row }">
                      <el-row align="middle">
                        <el-col :span="6">
                          <upload-form
                              :address="row.uploadUrl"
                              :date="tomorrow"
                              :schemeName="row.tableEnName"
                              @upSuccessCallback="upSuccessFn"
                          />
                        </el-col>
                        <el-col :span="6">
                          <el-button
                              v-if="row.tableChName == '日前出清结果' ||
                           row.tableChName == '24时段上网电量' ||
                           row.tableChName == '火电现货出清结果'||
                           row.tableChName == '实时出清结果' ||
                           row.tableChName == '中长期合同明细' ||
                           row.tableChName == '调度披露日发电情况'
                            "
                              @click="previewUpload(row)"
                              type="primary"
                              :icon="Upload"
                              size="small"
                          >
                            预览数据
                          </el-button>
                        </el-col>
                        <el-col :span="6">
                          <el-button
                              @click="downloadTemplate(row)"
                              type="primary"
                              :icon="Download"
                              size="small"
                          >
                            下载模板
                          </el-button>
                        </el-col>
                        <el-col :span="6">
                          <el-button
                              @click="previewTemplate(row)"
                              type="primary"
                              :icon="Upload"
                              size="small"
                          >
                            模板预览
                          </el-button>
                        </el-col>
                      </el-row>
                    </template>
                  </el-table-column>
                  <el-table-column
                      label="状态"
                      prop="importStatus"
                      align="center"
                      width="120"
                  >
                    <template #default="{ row }">
                      {{ row.importStatus === 1 ? '导入完成' : '暂未导入' }}
                    </template>
                  </el-table-column>
                </el-table>
              </el-collapse-item>
            </el-collapse>
          </el-card>
        </div>
      </div>
      <div v-if="active === '月'">
        <el-card
            class="jt-card"
            style="margin: 15px 0;width: 100%"
            v-for="(list, index) in tableDataDetail"
            :key="index"
        >
          <div class="header">
            <div class="header-title">
              <img src="@/assets/svg/title-arrow.svg" alt="" />
              {{ list.categoryChName }}
            </div>
            <div class="header-title-right">
              <div class="header-title-right__null"></div>
              <span>暂无数据</span>
              <div class="header-title-right__has"></div>
              <span>已有数据</span>
            </div>
          </div>
            <el-table
                stripe
                :cell-style="{ borderColor: '#DCDFE6', color: '#1D2129' }"
                :header-cell-style="{
            borderColor: '#DCDFE6',
            color: '#1D2129',
            backgroundColor: '#F2F3F5',
          }"
                :data="list.tableInfoList"
                style="width: 100%"
                border
            >
              <el-table-column
                  type="index"
                  label="序号"
                  min-width="80"
                  align="center"
              />
              <el-table-column
                  prop="tableChName"
                  label="数据类型"
                  show-overflow-tooltip
                  align="center"
                  min-width="110"
              />
              <el-table-column
                  prop="staticsList"
                  :label="item"
                  align="center"
                  min-width="100"
                  v-for="(item, index) in tableList"
                  :key="item"
              >
                <template #default="{ row }">
                  <div
                      style="width: 100%; height: 50px; background-color: #c7c7c7"
                      v-if="row.staticsList[index] === 0"
                  ></div>
                  <div
                      style="width: 100%; height: 50px; background-color: #688fe1"
                      v-if="row.staticsList[index] === 1"
                  ></div>
                  <!-- <div>
																				{{ row.staticsList[index] }}
																		</div> -->
                </template>
              </el-table-column>
            </el-table>
        </el-card>
      </div>
    </div>
    <el-dialog title="上传文件预览" v-model="uploadDialogVisible" width="45%">
      <el-table :data="uploadData"
                stripe
                class="table"
                :cell-style="{ borderColor: '#DCDFE6', color: '#1D2129' }"
                :header-cell-style="{
            borderColor: '#DCDFE6',
            color: '#1D2129',
            backgroundColor: '#F2F3F5',
          }"
                style="width: 100%"
                border>
        <template #default>
          <el-table-column v-for="(item, index) in uploadHeaders" :key="index" :label="item.label" :prop="item.prop" width="80" align="center" />
        </template>
      </el-table>
    </el-dialog>
    <el-dialog title="模板预览" v-model="templateDialogVisible" width="85%">
      <TemplatePreview :fileId="templateId" ref="templatePreviewRef" />
    </el-dialog>
    <el-dialog title="数据预览" v-model="dataPreviewDialogVisible" width="85%">
      <TemplatePreview :fileId="templateId" ref="templatePreviewRef" />
    </el-dialog>
  </section>
</template>

<script setup lang="ts">
import {nextTick, onMounted, ref, watch} from 'vue'
import { Fnyyyymmdd, getMonthDays, getTomorrow } from '@/utils/time'
import {
  downloadMarketDataTemplateAPI,
  reqPreview24hDailySettlement,
  reqPreviewContractDetails,
  reqPreviewDailyGenerationReportAPI,
  reqPreviewDailySettlement,
  reqPreviewFrequency24hSettlement,
  reqPreviewFrequencySettlement,
  reqPreviewRealTimeSettlement,
  reqPreviewSpotDailySettlement,
  reqUploadMounth,
  reqUploadMounthLog,
  reqUploadMounthLogDetail,
  reqUploadTableData,
} from '@/api/upload'
import { ElLoading, ElMessage } from 'element-plus'
import dayjs from "dayjs";
import {log} from "handsontable/helpers";
import {Download, Refresh, Upload} from "@element-plus/icons-vue";
import uploadForm from '@/components/UploadForm/index.vue'
import TemplatePreview from '@/components/TemplatePreview/index.vue'
// let layoutSettingStore = useLayOutSettingStore()
const active = ref('日')
const tomorrow = ref(getTomorrow(new Date()))
//#region 日-左侧日历
const secondTotalDays = ref(0)
const secondCompleteDays = ref(0)
const secondPartDays = ref(0)
const secondMissingDays = ref(0)
const secondCompleteness = ref(0)
const dataListLeft = ref([])
const thirdAllcount = ref(0)
const thirdHasCount = ref(0)
const thirdNoCount = ref(0)
const thirdLogList = ref([])
watch(tomorrow, async (val, old) => {
  console.log(val, old)
  // if (Fnyyyymmdd(new Date(val)).split('-')[1] !== old.split('-')[1]) {
  if (dayjs(val).format('MM') !== dayjs(old).format('MM')) {
    tableList.value = getMonthDays(tomorrow.value)
    await uploadTableDataDetail(Fnyyyymmdd(new Date(val)))
  }
  tomorrow.value = Fnyyyymmdd(new Date(val))
  await initCalendarFn(val)
})
onMounted(async () => {
  await initCalendarFn(tomorrow.value)
})
const initCalendarFn = debounce(initCalendar)
async function initCalendar(val: string) {
  //请求日历数据
  const res = await reqUploadMounth(val)
  secondTotalDays.value = res.totalDays
  secondCompleteDays.value = res.completeDays
  secondPartDays.value = res.partDays
  secondMissingDays.value = res.missingDays
  secondCompleteness.value = res.completeness
  dataListLeft.value = res.percentage
  // 请求当月当天数据
  const result = await reqUploadMounthLog(val)
  thirdAllcount.value = result.allCount
  thirdHasCount.value = result.hasCount
  thirdNoCount.value = result.noCount
  thirdLogList.value = result.logList
  // 请求当天导入情况
  treeTableData.value = await reqUploadTableData(tomorrow.value)
}
function debounce(cb: any) {
  let timer: any = null
  return function (val: string) {
    clearTimeout(timer)
    timer = setTimeout(() => {
      cb.call(this, val)
    }, 100)
  }
}
//#endregion

// 剩余导入数据计数
const importSuccessCount = (uplist: any) => {
  let count = 0
  uplist.tableInfoList.forEach((item: any) => {
    if (item.importStatus === 0) {
      count++
    }
  })
  return count
}

// 下载模板
async function downloadTemplate(row: any) {
  const loading = ElLoading.service({ text: '正在下载...' })
  console.log(row)
  const apiFunctions: any = {
    'import/dayahead/t-node-result-data':
      '/import/dayahead/export-t-node-result-data',
    'disclose/import': '/disclose/exportDiscloseInfoTemplate',
    'import/dayahead/t-positive-negative-data':
      '/import/dayahead/export-t-positive-negative-data',
    'import/dayahead/t-block-section-data':
      '/import/dayahead/export-t-block-section-data',
    '/TDayaheadMarketclearResult/importDayaheadMarketclearResult':
      '/TDayaheadMarketclearResult/exportDayaheadMarketclearResult',
    '/TDayaheadFmMarketclearResult/importDayaheadMarketclearResult':
      '/TDayaheadFmMarketclearResult/exportDayaheadMarketclearResult',
    importElectricityInfoData: '/exportRealElectricityInfoData',
    importNodeResultChokeData: '/exportNodeResultChokeData',
    importTransmitElectricityChokeData: '/exportTransmitElectricityChokeData',
    importInterconnectionPlanData: '/exportInterconnectionPlanData',
    '/TRealtimeMaketclearResult/importRealtimeMarketclearResult':
      '/exportRealtimeMarketclearResult',
    '/TRealtimeFmMarketclearResult/importRealtimeFmMarketclearResult':
      '/exportRealtimeFmMarketclearResult',
    importRealityData: '/downloadRealityData',
    importpivotalTangentElectric: '/downloadPivotalTangentElectric',
    importActualElectircControl: '/downloadActualElectircControl',
    importShadowPriceData: '/downloadShadowPrice',
    '/TDayOnlineElectricityQuantity/importDayOnlineElectricityQuantity':
      '/TDayOnlineElectricityQuantity/download',
    '/production-info/import': '/production-info/download',
    '/unitOffer/importSettlementData': '/unitOffer/download',
    '/contracts/importContractEnergyDetail': '/contracts/download',
    'spotReport/import': '/spotReport/download',
    '/tradingInstitution/importMarketUnitContractInfo':
      '/tradingInstitution/exportMarketUnitContractInfo',
    '/import/dayahead/importSpotClearData': '/import/dayahead/exportSpotClearData',
    '/importSpotClearData': '/exportSpotClearData',
    '/disclose/importDailyGenerationReport': '/disclose/exportDailyGenerationReportTemplate'
  }

  // const requestAPI = apiFunctions[row.url]
  const requestAPI = row.downloadUrl

  if (!requestAPI) {
    ElMessage.warning('暂不支持下载模板')
    loading.close()
    return
  }

  downloadMarketDataTemplateAPI(requestAPI)
    .then((data: any) => {
      const blob = new Blob([data])
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `${row.tableChName}导入模板.xlsx`
      link.click()
      window.URL.revokeObjectURL(url)
      loading.close()
    })
    .catch(() => {
      ElMessage.error('下载失败')
      loading.close()
    })
}

//#region 日-右侧折叠面板
const activeKey = ref('0')
const upSuccessFn = () => {
  initCalendar(tomorrow.value)
  uploadTableDataDetail(tomorrow.value)
}
//#endregion
//#region 月-表格
const tableDataDetail = ref<any[]>([])
const tableList = ref<string[]>(getMonthDays(tomorrow.value))
async function uploadTableDataDetail(val: string) {
  tableDataDetail.value = await reqUploadMounthLogDetail(val)
}
onMounted(() => {
  uploadTableDataDetail(tomorrow.value)
})
//#endregion
const treeTableData = ref<any[]>([])
watch(active, async (val) => {
  if (val === '月') {
    await uploadTableDataDetail(tomorrow.value)
  }
  if (val === '日') {
    initCalendarFn(tomorrow.value)
    treeTableData.value = await reqUploadTableData(tomorrow.value)
  }
})
const templateDialogVisible = ref(false)
const dataPreviewDialogVisible = ref(false)
const uploadDialogVisible = ref(false)

const uploadData = ref([])
const uploadHeaders = ref(
  [
    {
    label: '列1',
    prop: 'line'
  },
    {
      label: '列2',
      prop: 'line'
    },
    {
      label: '列3',
      prop: 'line'
    }
  ]
)

const templateId = ref('')
const templatePreviewRef = ref()

const previewTemplate = (row) => {
  templateId.value = '模板预览'
  templateDialogVisible.value = true
  nextTick(() => {
    templatePreviewRef.value?.show(row)
  })
}

const previewUpload = (row) => {
  console.log('预览row', row)
  templateId.value = '数据预览'
  dataPreviewDialogVisible.value = true
  nextTick(() => {
    if(row.tableChName == '日前出清结果') {
      // 日前出清结果数据预览
      reqPreviewDailySettlement(tomorrow.value).then((res) => {
        console.log(res)
        templatePreviewRef.value.initShow(res)
      })
    } else if (row.tableChName == '日前调频出清结果') {
      ElMessage.info('暂不支持')
      dataPreviewDialogVisible.value = false
      // reqPreviewFrequency24hSettlement(tomorrow.value).then((res) => {
      //   templatePreviewRef.value.initShow(res)
      // })
    } else if (row.tableChName == '实时调频出清结果') {
      ElMessage.info('暂不支持')
      dataPreviewDialogVisible.value = false
      // reqPreviewFrequencySettlement(tomorrow.value).then((res) => {
      //   templatePreviewRef.value.initShow(res)
      // })
    } else if (row.tableChName == '24时段上网电量') {
      templateId.value = '数据预览24时段上网电量'
      reqPreview24hDailySettlement(tomorrow.value).then((res) => {
        templatePreviewRef.value.initShow(res)
      })
    } else if (row.tableChName == '火电现货出清结果') {
      reqPreviewSpotDailySettlement(tomorrow.value).then((res) => {
        templatePreviewRef.value.initShow(res)
      })
    } else if (row.tableChName == '实时出清结果') {
      reqPreviewRealTimeSettlement(tomorrow.value).then((res) => {
        templatePreviewRef.value.initShow(res)
      })
    } else if (row.tableChName == '中长期合同明细') {
      templateId.value = '数据预览合同明细'
      reqPreviewContractDetails(tomorrow.value).then((res) => {
        templatePreviewRef.value.initShow(res)
      })
    } else if (row.tableChName == '调度披露日发电情况') {
      templateId.value = '调度披露日发电情况'
      reqPreviewDailyGenerationReportAPI(tomorrow.value).then((res) => {
        templatePreviewRef.value.initShow(res)
      })
    }
  })
}


</script>

<style scoped lang="scss">
.el-progress {
  width: 100%;
}

.header-title-right {
  display: flex;
  align-items: center;

  .header-title-right__null {
    margin: 0 10px;
    width: 24px;
    height: 12px;
    background-color: #c7c7c7;
  }

  .header-title-right__has {
    margin: 0 10px;
    width: 24px;
    height: 12px;
    background-color: #5c90e7;
  }
}

:deep(.el-table__cell) {
  padding: 2px 0;

  .cell {
    padding: 0 2px;
  }
}

.header {
  margin-bottom: 10px;
}

.primary {
  border-color: var(--btn-color) !important;

  :deep(.el-tag__content) {
    color: var(--btn-color) !important;
  }
}

// :deep(.el-calendar) {
//   .current.is-today {
//     color: #254F7A !important;
//   }
// }

.el-badge {
  :deep(.el-badge__content) {
    margin-top: 10px;
  }
}
:deep(.el-progress-bar__innerText) {
  font-size: 12px !important;
  font-weight: bolder;
  color: #2f2f2f !important;
  span {
    font-size: 14px;
  }
}
:deep(.el-progress-bar__inner) {
  background-color: rgba(119, 201, 69, 0.6) !important;
}
:deep(.el-progress-bar__outer) {
  height: 24px !important;
}
</style>
