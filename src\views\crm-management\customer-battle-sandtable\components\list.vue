<template>
  <div>
    <pure-table
      :columns="columns"
      border
      stripe
      :loading="loading"
      :data="tableData"
      :pagination="pagination"
      @page-size-change="onSizeChange"
      @page-current-change="onCurrentChange"
    >
        <template #statusHeader>
          <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate" :column="columns[3]" :filter-options="statusOptions"/>
        </template>
  </pure-table>
  </div>
</template>

<script setup lang="ts">
import TableHeadPopover from "@/components/Core/TableHeadPopover/index.vue";
import { ref, reactive, onMounted, watch } from "vue";
import { columns } from "./data";
import type { PaginationProps } from "@pureadmin/table";
import { delay } from "@pureadmin/utils";
import dayjs from "dayjs";
import { getCustomerListApi } from "@/api/customer-management/index";
import { useDictOptions } from "@/hooks/dict/useDictOptions";
const { statusOptions } = useDictOptions();
const props = defineProps({
  queryId: {
    type: String as PropType<string>,
    default: "0"
  },
  time: {
    type: String,
    default: ""
  }
  
});
const timeSlot = ref<number>(dayjs().valueOf());
const loading = ref(false);
const tableData = ref([]);
const searchInfo = ref({
  name: "",
  isSign:true,
  customGrade: "",
  time: props.time?props.time:"",
  pageNo: 1,
  pageSize: 10
});
/** 分页配置 */
const pagination = reactive<PaginationProps>({
  pageSize: 10,
  currentPage: 1,
  pageSizes: [10, 20, 40, 50],
  total: 0,
  align: "right",
  background: true,
  small: false
});
function onSizeChange(val) {
  searchInfo.value.pageSize = val;
  getList();
}
function onCurrentChange(val) {
  searchInfo.value.pageNo = val;
  getList();
}
async function getList() {
  loading.value = true;
  const { data } = await getCustomerListApi(searchInfo.value);
  pagination.total = data ? Number(data.totalCount) : 0;
  tableData.value = data ? data.data : [];
  delay(300).then(() => {
    loading.value = false;
  });
}

// 表格筛选
function handleTableUpdate(data) {
  
  searchInfo.value[data.propKey] = data.value;
  console.log("aaaa",searchInfo.value[data.propKey], data);
  getList();
}
onMounted(() => {
  getList();
});
watch(
  () => props.queryId,
  newVal => {
    searchInfo.value.customGrade = newVal;
    getList();
  },
  { immediate: true }
);
</script>

<style scoped></style>
