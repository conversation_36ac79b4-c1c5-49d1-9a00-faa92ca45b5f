<template>
  <div>
    <el-card class="jt-card" style="margin-top: 20px">
      <div class="header">
        <div class="header-title">
          <img :src="getAssetURL('title-arrow')" alt="" />
          中长期量价分析
        </div>
      </div>
      <Echarts :echartsData="optionDeal1" EWidth="100%" EHeight="550px" echartId="settleSumInfo1"></Echarts>
    </el-card>
    <el-card class="jt-card" style="margin-top: 20px">
      <div class="header">
        <div class="header-title">
          <img :src="getAssetURL('title-arrow')" alt="" />
          日前偏差结算量价
        </div>
      </div>
      <Echarts :echartsData="optionDeal2" EWidth="100%" EHeight="550px" echartId="settleSumInfo2"></Echarts>
    </el-card>
    <el-card class="jt-card" style="margin-top: 20px">
      <div class="header">
        <div class="header-title">
          <img :src="getAssetURL('title-arrow')" alt="" />
          实时偏差结算量价
        </div>
      </div>
      <Echarts :echartsData="optionDeal3" EWidth="100%" EHeight="550px" echartId="settleSumInfo3"></Echarts>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { echartsConfigBottom } from '@/utils/echartsConfig'
import { ref, watch } from 'vue'
const getAssetURL = (image: any) => {
  return new URL(`../../../assets/svg/${image}.svg`, import.meta.url).href
}
const props = defineProps({
  allData: {
    default: {}
  }
})
watch(
  () => props.allData,
  (newVal: any) => {
    option1.value.xAxis.data = []
    option2.value.xAxis.data = []
    option3.value.xAxis.data = []
    option1.value.series[0].data = []
    option1.value.series[1].data = []
    option2.value.series[0].data = []
    option2.value.series[1].data = []
    option2.value.series[2].data = []
    option3.value.series[0].data = []
    option3.value.series[1].data = []
    option3.value.series[2].data = []
    option1.value.xAxis.data = newVal?.map((item: any) => item.time)
    option2.value.xAxis.data = newVal?.map((item: any) => item.time)
    option3.value.xAxis.data = newVal?.map((item: any) => item.time)
    option1.value.series[0].data = newVal?.map((item: any) => item.contractQuantity)
    option1.value.series[1].data = newVal?.map((item: any) => item.contractUnitPrice)
    option2.value.series[0].data = newVal?.map((item: any) => item.dayaheadDemandQuantity)
    option2.value.series[1].data = newVal?.map((item: any) => item.dayaheadMarketAvgCost)
    option2.value.series[2].data = newVal?.map((item: any) => item.contractQuantity)
    option3.value.series[0].data = newVal?.map((item: any) => item.dayaheadDemandQuantity)
    option3.value.series[1].data = newVal?.map((item: any) => item.realtimeClearingQuantity)
    option3.value.series[2].data = newVal?.map((item: any) => item.realtimeMarketAvgCost)
  }
)
const option1 = ref({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      crossStyle: {
        color: '#999',
      },
    },
  },
  xAxis:
  {
    type: 'category',
    data: [],
    axisPointer: {
      type: 'shadow',
    },
  },
  yAxis: [
    {
      type: 'value',
      name: '电量（MWh）',
    },
    {
      type: 'value',
      name: '电价（元/MWh）',
    },
  ],
  series: [
    {
      name: '中长期合同电量',
      type: 'bar',
      tooltip: {
        valueFormatter: function (value: number) {
          return (value || '--') + ' MWh'
        },
      },
      data: [],
    },
    {
      name: '中长期合同均价',
      yAxisIndex: 1, //设置y轴归属
      type: 'line',
      tooltip: {
        valueFormatter: function (value: number) {
          return (value || '--') + ' 元/MWh'
        },
      },
      data: [],
    },
  ],
})
const optionDeal1 = echartsConfigBottom(option1.value)
const option2 = ref({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      crossStyle: {
        color: '#999',
      },
    },
  },
  xAxis:
  {
    type: 'category',
    data: [],
    axisPointer: {
      type: 'shadow',
    },
  },
  yAxis: [
    {
      type: 'value',
      name: '电量（MWh）',
    },
    {
      type: 'value',
      name: '电价（元/MWh）',
    },
  ],
  series: [
    {
      name: '日前需求电量',
      type: 'bar',
      tooltip: {
        valueFormatter: function (value: number) {
          return (value || '--') + ' MWh'
        },
      },
      data: [],
    },
    {
      name: '日前市场平均电价',
      yAxisIndex: 1, //设置y轴归属
      type: 'line',
      tooltip: {
        valueFormatter: function (value: number) {
          return (value || '--') + ' 元/MWh'
        },
      },
      data: [],
    },
    {
      name: '中长期合同电量',
      type: 'bar',
      tooltip: {
        valueFormatter: function (value: number) {
          return (value || '--') + ' MWh'
        },
      },
      data: [],
    },
  ],
})
const optionDeal2 = echartsConfigBottom(option2.value)
const option3 = ref({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      crossStyle: {
        color: '#999',
      },
    },
  },
  xAxis:
  {
    type: 'category',
    data: [],
    axisPointer: {
      type: 'shadow',
    },
  },
  yAxis: [
    {
      type: 'value',
      name: '电量（MWh）',
    },
    {
      type: 'value',
      name: '电价（元/MWh）',
    },
  ],
  series: [
    {
      name: '日前需求电量',
      type: 'bar',
      tooltip: {
        valueFormatter: function (value: number) {
          return (value || '--') + ' MWh'
        },
      },
      data: [],
    },
    {
      name: '实际上网电量',
      type: 'bar',
      tooltip: {
        valueFormatter: function (value: number) {
          return (value || '--') + ' MWh'
        },
      },
      data: [],
    },
    {
      name: '实时市场平均电价',
      yAxisIndex: 1, //设置y轴归属
      type: 'line',
      tooltip: {
        valueFormatter: function (value: number) {
          return (value || '--') + ' 元/MWh'
        },
      },
      data: [],
    },
  ],
})
const optionDeal3 = echartsConfigBottom(option3.value)
</script>

<style scoped lang="scss"></style>
