import { BasicFetchResult, BaseRequestParams } from "./baseModel";

export type GetDictListResultModel = BasicFetchResult<DictResultModel[]>;

export interface DictResultModel {
  id: number | string;
  code: string;
  name: string;
  remark?: string;
  status?: number;
  items?: DictsModel[]
}

// 字典项
export interface  DictsModel {
  id?: string | number;
  no: number | string;
  value: string | number;
  label: string;
  serialNumber?: string | number;
}

// 分页查询请求参数
export type DictPageModel = BaseRequestParams & {
  code?: string;
  name?: string;
};

export interface DictItemsModel {
  label: string;
  no: number;
  value: string;
}
export interface GetDictListModel {
  code: string;
  items: Array<DictItemsModel>;
  name: string;
}