import { BasicFetchResult, BaseRequestParams } from "./baseModel";

export type GetCityListResultModel = BasicFetchResult<CityResultModel[]>;

export interface CityResultModel {
  id: number | string;
  name: string;
  parentId?: string;
  parentName?: string;
  serialNumber?: number;
  remark?: string;
  status?: number;
}

// 树返回结构
export type CityTreeListModel = {
  id: string;
  name: string;
  children: CityTreeListModel[];
};

// 分页查询请求参数
export type CityPageModel = BaseRequestParams & {
  parentId?: string;
  name?: string;
};
