# 针对您当前问题的Nginx配置解决方案
# 解决 "frame-ancestors" CSP 错误

server {
    listen 8187;
    server_name ***********;
    
    # 项目根目录
    root /var/www/html/dist;  # 替换为您的实际项目路径
    index index.html;
    
    # 主要配置：解决iframe嵌入问题
    location / {
        try_files $uri $uri/ /index.html;
        
        # 方案1：完全移除X-Frame-Options限制（最宽松）
        more_clear_headers "X-Frame-Options";
        
        # 方案2：设置允许所有域名嵌入
        add_header X-Frame-Options "ALLOWALL" always;
        
        # 方案3：设置CSP允许特定父域名嵌入（推荐）
        add_header Content-Security-Policy "frame-ancestors http://***********:9291 https://***********:9291 'self';" always;
        
        # 如果需要允许所有域名嵌入（不推荐用于生产环境）
        # add_header Content-Security-Policy "frame-ancestors *;" always;
        
        # 移除可能存在的其他CSP限制
        more_clear_headers "Content-Security-Policy";
        
        # 重新设置安全的CSP（保留iframe嵌入权限）
        add_header Content-Security-Policy "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; frame-ancestors http://***********:9291 https://***********:9291 'self';" always;
        
        # 其他安全头部
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    }
    
    # 静态资源配置
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        
        # 确保静态资源也允许iframe嵌入
        more_clear_headers "X-Frame-Options";
        more_clear_headers "Content-Security-Policy";
        add_header X-Frame-Options "ALLOWALL" always;
        add_header Content-Security-Policy "frame-ancestors http://***********:9291 https://***********:9291 'self';" always;
    }
    
    # API代理（如果有后端API）
    location /api/ {
        proxy_pass http://your-backend-server/;  # 替换为实际后端地址
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 移除后端可能设置的CSP限制
        proxy_hide_header X-Frame-Options;
        proxy_hide_header Content-Security-Policy;
        
        # 重新设置允许iframe的头部
        add_header X-Frame-Options "ALLOWALL" always;
        add_header Content-Security-Policy "frame-ancestors http://***********:9291 https://***********:9291 'self';" always;
    }
}

# 如果您需要支持HTTPS
server {
    listen 443 ssl http2;
    server_name ***********;
    
    # SSL证书配置
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    root /var/www/html/dist;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
        
        # HTTPS环境下的iframe配置
        more_clear_headers "X-Frame-Options";
        more_clear_headers "Content-Security-Policy";
        add_header X-Frame-Options "ALLOWALL" always;
        add_header Content-Security-Policy "frame-ancestors https://***********:9291 http://***********:9291 'self';" always;
        
        # HTTPS安全头部
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
    }
}
