# 快速解决iframe嵌入问题

## 问题分析
您遇到的错误：`X-Frame-Options` 设置为 `deny`，这意味着目标服务器明确禁止了iframe嵌入。

## 解决方案：使用nginx反向代理

### 1. 创建nginx配置文件

```bash
# 创建配置文件
sudo nano /etc/nginx/sites-available/iframe-proxy

# 或者直接编辑默认配置
sudo nano /etc/nginx/sites-available/default
```

### 2. 复制以下配置内容

```nginx
server {
    listen 80;
    server_name localhost;  # 改为您的服务器IP或域名
    
    location / {
        # 代理到目标系统
        proxy_pass http://***********:8187;
        
        # 基本代理设置
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 关键：移除限制性头部
        proxy_hide_header X-Frame-Options;
        proxy_hide_header Content-Security-Policy;
        
        # 允许iframe嵌入
        add_header X-Frame-Options "ALLOWALL" always;
        
        # 解决跨域问题
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization" always;
        
        # 处理OPTIONS请求
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
            add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization";
            add_header Content-Length 0;
            return 204;
        }
    }
}
```

### 3. 启用配置并重启nginx

```bash
# 如果创建了新配置文件
sudo ln -s /etc/nginx/sites-available/iframe-proxy /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启nginx
sudo systemctl restart nginx
# 或者
sudo service nginx restart
```

### 4. 测试iframe嵌入

现在您可以通过代理服务器访问：

```html
<!-- 原来的地址（会被拒绝） -->
<!-- <iframe src="http://***********:8187/?hide=true"></iframe> -->

<!-- 新的代理地址（可以正常嵌入） -->
<iframe src="http://您的代理服务器IP/?hide=true" width="100%" height="600px"></iframe>
```

### 5. 验证配置是否生效

```bash
# 检查响应头部
curl -I http://您的代理服务器IP/

# 应该看到类似输出：
# X-Frame-Options: ALLOWALL
# Access-Control-Allow-Origin: *
```

## 如果您使用Docker

### 创建docker-compose.yml

```yaml
version: '3'
services:
  nginx-proxy:
    image: nginx:alpine
    ports:
      - "8080:80"  # 映射到8080端口
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
    restart: unless-stopped
```

### 创建nginx.conf

```nginx
server {
    listen 80;
    
    location / {
        proxy_pass http://***********:8187;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        
        proxy_hide_header X-Frame-Options;
        proxy_hide_header Content-Security-Policy;
        
        add_header X-Frame-Options "ALLOWALL" always;
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization" always;
    }
}
```

### 启动容器

```bash
docker-compose up -d
```

## 最终测试

### 1. 创建测试HTML文件

```html
<!DOCTYPE html>
<html>
<head>
    <title>iframe测试</title>
</head>
<body>
    <h1>iframe嵌入测试</h1>
    <iframe 
        src="http://您的代理服务器IP/?hide=true" 
        width="100%" 
        height="800px"
        frameborder="0">
    </iframe>
</body>
</html>
```

### 2. 在浏览器中打开测试文件

如果配置正确，您应该能看到：
- ✅ iframe正常显示内容
- ✅ 没有跨域错误
- ✅ `?hide=true` 参数生效，头部和侧边栏被隐藏
- ✅ 浏览器控制台没有错误信息

## 故障排除

### 如果仍然有问题：

1. **检查nginx错误日志**：
   ```bash
   sudo tail -f /var/log/nginx/error.log
   ```

2. **确认目标服务器可访问**：
   ```bash
   curl -I http://***********:8187/
   ```

3. **检查防火墙设置**：
   ```bash
   sudo ufw status
   ```

4. **重启nginx服务**：
   ```bash
   sudo systemctl restart nginx
   ```

---

这个配置应该能解决您的iframe嵌入问题！
