import { request } from '@/utils/request'
import type {
  loginFormI,
  dictionaryPaListI,
  dictionaryListI,
} from '@/api/user/type'

/**
 * 用户-获取全局字典
 * @returns
 */
export const reqDictionaryPa = () => {
  const res = request.post<dictionaryPaListI>({
    url: '/dictionary/list',
  })
  return res
}
/**
 * 用户-获取字典
 * @param dictId
 * @returns
 */
export const reqDictionary = (dictId: string | undefined) => {
  const res = request.get<dictionaryListI>({
    url: `/dictionaryItem/getByDictId/${dictId}`,
  })
  return res
}
export interface VerificationCodeI {
  username: string
  password: string
  tenantId: string
}
// 获取验证码
export const reqGetVerificationCode = (data: VerificationCodeI) => {
  const res = request.post({
    url: '/auth-center/getVerificationCode',
    data,
  })
  return res
}
// 获取租户公司列表
export const reqExchangeToken = (data: { code: string }) => {
  const res = request.post({
    url: `/auth-center/exchange`,
    data
  })
  return res
}
// 退出系统
export const reqLogout = () => {
  const res = request.get({
    url: '/auth-center/loginout',
  })
  return res
}
/**
 * 用户-登录
 * @param userInfo
 * @returns
 */
export const reqLogin = (userInfo: loginFormI) => {
  const res = request.post<any>({
    url: '/auth-center/login',
    data: userInfo,
  })
  return res
}
/**
 * 用户-验证token
 * @returns
 */
export const reqUserInfo = () => {
  const res = request.get<any>({
    url: '/auth-center/getUserByToken',
  })
  return res
}
// 修改密码
export const reqChangePassword = (data: {
  oldPassword: string
  newPassword: string
}) => {
  const res = request.post({
    url: '/auth-center/updatePassword',
    data,
  })
  return res
}
// 获取权限
export const queryUserFunction = (params: any) => {
  const res = request.get({
    url: '/auth-center/queryUserFunction',
    params,
  })
  return res
}
// 根据token获取系统信息
export const querySystemByToken = (params: any) => {
  const res = request.get({
    url: '/auth-center/querySystemByToken',
    params,
  })
  return res
}

//#region 手机号登录
// 登录
// 获取租户信息
export const getUserTenant = (userInfo: any) => {
  return request.post({
    url: '/auth-center/getUserTenant',
    data: userInfo,
  })
}
// 修改用户信息
export const updateUserInfo = (userInfo: any) => {
  return request.post({
    url: '/auth-center/updateUserInfo',
    data: userInfo,
  })
}

// 判断手机号是否存在
export const CheckPhone = (params?: any) => {
  return request.get({
    url: '/auth-center/checkPhone',
    params,
  })
}


//#endregion
// 判断应用是否上锁 true-上锁 false-未上锁
export const checkApplicationLock = (params?: any) => {
  return request.get({
    url: '/auth-center/checkApplicationLock',
    params,
  })
}

