import { ref, reactive, onMounted, nextTick } from "vue";
import { ElMessage } from "element-plus";
import { delay } from "@pureadmin/utils";
import type { FormInstance } from "element-plus";
import { useUserStore } from "@/store/modules/user";
import { InfoFilled } from "@element-plus/icons-vue";
import { getAllUserListApi } from "@/api/user";
import { aoaToSheetXlsx } from "@/components/Excel";
import {
  saveCustomerDataApi,
  getCustomerListApi,
  getAreaUserIdApi,
  delCustomerByIdApi
} from "@/api/customer-management/index";
import { useRouter } from "vue-router";
import { getCityTreeApi } from "@/api/sys/city";
import { CustomerResultModel } from "@/model/customerModel";
import { useDictOptions } from "@/hooks/dict/useDictOptions";
import { columns } from "@/views/customer-management/column";
import type { PaginationProps } from "@pureadmin/table";
import dayjs from "dayjs";
import {getAllSalesmanList} from "@/api";
type SelectTagsType = {
  customId?: number;
  id?: number;
  tagType: string;
  tagValueId: number;
};

export function usePowerCustomer(isOpenSea: boolean) {
  const { push } = useRouter();
  const ruleFormRef = ref<FormInstance>();
  const activeName = ref<string>("基本信息");
  const createDialog = ref<boolean>(false);
  const selectVisible = ref<boolean>(false);
  const editFormRef = ref(null);
  const timeSlot = ref<number>(dayjs().valueOf());
  const userStore = useUserStore();
  const openFlag = ref(0);
  const optionsList = ref([]);
  const selectTags = ref<SelectTagsType[]>();
  // 字典项
  const {
    agentTypeOptions,
    statusOptions,
    customerSourceOptions,
    customGradeOptions,
    electricityConsumptionOptions,
    productTypeOptions,
    electricityCharacteristicsOptions,
    contractPeriodOptions,
    packageTypeOptions,
    packageNameOptions,
    deviationCharacteristicsOptions,
    industryOptions,
    electricityTypeOptions
  } = useDictOptions();
  // 查询id
  const queryId = ref<string>("");
  const searchInfo = reactive({
    name: "",
    // isOpenSea,
    isSign: true,//  默认已签约
    areaId: undefined,
    signYear: dayjs().format('YYYY'),
    industryId: undefined,
    customGrade: undefined,
    customType: undefined,
    terminationDate: undefined,
    customerSource: undefined,
    tags: undefined,
    followerId: undefined,
    effectiveDateMin: undefined,
    effectiveDateMax: undefined,
    terminationDateMin: undefined,
    terminationDateMax: undefined,
    monthlyAverageElectricityMin: undefined,
    monthlyAverageElectricityMax: undefined,
    electricalNature: undefined,
    // customIdentity: 1,
    pageNo: 1,
    pageSize: 10,
  });

  const formInline = ref<CustomerResultModel>({
    basic: {
      name: "",
      ownership: undefined,
      areaId: "",
      followerId: undefined,
      formerName: "",
      annualElectricity: undefined,
      customGrade: undefined,
      industryId: undefined,
      terminationDate: undefined,
      effectiveDate: undefined,
      status: undefined,
      electricalNature: undefined,
      socialCreditCode: "",
      bankName: "",
      bankAccount: "",
      registeredCapital: "",
      membershipGroup: "",
      greenDemand: "",
      followerName: "",
      description: "",
      isOpenSea,
      customIdentity: 1
    },
    electricity: {
      agentType: undefined,
      annualElectricity: undefined,
      customerSource: undefined,
      greenDemand: ""
    },
    middleman: {
      annualProxyElectricity: undefined
    },
    tagNames: "",
    tagList: []
  });

  const title = ref<string>("新增");
  const editDialog = ref(false);
  const loading = ref(false);
  const multipleSelection = ref([]);
  const dialogVisible = ref<boolean>(false);
  const selected = ref<number>(0);
  const tableData = ref([]);
  function tabClick({ index }) {
    selected.value = index;
  }
  const treeData = ref([]);

  async function getCityTreeData() {
    const tree = await getCityTreeApi();
    treeData.value = tree.data;
    console.log(treeData.value);
  }
  // 获取所有用户
  async function getUserList() {
    const res = await getAllSalesmanList();
    if (res) {
      optionsList.value = res.map(item => {
        return {
          label: item.name,
          value: String(item.id)
        };
      });
    }
  }
  // 地区改变事件
  async function handleAreaChange(data) {
    const res = await getAreaUserIdApi(data.id);
    if (res.data) {
      handleSelect(res.data);
      formInline.value.basic.followerId = res.data;
    }
  }

  /** 分页配置 */
  const pagination = reactive<PaginationProps>({
    pageSize: 10,
    currentPage: 1,
    pageSizes: [10, 20, 40, 50],
    total: 0,
    align: "right",
    background: true,
    small: false
  });

  function clearForm() {
    Object.keys(formInline.value.basic).forEach(key => {
      if (!["customIdentity"].includes(key))
        formInline.value.basic[key] = undefined;
    });
    Object.keys(formInline.value.electricity).forEach(
      key => (formInline.value.electricity[key] = "")
    );
    formInline.value.tagNames = "";
    formInline.value.tagList = [];
  }

  function handleCreate() {
    push(`/customer-management/customCreate?isOpenSea=${isOpenSea}`);
  }

  function handleOpen(type: string) {
    clearForm();
    createDialog.value = true;
    title.value = type;
  }

  function handleSelect(data) {
    const name = optionsList.value.find(i => i.value == data).label;
    formInline.value.basic.followerName = name;
  }

  function handleUpdate(id) {
    nextTick(() => {
      console.log(editFormRef.value, "editFormRef.valu");
      editFormRef.value?.getDetail(id);
    });
  }

  async function handleEdit(id, followerId) {
    push(`/customer-management/customUpdate?id=${id}&followerId=${followerId}`);

    // title.value = "编辑";
    // editDialog.value = true;
    // nextTick(() => {
    //   editFormRef.value?.getDetail(id);
    // });
  }
  // 子组件触发表单提交方法
  async function handleConfirm(data) {
    const res = await saveCustomerDataApi(data);
    if (res.code === "200") {
      ElMessage({
        message: "操作成功",
        type: "success"
      });
      getList();
      editDialog.value = false;
    } else {
      ElMessage({
        message: res.message,
        type: "error"
      });
    }
  }

  async function handleDel(id: string) {
    const res = await delCustomerByIdApi(id);
    if (res.code === "200") {
      ElMessage({
        message: "操作成功",
        type: "success"
      });
      getList();
    } else {
      ElMessage({
        message: res.message,
        type: "error"
      });
    }
  }

  function onSizeChange(val) {
    searchInfo.pageSize = val;
    getList();
  }

  function onCurrentChange(val) {
    searchInfo.pageNo = val;
    getList();
  }

  function handleDetail(row) {
    // queryId.value = row.id;
    // dialogVisible.value = true;
    // activeName.value = "基本信息";
    push(`/customer-management/customInfo?isOpenSea=${isOpenSea}&id=${row.id}`);
  }

  const handleSelectionChange = val => {
    multipleSelection.value = val;
  };

  function handleReset() {
    searchInfo.name = undefined;
    searchInfo.industryId = undefined;
    searchInfo.areaId = undefined;
    searchInfo.customGrade = undefined;
    searchInfo.customType = undefined;
    searchInfo.effectiveDateMin = undefined;
    searchInfo.signYear = undefined;
    searchInfo.effectiveDateMax = undefined;
    searchInfo.terminationDateMin = undefined;
    searchInfo.terminationDateMax = undefined;
    searchInfo.terminationDate = undefined;
    searchInfo.customerSource = undefined;
    searchInfo.electricalNature = undefined;
    searchInfo.monthlyAverageElectricityMin = undefined;
    searchInfo.monthlyAverageElectricityMax = undefined;
    searchInfo.tags = undefined;
    searchInfo.followerId = undefined;
    searchInfo.isSign = true;
    tagNames.value = "";
    timeSlot.value = dayjs().valueOf();
    getList()
  }

  async function getList() {
    loading.value = true;
    const { data } = await getCustomerListApi(searchInfo);
    console.log(data, "data");
    pagination.total = data ? Number(data.totalCount) : 0;
    tableData.value = data ? data.data : [];
    delay(300).then(() => {
      loading.value = false;
    });
  }

  // 字典方法
  function filterDictText(value, array) {
    return array.find(i => i.value == String(value))?.label;
  }

  async function submit(formEl: FormInstance | undefined) {
    if (!formEl) return;
    await formEl.validate(async (valid, fields) => {
      if (valid) {
        const res = await saveCustomerDataApi(formInline.value);
        if (res.code === "200") {
          ElMessage({
            message: "操作成功",
            type: "success"
          });
          getList();
          createDialog.value = false;
        } else {
          ElMessage({
            message: res.message,
            type: "error"
          });
        }
      } else {
        console.log("error submit!", fields);
      }
    });
  }
  // 字典名称对应
  function getDictLabelByCode(array) {
    if (Array.isArray(array) && array.length) {
      array.forEach(item => {
        if (item.tagType === "electricityConsumptionScale") {
          item.tagName = filterDictText(
            item.tagValueId,
            electricityConsumptionOptions
          );
        } else if (item.tagType === "productType") {
          item.tagName = filterDictText(item.tagValueId, productTypeOptions);
        } else if (item.tagType === "electricityConsumptionCharacteristics") {
          item.tagName = filterDictText(
            item.tagValueId,
            electricityCharacteristicsOptions
          );
        } else if (item.tagType === "contractPeriod") {
          item.tagName = filterDictText(item.tagValueId, contractPeriodOptions);
        } else if (item.tagType === "packageType") {
          item.tagName = filterDictText(item.tagValueId, packageTypeOptions);
        } else if (item.tagType === "packageName") {
          item.tagName = filterDictText(item.tagValueId, packageNameOptions);
        } else if (item.tagType === "deviationCharacteristics") {
          item.tagName = filterDictText(
            item.tagValueId,
            deviationCharacteristicsOptions
          );
        }
      });
    }
  }
  const tagNames = ref<string>("");
  function handleChange(data) {
    selectVisible.value = false;
    if (Array.isArray(data) && data.length) {
      getDictLabelByCode(data);
      searchInfo.tags = data;
      tagNames.value = data.map(i => i.tagName).join("、");
      formInline.value.tagNames = data.map(i => i.tagName).join("、");
      formInline.value.tagList = data.map(item => {
        return {
          ...item
        };
      });
    } else {
      formInline.value.tagList = [];
      tagNames.value = "";
      searchInfo.tags = undefined;
      formInline.value.tagNames = "";
    }
  }
  // 表格筛选
  function handleTableUpdate(data) {
    console.log(data, "data");
    if (
      data.propKey === "effectiveDate" &&
      Array.isArray(data.value) &&
      data.value.length === 2
    ) {
      searchInfo.effectiveDateMin = data.value[0];
      searchInfo.effectiveDateMax = data.value[1];
    }
    if (
      data.propKey === "terminationDate" &&
      Array.isArray(data.value) &&
      data.value.length === 2
    ) {
      searchInfo.terminationDateMin = data.value[0];
      searchInfo.terminationDateMax = data.value[1];
    }
    if (
      data.propKey === "monthlyAverageElectricity" &&
      Array.isArray(data.tagValue) &&
      data.tagValue.length === 2
    ) {
      console.log(data.value[0]);
      searchInfo.monthlyAverageElectricityMin = data.tagValue[0];
      searchInfo.monthlyAverageElectricityMax = data.tagValue[1];
    }
    if (
      ![
        "effectiveDate",
        "terminationDate",
        "monthlyAverageElectricity"
      ].includes(data.propKey)
    ) {
      searchInfo[data.propKey] = data.value;
    }
    if (!data.value) {
      searchInfo.effectiveDateMin = undefined;
      searchInfo.effectiveDateMax = undefined;
      searchInfo.terminationDateMin = undefined;
      searchInfo.terminationDateMax = undefined;
      searchInfo.monthlyAverageElectricityMin = undefined;
      searchInfo.monthlyAverageElectricityMax = undefined;
    }
    getList();
  }
  async function handleExport() {
    const tHeader = columns
      .map(i => i.label)
      .filter(item => !["序号", "操作"].includes(item));
    const filterVal = columns
      .map(i => i.prop)
      .filter(item => ![undefined].includes(item));
    const res = await getCustomerListApi({
      ...searchInfo,
      pageSize: 9999
    });
    if (res.data.data.length) {
      const list = res.data.data.map(item => {
        return {
          ...item,
          effectiveDate: !["0", 0, undefined, null].includes(item.effectiveDate)
            ? dayjs(Number(item.effectiveDate)).format("YYYY-MM-DD")
            : "",
          terminationDate: !["0", 0, undefined, null].includes(
            item.terminationDate
          )
            ? dayjs(Number(item.terminationDate)).format("YYYY-MM-DD")
            : "",
          electricalNature: ![undefined, null].includes(item.electricalNature)
            ? electricityTypeOptions.find(
              i => Number(i.value) === Number(item.electricalNature)
            ).label
            : "",
          industryId: ![undefined, null].includes(item.industryId)
            ? industryOptions.find(i => Number(i.value) === item.industryId)
              .label
            : "",
          customerSource: ![undefined, null].includes(item.customerSource)
            ? customerSourceOptions.find(
              i => Number(i.value) === item.customerSource
            ).label
            : "",
          agentType: ![undefined, null].includes(item.agentType)
            ? agentTypeOptions.find(i => i.value == item.agentType).label
            : "",
          status: ![undefined, null].includes(item.isSign)
            ? statusOptions.find(i => i.value == item.isSign)?.label
            : item.isSign == null ? "未签约" : ""
        };
      });
      const xlsxData = formatJson(filterVal, list);
      aoaToSheetXlsx({
        data: xlsxData,
        header: tHeader,
        filename: isOpenSea ? `客户公海列表.xlsx` : `客户档案列表.xlsx`,
        cellSetup: {
          sizing: [
            { width: 28 },
            { width: 12 },
            { width: 16 },
            { width: 16 },
            { width: 12 },
            { width: 12 },
            { width: 12 },
            { width: 12 },
            { width: 12 },
            { width: 12 },
            { width: 12 }
          ],
          style: {
            font: {
              sz: "12"
            },
            alignment: {
              horizontal: "center",
              vertical: "center",
              wrapText: false
            },
            border: {
              top: { style: "thin" },
              right: { style: "thin" },
              bottom: { style: "thin" },
              left: { style: "thin" }
            }
          }
        }
      });
    }
  }
  // 导出excel数据map方法
  function formatJson(filterVal, jsonData) {
    return jsonData.map(v =>
      filterVal.map(j => {
        // null会过滤样式
        return v[j] === null ? "" : v[j];
      })
    );
  }
  return {
    handleExport,
    handleTableUpdate,
    handleChange,
    activeName,
    selectTags,
    selectVisible,
    timeSlot,
    InfoFilled,
    submit,
    handleReset,
    handleDel,
    handleAreaChange,
    filterDictText,
    customerSourceOptions,
    dialogVisible,
    treeData,
    tabClick,
    handleOpen,
    editFormRef,
    openFlag,
    handleEdit,
    ruleFormRef,
    selected,
    tableData,
    loading,
    pagination,
    onSizeChange,
    onCurrentChange,
    handleSelectionChange,
    handleSelect,
    handleDetail,
    editDialog,
    createDialog,
    title,
    agentTypeOptions,
    statusOptions,
    queryId,
    handleConfirm,
    formInline,
    handleUpdate,
    optionsList,
    searchInfo,
    getList,
    tagNames,
    userStore,
    getCityTreeData,
    getUserList,
    handleCreate,
    customGradeOptions,
    getDictLabelByCode,
    industryOptions,
    electricityTypeOptions
  };
}
