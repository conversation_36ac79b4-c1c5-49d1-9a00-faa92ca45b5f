<template>
  <div>
    <div class="font-bold mb-[10px]">客户跟进记录</div>
    <pure-table :columns="followColumns" :data="dataForm.followList">
    </pure-table>
    <div class="font-bold mt-[20px] mb-[10px]">商机记录</div>
    <pure-table :columns="bussinesColumns" :data="dataForm.opportunityList">
    </pure-table>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted } from "vue";
import { followColumns, bussinesColumns } from "./data";
import {
  getOpportunityListApi,
  getFollowRecordListApi
} from "@/api/customer-management/index";
const props = defineProps({
  formInline: {
    type: Object,
    default: () => {}
  }
});
const dataForm = ref({
  opportunityList: [],
  followList: []
});
// 获取商机记录
async function getOpportunityList(id: string) {
  const res = await getOpportunityListApi({ customId: id });
  dataForm.value.opportunityList = res.data.data;
}
// 获取跟进记录
async function getFollowRecordList(id: string) {
  const res = await getFollowRecordListApi({ customId: id });
  dataForm.value.followList = res.data.data;
}
watch(
  () => props.formInline,
  newVal => {
    getOpportunityList(newVal.basic.id);
    getFollowRecordList(newVal.basic.id);
  },
  { deep: true }
);
onMounted(() => {
  if (props.formInline && props.formInline.basic) {
    getOpportunityList(props.formInline.basic.id);
    getFollowRecordList(props.formInline.basic.id);
  }
});
</script>

<style lang="scss" scoped></style>
