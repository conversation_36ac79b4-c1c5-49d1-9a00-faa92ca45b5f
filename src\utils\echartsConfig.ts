export const echartsConfigBottom = (option: any, unit?: string) => {
  if (option) {
    if (!option.legend) {
      option.legend = {}
    }
    if (!option.tooltip) {
      option.tooltip = {}
    }
    option.tooltip.trigger = 'axis'
    option.color = ['#254F7A', '#FF7D00', '#029CD4', '#25AF45', '#E37318']
    option.tooltip.backgroundColor = 'rgba(255,255,255,0.90)'
    option.tooltip.borderRadius = '4px'
    option.tooltip.boxShadow = '0px 2px 10px 0px rgba(0, 0, 0, 0.16)'
    option.tooltip.extraCssText = 'max-height:300px;overflow-y:auto;'
    option.tooltip.enterable = true
    if (unit) {
      option.tooltip.formatter = function (params: any) {
        //params[0].name表示x轴数据
        let str = params[0].name + '<br/>'
        //params是数组格式
        for (const item of params) {
          if (item.value == null) continue
          //设置浮层图形的样式跟随图中展示的颜色
          str +=
            "<span style='display:inline-block;width:10px;height:10px;border-radius:10px;background-color:" +
            item.color +
            ";'></span>" +
            ' ' +
            `<span style='display:inline-block'
            >
            ${item.seriesName}:${item.value}${unit}</span>` +
            '<br/>'
        }
        return str
      }
      option.yAxis = merge(option.yAxis, {
        name: unit,
        nameTextStyle: {
          // y轴name的样式调整
          fontSize: 14,
        },
        axisLine: {
          show: false, //不显示坐标轴线
        },
        axisTick: {
          show: false, // 将此选项设置为 false 来隐藏 X 轴刻度
        },
        splitLine: {
          show: true, //网格线
        },
      })
    }
    option.legend.x = 'center'
    option.legend.y = 'bottom'
    option.legend.icon = 'rect'
    option.legend.itemHeight = 5
    option.legend.itemWidth = 10
    option.legend.type = 'scroll'
    option.legend.width = option.legend.width || '600'

    if (option.series.length < 2) {
      option.legend.show = false
    }
    return option
  }
}
export const echartsConfig = (option: any, unit?: string) => {
  if (option) {
    if (!option.legend) {
      option.legend = {}
    }
    if (!option.tooltip) {
      option.tooltip = {}
    }
    option.tooltip.trigger = 'axis'
    option.color = ['#254F7A', '#FF7D00', '#029CD4', '#25AF45', '#E37318']
    option.tooltip.backgroundColor = 'rgba(255,255,255,0.90)'
    option.tooltip.borderRadius = '4px'
    option.tooltip.boxShadow = '0px 2px 10px 0px rgba(0, 0, 0, 0.16)'
    option.tooltip.extraCssText = 'max-height:300px;overflow-y:auto;'
    option.tooltip.enterable = true
    if (unit) {
      option.tooltip.formatter = function (params: any) {
        //params[0].name表示x轴数据
        let str = params[0].name + '<br/>'
        //params是数组格式
        for (const item of params) {
          //设置浮层图形的样式跟随图中展示的颜色
          str +=
            "<span style='display:inline-block;width:10px;height:10px;border-radius:10px;background-color:" +
            item.color +
            ";'></span>" +
            '\t' +
            item.seriesName +
            ' : ' +
            item.value +
            unit
        }
        return str
      }
      option.yAxis = merge(option.yAxis, {
        name: unit,
        nameTextStyle: {
          // y轴name的样式调整
          fontSize: 14,
        },
        axisLine: {
          show: false, //不显示坐标轴线
        },
        axisTick: {
          show: false, // 将此选项设置为 false 来隐藏 X 轴刻度
        },
        splitLine: {
          show: true, //网格线
        },
      })
    }
    option.legend.x = 'right'
    option.legend.y = 'top'
    option.legend.icon = 'circle'
    option.legend.itemHeight = option.legend.itemHeight || 10
    option.legend.itemGap = 10
    option.legend.type = 'scroll'
    option.legend.width = option.legend.width || '600'
    if (option.series.length < 2) {
      option.legend.show = false
    }
    return option
  }
}

//对象合并
export function merge(target: any, source: any) {
  if (typeof target !== 'object') {
    target = {}
  }
  if (Array.isArray(source)) {
    return source.slice()
  }
  for (const property in source) {
    // if (source.hasOwnProperty(property)) {
    if (Object.prototype.hasOwnProperty.call(source, property)) {
      const sourceProperty = source[property]
      if (typeof sourceProperty === 'object') {
        target[property] = merge(target[property], sourceProperty)
        continue
      }
      target[property] = sourceProperty
    }
  }
  return target
}
