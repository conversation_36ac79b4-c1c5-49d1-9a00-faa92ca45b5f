<script setup lang="ts">
import {
  ref,
  computed,
  onMounted,
  watch,
  type Ref,
  PropType,
  nextTick,
  onActivated
} from "vue";
import { useAppStoreHook } from "@/store/modules/app";
import {
  delay,
  useDark,
  useECharts,
  type EchartOptions
} from "@pureadmin/utils";
import * as echarts from "echarts/core";
import type { EChartsOption } from "echarts";
import { triggerWindowResize } from "@/utils/event";
const emit = defineEmits(["change"]);

const props = defineProps({
  height: {
    type: String as PropType<string>,
    default: "100%"
  },
  width: {
    type: String as PropType<string>,
    default: "100%"
  },
  xData: {
    type: Array as PropType<string[] | number[]>,
    default: () => []
  },
  yData: {
    type: Array as PropType<string[] | number[]>,
    default: () => []
  },
  series: {
    type: Array as PropType<Array<object>>,
    default: () => []
  },
  totalSeries: {
    type: Array as PropType<Array<object>>,
    default: () => []
  },
  legendData: {
    type: Array as PropType<string[]>,
    default: () => []
  }
});
const { isDark } = useDark();

const theme: EchartOptions["theme"] = computed(() => {
  return isDark.value ? "dark" : "light";
});

const chartRef = ref<HTMLDivElement | null>(null);
const { setOptions, resize } = useECharts(chartRef as Ref<HTMLDivElement>, {
  theme
});
const getOption = (): EChartsOption => {
  return {
    tooltip: {
      trigger: "item",
      formatter: "{b} : {c}"
    },
    grid: {
      bottom: "10%"
    },
    legend: {
      right: "4%",
      top: "center",
      data: props.legendData,
      orient: "vertical",
      show: false
    },

    series: [
      // {
      //   name: "Expected",
      //   type: "funnel",
      //   left: "10%",
      //   right: "10%",
      //   width: "80%",
      //   label: {
      //     show: true,
      //     position: "inside"
      //   },
      //   tooltip: {
      //     show: false,
      //     valueFormatter: () => ""
      //   },
      //   labelLine: {
      //     length: 10,
      //     lineStyle: {
      //       width: 1,
      //       type: "solid"
      //     }
      //   },
      //   itemStyle: {
      //     borderColor: "#fff",
      //     opacity: 0.6,
      //     borderWidth: 1
      //   },
      //   emphasis: {
      //     disabled: true,
      //     label: {
      //       show: false,
      //       fontSize: 20
      //     }
      //   },
      //   data: props.totalSeries
      // },
      {
        name: "Actual",
        type: "funnel",
        left: "10%",
        right: "10%",
        width: "80%",
        maxSize: "80%",
        label: {
          show: true,
          color: "#fff",
          position: "inside"
        },
        tooltip: {
          show: false,
          valueFormatter: () => ""
        },
        labelLine: {
          length: 10,
          lineStyle: {
            width: 1,
            type: "solid"
          }
        },
        itemStyle: {
          borderColor: "#fff",
          borderWidth: 1
        },
        emphasis: {
          label: {
            fontSize: 20
          }
        },
        data: props.totalSeries
      }
    ]
  };
};

watch(
  () => useAppStoreHook().getSidebarStatus,
  () => {
    delay(600).then(() => resize());
  }
);
watch(
  () => props,
  () => setOptions(getOption() as EChartsOption),
  {
    immediate: true,
    deep: true
  }
);
onMounted(() => {
  nextTick(() => {
    var myChart = echarts.init(chartRef.value!);
    myChart.on("click", params => {
      emit("change", params.data.id);
    });
  });
  delay(300).then(() => resize());
});
onActivated(() => triggerWindowResize());
</script>

<template>
  <div ref="chartRef" :style="{ height, width }" />
</template>
