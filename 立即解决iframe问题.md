# 立即解决iframe问题 - 紧急方案

## 🚨 问题分析

您仍然看到 `X-Frame-Options: deny` 错误，说明：
1. 开发服务器可能没有重启
2. 或者有其他地方设置了这个头部
3. 或者浏览器缓存了旧的响应

## 🔥 立即执行以下步骤

### 步骤1: 强制重启开发服务器
```bash
# 完全停止服务器 (Ctrl+C)
# 然后重新启动
npm run dev

# 或者杀死所有node进程后重启
pkill -f node
npm run dev
```

### 步骤2: 清除浏览器缓存
- 按 `Ctrl+Shift+R` (强制刷新)
- 或者按 `F12` 打开开发者工具 → Network标签 → 勾选 "Disable cache"

### 步骤3: 检查响应头
在浏览器开发者工具中：
1. 打开 Network 标签页
2. 访问 `http://***********:8187/`
3. 查看响应头是否包含 `X-Frame-Options: ALLOWALL`

## 🛠️ 如果仍然有问题，使用运行时解决方案

在 `src/main.ts` 中添加以下代码，强制移除所有限制：

```javascript
// 在文件顶部添加
console.log('🔧 正在移除所有iframe限制...');

// 动态移除所有限制性头部
if (typeof document !== 'undefined') {
  // 移除所有CSP和Frame-Options相关的meta标签
  const removeRestrictiveMetas = () => {
    const metas = document.querySelectorAll('meta[http-equiv]');
    metas.forEach(meta => {
      const httpEquiv = meta.getAttribute('http-equiv');
      if (httpEquiv && (
        httpEquiv.toLowerCase().includes('x-frame-options') ||
        httpEquiv.toLowerCase().includes('content-security-policy')
      )) {
        meta.remove();
        console.log('🗑️ 移除限制性meta标签:', httpEquiv);
      }
    });
    
    // 添加允许所有iframe的meta标签
    const allowAllFrame = document.createElement('meta');
    allowAllFrame.httpEquiv = 'X-Frame-Options';
    allowAllFrame.content = 'ALLOWALL';
    document.head.appendChild(allowAllFrame);
    console.log('✅ 添加 X-Frame-Options: ALLOWALL');
  };
  
  // 立即执行
  removeRestrictiveMetas();
  
  // DOM加载完成后再次执行
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', removeRestrictiveMetas);
  }
  
  // 页面完全加载后再次执行
  window.addEventListener('load', removeRestrictiveMetas);
}

// 拦截所有HTTP响应，移除限制性头部
if (typeof window !== 'undefined' && window.fetch) {
  const originalFetch = window.fetch;
  window.fetch = function(...args) {
    return originalFetch.apply(this, args).then(response => {
      // 这里无法直接修改响应头，但可以在控制台输出调试信息
      console.log('📡 HTTP响应头部:', response.headers);
      return response;
    });
  };
}

console.log('🎯 iframe限制移除脚本已加载');
```

## 🔧 终极解决方案：修改webpack配置

如果上面的方法还不行，在 `vite.config.ts` 中添加一个插件来强制设置响应头：

```javascript
// 在 vite.config.ts 中添加自定义插件
const forceRemoveFrameOptions = () => {
  return {
    name: 'force-remove-frame-options',
    configureServer(server) {
      server.middlewares.use((req, res, next) => {
        // 移除所有限制性头部
        res.removeHeader('X-Frame-Options');
        res.removeHeader('Content-Security-Policy');
        
        // 设置允许iframe的头部
        res.setHeader('X-Frame-Options', 'ALLOWALL');
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', '*');
        res.setHeader('Access-Control-Allow-Headers', '*');
        
        console.log('🔧 强制设置响应头部');
        next();
      });
    }
  };
};

// 然后在plugins数组中添加这个插件
plugins: [
  forceRemoveFrameOptions(),
  ...getPluginsList(command, VITE_CDN, VITE_COMPRESSION)
],
```

## 🚀 最简单的测试方法

创建这个测试文件 `test-iframe.html`：

```html
<!DOCTYPE html>
<html>
<head>
    <title>iframe测试</title>
</head>
<body>
    <h1>iframe测试页面</h1>
    <p>当前时间: <span id="time"></span></p>
    
    <div style="border: 2px solid red; padding: 10px; margin: 10px 0;">
        <h3>测试iframe嵌入:</h3>
        <iframe 
            id="testFrame"
            src="http://***********:8187/?hide=true" 
            width="100%" 
            height="600px"
            frameborder="1"
            onload="onIframeLoad()"
            onerror="onIframeError()">
        </iframe>
    </div>
    
    <div id="status" style="padding: 10px; margin: 10px 0; background: #f0f0f0;">
        状态: 正在加载...
    </div>
    
    <script>
        // 显示当前时间
        document.getElementById('time').textContent = new Date().toLocaleString();
        
        function onIframeLoad() {
            document.getElementById('status').innerHTML = '✅ <strong style="color: green;">iframe加载成功！问题已解决！</strong>';
            console.log('✅ iframe加载成功');
        }
        
        function onIframeError() {
            document.getElementById('status').innerHTML = '❌ <strong style="color: red;">iframe加载失败</strong>';
            console.log('❌ iframe加载失败');
        }
        
        // 5秒后检查iframe状态
        setTimeout(() => {
            const iframe = document.getElementById('testFrame');
            try {
                if (iframe.contentWindow) {
                    console.log('✅ iframe可以访问');
                } else {
                    console.log('❌ iframe无法访问');
                }
            } catch (e) {
                console.log('⚠️ iframe跨域限制:', e.message);
            }
        }, 5000);
        
        console.log('🧪 测试页面已加载，等待iframe结果...');
    </script>
</body>
</html>
```

## 📋 检查清单

请按顺序检查：

1. ✅ 是否重启了开发服务器？
2. ✅ 是否清除了浏览器缓存？
3. ✅ Network标签页中的响应头是什么？
4. ✅ 控制台是否有错误信息？
5. ✅ 是否在正确的端口访问？

## 🆘 如果还是不行

请告诉我：
1. 您使用的是 `npm run dev` 还是其他命令？
2. 开发服务器运行在哪个端口？
3. 浏览器Network标签页显示的响应头是什么？

我会根据具体情况提供针对性的解决方案！
