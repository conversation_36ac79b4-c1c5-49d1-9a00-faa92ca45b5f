<template>
  <div>
    <div class="font-bold mb-[10px]">企业工商营业执照信息</div>
    <el-descriptions :column="2" border>
      <el-descriptions-item width="150px" label="企业名称">{{
        dataForm.basic.name
      }}</el-descriptions-item>
      <el-descriptions-item width="150px" label="统一社会信用代码">{{
        dataForm.basic.socialCreditCode
      }}</el-descriptions-item>
      <el-descriptions-item label="注册号">{{
        dataForm.basic.registrationNo
      }}</el-descriptions-item>
      <el-descriptions-item label="法定代表人">{{
        dataForm.basic.legalRepresentative
      }}</el-descriptions-item>
      <el-descriptions-item label="企业性质">{{
        filterDictText(dataForm.basic.ownership, ownershipOptions)
      }}</el-descriptions-item>
      <el-descriptions-item label="工商注册时间">{{
        dataForm.basic.businessRegistrationDate
          ? dayjs(Number(dataForm.basic.businessRegistrationDate)).format(
              "YYYY-MM-DD"
            )
          : ""
      }}</el-descriptions-item>
      <el-descriptions-item label="注册资本">{{
        dataForm.basic.registeredCapital
      }}</el-descriptions-item>
      <el-descriptions-item label="校核日期">{{
        dataForm.basic.issueDate
          ? dayjs(Number(dataForm.basic.issueDate)).format("YYYY-MM-DD")
          : ""
      }}</el-descriptions-item>
      <el-descriptions-item label="登记机关">{{
        dataForm.basic.registrationAuthority
      }}</el-descriptions-item>
      <el-descriptions-item label="登记状态">{{
        filterDictText(
          dataForm.basic.registrationStatus,
          registrationStatusOptions
        )
      }}</el-descriptions-item>
      <el-descriptions-item label="所属区域">{{
        dataForm.basic.areaName
      }}</el-descriptions-item>
      <el-descriptions-item label="住所">{{
        dataForm.basic.registeredAddress
      }}</el-descriptions-item>
      <el-descriptions-item label="经营范围">{{
        dataForm.basic.businessScope
      }}</el-descriptions-item>
      <el-descriptions-item label="营业期限">{{
        dataForm.basic.businessTerm
      }}</el-descriptions-item>
    </el-descriptions>
    <div class="font-bold mt-[20px] mb-[10px]">联系人</div>
    <pure-table :columns="contractColumns" :data="dataForm.contactList">
    </pure-table>
    <div class="font-bold mt-[20px] mb-[10px]">企业基本信息</div>
    <el-descriptions :column="2" border>
      <!-- <el-descriptions-item width="150px" label="用电类别">{{
        filterDictText(dataForm.basic.electricalNature, electricityTypeOptions)
      }}</el-descriptions-item> -->
      <el-descriptions-item width="150px" label="主营业务">{{
        dataForm.basic.mainBusiness
      }}</el-descriptions-item>
      <el-descriptions-item width="150px" label="所属行业">{{
        filterDictText(dataForm.basic.industryId, industryOptions)
      }}</el-descriptions-item>
      <el-descriptions-item width="150px" label="企业类型">{{
        filterDictText(dataForm.basic.ownership, ownershipOptions)
      }}</el-descriptions-item>
      <el-descriptions-item width="150px" label="用电性质">{{
        filterDictText(dataForm.basic.electricalNature, electricityTypeOptions)
      }}</el-descriptions-item>
      <el-descriptions-item width="150px" label="代理电量">{{
        dataForm.basic.monthlyAverageElectricity
      }}</el-descriptions-item>
    </el-descriptions>
    <div class="mt-[20px]" v-if="query.isOpenSea === 'false'">
      <div class="font-bold mt-[20px] mb-[10px]">计量点管理</div>
      <pure-table :columns="pointColumns" :data="pointTableData"> </pure-table>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from "vue";
import { contractColumns, pointColumns } from "./data";
import { CustomerResultModel } from "@/model/customerModel";
import { useDictOptions } from "@/hooks/dict/useDictOptions";
import { useRoute } from "vue-router";
import { getPortraitMeterListApi } from "@/api/customer-management/index";
import dayjs from "dayjs";
const { query } = useRoute();
const {
  registrationStatusOptions,
  ownershipOptions,
  electricityTypeOptions,
  industryOptions
} = useDictOptions();
const props = defineProps({
  formInline: {
    type: Object,
    default: () => {}
  }
});
const dataForm = ref<CustomerResultModel>({
  basic: {
    name: "",
    ownership: undefined,
    areaId: "",
    followerId: "",
    formerName: "",
    annualElectricity: undefined,
    customGrade: undefined,
    industryId: undefined,
    terminationDate: undefined,
    effectiveDate: undefined,
    status: undefined,
    socialCreditCode: "",
    bankName: "",
    bankAccount: "",
    registeredCapital: "",
    membershipGroup: "",
    greenDemand: "",
    followerName: "",
    description: "",
    isOpenSea: undefined,
    customIdentity: 1,
    registrationNo: "",
    legalRepresentative: "",
    businessTerm: "",
    businessRegistrationDate: undefined,
    issueDate: undefined,
    registrationAuthority: "",
    registrationStatus: undefined,
    registeredAddress: "",
    businessScope: "",
    electricalNature: undefined,
    mainBusiness: "",
    monthlyAverageElectricity: undefined
  },
  electricity: {
    agentType: undefined,
    annualElectricity: undefined,
    customerSource: undefined,
    greenDemand: ""
  },
  contactList: []
});
const pointTableData = ref([]);
// 字典方法
function filterDictText(value, array) {
  return array.find(i => i.value == String(value))?.label;
}
// 获取计量点信息
async function getPointDataList(id) {
  if (id) {
    const res = await getPortraitMeterListApi(id);
    pointTableData.value = res.data;
  }
}
watch(
  () => props.formInline,
  newVal => {
    dataForm.value.basic = newVal.basic;
    dataForm.value.contactList = newVal.contactList;
  },
  { deep: true }
);
onMounted(() => {
  if (props.formInline && props.formInline.basic) {
    getPointDataList(props.formInline.basic.id);
    dataForm.value.basic = props.formInline.basic;
  }
  if (props.formInline && props.formInline.contactList) {
    dataForm.value.contactList = props.formInline.contactList;
  }
});
</script>

<style lang="scss" scoped></style>
