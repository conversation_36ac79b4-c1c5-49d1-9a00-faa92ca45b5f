export default {
    path: '/ratail',
    meta: {
      icon: "material-symbols:supervisor-account-rounded",
      title: '零售交易',
      rank: 7
    },
    redirect: '/ratail/monthSum',
    children: [
      {
        path:'/ratail/monthSum',
        component: () => import('@/views/settleManage/monthSum/index.vue'),
        name: 'monthSum',
        meta:{
          // title:'月度结算',
          title:'用户结算',
          icon:'',
        }
      },
      {
        path:'/ratail/setMeal',
        component: () => import('@/views/settleManage/setMeal/index.vue'),
        name: 'setMeal',
        meta:{
          title:'套餐定制',
          icon:'',
        }
      },
      {
        path:'/ratail/setMeal/edit',
        component: () => import('@/views/settleManage/setMeal/edit.vue'),
        name: 'edit',
        meta:{
          title:'套餐编辑',
          icon:'',
          showLink: false
        }
      },
      {
        path:'/ratail/setMeal/add',
        component: () => import('@/views/settleManage/setMeal/add.vue'),
        name: 'add',
        meta:{
          title:'添加套餐',
          icon:'',
          showLink: false
        }
      },
      {
        path:'/ratail/setMeal/details',
        component: () => import('@/views/settleManage/setMeal/details.vue'),
        name: 'details',
        meta:{
          title:'查看详情',
          icon:'',
          showLink: false
        }
      },
    ],
  };
  