import dayjs from "dayjs";
import { useUserStore } from "@/store/modules/user";
const userStore = useUserStore();
const followStageOptions = userStore.getDictList.find(
  i => i.code === "fiveCustomStatus"
)?.items;
export const columns: TableColumnList = [
  {
    label: "序号",
    width: 80,
    align: "center",
    type: "index"
  },
  {
    label: "客户名称",
    slot: "customName",
    prop: "customName",
    headerSlot: "nameHeader",
    width: 180,
    searchType: "text"
  },
  {
    label: "机会名称",
    prop: "title",
    headerSlot: "titleHeader",
    searchType: "text",
    width: 180
  },
  {
    label: "所在区域",
    prop: "areaName"
  },
  {
    label: "商机描述",
    prop: "content"
  },
  {
    label: "跟进阶段",
    prop: "followStage",
    headerSlot: "followStageHeader",
    searchType: "select",
    formatter: ({ followStage }) =>
      followStage !== null
        ? followStageOptions.find(item => item.value == followStage)?.label
        : ""
  },
  {
    label: "跟进人",
    prop: "followerName"
  },
  // {
  //   label: "年用电量",
  //   sortable: true,
  //   prop: "yearlyElectricityQty"
  // },
  {
    label: "合同电量",
    sortable: true,
    prop: "contractElectricityQty"
  },
  // {
  //   label: "营销区域",
  //   prop: "marketingAreaName"
  // },
  {
    label: "预计交易日期",
    sortable: true,
    prop: "predictTradeDate",
    width: 130,
    formatter: ({ predictTradeDate }) =>
      predictTradeDate ? dayjs(predictTradeDate).format("YYYY-MM-DD") : ""
  },
  {
    label: "已有合同电量",
    sortable: true,
    prop: "existElectricityQty"
  },
  {
    label: "操作",
    width: "120",
    fixed: "right",
    slot: "operation"
  }
];
