<template>
  <div>
    <pure-table
      border
      stripe
      :columns="constractColumns"
      :loading="loading"
      :data="tableData"
      :pagination="pagination"
      @page-size-change="onSizeChange"
      @page-current-change="onCurrentChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from "vue";
import { constractColumns } from "./data";
import type { PaginationProps } from "@pureadmin/table";
import { delay } from "@pureadmin/utils";
import { getContractListApi } from "@/api/customer-management/index";
const props = defineProps({
  queryId: {
    type: String as PropType<string>,
    default: "0"
  },
  time: {
    type: String,
    default: ""
  }
});
const loading = ref(false);
const tableData = ref([]);
const searchInfo = ref({
  code: undefined,
  time:props.time,
  customName: undefined,
  name: undefined,
  entryId: undefined,
  pageNo: 1,
  pageSize: 10
});
/** 分页配置 */
const pagination = reactive<PaginationProps>({
  pageSize: 10,
  currentPage: 1,
  pageSizes: [10, 20, 40, 50],
  total: 0,
  align: "right",
  background: true,
  small: false
});
function onSizeChange(val) {
  searchInfo.value.pageSize = val;
  getList();
}
function onCurrentChange(val) {
  searchInfo.value.pageNo = val;
  getList();
}
async function getList() {
  loading.value = true;
  const { data } = await getContractListApi(searchInfo.value);
  pagination.total = data ? Number(data.totalCount) : 0;
  tableData.value = data ? data.data : [];
  console.log(data);
  delay(300).then(() => {
    loading.value = false;
  });
}
onMounted(() => {
  getList();
});
watch(
  () => props.queryId,
  newVal => {
    searchInfo.value.entryId = newVal;
    getList();
  },
  { immediate: true ,
  deep: true
  }
);
</script>

<style scoped></style>
