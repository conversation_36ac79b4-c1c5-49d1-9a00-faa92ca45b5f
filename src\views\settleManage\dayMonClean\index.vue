<template>
  <section>
    <el-card>
      <div class="header">
        <div class="header-title">查询条件</div>
      </div>
      <div style="margin-top: 20px; display: flex; align-items: center">
        <MyTab v-model="searchConditionActive" @change="searchConditionChange" :tabs="['日清算', '月结算']"></MyTab>
        <div style="margin-left: 50px; display: flex; align-items: center">
          <el-date-picker v-if="searchConditionActive === '日清算'" @change="dayChange" style="width: 228px"
            range-separator="至" format="YYYY-MM-DD" value-format="YYYY-MM-DD" v-model="dayPickerData" type="daterange"
            start-placeholder="开始日期" end-placeholder="结束日期" />
          <el-date-picker type="monthrange" v-if="searchConditionActive === '月结算'" @change="monthChange"
            style="width: 228px" range-separator="至" format="YYYY-MM" value-format="YYYY-MM" v-model="monthPickerData"
            start-placeholder="开始日期" end-placeholder="结束日期" />
        </div>
      </div>
    </el-card>

    <el-card class="jt-card" style="margin-top: 20px">
      <div class="header" >
        <div style="display: flex;align-items: center">
          <MyTab v-model="tableActive" @change="tableChange" :tabs="['交易中心结果']"></MyTab>
          <span style="margin-left: 20px">电量: MWh &nbsp&nbsp&nbsp 价格: 元/MWh &nbsp&nbsp&nbsp 费用: 元</span>
        </div>
        <el-popover placement="bottom" :width="180" trigger="hover" v-if="tableActive === '交易中心结果'">
          <template #reference>
            <el-button style="margin-left: 20px" type="primary">
              <b>交易中心结果导入</b>
            </el-button>
          </template>
          <div style="display: flex; flex-direction: column">
            <el-upload v-if="searchConditionActive === '日清算'" style="margin-bottom: 5px;" :http-request="tradingCenterUploadFunction" class="upload" action=""
              :on-change="tradingCenterUploadFile">
              <el-button :icon="Upload" type="primary">日结算结果导入</el-button>
            </el-upload>
            <el-upload v-if="searchConditionActive === '月结算'" style="margin-bottom: 5px;" :http-request="tradingCenterUploadFunction" class="upload" action=""
              :on-change="tradingCenterUploadFile">
              <el-button :icon="Upload" type="primary">月结算结果导入</el-button>
            </el-upload>
            <el-upload v-if="searchConditionActive === '日清算'" :http-request="timeFrameUploadFunction" class="upload" action=""
              :on-change="timeFrameUploadFile">
              <el-button :icon="Upload" type="primary">24时段结果导入</el-button>
            </el-upload>
          </div>
        </el-popover>
      </div>
      <el-table v-if="searchConditionActive === '日清算'" class="table-test"
        :cell-style="{ borderColor: '#DCDFE6', color: '#1D2129' }" :header-cell-style="{
          borderColor: '#DCDFE6',
          color: '#1D2129',
          backgroundColor: '#F2F3F5',
        }" :data="dayTradingCenterTableData" style="width: 100%;margin-top: 20px;">
        <el-table-column fixed prop="operationDate" label="运行日期" align="center" />
        <el-table-column label="中长期合约电费" align="center">
          <el-table-column label="合同电量" align="center" prop="contractQuantity" />
          <el-table-column label="合同均价" align="center" prop="contractUnitPrice" />
          <el-table-column label="合同电费" align="center" prop="contractElectricityCost" />
        </el-table-column>
        <el-table-column label="日前市场偏差" align="center">
          <el-table-column label="日前需求电量" align="center" prop="dayaheadDemandQuantity" />
          <el-table-column label="日前偏差电费" align="center" prop="dayaheadtestiationElectricityCost" />
        </el-table-column>
        <el-table-column label="实时市场偏差" align="center">
          <el-table-column label="实时用电量" align="center" prop="realtimeClearingQuantity" />
          <el-table-column label="实时偏差电费" align="center" prop="realtimetestiationElectricityCost" />
        </el-table-column>
        <el-table-column label="偏差回收电费" align="center" prop="testiationRecoveryElectricityCost" />
        <el-table-column label="超额回收电费" prop="excessRecoveryElectricityCost" align="center"></el-table-column>
        <el-table-column fixed="right" label="电能量电费合计" align="center">
          <el-table-column label="电费合计" align="center" prop="totalElectricityCost" />
          <el-table-column label="结算均价" align="center" prop="settlementAveragePrice" />
        </el-table-column>
        <el-table-column fixed="right" label="操作" align="center">
          <template #default="scope">
            <div style="display: flex;justify-content: center;">
              <div v-if="scope.row.operationDate !== '合计'" @click="onDetail(scope.row)"
                style="color: #3382D3; cursor: pointer">
                详情
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <el-table v-else class="table-test" :cell-style="{ borderColor: '#DCDFE6', color: '#1D2129' }" :header-cell-style="{
        borderColor: '#DCDFE6',
        color: '#1D2129',
        backgroundColor: '#F2F3F5',
      }" :data="monthTradingCenterTableData" style="width: 100%; margin-top: 20px">
        <el-table-column fixed label="运行日期" width="130" align="center" prop="settlementMonth" />
        <el-table-column label="中长期合约电量" width="130" align="center" prop="mediumLongTermContractQuantity" />
        <el-table-column label="电能量电费" width="130" align="center" prop="electricityCost" />
        <el-table-column label="偏差回收电费" width="130" align="center" prop="testiationRecoveryElectricityCost" />
        <el-table-column label="资金余缺分摊金额" width="140" align="center" prop="fundsShortageAllocationAmount" />
        <el-table-column label="资金余缺返还金额" width="140" align="center" prop="fundsShortageReturnAmount" />
        <el-table-column label="日用电量合计" width="130" align="center" prop="dailyOnlineElectricityQuantityTotal" />
        <el-table-column label="实际月度用电量" width="140" align="center" prop="actualMonthlyOnlineElectricityQuantity" />
        <el-table-column label="月度用电量偏差电量" width="170" align="center"
          prop="monthlyOnlineElectricityQuantitytestiation" />
        <el-table-column label="月度用电量偏差率" width="160" align="center"
          prop="monthlyOnlineElectricityQuantitytestiationRate" />
        <el-table-column fixed="right" label="结算总费用" width="130" align="center" prop="totalSettlementCost" />
        <el-table-column fixed="right" label="结算均价" width="130" align="center" prop="settlementAveragePrice" />
      </el-table>
    </el-card>

    <!-- 详情弹窗 -->
    <el-dialog v-model="detailVisible" title="详情" style="width: 85%">
      <el-table class="table-test" :cell-style="{ borderColor: '#DCDFE6', color: '#1D2129' }" :header-cell-style="{
        borderColor: '#DCDFE6',
        color: '#1D2129',
        backgroundColor: '#F2F3F5',
      }" :data="timedayTradingCenterTableData" style="width: 100%;margin-top: 20px;">
        <el-table-column fixed prop="time" label="时段" align="center" />
        <el-table-column label="中长期合约电费" align="center">
          <el-table-column label="合同电量" align="center" prop="contractQuantity" />
          <el-table-column label="合同均价" align="center" prop="contractUnitPrice" />
          <el-table-column label="合同电费" align="center" prop="contractElectricityCost" />
        </el-table-column>
        <el-table-column label="日前市场偏差" align="center">
          <el-table-column label="日前需求电量" align="center" prop="dayaheadDemandQuantity" />
          <el-table-column label="日前市场平均电价" align="center" prop="dayaheadMarketAvgCost" />
          <el-table-column label="日前偏差电费" align="center" prop="dayaheadTestiationElectricityCost" />
        </el-table-column>
        <el-table-column label="实时市场偏差" align="center">
          <el-table-column label="实时用电量" align="center" prop="realtimeClearingQuantity" />
          <el-table-column label="实时市场平均电价" align="center" prop="realtimeMarketAvgCost" />
          <el-table-column label="实时偏差电费" align="center" prop="testiationElectricityCost" />
        </el-table-column>
        <el-table-column label="用电偏差回收" align="center">
          <el-table-column label="偏差电量" align="center" prop="realtimeEnergy" />
          <el-table-column label="偏差率" align="center" prop="realtimeRate" />
          <el-table-column label="偏差回收电量" align="center" prop="testiationRecoveryEnergy" />
          <el-table-column label="偏差考核价" align="center" prop="testiationCheckPrice" />
          <el-table-column label="偏差回收电费" align="center" prop="testiationRecoveryElectricityCost" />
        </el-table-column>
        <el-table-column fixed="right" label="电能量电费合计" align="center">
          <el-table-column label="电费合计" align="center" prop="totalElectricityCost" />
          <el-table-column label="结算均价" align="center" prop="settlementAveragePrice" />
        </el-table-column>
      </el-table>
    </el-dialog>
  </section>
</template>

<script setup lang="ts">
import { nextTick, onMounted, ref } from 'vue'
import dayjs from 'dayjs' // 引入dayjs
import { Upload } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import {
  dailySettlementInfo, //查询日结算列表
  hourSettlementInfo, //查询24小时结算列表
  monthSettlementInfo, //查询月结算列表
  importDailySettlementData, //导入日结算数据
  importHourSettlementData, //导入24小时结算数据
  importMonthSettlementData, //导入月结算数据
} from '@/api'

// 24时段详情
const detailVisible = ref<any>(false)
function onDetail(data: any) {
  console.log(`output->data`, data)
  gethourSettlementInfo(data.operationDate, data.operationDate)
  detailVisible.value = true
}
async function gethourSettlementInfo(startTime: any, endTime: any) {
  timedayTradingCenterTableData.value = []
  const res = await hourSettlementInfo({
    startTime,
    endTime
  })
  timedayTradingCenterTableData.value = res.dailySettlementInfoList.sort((a: any, b: any) => a.time - b.time)
}

// 查询条件tab
const searchConditionActive = ref<any>('日清算')
function searchConditionChange() {
  nextTick(() => {
    if (searchConditionActive.value === '日清算') {
      getDailySettlementInfo()
    } else {
      getMonthSettlementInfo()
    }
  })
}
// 查询日表单日期
const dayPickerData = ref([
  dayjs().startOf('month').format('YYYY-MM-DD'),
  dayjs().format('YYYY-MM-DD'),
])
function dayChange() {
  if (searchConditionActive.value === '日清算') {
    getDailySettlementInfo()
  } else {
    getMonthSettlementInfo()
  }
}
const monthPickerData = ref([
  dayjs().startOf('month').format('YYYY-MM'),
  dayjs().startOf('month').format('YYYY-MM'),
])
function monthChange() {
  if (searchConditionActive.value === '日清算') {
    getDailySettlementInfo()
  } else {
    getMonthSettlementInfo()
  }
}


// 表格上方tab
const tableActive = ref('交易中心结果')
function tableChange() { }

// 日清算表格
const dayTradingCenterTableData = ref<any>([])
// 日清算表格24时段
const timedayTradingCenterTableData = ref<any>([])
// 月清算表格
const monthTradingCenterTableData = ref<any>([])

// 获取日清算表格数据
async function getDailySettlementInfo() {
  const res = await dailySettlementInfo({
    startTime: dayPickerData.value[0],
    endTime: dayPickerData.value[1],
  })
  dayTradingCenterTableData.value = res.dailySettlementInfoList
  dayTradingCenterTableData.value.unshift({
    operationDate: '合计',
    contractQuantity: res.totalInfo?.contractQuantity,
    contractUnitPrice: res.totalInfo?.contractUnitPrice,
    contractElectricityCost: res.totalInfo?.contractElectricityCost,
    dayaheadDemandQuantity: res.totalInfo?.dayaheadDemandQuantity,
    dayaheadtestiationElectricityCost:
      res.totalInfo?.dayaheadtestiationElectricityCost,
    realtimeClearingQuantity: res.totalInfo?.realtimeClearingQuantity,
    realtimetestiationElectricityCost:
      res.totalInfo?.realtimetestiationElectricityCost,
    testiationRecoveryElectricityCost:
      res.totalInfo?.testiationRecoveryElectricityCost,
    totalElectricityCost: res.totalInfo?.totalElectricityCost,
    settlementAveragePrice: res.totalInfo?.settlementAveragePrice,
  })
}
// 获取月清算表格数据
async function getMonthSettlementInfo() {
  const res = await monthSettlementInfo({
    startTime: monthPickerData.value[0],
    endTime: monthPickerData.value[1],
  })
  monthTradingCenterTableData.value = res.dailySettlementInfoList
  monthTradingCenterTableData.value.unshift({
    settlementMonth: '合计',
    mediumLongTermContractQuantity:
      res.totalInfo?.mediumLongTermContractQuantity,
    electricityCost: res.totalInfo?.electricityCost,
    testiationRecoveryElectricityCost: res.totalInfo?.testiationRecoveryElectricityCost,
    fundsShortageAllocationAmount:
      res.totalInfo?.fundsShortageAllocationAmount,
    fundsShortageReturnAmount: res.totalInfo?.fundsShortageReturnAmount,
    dailyOnlineElectricityQuantityTotal: res.totalInfo?.dailyOnlineElectricityQuantityTotal,
    actualMonthlyOnlineElectricityQuantity:
      res.totalInfo?.actualMonthlyOnlineElectricityQuantity,
    monthlyOnlineElectricityQuantitytestiation:
      res.totalInfo?.monthlyOnlineElectricityQuantitytestiation,
    monthlyOnlineElectricityQuantitytestiationRate:
      res.totalInfo?.monthlyOnlineElectricityQuantitytestiationRate,
    totalSettlementCost: res.totalInfo?.totalSettlementCost,
    settlementAveragePrice: res.totalInfo?.settlementAveragePrice,
  })
}

// 交易中心结果导入文件上传
function tradingCenterUploadFunction() { }
const fileList = ref<any>([])
// 交易中心结果导入文件上传状态改变
function tradingCenterUploadFile(file: any) {
  const fileExtension = file.name.split('.').pop()
  const size = file.size / 1024 / 1024
  if (fileExtension !== 'xlsx' && fileExtension !== 'xls') {
    ElMessage.warning('只能上传excel文件')
    fileList.value = []
    return
  }
  if (size > 10) {
    ElMessage.warning('文件大小不得超过10M')
    fileList.value = []
    return
  }
  fileList.value = [file]
  handleUpload()
}
// 上传函数
const handleUpload = () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('未选择文件')
  } else {
    const formData = new FormData()
    formData.append('file', fileList.value[0].raw)
    if (searchConditionActive.value === '日清算') {
      importDailySettlementData(formData)
        .then(() => {
          ElMessage.success('上传成功')
          getDailySettlementInfo()
        })
        .catch((e: any) => {
          console.log(e)
        })
    } else {
      importMonthSettlementData(formData)
        .then(() => {
          ElMessage.success('上传成功')
          getMonthSettlementInfo()
        })
        .catch((e: any) => {
          console.log(e)
        })
    }
  }
}
// 24时段结果导入文件上传
function timeFrameUploadFunction() { }
const fileList2 = ref<any>([])
// 交易中心结果导入文件上传状态改变
function timeFrameUploadFile(file: any) {
  const fileExtension = file.name.split('.').pop()
  const size = file.size / 1024 / 1024
  if (fileExtension !== 'xlsx' && fileExtension !== 'xls') {
    ElMessage.warning('只能上传excel文件')
    fileList2.value = []
    return
  }
  if (size > 10) {
    ElMessage.warning('文件大小不得超过10M')
    fileList2.value = []
    return
  }
  fileList2.value = [file]
  handleUpload2()
}
// 上传函数
const handleUpload2 = () => {
  if (fileList2.value.length === 0) {
    ElMessage.warning('未选择文件')
  } else {
    const formData = new FormData()
    formData.append('file', fileList2.value[0].raw)
    importHourSettlementData(formData)
      .then(() => {
        ElMessage.success('上传成功')
      })
      .catch((e: any) => {
        console.log(e)
      })
  }
}


onMounted(async () => {
  getDailySettlementInfo()
})
</script>

<style scoped lang="scss">
//el-card头部样式
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .header-title {
    padding: 0px 10px;
    border-left: 3px solid #c3d7f0;
    color: #303133;
    /* 文字/18加粗 */
    font-size: 18px;
    font-weight: 700;
    line-height: 22px;
  }
}

// 修改el-card阴影
.jt-card {
  box-shadow: 0px 2px 22px 0px rgba(0, 0, 0, 0.06) !important;
  border-radius: 6px !important;
  border: none !important;
}

:deep(.is-group) {
  .el-table-fixed-column--right {
    background-color: #BECBD8 !important;
  }
}

:deep(.el-table-fixed-column--right.is-leaf) {
  background-color: #BECBD8 !important;
}

:deep(.is-group) {
  .el-table-fixed-column--left {
    background-color: #BECBD8 !important;
  }
}

:deep(.el-table-fixed-column--left.is-leaf) {
  background-color: #BECBD8 !important;
}

:deep(.el-upload-list.el-upload-list--text) {
  display: none;
}
</style>
