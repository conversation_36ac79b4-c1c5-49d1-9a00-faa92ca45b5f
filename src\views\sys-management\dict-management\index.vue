<template>
  <section>
    <div class="app-content-container container-h">
      <div class="flex justify-between mb-[20px]">
        <div class="flex">
          <div class="app-form-group">
            <div>
              <span>名称：</span>
              <el-input
                v-model="searchInfo.name"
                clearable
                placeholder="请输入名称"
                class="filter-item"
              />
            </div>
            <div class="ml-[20px]">
              <span>编码：</span>
              <el-input
                v-model="searchInfo.code"
                clearable
                placeholder="请输入编码"
                class="filter-item"
              />
            </div>
          </div>
          <div class="app-btn-group">
            <el-button class="filter-item" type="primary" @click="getList"
              >查询</el-button
            >
            <el-button class="filter-item" @click="handleReset">重置</el-button>
          </div>
        </div>
        <el-button class="filter-item" @click="handleCreate" type="primary"
          >新增</el-button
        >
      </div>
      <pure-table
        :columns="columns"
        border
        stripe
        :loading="loading"
        :data="tableData"
        :pagination="pagination"
        @page-size-change="onSizeChange"
        @page-current-change="onCurrentChange"
      >
        <template #name="{ row }">
          <a @click="getDetail(row.id)" style="color: #007bf7">{{
            row.name
          }}</a>
        </template>
        <template #operation="{ row }">
          <el-button link type="primary" size="small" @click="getDetail(row.id)"
            >编辑</el-button
          >
          <el-popconfirm
            width="220"
            confirm-button-text="确定"
            cancel-button-text="取消"
            :icon="InfoFilled"
            icon-color="#626AEF"
            title="确认删除？"
            @confirm="handleDel(row.id)"
          >
            <template #reference>
              <el-button link type="danger" size="small">删除</el-button>
            </template>
          </el-popconfirm>
        </template>
      </pure-table>
    </div>

    <el-dialog v-model="dialogVisible" :title="title" width="50%">
      <el-form
        ref="ruleFormRef"
        :model="formInline"
        :rules="dataFormRules"
        label-width="160px"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="名称：" prop="name">
              <el-input
                maxlength="20"
                show-word-limit
                v-model="formInline.name"
                placeholder="请输入"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="编码：" prop="code">
              <el-input
                maxlength="50"
                show-word-limit
                v-model="formInline.code"
                placeholder="请输入"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <pure-table
            :columns="formColums"
            @selection-change="handleSelectionChange"
            :data="formTableData"
          >
            <template #label="{ row }">
              <el-input
                maxlength="10"
                show-word-limit
                placeholder="请输入"
                v-model="row.label"
              />
            </template>
            <template #dictValue="{ row }">
              <el-input
                maxlength="10"
                show-word-limit
                placeholder="请输入"
                v-model="row.value"
              />
            </template>
            <template #no="{ row }">
              <el-input
                maxlength="10"
                show-word-limit
                placeholder="请输入"
                v-model="row.no"
              />
            </template>
            <template #serialNumber="{ row }">
              <el-input
                maxlength="10"
                show-word-limit
                placeholder="请输入"
                v-model="row.serialNumber"
              />
            </template>
          </pure-table>
        </el-row>
      </el-form>
      <div style="text-align: center; margin-top: 20px; width: 100%">
        <el-button type="danger" plain @click="delRow">删除</el-button>
        <el-button plain @click="addRow">添加</el-button>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submit(ruleFormRef)">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </section>
</template>

<script lang="ts">
import { ref, reactive, onMounted } from "vue";
import { columns, formColums } from "./components/data";
import type { PaginationProps } from "@pureadmin/table";
import { delay } from "@pureadmin/utils";
import { useUserStore } from "@/store/modules/user";
import { InfoFilled } from "@element-plus/icons-vue";
import {
  saveSysDictionaryApi,
  getDictListApi,
  getDictDetailApi,
  delDictByIdApi
} from "@/api/sys/dict";
import { DictResultModel } from "@/model/dictModel";
import type { FormInstance } from "element-plus";
import { ElMessage } from "element-plus";
export default {
  name: "DictManagement",
  setup() {
    onMounted(async () => {
      getList();
    });
    const ruleFormRef = ref<FormInstance>();
    const title = ref<string>("添加");
    const loading = ref(false);
    const dialogVisible = ref<boolean>(false);
    const tableData = ref([]);
    const selectList = ref([]);

    // 判断是否为中文
    const checkIsChinese = (rule: any, value: any, callback: any) => {
      if (!value) {
        callback(new Error("字典编码是必填项"));
      }
      if (/^[\u4E00-\u9FA5\uF900-\uFA2D]+$/.test(value)) {
        callback(new Error("字典编码不能包含中文"));
      } else {
        callback();
      }
    };
    const dataFormRules = {
      code: [
        {
          validator: checkIsChinese,
          trigger: "blur"
        }
      ],
      name: [
        {
          required: true,
          message: "字典名称是必填项",
          trigger: "blur"
        }
      ]
    };
    /** 分页配置 */
    const pagination = reactive<PaginationProps>({
      pageSize: 10,
      currentPage: 1,
      pageSizes: [10, 20, 40, 50],
      total: 0,
      align: "right",
      background: true,
      small: false
    });
    const searchInfo = reactive({
      code: undefined,
      name: undefined,
      pageNo: 1,
      pageSize: 10
    });
    const formInline = reactive<DictResultModel>({
      id: undefined,
      code: "",
      name: "",
      items: []
      // date: ""
    });
    const formTableData = ref([]);
    async function getList() {
      loading.value = true;
      const { data } = await getDictListApi(searchInfo);
      pagination.total = data ? Number(data.totalCount) : 0;
      tableData.value = data ? data.data : [];
      delay(300).then(() => {
        loading.value = false;
      });
    }
    function handleReset() {
      searchInfo.code = undefined;
      searchInfo.name = undefined;
      getList();
    }
    function addRow() {
      formTableData.value.push({
        label: undefined,
        no: undefined,
        value: undefined,
        serialNumber: undefined
      });
    }
    function delRow() {
      selectList.value.forEach(item => {
        const index = formTableData.value.indexOf(item);
        formTableData.value.splice(index, 1);
      });
    }
    function handleSelectionChange(row) {
      selectList.value = row;
    }
    function onSizeChange(val) {
      searchInfo.pageSize = val;
      getList();
    }

    function onCurrentChange(val) {
      searchInfo.pageNo = val;
      getList();
    }

    async function getDetail(id: number) {
      title.value = "编辑";
      dialogVisible.value = true;
      const res = await getDictDetailApi(id);
      formInline.id = res.data.id;
      formInline.name = res.data.name;
      formInline.code = res.data.code;
      formInline.items = res.data.items;
      formTableData.value = res.data.items;
    }

    async function handleDel(id: number) {
      await delDictByIdApi(id);
      ElMessage({
        message: "操作成功",
        type: "success"
      });
      useUserStore().setDictList()
      getList();
    }

    async function submit(formEl: FormInstance | undefined) {
      if (!formEl) return;
      await formEl.validate(async (valid, fields) => {
        if (valid) {
          if (!formTableData.value.length) {
            ElMessage({
              message: "请添加字典项",
              type: "error"
            });
            return;
          }
          formTableData.value.forEach(item => {
            if (
              !Number.isInteger(Number(item.no)) ||
              !Number.isInteger(Number(item.serialNumber))
            ) {
              ElMessage({
                message: "字典编号和排序必须为整数",
                type: "error"
              });
              throw new Error("字典编号和排序必须为整数");
            }
          });
          formInline.items = formTableData.value;
          const res = await saveSysDictionaryApi(formInline);
          if (res.code === "200") {
            ElMessage({
              message: "操作成功",
              type: "success"
            });
            getList();
            useUserStore().setDictList()
            dialogVisible.value = false;
          } else {
            ElMessage({
              message: res.message,
              type: "error"
            });
          }
        } else {
          console.log("error submit!", fields);
        }
      });
    }

    function handleCreate() {
      title.value = "新增";
      formInline.id = undefined;
      formInline.name = "";
      formInline.code = "";
      formInline.items = [];
      formTableData.value = [];
      dialogVisible.value = true;
    }
    return {
      InfoFilled,
      submit,
      getList,
      getDetail,
      handleDel,
      handleReset,
      loading,
      dialogVisible,
      pagination,
      searchInfo,
      onSizeChange,
      onCurrentChange,
      columns,
      formColums,
      tableData,
      title,
      handleCreate,
      formInline,
      dataFormRules,
      addRow,
      formTableData,
      handleSelectionChange,
      delRow,
      ruleFormRef
    };
  }
};
</script>

<style lang="scss" scoped></style>
