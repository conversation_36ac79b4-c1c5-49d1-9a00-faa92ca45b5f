<template>
  <div class="app-card">
    <div class="card-header">
      <div class="font-bold">客户信息</div>
      <div>
        <el-button @click="goBackAndClearStack">返回</el-button>
        <el-button @click="handleEdit" type="primary">编辑</el-button>
      </div>
    </div>
    <div class="p-[20px]">
      <div class="flex items-center mb-[10px]">
        <img
          class="w-[40px] h-[40px]"
          src="/src/assets/images/customtitle.png"
          alt=""
        />
        <div class="ml-[10px]">
          <div>{{ formInline.basic.name }}</div>
          <el-tag
            v-if="formInline.basic.monthlyAverageElectricity"
            class=""
            type="success"
            >代理电量：{{
              formInline.basic.monthlyAverageElectricity
            }}MWh</el-tag
          >
        </div>
      </div>

      <el-tabs @tab-click="tabClick" v-if="isOpenSea === 'true'">
        <template v-for="(item, index) of openSeaList" :key="item.key">
          <el-tab-pane :label="item.name" :lazy="true">
            <component
              :formInline="formInline"
              v-if="selected == index"
              :is="item.component"
            />
          </el-tab-pane>
        </template>
      </el-tabs>
      <el-tabs @tab-click="tabClick" v-else>
        <template v-for="(item, index) of list" :key="item.key">
          <el-tab-pane :label="item.name" :lazy="true">
            <component
              :formInline="formInline"
              v-if="selected == index"
              :is="item.component"
            />
          </el-tab-pane>
        </template>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, provide } from "vue";
import { useRouter, useRoute } from "vue-router";
import userPortrait from "./components/user-portrait/index.vue";
import basicInfomation from "./components/basic-infomation/index.vue";
import serviceTrace from "./components/service-trace/index.vue";
import { CustomerResultModel } from "@/model/customerModel";
import { getCustomerDetailApi } from "@/api/customer-management/index";
defineOptions({
  name: "CustomerInfomation"
});
const { push, go } = useRouter();
const { query } = useRoute();
const selected = ref<number>(0);
const isOpenSea = ref<string>("true");
const openSeaList = ref([
  {
    name: "基本信息",
    component: basicInfomation
  },
  {
    name: "业务跟踪",
    component: serviceTrace
  }
]);
const list = ref([
  {
    name: "基本信息",
    component: basicInfomation
  },
  {
    name: "用户画像",
    component: userPortrait
  },
  {
    name: "业务跟踪",
    component: serviceTrace
  }
]);
const formInline = ref<CustomerResultModel>({
  basic: {
    name: "",
    ownership: undefined,
    areaId: "",
    followerId: "",
    formerName: "",
    annualElectricity: undefined,
    customGrade: undefined,
    industryId: undefined,
    terminationDate: undefined,
    effectiveDate: undefined,
    status: undefined,
    socialCreditCode: "",
    bankName: "",
    bankAccount: "",
    registeredCapital: "",
    membershipGroup: "",
    greenDemand: "",
    followerName: "",
    description: "",
    isOpenSea: undefined,
    customIdentity: 1,
    registrationNo: "",
    legalRepresentative: "",
    businessTerm: "",
    businessRegistrationDate: undefined,
    issueDate: undefined,
    registrationAuthority: "",
    registrationStatus: undefined,
    registeredAddress: "",
    businessScope: "",
    electricalNature: undefined,
    mainBusiness: "",
    monthlyAverageElectricity: undefined
  },
  electricity: {
    agentType: undefined,
    annualElectricity: undefined,
    customerSource: undefined,
    greenDemand: ""
  },
  tagNames: "",
  tagList: [],
  contactList: [
    {
      id: undefined,
      sex: undefined,
      name: "",
      role: "",
      post: "",
      birthday: undefined,
      mobile: "",
      fixedTelephone: "",
      fax: "",
      wechat: "",
      qq: "",
      email: "",
      customName: "",
      customId: undefined
    }
  ]
});
async function getDetail(id) {
  const res = await getCustomerDetailApi(id);
  formInline.value.basic = { ...res.data.basic };
  formInline.value.electricity = { ...res.data.electricity };
  formInline.value.contactList = res.data.contactList
    ? res.data.contactList
    : [];
}
function tabClick({ index }) {
  selected.value = index;
}
function goBackAndClearStack() {
  if (window.history.length <= 1) {
    push({ path: "/" });
    return false;
  } else {
    go(-1);
  }
}
function handleEdit() {
  push(`/customer-management/customUpdate?id=${query.id}`);
}
onMounted(async () => {
  isOpenSea.value = query.isOpenSea as string;
  await getDetail(query.id);
  // provide("formInline", formInline.value);
});
</script>

<style scoped></style>
