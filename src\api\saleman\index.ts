import {request} from '@/utils/request'

// 添加营销人员用户关联
export const addSaleUser = (data: any) => {
  const res = request.post<any>({
    url: '/sales/addSaleUser',
    data,
  })
  return res
}
// 添加营销人员
export const addSalesman = (data: any) => {
  const res = request.post<any>({
    url: '/sales/addSalesman',
    data,
  })
  return res
}
// 批量修改营销人员用户关联
export const batchUpdateSaleUser = (data: any) => {
  const res = request.post<any>({
    url: '/sales/batchSaveSaleUser',
    data,
  })
  return res
}
// 批量修改营销人员用户关联
export const batchUpdateSourceUser = (data: any) => {
  const res = request.post<any>({
    url: '/sales/batchSaveUserSource',
    data,
  })
  return res
}
// 获取营销人员列表
export const getAllSalesmanList = () => {
  const res = request.post<any>({
    url: `/sales/getAllSalesmanList`,
  })
  return res
}
// 获取营销人员列表
export const contractDataUpdate = (data:any) => {
  const res = request.post<any>({
    url: `/custom/contractNew/contractDataUpdate`,
    data
  })
  return res
}
// 获取营销人员列表
export const getAllSalesmanListByTime = (time: any) => {
  const res = request.post<any>({
    url: `/sales/getAllSalesmanListByTime?time=${time}`,
  })
  return res
}
// 分页查询营销人员用户关联
export const queryPageSaleUser = (data: any) => {
  const res = request.post<any>({
    url: '/sales/queryPageSaleUser',
    data,
  })
  return res
}
// // 分页查询营销人员用户关联
// export const queryPageSaleUser2 = (data: any) => {
//   const res = request.post<any>({
//     url: '/sales/queryPageSaleUser',
//     data,
//   })
//   return res
// }
// 修改营销人员用户关联
export const updateSaleUser = (data: any) => {
  const res = request.post<any>({
    url: '/sales/updateSaleUser',
    data,
  })
  return res
}
// 修改营销人员
export const updateSalesman = (data: any) => {
  const res = request.post<any>({
    url: '/sales/updateSalesman',
    data,
  })
  return res
}

// 营销人员导入
export const marketerImportAPI = (formData: any) => {
  return request.post<any>(
      {
        url: '/custom/manageInfo/upload',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
      {
        formatDate: true,
      },
  )
}
// 营销人员导出
export const marketerExportAPI = (data: any) => {
  return request.post<any>(
      {
        url: '/custom/manageInfo/export',
        responseType: 'blob',
        data,
      },
      {isTransformResponse: false},
  )
}
// 营销人员信息分页查询
export const marketerPageAPI = (data: any) => {
  return request.post<any>({
    url: '/custom/manageInfo/queryPage',
    data,
  })
}

// 批量更新营销人员信息
export const marketerBatchUpdateAPI = (data: any) => {
  return request.post<any>({
    url: '/custom/manageInfo/update',
    data,
  })
}


