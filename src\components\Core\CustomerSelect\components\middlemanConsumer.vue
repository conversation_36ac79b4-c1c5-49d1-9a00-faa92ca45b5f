<template>
  <section>
    <div class="app-func-bar">
      <div class="app-form-group">
        <div>
          <span>用户名称：</span>
          <el-input
            clearable
            v-model="searchInfo.name"
            placeholder="请输入用户名称"
            class="filter-item"
          />
        </div>
      </div>
      <div class="app-btn-group">
        <el-button class="filter-item" type="primary" @click="getList"
          >查询</el-button
        >
        <el-button class="filter-item" @click="handleReset">重置</el-button>
      </div>
    </div>
    <pure-table
      :columns="columns1"
      border
      stripe
      :data="tableData"
      :loading="loading"
      :pagination="pagination"
      :highlight-current-row="true"
      @row-click="handleRowClick"
      @page-size-change="onSizeChange"
      @page-current-change="onCurrentChange"
    >
    </pure-table>
  </section>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { columns1 } from "../data";
import type { PaginationProps } from "@pureadmin/table";
import { getCustomerListApi } from "@/api/customer-management/index";
import { delay } from "@pureadmin/utils";

const emit = defineEmits(["change"]);
const loading = ref(false);

const tableData = ref([]);
const searchInfo = reactive({
  name: "",
  customIdentity: 2,
  pageNo: 1,
  pageSize: 10
});
/** 分页配置 */
const pagination = reactive<PaginationProps>({
  pageSize: 10,
  currentPage: 1,
  pageSizes: [10, 20, 40, 50],
  total: 0,
  align: "right",
  background: true,
  small: false
});

async function getList() {
  loading.value = true;
  const { data } = await getCustomerListApi(searchInfo);
  pagination.total = data ? Number(data.totalCount) : 0;
  tableData.value = data ? data.data : [];
  delay(300).then(() => {
    loading.value = false;
  });
}

function handleReset() {
  searchInfo.name = "";
  getList();
}

function onSizeChange(val) {
  searchInfo.pageSize = val;
  getList();
}

function onCurrentChange(val) {
  searchInfo.pageNo = val;
  getList();
}
function handleRowClick(row) {
  emit("change", row);
  // console.log(row, "row");
}

onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped></style>
