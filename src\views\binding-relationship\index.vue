<template>
  <section>
    <div class="app-func-bar">
      <div class="flex">
        <div class="app-form-group">
          <div>
            <span>电力用户名称：</span>
            <el-input
              v-model="searchInfo.customElectricityName"
              clearable
              placeholder="请输入电力用户名称"
              class="filter-item"
            />
          </div>
          <div class="ml-[20px]">
            <span>居间商用户名称：</span>
            <el-input
              v-model="searchInfo.customMiddlemanName"
              clearable
              placeholder="请输入居间商用户名称"
              class="filter-item"
            />
          </div>
        </div>
        <div class="app-btn-group">
          <el-button class="filter-item" type="primary" @click="getList"
            >查询</el-button
          >
          <el-button class="filter-item" @click="handleReset">重置</el-button>
        </div>
      </div>
      <div>
        <el-button class="filter-item" @click="handleCreate" type="primary"
          >添加</el-button
        >
      </div>
    </div>
    <div class="app-content-container container-h">
      <pure-table
        :columns="columns"
        :loading="loading"
        :data="tableData"
        :pagination="pagination"
        @page-size-change="onSizeChange"
        @page-current-change="onCurrentChange"
      >
        <template #customElectricityName="{ row }">
          <a @click="getDetail(row.id)" style="color: #007bf7">{{
            row.customElectricityName
          }}</a>
        </template>
        <template #operation="{ row }">
          <el-button link type="primary" size="small" @click="getDetail(row.id)"
            >编辑</el-button
          >
          <el-popconfirm
            width="220"
            confirm-button-text="确定"
            cancel-button-text="取消"
            :icon="InfoFilled"
            icon-color="#626AEF"
            title="确认删除？"
            @confirm="handleDel(row.id)"
          >
            <template #reference>
              <el-button link type="danger" size="small">删除</el-button>
            </template>
          </el-popconfirm>
        </template>
      </pure-table>
    </div>

    <el-dialog v-model="dialogVisible" :title="title" width="60%">
      <el-form
        ref="ruleFormRef"
        :model="formInline"
        :rules="dataFormRules"
        label-width="160px"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="绑定电力用户：" prop="customElectricityName">
              <el-input
                @click="selectPowerVisible = true"
                v-model="formInline.customElectricityName"
                placeholder="请选择"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="绑定居间商用户：" prop="customMiddlemanName">
              <el-input
                @click="selectMiddleVisible = true"
                v-model="formInline.customMiddlemanName"
                placeholder="请选择"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="14">
            <el-form-item label="绑定时间：">
              <el-date-picker
                v-model="formInline.bindStart"
                type="date"
                placeholder="开始时间"
                value-format="x"
              />
              <span class="mx-[5px]">-</span>
              <el-date-picker
                v-model="formInline.bindEnd"
                type="date"
                placeholder="结束时间"
                value-format="x"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submit(ruleFormRef)">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog v-model="selectPowerVisible" title="电力客户选择">
      <power-consumer @change="handleClosePower" />
      <div class="flex justify-end mt-[40px]">
        <el-button type="primary" @click="selectPowerVisible = false"
          >确认选择</el-button
        >
      </div>
    </el-dialog>
    <el-dialog v-model="selectMiddleVisible" title="居间商客户选择">
      <middleman-consumer @change="handleCloseMiddle" />
      <div class="flex justify-end mt-[40px]">
        <el-button type="primary" @click="selectMiddleVisible = false"
          >确认选择</el-button
        >
      </div>
    </el-dialog>
  </section>
</template>

<script lang="ts">
import { ref, reactive, onMounted } from "vue";
import { columns } from "./components/data";
import type { PaginationProps } from "@pureadmin/table";
import { delay } from "@pureadmin/utils";
import { InfoFilled } from "@element-plus/icons-vue";
import {
  saveBindRelationshipApi,
  getBindRelationshipListApi,
  getBindRelationshipDetailApi,
  delBindRelationshipByIdApi
} from "@/api/customer-management/index";
import { CustomerBindListModel } from "@/model/customerModel";
import type { FormInstance } from "element-plus";
import { ElMessage } from "element-plus";
import powerConsumer from "@/components/Core/CustomerSelect/components/powerConsumer.vue";
import middlemanConsumer from "@/components/Core/CustomerSelect/components/middlemanConsumer.vue";
export default {
  name: "BindingRelationship",
  components: {
    middlemanConsumer,
    powerConsumer
  },
  setup() {
    onMounted(async () => {
      getList();
    });
    const ruleFormRef = ref<FormInstance>();
    const title = ref<string>("添加");
    const loading = ref(false);
    const dialogVisible = ref<boolean>(false);
    const selectPowerVisible = ref<boolean>(false);
    const selectMiddleVisible = ref<boolean>(false);
    const tableData = ref([]);
    const dataFormRules = {
      name: [
        {
          required: true,
          message: "姓名是必填项",
          trigger: "blur"
        }
      ],
      customId: [
        {
          required: true,
          message: "所属客户是必填项",
          trigger: "blur"
        }
      ]
    };
    /** 分页配置 */
    const pagination = reactive<PaginationProps>({
      pageSize: 10,
      currentPage: 1,
      pageSizes: [10, 20, 40, 50],
      total: 0,
      align: "right",
      background: true,
      small: false
    });
    const searchInfo = reactive({
      customElectricityName: undefined,
      customMiddlemanName: undefined,
      pageNo: 1,
      pageSize: 10
    });
    const formMap: CustomerBindListModel = {
      id: undefined,
      bindStart: 0,
      bindEnd: 0,
      customMiddlemanId: undefined,
      customMiddlemanName: "",
      customElectricityId: undefined,
      customElectricityName: ""
    };
    const formInline = ref<CustomerBindListModel>({
      id: undefined,
      bindStart: undefined,
      bindEnd: undefined,
      customMiddlemanId: undefined,
      customMiddlemanName: "",
      customElectricityId: undefined,
      customElectricityName: ""
    });
    const formTableData = ref([]);
    async function getList() {
      loading.value = true;
      const { data } = await getBindRelationshipListApi(searchInfo);
      pagination.total = Number(data.totalCount);
      tableData.value = data.data;
      delay(600).then(() => {
        loading.value = false;
      });
    }
    function handleReset() {
      searchInfo.customElectricityName = undefined;
      searchInfo.customMiddlemanName = undefined;
      getList();
    }

    function onSizeChange(val) {
      searchInfo.pageSize = val;
      getList();
    }

    function onCurrentChange(val) {
      searchInfo.pageNo = val;
      getList();
    }

    async function getDetail(id: string) {
      title.value = "编辑";
      dialogVisible.value = true;
      const res = await getBindRelationshipDetailApi(id);
      formInline.value = { ...res.data };
      formInline.value.bindStart = Number(formInline.value.bindStart);
      formInline.value.bindEnd = Number(formInline.value.bindEnd);
    }

    async function handleDel(id: string) {
      await delBindRelationshipByIdApi(id);
      ElMessage({
        message: "操作成功",
        type: "success"
      });
      getList();
    }

    async function submit(formEl: FormInstance | undefined) {
      if (!formEl) return;
      await formEl.validate(async (valid, fields) => {
        if (valid) {
          const res = await saveBindRelationshipApi(formInline.value);
          if (res.code === "200") {
            ElMessage({
              message: "操作成功",
              type: "success"
            });
            getList();
            dialogVisible.value = false;
          } else {
            ElMessage({
              message: res.message,
              type: "success"
            });
          }
        } else {
          console.log("error submit!", fields);
        }
      });
    }

    function handleCreate() {
      title.value = "新增";
      Object.assign(formInline.value, formMap);
      dialogVisible.value = true;
    }
    // 电力客户确认选择弹框事件
    function handleClosePower(row) {
      formInline.value.customElectricityId = row.id;
      formInline.value.customElectricityName = row.name;
    }
    // 居间商客户确认选择弹框事件
    function handleCloseMiddle(row) {
      formInline.value.customMiddlemanId = row.id;
      formInline.value.customMiddlemanName = row.name;
    }
    return {
      InfoFilled,
      submit,
      getList,
      getDetail,
      handleDel,
      handleReset,
      loading,
      dialogVisible,
      pagination,
      searchInfo,
      onSizeChange,
      onCurrentChange,
      columns,
      tableData,
      title,
      handleCreate,
      formInline,
      dataFormRules,
      formTableData,
      ruleFormRef,
      selectPowerVisible,
      selectMiddleVisible,
      handleClosePower,
      handleCloseMiddle
    };
  }
};
</script>

<style lang="scss" scoped></style>
