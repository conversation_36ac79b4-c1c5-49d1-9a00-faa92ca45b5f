import { useUserStore } from "@/store/modules/user";
const userStore = useUserStore();
const declareTypeOptions = userStore.getDictList.find(
  i => i.code === "declareType"
)?.items;
const electricityTypeOptions = userStore.getDictList.find(
  i => i.code === "electricityType"
)?.items;
export const columns: TableColumnList = [
  {
    label: "序号",
    align: "center",
    width: 80,
    type: "index"
  },
  {
    label: "用电户号",
    prop: "accountNo",
    headerSlot: "accountNoHeader",
    propKey: "accountNo",
    searchType: "text",
    slot: "accountNo"
  },
  {
    label: "用电地址",
    prop: "address"
  },
  {
    label: "基本电费计费方式",
    prop: "billingMethod"
  },
  {
    label: "客户名称",
    prop: "customName",
    headerSlot: "customNameHeader",
    propKey: "customName",
    searchType: "text",
  },
  {
    label: "计量点ID",
    prop: "meterCode",
    headerSlot: "meterCodeHeader",
    propKey: "meterCode",
    searchType: "text",
  },
  {
    label: "用电性质",
    prop: "meterNature"
  },
  {
    label: "申报类型",
    prop: "declareType",
    formatter: ({ declareType }) =>
      declareType !== null
        ? declareTypeOptions.find(item => item.value == declareType).label
        : ""
  },
  {
    label: "是否光伏",
    prop: "isPhotovoltaic",
    formatter: ({ isPhotovoltaic }) =>
      isPhotovoltaic !== null ? (isPhotovoltaic ? "是" : "否") : ""
  },
  {
    label: "变压器容量",
    sortable: true,
    prop: "transformerCapacity"
  },
  {
    label: "用电类型",
    prop: "useType",
    formatter: ({ useType }) =>
      useType !== null
        ? electricityTypeOptions.find(item => item.value == useType).label
        : ""
  },
  {
    label: "电压等级",
    sortable: true,
    prop: "voltageClass"
  },
  {
    label: "操作",
    width: "120",
    fixed: "right",
    slot: "operation"
  }
];
