<template>
    <div class="app-card">
        <div class="card-header">
            <div>详情</div>
            <div>
                <el-button @click="goBackAndClearStack">返回</el-button>
                <!-- <el-button type="primary" @click="submit(ruleFormRef)">保存</el-button> -->
            </div>
        </div>
        <div class="m-[20px]">
            <div class="font-bold mb-[10px]">基本信息</div>
            <el-form ref="ruleFormRef" :rules="dataFormRules" :model="condition" label-width="120px">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="套餐名称:" prop="packageName">
                            <el-input maxlength="50" show-word-limit v-model="condition.packageName" disabled
                                />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="套餐类型:" prop="packageType">
                            <el-input maxlength="50" show-word-limit v-model="condition.packageType"
                                disabled/>
                        </el-form-item>
                    </el-col>

                </el-row>
       
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="可选购买时间:">
                            <el-date-picker v-model="condition.purchaseTimeList" type="monthrange" range-separator="至" disabled
                                start-placeholder="开始时间" end-placeholder="结束时间" style="width: 100%" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="最短购买时间:">
                            <el-select v-model="condition.minPurchaseTime" placeholder="请选择"  style="width: 100%" disabled>
                                <el-option v-for="item in options" :key="item.value" :label="item.label"
                                    :value="item.value"  />
                            </el-select>
                        </el-form-item>
                    </el-col>

                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="偏差考核方式:">
                            <el-input maxlength="50" show-word-limit v-model="condition.deviationAssessmentMethod" disabled
                                placeholder="请输入" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="是否有绿电:">
                            <el-select v-model="condition.greenElectricity" placeholder="请选择"  style="width: 100%"  @change="changeElectricity" disabled>
                                <el-option v-for="item in options2" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>

                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="套餐描述:" prop="packageDescription">
                            <el-input maxlength="100" show-word-limit v-model="condition.packageDescription"
                                 disabled/>
                        </el-form-item>
                    </el-col>


                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="参数设置:" prop="argumentSet"> 
                            <el-input maxlength="200" show-word-limit v-model="condition.argumentSet"
                                disabled/>
                        </el-form-item>
                    </el-col>


                </el-row>

                <el-row>
                    <el-col :span="24">
                        <el-form-item prop="icon" label="上传图片">
                            <el-upload v-model:file-list="fileList" with-credentials :headers="header"
                                :data="uploadData" :on-preview="handleDownload" :on-remove="handleRemove"
                                :action="actionUrl" :before-upload="beforeUpload">
                                <el-button type="primary" disabled> 点击上传</el-button>
                                <template #tip>
                                    <div class="el-upload__tip">
                                        支持所有格式，且大小不超过10M
                                    </div>
                                </template>
                            </el-upload>
                        </el-form-item>
                    </el-col>


                </el-row>



            </el-form>


            <div>
                <div class="font-bold mt-[20px] mb-[10px]">套餐内容</div>

                <p style="margin: 10px 0;">售电公司收益模式计算公式</p>
                <div style="border: 1px solid #ccc">
                    <Toolbar style="border-bottom: 1px solid black" :editor="editorRef" :defaultConfig="toolbarConfig"
                        :mode="mode" />
                    <Editor style="height: 200px;" v-model="condition.computeFormula" :defaultConfig="editorConfig" :mode="mode"
                        @onCreated="handleCreated" disabled  readOnly/>
                </div>

            </div>

            <div>


                <p style="margin: 10px 0;">售电公司收益模式计算公式说明

</p>
                <div style="border: 1px solid #ccc">
                    <Toolbar style="border-bottom: 1px solid black" :editor="editorRef2" :defaultConfig="toolbarConfig2"
                        :mode="mode2" />
                    <Editor style="height: 200px;" v-model="condition.computeFormulaExplain" :defaultConfig="editorConfig" :mode="mode"
                        @onCreated="handleCreated2" readOnly/>
                </div>

            </div>

            <div v-for=" (item, index) in arrList" :key="index">


<div style="margin: 20px 0; display: flex;justify-content: space-between;">
    <div> <el-input v-model="item.title" style="width: 240px" placeholder="请输入标题"  disabled/></div>
    

</div>
<div style="border: 1px solid #ccc">
    <Toolbar style="border-bottom: 1px solid black" :editor="item.editor" :defaultConfig="toolbarConfig2"
        :mode="mode2" />
    <Editor style="height: 200px;" v-model="item.content" :defaultConfig="editorConfig" :mode="mode"
        @onCreated="(editor) => handleCreated2(item, editor)"  :readOnly="true"/>

</div>

</div>



        </div>
        <!-- <el-dialog width="60%" append-to-body v-model="selectVisible" title="标签选择">
            <tag-select @change="handleChange" />
        </el-dialog> -->

    </div>
</template>
<script setup lang="ts">
import { ref, onMounted, shallowRef, onBeforeUnmount, onActivated } from "vue";
import TagSelect from "@/components/Core/TagSelect/index.vue";
import type { FormInstance } from "element-plus";
import { CustomerResultModel } from "@/model/customerModel";
import { useRouter, useRoute } from "vue-router";
import { ElMessage, dayjs } from "element-plus";

import { usePowerCustomer } from "@/hooks/customer/usePowerCustomer";
import '@wangeditor/editor/dist/css/style.css' // 引入 css
import { useFileAction } from "@/hooks/fileAction/useFileAction";
const {
    getFileList,
    handleDownload,
    header,
    handleRemove,
    beforeUpload,
    actionUrl,
    fileList
} = useFileAction();
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
defineOptions({
    name: "CustomerCreate"
});
import {
    getAllSalesmanList, //获取营销人员列表
    updateSalePackage,selectById
} from '@/api'
import { cloneDeep } from 'lodash'
const optionsList = ref<any>([])
async function getAllSalesmanListInfo() {
    const res = await getAllSalesmanList()
    if (res && res.length) {
        optionsList.value = res.map((item: any) => {
            return {
                label: item.name,
                value: item.id
            }
        })
    }

}

const mode = ref('default')
const mode2 = ref('default')
const ruleFormRef = ref<FormInstance>();
const { push, go } = useRouter();
const { query } = useRoute();
const route = useRoute();
type UploadType = {
  type: string;
  id: number;
};
const uploadData = ref<UploadType>({
  type: "9",
  id: undefined
});

const id = route.query.id

onActivated(() => {
    initdetail()
});
const initdetail = async ()=>{
   const row = await selectById(id)
  
   condition.value.packageType = row.packageType
   condition.value.packageName = row.packageName
   condition.value.argumentSet = row.argumentSet
   condition.value.packageDescription = row.packageDescription
   condition.value.purchaseTimeList = row?.purchaseTimeList || ""
    condition.value.minPurchaseTime = row?.minPurchaseTime || ""
    condition.value.deviationAssessmentMethod = row?.deviationAssessmentMethod || ""
    condition.value.greenElectricity = row?.greenElectricity == 1 ? "是" : "否"
    condition.value.photoId = row?.photoId || ""
    uploadData.value.id = row?.photoId || ""
   condition.value.computeFormulaExplain =row.computeFormulaExplain ?  `${row.computeFormulaExplain}` : ""
   condition.value.computeFormula =row.computeFormula ? `${ row.computeFormula }` :  ""
   arrList.value = row.modeExplainList
//    condition.value.computeFormulaExplain = row.computeFormulaExplain
   condition.value.id= row.id
   getFileInofr(row?.photoId || "")
   
}
const getFileInofr = (id) => {
    getFileList("9", id);
}
const dataFormRules = {
    packageName: [
        {
            required: true,
            message: "套餐名称不能为空",
            trigger: "blur"
        }
    ],
    packageType:[
    {
            required: true,
            message: "套餐类型不能为空",
            trigger: "blur"
        }
    ]
 
   
};

const {
    getDictLabelByCode,
    getCityTreeData,
    treeData,
    getUserList
} = usePowerCustomer(false);


const condition = ref<any>({
    packageName:"",
    packageType:"",
    packageDescription:"",
    argumentSet:"",
    computeFormulaExplain:"",
    
    computeFormula:"",
    modeExplain:"",
    purchaseTimeList: "",
    minPurchaseTime: "",
    deviationAssessmentMethod: "",
    greenElectricity: "",
    isGreenElectricity: "",
    photoId: "",
    modeExplainList:[],
    id:""
});

const options = [
    {
        value: '1月',
        label: '1月',
    },
    {
        value: '2月',
        label: '2月',
    },
    {
        value: '3月',
        label: '3月',
    },
    {
        value: '4月',
        label: '4月',
    },
    {
        value: '5月',
        label: '5月',
    },
    {
        value: '6月',
        label: '6月',
    },
    {
        value: '7月',
        label: '7月',
    },
    {
        value: '8月',
        label: '8月',
    },
    {
        value: '9月',
        label: '9月',
    },
    {
        value: '10月',
        label: '10月',
    },
    {
        value: '11月',
        label: '11月',
    },
    {
        value: '一年',
        label: '一年',
    },
    {
        value: '两年',
        label: '两年',
    }, {
        value: '三年',
        label: '三年',
    }
]


const changeElectricity = (item:any)=>{
    if(item == "1"){
        condition.value.isGreenElectricity = 0
    }else{
        condition.value.isGreenElectricity = 1
 }
}
const options2 = [
    {
        value: '1',
        label: '是',
    },
    {
        value: '0',
        label: '否',
    }

]
const formInline = ref<CustomerResultModel>({

    basic: {
        name: "",
        isSign: false,
        ownership: undefined,
        areaId: "",
        followerId: "",
        formerName: "",
        annualElectricity: undefined,
        customGrade: undefined,
        industryId: undefined,
        terminationDate: undefined,
        effectiveDate: undefined,
        status: 0,
        socialCreditCode: "",
        bankName: "",
        bankAccount: "",
        registeredCapital: "",
        membershipGroup: "",
        greenDemand: "",
        followerName: "",
        description: "",
        isOpenSea: undefined,
        customIdentity: 1,
        registrationNo: "",
        legalRepresentative: "",
        businessTerm: "",
        businessRegistrationDate: undefined,
        issueDate: undefined,
        registrationAuthority: "",
        registrationStatus: undefined,
        registeredAddress: "",
        businessScope: "",
        electricalNature: undefined,
        mainBusiness: "",
        monthlyAverageElectricity: undefined
    },
    electricity: {
        agentType: undefined,
        annualElectricity: undefined,
        customerSource: undefined,
        greenDemand: ""
    },
    middleman: {
        annualProxyElectricity: undefined
    },
    tagNames: "",
    tagList: [],
    contactList: [
        {
            id: undefined,
            sex: undefined,
            name: "",
            role: "",
            post: "",
            birthday: undefined,
            mobile: "",
            fixedTelephone: "",
            fax: "",
            wechat: "",
            qq: "",
            email: "",
            customName: "",
            customId: undefined
        }
    ]
});

const arrList = ref<any>([])


async function submit(formEl: FormInstance | undefined) {
    if (!formEl) return;
    await formEl.validate(async (valid, fields) => {
        if (valid) {
           condition.value.modeExplainList.push({
            content:condition.value.computeFormulaExplain,
            title:"售电公司收益模式计算公式"
           },{
            content:condition.value.modeExplain,
            title:"售电公司收益模式计算公式"
           })
           updateSalePackage(condition.value).then(res=>{
                goBackAndClearStack()
                ElMessage({
                    message: "修改套餐成功",
                    type: "success"
                });
            }).catch(err=>{
                ElMessage({
                    message: "修改套餐失败",
                    type: "error"
                });
            });
        
         
        } else {
            ElMessage({
                    message: "请检查表单是否有必填项",
                    type: "error"
                });
        }
    });
}

// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef()
const editorRef2 = shallowRef()



const toolbarConfig = {
    excludeKeys: [ 'group-image', 'group-video',"headerSelect","group-more-style","emotion", "insertTable","codeBlock","divider","undo", "redo","fullScreen","blockquote","color", "bgColor","fontSize", "fontFamily", "lineHeight","todo","group-justify", "group-indent", "insertLink" ]
       
    
}
const toolbarConfig2 = {
    excludeKeys: [ 'group-image', 'group-video',"headerSelect","group-more-style","emotion", "insertTable","codeBlock","divider","undo", "redo","fullScreen","blockquote","color", "bgColor","fontSize", "fontFamily", "lineHeight","todo","group-justify", "group-indent", "insertLink" ]
       
    
}

    placeholder: '请输入内容.. '
const editorConfig = { placeholder: '',readOnly: true,
    
 }


// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
    const editor = editorRef.value
    const editor2 = editorRef2.value
    if (editor == null || editor2) return
    editor.destroy()
})

const handleCreated = (editor) => {
    editorRef.value = editor // 记录 editor 实例，重要！

}

const handleCreated2 = (item, editor) => {
    item.editor = editor
}



function handleSelect(data) {
    const name = optionsList.value.find(i => i.value == data).label;
    formInline.value.basic.followerName = name;
}

function goBackAndClearStack() {
    if (window.history.length <= 1) {
        push({ path: "/setMeal" });
        return false;
    } else {
        go(-1);
    }
}
onMounted(() => {
    formInline.value.basic.isOpenSea = query.isOpenSea === "true" ? true : false;
  

    getCityTreeData();
    getUserList();
    getAllSalesmanListInfo()
    initdetail()
});
</script>
<style lang="scss" scoped>
.contacts-form {
    margin-top: 20px;
    border-bottom: 1px solid #e4e7ed;
    padding-bottom: 20px;

    &:last-of-type {
        border: none;
    }
}


.wangeditor-container .w-e-toolbar,
.wangeditor-container .w-e-text-container {
    all: unset;
    /* 取消任何继承的样式 */
}

.main-content {
    height: 1000px;
}

.compiler-container {
    margin-bottom: 20px;
    /* 在容器之间添加一些间距 */
}
.main-content {
    height: 100%;
    padding-bottom: 24px;
}
::v-deep .w-e-text-container{
    cursor: not-allowed;
}

::v-deep .el-form-item__label{
    font-weight: 700
}
</style>