<template>
  <section>
    <el-card class="jt-card">
      <div class="header">
        <div style="display: flex; align-items: center">
          <MyTab @change="positionOverViewCahnge" v-model="positionOverViewActive" :tabs="['年', '月', '日']"></MyTab>
          <el-date-picker :clearable="false" @change="dataChange" style="width: 228px; margin-left: 20px"
            value-format="YYYY" v-if="positionOverViewActive === '年'" v-model="fileYear" type="year"
            placeholder="选择年" />
          <el-date-picker :clearable="false" @change="dataChange" style="width: 228px; margin-left: 20px"
            value-format="YYYY-MM" v-if="positionOverViewActive === '月'" v-model="monthYear" type="month"
            placeholder="选择月"></el-date-picker>
          <el-date-picker :clearable="false" @change="dataChange" v-if="positionOverViewActive === '日'"
            style="width: 228px; margin-left: 20px" value-format="YYYY-MM-DD" v-model="pickerData" type="date" />
        </div>
      </div>
    </el-card>

    <div class="flex-card-wrap" style="display: flex; margin-top: 20px;justify-content: space-between;">
      <el-card v-for="(item, index) in topItem" class='jt-card' style="width: 19%;position: relative;">
        <div v-if="index === 3 || index === 4">
          <div v-if="index === 3">
            <el-button v-if="isEditJunjiaInput" class="change-price" @click="confirmThreePrice()" type="primary">
              确定
            </el-button>
            <el-button v-else class="change-price" @click="changeThreePrice()" type="primary">
              修改
            </el-button>
          </div>
          <div v-if="index === 4">
            <el-button v-if="isEditDianfeiInput" class="change-price" @click="confirmFourPrice()" type="primary">
              确定
            </el-button>
            <el-button v-else class="change-price" @click="changeFourPrice()" type="primary">
              修改
            </el-button>
          </div>
        </div>
        <div class="top-item">
          <div class="left">
            <img width="58" :src="item.img" alt="" />
          </div>
          <div class="right">
            <div class="top">
              <div class="top-top">
                <div v-if="index === 3 || index === 4">
                  <div v-if="index === 3">
                    <el-input @input="(v: any) => (junjiaInput = v.replace(/[^\d.]/g, ''))
            " style="width: 80px" v-if="isEditJunjiaInput" v-model="junjiaInput"></el-input>
                    <div class="top-top-left" v-else>{{ item.topValue }}</div>
                  </div>
                  <div v-if="index === 4">
                    <el-input @input="(v: any) => (dianfeiInput = v.replace(/[^\d.]/g, ''))
            " style="width: 80px" v-if="isEditDianfeiInput" v-model="dianfeiInput"></el-input>
                    <div class="top-top-left" v-else>{{ item.topValue }}</div>
                  </div>
                </div>
                <div v-else>
                  <div class="top-top-left">{{ item.topValue }}</div>
                </div>
                <div class="top-top-right">{{ item.topUnit }}</div>
              </div>

              <div class="top-bottom">{{ item.topName }}</div>
            </div>
            <div class="bottom">
              <div class="bottom-top">
                <div class="bottom-top-top">
                  <div class="bottom-top-top-left">
                    <span>{{ item.linkRelativeRatio }}</span>
                    %
                  </div>
                  <div class="bottom-top-top-right">
                    <span>{{ item.bottomValue }}</span>
                    {{ item.topUnit }}
                  </div>
                </div>
                <div class="bottom-top-bottom">环比</div>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <el-card style="margin-top: 20px" class="jt-card">
      <div class="header">
        <div class="header-title">持仓分析</div>
        <MyTab v-model="analyseActive" @change="analyseChange" :tabs="['图', '表']"></MyTab>
      </div>

      <Echarts v-if="analyseActive === '图'" :echartsData="analyseOptionDeal" EWidth="100%" EHeight="430px"
        echartId="analyse"></Echarts>

      <el-table v-else class="table" :header-cell-style="{
            borderColor: '#DCDFE6',
            color: '#1D2129',
            backgroundColor: '#F2F3F5',
          }" :data="TableData" border style="width: 100%; margin-top: 20px" height="430">
        <el-table-column prop="holdingTime" label="日期" />
        <el-table-column prop="energy" label="持仓电量(MWh)" />
        <el-table-column prop="predictedPower" label="实际发电量(MWh)" />
        <el-table-column prop="price" label="持仓均价(元/MWh)" />
      </el-table>
    </el-card>
  </section>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick} from 'vue'
import dayjs from 'dayjs'
import { echartsConfigBottom } from '@/utils/echartsConfig'

import {
  overviewOfHoldings,
  addHolding, // 新增市场化信息
  updateHolding, // 修改市场化信息
  getHoldingViewInfo, // 持仓分析
} from '@/api'

const TableData = ref<any>([])

// 顶部选择框
const positionOverViewActive = ref<any>('日')
function positionOverViewCahnge() {
  nextTick(() => {
    getoverviewOfHoldings()
    fetHoldingViewInfo()
  })
}
// 时间选择器
const fileYear = ref<any>(dayjs().format('YYYY'))
const monthYear = ref<any>(dayjs().format('YYYY-MM'))

const pickerData = ref<any>(dayjs().format('YYYY-MM-DD'))

function dataChange() {
  getoverviewOfHoldings()
  fetHoldingViewInfo()
}
// 查询按钮
const getAssetURL = (image: any) => {
  return new URL(`../../../assets/svg/${image}.svg`, import.meta.url).href
}

// 顶部五个卡片
const topItem = ref([
  {
    img: getAssetURL('Frame'),
    topValue: 0,
    topUnit: 'MWh',
    topName: '中长期持仓量',
    linkRelativeRatio: 0,
    bottomValue: 0,
  },
  {
    img: getAssetURL('Frame 113350'),
    topValue: 0,
    topUnit: '元/MWh',
    topName: '中长期持仓均价',
    linkRelativeRatio: 0,
    bottomValue: 0,
  },
  {
    img: getAssetURL('Frame1'),
    topValue: 0,
    topUnit: '元',
    topName: '中长期电费',
    linkRelativeRatio: 0,
    bottomValue: 0,
  },
  {
    img: getAssetURL('Frame2'),
    topValue: 0,
    topUnit: '元/MWh',
    topName: '市场化合约均价',
    linkRelativeRatio: 0,
    bottomValue: 0,
  },
  {
    img: getAssetURL('Frame 427318717'),
    topValue: 0,
    topUnit: '元',
    topName: '市场化合约电费',
    linkRelativeRatio: 0,
    bottomValue: 0,
  },
])

// 顶部卡片修改
function changeThreePrice() {
  junjiaInput.value = null
  isEditJunjiaInput.value = true
}
async function confirmThreePrice() {
  let time
  if (positionOverViewActive.value === '年') {
    time = fileYear.value
  }
  if (positionOverViewActive.value === '月') {
    time = monthYear.value
  }
  if (positionOverViewActive.value === '日') {
    time = pickerData.value
  }
  if (!topValue.value.marketId) {
    await addHolding({
      marketAvgPrice: junjiaInput.value,
      type: obj[positionOverViewActive.value],
      holdingTime: time,
    })
    getoverviewOfHoldings()
  } else {
    await updateHolding({
      marketAvgPrice: junjiaInput.value,
      type: obj[positionOverViewActive.value],
      holdingTime: time,
      id: topValue.value.marketId,
    })
    getoverviewOfHoldings()
  }
  isEditJunjiaInput.value = false
}
function changeFourPrice() {
  dianfeiInput.value = null
  isEditDianfeiInput.value = true
}
async function confirmFourPrice() {
  let time
  if (positionOverViewActive.value === '年') {
    time = fileYear.value
  }
  if (positionOverViewActive.value === '月') {
    time = monthYear.value
  }
  if (positionOverViewActive.value === '日') {
    time = pickerData.value
  }
  if (!topValue.value.marketId) {
    await addHolding({
      marketElectricityCost: dianfeiInput.value,
      type: obj[positionOverViewActive.value],
      holdingTime: time,
    })
    getoverviewOfHoldings()
  } else {
    await updateHolding({
      marketElectricityCost: dianfeiInput.value,
      type: obj[positionOverViewActive.value],
      holdingTime: time,
      id: topValue.value.marketId,
    })
    getoverviewOfHoldings()
  }
  isEditDianfeiInput.value = false
}
const isEditJunjiaInput = ref(false)
const isEditDianfeiInput = ref(false)
// 市场化合约均价
const junjiaInput = ref<any>()
// 市场化合约电费
const dianfeiInput = ref<any>()

// 持仓分析切换
const analyseActive = ref('图')
function analyseChange() { }

// 持仓分析图
const analyseOption = ref({
  confine: true,
  tooltip: {
    trigger: 'axis',
    backgroundColor: 'rgba(255,255,255,0.90)',
    borderRadius: '4px',
    boxShadow: ' 0px 2px 10px 0px rgba(0, 0, 0, 0.16)',
    formatter: function (params: any) {
      let context
      let content = params.map((item: any) => {
        if (
          item.seriesName === '持仓电量' ||
          item.seriesName === '实际发电量'
        ) {
          return `${item.marker}<span>${item.seriesName
            }</span>:<span style="margin-left: 10px;float: right">${item.value ? item.value : '--'
            }MWh</span><br>`
        }
        if (item.seriesName === '持仓均格') {
          return `${item.marker}<span>${item.seriesName
            }</span>:<span style="margin-left: 10px;float: right">${item.value ? item.value : '--'
            }元/MWh</span><br>`
        }
      })
      let newContent = ''
      content.forEach((item: any) => {
        newContent = newContent + item
      })
      context = `<div>${params[0].name}</div><div>${newContent}</div>`
      return context
    },
  },
  legend: {},
  grid: {
    top: '18%',
    left: '5%',
    right: '5%',
    bottom: '15%',
  },
  xAxis: {
    type: 'category',
    data: [],
    axisTick: {
      show: false, // 将此选项设置为 false 来隐藏 X 轴刻度
    },
  },
  yAxis: [
    {
      position: 'left',
      type: 'value',
      name: '发电量(MWh)',
      nameTextStyle: {
        // y轴name的样式调整
        fontSize: 14,
      },
      splitLine: {
        show: false, //想要不显示网格线，改为false
      },
    },
    {
      position: 'right',
      type: 'value',
      name: '价格(元/MWh)',
      nameTextStyle: {
        // y轴name的样式调整
        fontSize: 14,
      },
      splitLine: {
        show: false, //想要不显示网格线，改为false
      },
    },
  ],
  series: [
    {
      name: '持仓电量',
      data: [],
      type: 'bar',
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        color: '#254f7a',
      },
    },
    {
      name: '实际发电量',
      data: [],
      type: 'bar',
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        color: '#029CD4',
      },
    },
    {
      name: '持仓均格',
      data: [],
      type: 'line',
      yAxisIndex: 1,
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      lineStyle: {
        color: '#F5BA18',
        width: 2,
        type: 'solid', //'dotted'虚线 'solid'实线
      },
      itemStyle: {
        color: '#F5BA18', //改变折线点的颜色
      },
    },
  ],
})
const analyseOptionDeal = echartsConfigBottom(analyseOption.value)

// 顶部卡片全部数据
let topValue = ref<any>({})
const obj: any = {
  年: 1,
  月: 2,
  日: 3,
}
async function getoverviewOfHoldings() {
  let time
  if (positionOverViewActive.value === '年') {
    time = fileYear.value
  }
  if (positionOverViewActive.value === '月') {
    time = monthYear.value
  }
  if (positionOverViewActive.value === '日') {
    time = pickerData.value
  }
  const res = await overviewOfHoldings({
    queryTime: time,
    queryType: obj[positionOverViewActive.value],
  })
  topValue.value = res
  topItem.value[0].topValue = res.totalElectricity || 0
  topItem.value[0].linkRelativeRatio = res.momElectricityRate * 100 || 0
  topItem.value[0].bottomValue = res.momElectricity || 0

  topItem.value[1].topValue = res.avgPrice || 0
  topItem.value[1].linkRelativeRatio = res.momAvgPriceRate * 100 || 0
  topItem.value[1].bottomValue = res.momAvgPrice || 0

  topItem.value[2].topValue = res.midLongTermPrice || 0
  topItem.value[2].linkRelativeRatio = res.momMidLongTermRate * 100 || 0
  topItem.value[2].bottomValue = res.momMidLongTermPrice || 0

  topItem.value[3].topValue = res.marketAvgPrice || 0
  topItem.value[3].linkRelativeRatio = res.momMarketAvgPriceRate * 100 || 0
  topItem.value[3].bottomValue = res.momMarketAvgPrice || 0

  topItem.value[4].topValue = res.marketElectricityCost || 0
  topItem.value[4].linkRelativeRatio =
    res.momMarketElectricityCostRate * 100 || 0
  topItem.value[4].bottomValue = res.momMarketElectricityCost || 0
}
// 获取持仓分析数据
async function fetHoldingViewInfo() {
  analyseOption.value.xAxis.data = []
  analyseOption.value.series[0].data = []
  analyseOption.value.series[1].data = []
  analyseOption.value.series[2].data = []
  TableData.value = []
  let time
  if (positionOverViewActive.value === '年') {
    time = fileYear.value
  }
  if (positionOverViewActive.value === '月') {
    time = monthYear.value
  }
  if (positionOverViewActive.value === '日') {
    time = pickerData.value
  }
  const res = await getHoldingViewInfo({
    queryTime: time,
    queryType: obj[positionOverViewActive.value],
  })
  TableData.value = res
  analyseOption.value.xAxis.data = res?.map((item: any) => item.time)
  analyseOption.value.series[0].data = res?.map((item: any) => item.energy)
  analyseOption.value.series[2].data = res?.map((item: any) => item.price)
  console.log(`output->res`, res)
}
onMounted(async () => {
  getoverviewOfHoldings()
  fetHoldingViewInfo()
})
</script>

<style scoped lang="scss">
//el-card头部样式
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .header-title {
    padding: 0px 10px;
    border-left: 3px solid #c3d7f0;
    color: #303133;
    /* 文字/18加粗 */
    font-size: 17px;
    font-weight: 700;
    line-height: 22px;
  }
}

.change-price {
  position: absolute;
  top: 0px;
  right: 5px;
}

.top-item {
  height: 160px;
  display: flex;

  .left {
    width: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .right {
    width: 100%;
    margin-left: 20px;

    .top {
      height: 50%;
      border-bottom: 1px solid #f5f5f5;
      display: flex;
      flex-direction: column;
      justify-content: center;

      &-top {
        margin-bottom: 5px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        &-left {
          color: #254f7a;
          font-size: 16px;
          font-weight: 700;
          line-height: 22px;
        }

        &-right {
          color: #606266;
          font-size: 12px;
          line-height: 22px;
        }
      }

      &-bottom {
        font-size: 14px;
        line-height: 22px;
      }
    }

    .bottom {
      height: 50%;
      display: flex;
      flex-direction: column;
      justify-content: center;

      &-top {
        &-top {
          margin-bottom: 5px;
          display: flex;
          justify-content: space-between;

          &-left {
            span {
              font-weight: 700;
              font-size: 16px;
              color: #00b42a;
            }

            font-size: 12px;
            line-height: 22px;
          }

          &-right {
            span {
              font-weight: 700;
              font-size: 16px;
              color: #00b42a;
            }

            color: #606266;
            font-size: 12px;
            line-height: 22px;
          }
        }

        &-bottom {
          font-size: 14px;
          line-height: 22px;
        }
      }


    }
  }
}
</style>
