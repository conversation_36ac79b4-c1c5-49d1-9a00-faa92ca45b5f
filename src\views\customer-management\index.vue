<template>
  <section>
    <div class="relative app-content-container container-h">
      <!-- <el-tabs @tab-click="tabClick">
        <template v-for="(item, index) of list" :key="item.key">
          <el-tab-pane :label="item.name" :lazy="true">
            <component ref="detailRef" v-if="selected == index" :is="item.component" />
          </el-tab-pane>
        </template>
      </el-tabs> -->
      <div class="absolute top-5 right-5" v-if="selected == 0">
        <el-upload v-model:file-list="fileList" :on-change="handleChange" :limit="1" with-credentials :headers="header"
          :action="actionUrl">
          <el-button type="primary">导入24时电量</el-button>
        </el-upload>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, getCurrentInstance } from "vue";
// import PowerConsumer from "./power-consumer/powerConsumer.vue";
// import MiddlemanConsumer from "./middleman-consumer/middlemanConsumer.vue";
import { getToken } from "@/utils/auth";
import type { UploadUserFile } from "element-plus";
import { ElMessage } from "element-plus";
import { useUserStore } from '@/store/modules/user'
const userStore = useUserStore()
defineOptions({
  name: "CustomerManagement"
});
const { BaseUrl } =
  getCurrentInstance().appContext.config.globalProperties.$config;
const actionUrl = BaseUrl + "/custom/power/upload";
const selected = ref<number>(0);
const detailRef = ref(null);
// 上传请求头
const header = ref({
  Authorization: getToken().accessToken,
  token: userStore.token,
  longToken: userStore.longToken
});
const fileList = ref<UploadUserFile[]>([]);
// const list = [
//   {
//     name: "电力用户",
//     component: PowerConsumer
//   },
//   {
//     name: "居间商",
//     component: MiddlemanConsumer
//   }
// ];
function tabClick({ index }) {
  selected.value = index;
}
function handleChange(data) {
  if (data.response && data.response.code && data.response.code === "200") {
    ElMessage({
      message: data.response.message,
      type: "success"
    });
    fileList.value = [];
  }
}
</script>

<style lang="scss" scoped></style>
