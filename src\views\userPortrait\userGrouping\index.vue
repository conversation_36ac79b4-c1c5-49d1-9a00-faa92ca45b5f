<template>
    <div>
        <el-card class="jt-card">
            <div class="header" style="margin-bottom: 20px">
                <div class="header-title">
                    <img :src="getAssetURL('title-arrow')" alt="" />
                    查询条件
                </div>
            </div>
            <div style="display: flex; justify-content: space-between;">

                <div class="flex flex-wrap gap-4 items-center">
                    <span>最新版本计算状态</span>
                    <el-select v-model="conditions.latestCalculationStatus" placeholder="不限" style="width: 130px"  @change="selectChangeStatus">
                        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                    <span>更新方式</span>
                    <el-select v-model="conditions.updateType" placeholder="不限" style="width: 130px" @change="selectupdataMethod">
                        <el-option v-for="item in options1" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                    <span>运行状态</span>
                    <el-select v-model="conditions.labelStatus" placeholder="不限" style="width: 130px" @change="selectLabelStatus">
                        <el-option v-for="item in options2" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                    <span>创建方式</span>
                    <el-select v-model="conditions.createType" placeholder="不限" style="width: 130px" @change="selectCreateType">
                        <el-option v-for="item in options3" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </div>
                <div>
                    <el-input v-model="conditions.searchKey" placeholder=" 搜索分群显示名/分群名称">
                        <template #append>
                            <el-button @click="handlerSearchKey" type="primary" :icon="Search" />
                        </template>
                    </el-input>
                </div>
            </div>

        </el-card>

        <el-card class="jt-card" style="margin-top: 20px;">
            <div class="header" style="margin-bottom: 20px">
                <div class="header-title">
                    <img :src="getAssetURL('title-arrow')" alt="" />
                    分群列表
                </div>
                <div>
                
                    <el-button style="margin-left: 20px" @click="handleList">
                        <b>列目显示</b>
                    </el-button>
                    <el-button style="margin-left: 20px" type="primary" @click="handleCreateLabel">
                        <b>创建分群</b>
                    </el-button>
                    <!-- <el-button style="margin-left: 20px" type="primary" @click="handledetail">
                        <b>创建标签</b>
                    </el-button> -->
                </div>
            </div>
            <el-table :data="tableData" style="width: 100%; margin-bottom: 20px" row-key="id" border
            :header-cell-style="{
                borderColor: '#DCDFE6',
                color: '#1D2129',
                backgroundColor: '#F2F3F5',
                textAlign: 'center'
            }" >
                <el-table-column fixed="left" prop="date" label="分群" width="200px"  align="center">
                    <template #default="scope" >
                     <span  @click="handledetail(scope.row)" style="cursor: pointer;color: #007bf7" >{{scope.row.groupNameCn  }}</span>
                         

                       

                       

                    </template>
                </el-table-column>
                <!-- <el-table-column prop="id" label="分组ID"  />
                <el-table-column prop="coverNum" label="人数"  >
                    <template #default="scope" >
                        <span v-if="scope.row.coverNum === null">暂无数据</span>
                        <span v-else>{{ scope.row.coverNum }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="latestCalculationStatus" label="最新计算状态">
                    <template #default="scope">
                        <span v-if="scope.row.latestCalculationStatus === 1">成功</span>
                        <span v-if="scope.row.latestCalculationStatus === 2">等待计算</span>
                        <span v-if="scope.row.latestCalculationStatus === 3">失败</span>
                    </template>
                </el-table-column>
                <el-table-column prop="updateType" label="更新方式" >
                    <template #default="scope">
                        <span v-if="scope.row.updateType === 1" class="point"  @click="handlerUpata(scope.row)">手动</span>
                        <span v-if="scope.row.updateType === 2">例行</span>
                        
                    </template>
                </el-table-column>
                <el-table-column prop="groupStatus" label="运行状态" >
                    <template #default="scope">
                    <span v-if="scope.row.groupStatus === 1">上线</span>
                    <span v-if="scope.row.groupStatus === 2">下线</span>
                </template>
                </el-table-column>
                <el-table-column prop="createType" label="创建方式" />
                <el-table-column prop="createTimeStr" label="创建时间" /> -->

                <el-table-column :prop="item.value" v-for="(item, index) in tableDatas" :key="index" :label="item.label"

                align="center">
                    <template #default="scope">
                       
                        <span  class="circle"  v-if="scope.row.latestCalculationStatus === 1 && item.value === 'latestCalculationStatus'"></span>
                        <span  class="grey"  v-if="(scope.row.latestCalculationStatus === 2 || scope.row.latestCalculationStatus === 3) && item.value === 'latestCalculationStatus'"></span>
                        <span v-if="scope.row.latestCalculationStatus === 1 && item.value === 'latestCalculationStatus'">成功</span>
                        <span v-if="scope.row.latestCalculationStatus === 2 && item.value === 'latestCalculationStatus'">等待计算</span>
                        <span v-if="scope.row.latestCalculationStatus === 3 && item.value === 'latestCalculationStatus'">失败</span>
                        <span v-if="scope.row.updateType === 1 && item.value === 'updateType'">
                            <el-icon><VideoPlay /></el-icon>
                        </span>
                        <span v-if="scope.row.updateType === 1 && item.value === 'updateType'" class="routine">例行</span>
                        <span v-if="scope.row.updateType === 2 && item.value === 'updateType'" @click="handlerUpata(scope.row)" class="handler">
                            <el-icon><Pointer /></el-icon>
                        </span>
                        <span v-if="scope.row.updateType === 2 && item.value === 'updateType'" @click="handlerUpata(scope.row)" class="point" style="color:green ;">手动</span>
                        <spa  class="circle"  v-if=" scope.row.groupStatus === 1 && item.value === 'groupStatus' "></spa>
                        <span v-if="scope.row.groupStatus === 1 && item.value === 'groupStatus' ">上线</span>
                        <span v-if="scope.row.groupStatus === 2  && item.value === 'groupStatus' ">下线</span>


                    </template>
                </el-table-column>
                <el-table-column fixed="right" prop="address" label="操作"   width="200px"  align="center">
                    <template #default="scope" >
                         
                            <div style="display: flex; justify-content: center;">
                                <div @click="handledetail2(scope.row)" style="color: #165dff; cursor: pointer;">
                                    详情
                                </div>
                                <el-dropdown style="margin-left: 10px;" trigger="click">
                                    <span class="el-dropdown-link" style="color: #165dff;">
                                        更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
                                    </span>
                                    <template #dropdown>
                                        <el-dropdown-menu>
                                            <el-dropdown-item  @click="handlerEdit(scope.row)" disabled>编辑</el-dropdown-item>
                                            <!-- <el-dropdown-item style="color: #165dff; cursor: pointer;" @click="handlerEdit(scope.row)" disabled>编辑</el-dropdown-item> -->
                                            <el-dropdown-item style="color: #165dff; cursor: pointer;" @click="handlerDel(scope.row)">删除</el-dropdown-item>
                                        </el-dropdown-menu>
                                    </template>
                                </el-dropdown>
                            </div>


                    </template>
                </el-table-column>
            </el-table>
            <div class="pagenation">
            <el-pagination v-model:currentPage="condition.pageNo" v-model:page-size="condition.pageSize"
                :page-sizes="[5, 10, 15, 20]" :disabled="false" :background="false"
                layout="total, prev, pager, next, sizes" :total="condition.total" @change="handleChange" />
        </div>

        </el-card>
        <createLabel ref="refLabel" ></createLabel>
        <showList ref="refShowList" @orderData="getOrderData"></showList>


    </div>

</template>


<script setup lang="ts">
import { nextTick, onMounted, reactive, ref ,onActivated, onDeactivated} from 'vue'
import { Search, Plus, FolderRemove, ArrowDown,VideoPlay,Pointer } from '@element-plus/icons-vue'
import createLabel from './components/createLabel.vue'
import showList from './components/showList.vue'
import { ElInput, ElMessage, ElMessageBox } from 'element-plus'
import { queryGroupByPage, queryLabelList, modifyLabelCategoryName, delCustomGroup, customGroupId } from '@/api'
const { push } = useRouter();

import { useRouter } from 'vue-router'
import { any } from 'vue-types'


const conditions = ref({
    searchKey: "",
    createType: "",
    labelStatus: "",
    latestCalculationStatus: "",
    updateType: ""

})

const inputValue = ref('')
const options = [
    {
        value: null,
        label: '不限',
    },
    {
        value: 1,
        label: '成功',
    },
    {
        value: 2,
        label: '等待计算',
    },
    {
        value: 3,
        label: '失败',
    }

]
const options1 = [
{
        value: null,
        label: '不限',
    },
    {
        value: 2,
        label: '手动',
    },
    {
        value: 1,
        label: '例行',
    }
]
const options2 = [
{
        value: null,
        label: '不限',
    },
    {
        value: 1,
        label: '上线',
    }
    // {
    //     value: '2',
    //     label: '下线',
    // }
]
const options3 = [
    {
        value: null,
        label: '不限',
    },
    {
        value: '规则创建',
        label: '规则创建',
    }
    ]

const condition = reactive({
  pageNo: 1,
  pageSize: 10,
  total: 0,
});
const getAssetURL = (image: any) => {
    return new URL(`../../../assets/svg/${image}.svg`, import.meta.url).href
}

// 表格数据
const tableData = ref([])

onDeactivated(() => {
    conditions.value.searchKey = "",
    conditions.value.createType = "",
    conditions.value.labelStatus = "",
    conditions.value.latestCalculationStatus = "",
    conditions.value.updateType = ""
})
// 获取初始化列表
const initList = async (item: any) => {
    const res = await queryGroupByPage(item)
    console.log(res)
    condition.total = res.totalCount
    tableData.value = res.data
   
}

function transformTreeData(data: any) {
    if (data.length == 0) return [];
    const treeData = ref<any>([]);
    for (const item of data) {
        treeData.value.push({
            ...item.data,
            children: transformTreeData(item.children),
        });
    }
    return treeData.value
}

const handlerUpata = async (row: any) => {
    // console.log(row.id)
    await customGroupId(row.id).then((res: any) => {
        initList({})
    }).catch((err: any) => { })
}
// 跳转分群明细
const handledetail = (row: any) => {
    console.log(row)
    push(`/userPortrait/userGrouping/groupUserDetails?id=${row.id}&&name=${row.groupNameCn}`)
}
const handledetail2 = (row: any) => {
    console.log(row)
    push(`/userPortrait/userGrouping/detail?id=${row.id}&&name=${row.groupNameCn}`)
}








//表格添加一行
const refLabel = ref(null);

const createLabelStatus = ref(false)
const handleCreateLabel = () => {
    refLabel.value.handleCreateLabel('createLable')
    createLabelStatus.value = true
}







const refShowList = ref(null);
//列表类目
const handleList = () => {
    refShowList.value.handleShowList()
}


const searchKey = ref('')
const selectChangeStatus = (row: any) => {
    initList({latestCalculationStatus:conditions.value.latestCalculationStatus, updateType:conditions.value.updateType, labelStatus:conditions.value.labelStatus, createType:conditions.value.createType,searchKey:conditions.value.searchKey})
}
const selectLabelStatus = (row: any) => {
    initList({latestCalculationStatus:conditions.value.latestCalculationStatus, updateType:conditions.value.updateType, labelStatus:conditions.value.labelStatus, createType:conditions.value.createType,searchKey:conditions.value.searchKey})
}
const selectupdataMethod = (row: any) => {
    initList({latestCalculationStatus:conditions.value.latestCalculationStatus, updateType:conditions.value.updateType, labelStatus:conditions.value.labelStatus, createType:conditions.value.createType,searchKey:conditions.value.searchKey})
}
const selectCreateType = (row: any) => {
    initList({latestCalculationStatus:conditions.value.latestCalculationStatus, updateType:conditions.value.updateType, labelStatus:conditions.value.labelStatus, createType:conditions.value.createType,searchKey:conditions.value.searchKey})
}
const handlerSearchKey = (row: any) => {
    initList({latestCalculationStatus:conditions.value.latestCalculationStatus, updateType:conditions.value.updateType, labelStatus:conditions.value.labelStatus, createType:conditions.value.createType,searchKey:conditions.value.searchKey})
}

// 点击删除分类标签

const handlerDel = async(item:any)=>{
    ElMessageBox.confirm(
        '是否确定要删除?',
        
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    )
        .then(() => {
             delCustomGroup(item.id).then(res=>{
            //     ElMessage({
            //     type: 'success',
            //     message: '取消删除',
            // })
                initList({}) 
             })
    
            

        })
        .catch(() => {
            ElMessage({
                type: 'info',
                message: '取消删除',
            })
        })
    
  
}
// 点击编辑
const handlerEdit = async(row:any)=>{
    push(`/userPortrait/userGrouping/editRule?id=${row.id}`);
}
    
const handleChange = () => {
    initList({})
};
onMounted(() => {
    initList({})
})

onActivated(() => {
    initList({})
})



const tableDatas = ref( [
{ label: '分组ID', value: 'id' },
    { label: '人数', value: 'coverNum' },
    { label: '最新计算状态', value: 'latestCalculationStatus' },
    { label: '更新方式', value: 'updateType' },
    { label: '运行状态', value: 'groupStatus' },
    { label: '创建方式', value: 'createType' },
    { label: '创建时间', value: 'createTimeStr' }
])
const getOrderData = (row: any) => {
    console.log(row)
    tableDatas.value = row
    // console.log(tableDatas.value)
}

</script>
<style lang="scss" scoped>
.btn-tag {
    border: none;
    width: 60px;
    margin: 0 15px;
}

.input_value {
    margin: 0 15px;
    width: 80px;
}
.routine{
  
  padding-left: 10px;
}

.pagenation {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
.point {
    display: inline-block;
    width: 30px;
    height: 10px;
    cursor: pointer;
}
.circle{
    display: inline-block;
    width: 8px;
    height: 8px;
    background: green;
    border-radius: 50%;
    margin-right: 5px;
}
.handler{
    padding-right: 10px;
    color:green ;
    cursor: pointer;
}
.grey{
    display: inline-block;
    width: 8px;
    height: 8px;
    background: #7f8385;
    border-radius: 50%;
    margin-right: 5px;
}

</style>