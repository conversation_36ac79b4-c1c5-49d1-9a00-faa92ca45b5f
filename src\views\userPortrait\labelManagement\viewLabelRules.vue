<template>
    <div style="overflow: hidden;">
        <!-- <el-button style="margin: 20px 0 10px 20px;" @click="goBackAndClearStack">返回</el-button> -->
        <!-- <section class="flex overflow-auto"> -->
            <div style="display: flex;height: 100%;">
                <div class="w-[20%] tree-wrapper " ref="dynamicHeight">
                <div style="display: flex;padding: 10px; ">
                    <img src="@/assets/svg/u626.png" alt="" style="width: 36px;height: 30px;">
                    <div style="line-height: 30px;">基础信息与标签规则</div>
                </div>

                <div style="margin-left: 40px;">
                    <div class="manus" @click="scrollTo('basic', '基础信息')" :class="{ activeMenu: activeMenu === 0 }">基础信息
                    </div>
                    <div class="manus" @click="scrollTo('rules', '标签规则')" :class="{ activeMenu: activeMenu === 1 }">标签规则
                    </div>
                </div>

            </div>
            <div  class="tree-wrapper  w-[85%]" ref="dynamicHeight">
                <div class="w-[100%]">
                    <div class="header" style="margin-bottom: 50px;position: fixed;z-index: 99;height: 50px;background-color: #ffffff;width: 65%;top: 115px;padding: 0 20px;">
                        <div class="header-title">
                            <!-- <el-image src="/src/assets/svg/title-arrow.svg" alt="" /> -->
                            <img :src="getAssetURL('title-arrow')" alt="" />
                            基础信息与标签规则
                        </div>

                    </div>
                    <div class="spacer" style="height: 50px;"></div>
                    <el-scrollbar  ref="scrollbarRef">
                        <div class="basic" id="basic">
                            <div class="heander">
                                <div class="circle"></div>
                                <div>基础信息</div>
                            </div>
                            <div class="contant">
                                <el-form :inline="true" :model="formInline" ref="ruleFormRef" :rules="rules">
                                    <!-- <el-form-item label="中文标签显示名" prop="labelNameCn" clearable>
                                        <el-input v-model="formInline.labelNameCn" placeholder="请输入标签显示名"/>
                                    </el-form-item> -->
                                    <el-form-item label="中文标签显示名" prop="labelNameCn" clearable>
                                        <el-input v-model="formInline.labelNameCn" placeholder="请输入标签显示名"  disabled/>
                                    </el-form-item>
                                    <el-form-item label="英文标签显示名" prop="labelNameEn">
                                        <el-input v-model="formInline.labelNameEn" placeholder="请输入英文标签显示名称" disabled>
                                            <template #prepend>user_tag</template>
                                        </el-input>
                                    </el-form-item>
                                    <el-form-item label="分类" prop="categoryId">
                                        <el-tree-select default-expand-all v-model="formInline.categoryId"
                                            check-strictly style="width: 100%"
                                            :props="{ children: 'children', label: 'labelNameCn', value: 'id' }"
                                            placeholder="请选择" :data="tableData" :render-after-expand="false"  disabled/>
                                        <!-- <el-tree-select v-model="value" :data="options5" :render-after-expand="false"
                                        style="width: 240px" /> -->
                                    </el-form-item>
                                    <br />
                                    <el-form-item label="备注" prop="labelRemark">
                                        <el-input type="textarea" v-model="formInline.labelRemark"
                                            style="width: 850px;" disabled></el-input>
                                    </el-form-item>
                                </el-form>
                            </div>
                        </div>
                        <div class="basic" style="margin-top: 20px;" id="rules">
                            <div class="heander">
                                <div class="circle"></div>
                                <div>标签规则</div>
                            </div>
                            <div class="contant">
                                <div>创建方式</div>

                                <el-button style="margin: 20px 0; display: flex; box-sizing: border-box;"
                                    disabled >

                                    <!-- <el-image src="/src/assets/svg/rules.svg" alt="" /> -->
                                    <img :src="getAssetURL('rules')" alt="" />
                                    <span style="margin: 10px;">{{ seletType }}</span>
                                    <!-- <el-image src="/src/assets/svg/reset.svg" alt="" /> -->
                                    <el-image :src="getAssetURL('reset')" alt="" />
                                </el-button>
                               
                                <p v-if="seletType === '规则匹配类'">在全部用户中，将满足以下条件的用户分为 {{ layers.length }} 个分层，系统会按照以下自定义分层的顺序进行用户匹配，同一用户会被优先匹配在顺序靠前的分层中</p>
                                <p v-if="seletType === '挖掘类'">在全部用户中，使用“挖掘类”作为标签值，进行用户的标记</p>
                                <p v-if="seletType === '统计类'">在全部用户中，使用“统计类”作为标签值，进行用户的标记</p>
                                <div v-if="seletType === '规则匹配类'">

                                    <el-button v-for="(item, index) in layers" :key="index" round size="large"
                                        class="layeredLevel el-icon--right"
                                        :class="{ activeLayere: layeredIndex === index }" @click="handlerTab(index)"
                                        style="border: 1px solid black;">

                                        <span>{{ layers[index].sliceName }}</span>

                                        <!-- <el-icon class="delete-icon" @click="handleDel(index)" disabled>
                                            <Delete />
                                        </el-icon> -->
                                    </el-button>
                                    <el-button round size="large" @click="addLayered" class="layered" disabled>
                                        <el-icon>
                                            <Plus />
                                        </el-icon>添加分层
                                    </el-button>

                                    <el-form :model="currentLayerForm" label-width="auto" style="width: 100%; "
                                        ref="ruleFormRef2" :rules="rules2">
                                        <el-form-item prop="sliceName">
                                            <el-input v-model="currentLayerForm.sliceName" maxlength="10"
                                                placeholder="请输入分层标题" show-word-limit @input="handleInput(layeredIndex)"
                                                type="text"  disabled/>
                                        </el-form-item>
                                        <el-form-item prop="sliceDescription">
                                            <!-- <el-input v-model="form.desc" type="textarea" /> -->
                                            <el-input type="textarea" v-model="currentLayerForm.sliceDescription"
                                                placeholder="请输入描述信息" @input="handleDescription(layeredIndex)"
                                                maxlength="200" show-word-limit disabled></el-input>
                                        </el-form-item>
                                    </el-form>
                                    <div class="attribute">
                                        <div style="display: flex;justify-content: space-between; ">
                                            <p>用户属性满足</p> <el-button type="primary" @click="addAttribute" disabled>

                                                <el-icon>
                                                    <Plus />
                                                </el-icon>添加</el-button>
                                        </div>
                                        <div style="display: flex;justify-content: space-between;">
                                            <div class="left" v-if="layers[layeredIndex].sliceRules.length > 1">
                                                <div class="round" >
                                                {{ roundName }} 
                                                </div>
                                                <div class="border"></div>
                                            </div>
                                            <div style="width: 90%;">
                                                <div v-for="(item, idx) in layers[layeredIndex].sliceRules" :key="idx"
                                            class="item">

                                            <div class="l_select">


                                                <div class="down">
                                                    <div class="sqlTop" >
                                                        <span>{{ item.sqlSegment }}</span>
                                                        <el-icon>
                                                            <ArrowDown />
                                                        </el-icon>
                                                    </div>
                                                    <!-- <el-input v-model="" style="width: 240px"
                                                    placeholder="Please input" @click="handleClick" /> -->
                                                    <div v-if="isDowns[idx]" class="isDown">
                                                        <div>
                                                            <el-input v-model="searchData" placeholder="请输入关键字" disabled
                                                                class="input-with-select search" @input="handlerSearch">
                                                                <template #prepend>
                                                                    <el-button :icon="Search" class="search" />
                                                                </template>
                                                            </el-input>

                                                        </div>
                                                        <el-scrollbar max-height="200px">

                                                            <ul>
                                                                <li v-for="(option, optionIdx) in options"
                                                                    :key="optionIdx" class="li"
                                                                    @mouseenter="toggleDetail(idx, true, option)"
                                                                    @mouseleave="toggleDetail(idx, false, option)"
                                                                   >
                                                                    <el-icon style="margin-right: 10px;">
                                                                        <Avatar />
                                                                    </el-icon>
                                                                    <span>{{ option.attributeChName }}</span>
                                                                </li>
                                                            </ul>
                                                        </el-scrollbar>
                                                    </div>
                                                    <div v-if="showDetails[idx]" class="d_r">
                                                        <div>{{ detailTitle }}</div>
                                                        <div style="color: #524f4f; font-size: 12px;margin: 10px 0;">
                                                            {{ detailEn }}</div>
                                                        <div>
                                                            <el-tag type="primary">Tag 1</el-tag>
                                                            <span style="margin-left: 10px;">字符类型</span>
                                                        </div>
                                                    </div>
                                                </div>

                                                <el-form-item prop="type" class="formItem">
                                                    <el-select v-model="item.type" placeholder="请选择" disabled
                                                        style="width: 160px; margin-right: 20px; height: 40px;">
                                                        <el-option v-for="item in options2" :key="item.value"
                                                            :label="item.lable" :value="item.value"  />
                                                    </el-select>
                                                </el-form-item>

                                                <el-form-item prop="input" class="formItem">
                                                    <el-input v-if="item.selectType === 0" v-model="item.input"
                                                        style="width: 160px;height: 40px;"
                                                        @change="handlerInput" disabled /></el-form-item>

                                                <DictSelect v-if="item.selectType === 1" v-model="item.input"
                                                    :clearable="true" :dictCode="item.downTypes"
                                                    @change="handlerDictSelect"  disabled/>
                                                <el-tree-select v-if="item.selectType === 3" default-expand-all
                                                    v-model="item.input" check-strictly
                                                    style="width: 240px;height: 200px;"
                                                    :props="{ children: 'children', label: 'name', value: 'id' }" disabled
                                                    placeholder="请选择" :data="treeData" :render-after-expand="false" />
                                                <el-select v-if="item.selectType === 4" v-model="item.label" disabled
                                                    placeholder="请选择" clearable @change="changeRegistration">
                                                    <el-option label="已签约" :value="1"></el-option>
                                                    <el-option label="未签约" :value="0"></el-option>
                                                </el-select>
                                                <el-select v-if="item.selectType === 8" v-model="item.label" disabled
                                                    placeholder="请选择" clearable @change="changeRegistration">
                                                    <el-option label="是" :value="1"></el-option>
                                                    <el-option label="否" :value="0"></el-option>
                                                </el-select>
                                                <el-select v-if="item.selectType === 5"  disabled
                                                    style="width: 160px;height: 40px;" v-model="item.input" filterable
                                                    placeholder="请选择" @change="changeselectType">
                                                    <el-option v-for="item in optionsList" :key="item.value"
                                                        :label="item.label" :value="item.value" />
                                                </el-select>
                                                <!-- <el-select  v-if="selectType === 5" v-model="item.label" placeholder="Select" style="width: 240px">
                                                    <el-option v-for="item in optionsList" :key="item.value"
                                                        :label="item.label" :value="item.value">
                                                        <span style="float: left">{{ item.label }}</span>

                                                    </el-option>
                                                </el-select> -->

                                                <el-date-picker v-if="item.selectType === 6"  disabled
                                                    style="width: 160px;height: 40px;" v-model="item.input"
                                                    type="datetime" placeholder="请选择" format="YYYY/MM/DD"
                                                    value-format="x" />

                                                <el-date-picker v-if="item.selectType === 7"  disabled
                                                    style="width: 160px;height: 40px;" v-model="item.input"
                                                    type="datetime" placeholder="请选择" format="YYYY/MM/DD"
                                                    value-format="x" />
                                            </div>
                                            <div style="display: flex;">
                                                <el-button type="primary" :icon="CirclePlus" disabled
                                                    @click="addItemOperator()"></el-button>
                                                <el-button type="primary" :icon="Delete" disabled
                                                    @click="removeItem(item, idx)" />
                                            </div>
                                        </div>
                                            </div>
                                       
                                        </div>

                                    </div>






                                </div>
                                <div v-if="seletType === '挖掘类'">
                                    <div class="attribute">
                                        
                                        <div style="margin: 10px 0 10px 0;">
                                            <el-form-item label="规则描述" prop="sliceDescription">
                                                <el-input v-model="formInline.sliceDescription" placeholder="请输入规则描述"
                                                    clearable @input="handleTotleDescription"  disabled/>
                                            </el-form-item>
                                        </div>
                                        <div style="display: flex;justify-content: space-between; ">
                                            <p>选择模型名称</p>
                                        </div>
                                        <el-select v-model="selectModle" placeholder="请选择模型"
                                            style="width: 130px;margin-top: 10px;" disabled>
                                            <el-option v-for="item in options1" :key="item.value" :label="item.label"
                                                :value="item.value" />
                                        </el-select>


                                    </div>

                                    
                                </div>

                                <div v-if="seletType === '统计类'" style="margin-top: 10px;">
                                    <div style="display: flex; justify-content: start; width: 700px;">
                                        <el-date-picker v-model="selectTime" type="daterange" unlink-panels disabled
                                            range-separator="至" start-placeholder="开始时间" @change="selectTimeChange"
                                            end-placeholder="结束时间" :shortcuts="shortcuts" :size="size" />
                                        <div style="margin: 6px 15px 0px 15px;">完成</div>
                                        <div>
                                            <!-- <el-select-v2 v-model="value" :options="options4" placeholder="请选择" 
                                                size="large" style="width: 240px" /> -->
                                            <el-select v-model="StatisticalRule.attributeEnName" placeholder="请选择"
                                                size="large" style="width: 240px" disabled>
                                                <el-option v-for="item in options4" :key="item.attributeEnName"
                                                    :label="item.attributeChName" :value="item.attributeEnName" />
                                            </el-select>
                                        </div>
                                        <!-- <div class="screen">
                                            <el-icon><Plus /></el-icon> 
                                            <span>添加筛选条件</span>
                                        </div> -->

                                    </div>
                                    <div style="margin: 20px 0 20px 0;">
                                        <el-select v-model="formInline.countDivideType" placeholder="请选择" size="large"
                                            style="width: 150px" disabled>
                                            <el-option v-for="(opt, index) in options7" :key="index" :label="opt.label"
                                                :value="opt.value" />
                                        </el-select>
                                        <!-- <el-select-v2 v-model="formInline.countDivideType" :options="options7"
                                            placeholder="请选择" size="large" style="width: 150px" /> -->
                                    </div>
                                    <div style="margin-top: 10px;">
                                        <span style="margin-right: 10px;">将标签按</span>
                                        <el-select-v2 v-model="StatisticalRule.type" :options="options6"
                                            placeholder="分层" size="large" style="width: 150px"  disabled/>
                                    </div>

                                    <div>
                                        <div style="margin: 10px 0 10px 0;">
                                            <el-form-item label="规则描述" prop="sliceDescription">
                                                <el-input v-model="formInline.sliceDescription" placeholder="请输入规则描述"
                                                    clearable @input="handleTotleDescription"  disabled/>
                                            </el-form-item>
                                        </div>
                                        <div class="addlayer">
                                            <el-row :gutter="20">
                                                <el-col :span="8">
                                                    <div class="grid-content ep-bg-purple" />分层
                                                </el-col>
                                                <el-col :span="12">
                                                    <div class="grid-content ep-bg-purple" />区间值 {{ StatisticalRule.type
                                                    == '1'?"(按数值由小到大)" :""
                                                    }}
                                                </el-col>

                                            </el-row>
                                        </div>
                                        <div v-if="StatisticalRule.type == '2'">
                                            <el-form :model="layerSectionData" label-width="auto" style="width: 100%; "
                                                ref="ruleFormRef3" :rules="rules3">
                                                <el-row :gutter="20" v-for="(itms, idx) in layerSectionData" :key="idx">
                                                    <el-col :span="8">

                                                        <el-form-item prop="sliceName">
                                                            <el-input v-model="itms.sliceName" placeholder="分层"
                                                                :clearable="false" style="width: 250px;"
                                                                @input="handerSliceName(itms, idx)"  disabled/>
                                                        </el-form-item>
                                                    </el-col>
                                                    <el-col :span="14" style="display: flex;">
                                                        [&nbsp;&nbsp;&nbsp; <el-form-item prop="startValue"
                                                            v-if="idx === 0" style="margin-right: 10px;">
                                                            <el-input placeholder="" :clearable="false"
                                                                v-model="itms.startValue" style="width: 100px;" disabled
                                                                @input="handlerStartValue(itms, idx)" />
                                                        </el-form-item>
                                                        <el-form-item prop="startValue" v-if="idx !== 0"
                                                            style="margin-right: 10px;">
                                                            <el-input placeholder="" :clearable="false"
                                                                @input="handlerStartValue(itms, idx)" disabled
                                                                v-model="layerSectionData[idx-1].endValue  " style="width: 100px;" />
                                                        </el-form-item>
                                                        <span> - &nbsp; </span>
                                                        <el-form-item prop="endValue">
                                                            <el-input placeholder="" :clearable="false"
                                                                v-model="itms.endValue" style="width: 100px;"
                                                                @input="handerNndValue(itms, idx)" disabled
                                                               />
                                                        </el-form-item> &nbsp;&nbsp;&nbsp; <span v-if="idx === layerSectionData.length-1">]</span><span v-else>)</span>

                                                        <!-- <el-icon
                                                            style="margin-top: 8px; margin-left: 15px; cursor: pointer;"
                                                            @click="removeLayerItem(idx)">
                                                            <CloseBold />
                                                        </el-icon> -->
                                                    </el-col>

                                                </el-row>
                                            </el-form>
                                        </div>
                                        <div v-if="StatisticalRule.type == '1'">
                                            <el-form :model="layerSectionData" label-width="auto" style="width: 100%; "
                                                ref="ruleFormRef3" :rules="rules3">
                                                <el-row :gutter="20" v-for="(itms, idx) in layerSectionData" :key="idx">
                                                    <el-col :span="8">

                                                        <el-form-item prop="sliceName">
                                                            <el-input v-model="itms.sliceName" placeholder="分层"
                                                                :clearable="false" style="width: 250px;"
                                                                @input="handerSliceName(itms, idx)" />
                                                        </el-form-item>
                                                    </el-col>
                                                    <el-col :span="14" style="display: flex;">
                                                        [&nbsp;&nbsp;&nbsp; <el-form-item prop="startValue"
                                                            v-if="idx === 0" style="margin-right: 10px;">
                                                            <el-input placeholder="" :clearable="false"
                                                                v-model="itms.startValue" style="width: 130px;" disabled
                                                                @input="handlerStartValue(itms, idx)"> <template
                                                                    #append>%</template>
                                                            </el-input>
                                                        </el-form-item>
                                                        <el-form-item prop="startValue" v-if="idx !== 0"
                                                            style="margin-right: 10px;">
                                                            <el-input placeholder="" :clearable="false"
                                                                @input="handlerStartValue(itms, idx)" disabled
                                                                v-model="itms.startValue" style="width: 130px;">
                                                                <template #append>%</template>
                                                            </el-input>
                                                        </el-form-item>
                                                        <span> - &nbsp; </span>
                                                        <el-form-item prop="endValue">
                                                            <el-input placeholder="" :clearable="false"
                                                                v-model="itms.endValue" style="width: 130px;"
                                                                @input="handerNndValue(itms, idx)" disabled
                                                                >
                                                                <template #append>%</template>
                                                            </el-input>
                                                        </el-form-item> &nbsp;&nbsp;&nbsp; <span v-if="idx === layerSectionData.length-1">]</span><span v-else>)</span>

                                                        <el-icon
                                                            style="margin-top: 8px; margin-left: 15px; cursor: pointer;"
                                                           >
                                                            <CloseBold disabled />
                                                        </el-icon>
                                                    </el-col>

                                                </el-row>
                                            </el-form>
                                        </div>

                                        <div class="addSection"  >
                                            <el-icon>
                                                <Plus />
                                            </el-icon>
                                            <span>添加区间</span>
                                        </div>
                                    </div>

                                </div>



                                <div style="margin-top: 30px ">
                                    <!-- <p style="margin: 20px 0 20px 0;">更新方式</p> -->
                                    <div style="display: flex;">
                                        <el-button style="margin-right: 10px; " @click="handleRoutine" disabled
                                            :class="{ activeLable: activeLable === 1 }">
                                            <img v-if="activeLable === 1" :src="getAssetURL('routine')" 
                                                style="margin-right: 10px;" />
                                            <img v-else :src="getAssetURL('routinegray')"  
                                                style="margin-right: 10px;" />
                                            例行</el-button>
                                        <el-button @click="handleManual" disabled :class="{ activeLable: activeLable === 2 }">

                                            <img v-if="activeLable === 2" :src="getAssetURL('manualgreen')"    
                                                style="margin-right: 10px;" />
                                            <img v-else :src="getAssetURL('manual')" 
                                                style="margin-right: 10px;" />
                                            手动</el-button>
                                    </div>
                                    <div style="margin-top: 20px;">
                                        <div v-if="updateStatus">
                                            <!-- <el-input v-model="formInline.updateDays" style="width: 160px" />  -->
                                            按 <el-input-number v-model="formInline.updateDays" style="width: 160px" disabled
                                                :min="1" :max="30" />
                                            天计算 <span style="color:#bcbcbc ;">标签每日凌晨更新</span>
                                        </div>
                                        <!-- <div v-else>标签生成后，点击“更新”进行数据的更新</div> -->

                                    </div>
                                </div>


                            </div>
                        </div>
                        <div style="height: 50px;"></div>
                    </el-scrollbar>

                </div>
                <div class="footer">
                        <!-- <el-button type="primary" style="margin-right: 20px;"
                            @click="submit(ruleFormRef, ruleFormRef2, ruleFormRef3, ruleFormRef4)">创建</el-button> -->
                        <el-button @click="goBackAndClearStack">返回</el-button>
                    </div>

            </div>

        </div>
            <createLabel ref="refLabel"  :flag="flag" @changeType="getType"></createLabel>
        <!-- </section> -->
    </div>

</template>
<script setup lang="ts">
import { computed, onMounted, ref, watchEffect } from 'vue';
import { Plus, CirclePlus, Delete, Search, Avatar, ArrowDown, Pointer, CloseBold } from "@element-plus/icons-vue";
import { reactive } from 'vue'
import createLabel from './components/createLabel.vue'
import { getUserAttributeMappingList, queryLabelList, getStatisticalUserAttributeMappingList, addCustomLabel, getAllSalesmanList,getLabelInfoById,modifyCustomLabel } from '@/api'
import { usePowerCustomer } from "@/hooks/customer/usePowerCustomer";
const ruleFormRef = ref<FormInstance>()
const ruleFormRef2 = ref<FormInstance>()
const ruleFormRef3 = ref<FormInstance>()
import { useRoute, useRouter } from "vue-router";
import { dayjs, ElMessage, FormInstance } from 'element-plus';
import { json } from 'stream/consumers';
import { nextTick } from 'process';

const { push, go } = useRouter();
const route = useRoute();
const StatisticalRule = ref({
    attributeEnName: "",
    type: "2",
    startValue: "",
    endValue: "",
})
const relate = ref(1)
const type = ref('编辑')
let formInline = reactive({
    categoryId: '',
    createType: '规则匹配类',
    labelId:"",
    labelNameCn: '',
    labelNameEn: '',
    labelRemark: '',
    labelSliceList: [],
    updateDays: 1,
    updateType: 1,
    sliceName: "",
    sliceDescription: "",
    areaId: "",
    countQueryType: StatisticalRule.value.type,
    countDivideType: "总次数",
    rulesDescription: "",
})

// 下拉框搜索内容
const searchData = ref('')


const options1 = [
    {
        value: '模型2',
        label: '模型1',
    },
    {
        value: '模型2',
        label: '模型2',
    },
    {
        value: '模型3',
        label: '模型3',
    },
    {
        value: '模型4',
        label: '模型4',
    },
    {
        value: '模型5',
        label: '模型5',
    }
]

const options6 = [
    {
        value: '2',
        label: '数值区间划分',
    },
    {
        value: '1',
        label: '百分比划分',
    }
]
const options7 = [
    {
        value: '总次数',
        label: '总次数',
    }

]
const options4 = ref([])
const selectDown = ["企业性质", "代理类型", "客户来源", "登记状态", "所属行业", "用电性质", "客户经理", "客户分级"]
const inputtype = ["企业名称","客户身份","用户名称","别名", "曾用名", "月平均用电量", "绿电需求", "统一社会信用代码", "开户银行", "开户账号", "注册资本", "所属集团", "法定代表人", "登记机关", "经营范围", "营业期限", "企业备注"]


const size = ref<'default' | 'large' | 'small'>('default')
const selectTime = ref([])
console.log("2024-6-7", '时间')
const shortcuts = [
    {
        text: '今日',
        value: () => {
            const end = new Date().setHours(23, 59, 59, 999);
            const start = new Date().setHours(0, 0, 0, 0);
            return [new Date(start), new Date(end)];
        },
    },
    {
        text: '昨日',
        value: () => {
            const now = new Date();
            const yesterday = new Date(now.getTime() - 3600 * 1000 * 24);
            const end = yesterday.setHours(23, 59, 59, 999);
            const start = yesterday.setHours(0, 0, 0, 0);
            return [new Date(start), new Date(end)];
        },
    },
    // 注意：这里简化了本周和上周的计算，实际可能需要更复杂的逻辑来处理不同星期的开始日
    {
        text: '本周',
        value: () => {
            // 假设星期日为一周的开始
            const now = new Date();
            const start = new Date(now.getFullYear(), now.getMonth(), now.getDate() - (now.getDay() || 7) + 1);
            const end = new Date(now.getFullYear(), now.getMonth(), now.getDate() + (6 - now.getDay()) || (35 - now.getDay()));
            start.setHours(0, 0, 0, 0);
            end.setHours(23, 59, 59, 999);
            return [start, end];
        },
    },
    // 上周的逻辑类似，但需要再减去一周的时间
    {
        text: '上周',
        value: () => {
            const now = new Date();
            const start = new Date(now.getFullYear(), now.getMonth(), now.getDate() - (now.getDay() || 7) - 6);
            const end = new Date(now.getFullYear(), now.getMonth(), now.getDate() - (now.getDay() || 7));
            start.setHours(0, 0, 0, 0);
            end.setHours(23, 59, 59, 999);
            return [start, end];
        },
    },

    {
        text: '过去7天',
        value: () => {
            const end = new Date();
            const start = new Date(end.getTime() - 3600 * 1000 * 24 * 7);
            start.setHours(0, 0, 0, 0);
            end.setHours(23, 59, 59, 999);
            return [start, end];
        }
    }
]
const selectModle = ref(null)
const activeMenu = ref(0)
const seletType = ref('规则匹配类')
const getType = (type) => {
    // console.log("type",type);
    seletType.value = type
    formInline.createType = type
}
const tableData = ref([])
const handlerDictSelect = (item) => {
     console.log(item);
    layers.value[layeredIndex.value].sliceRules.forEach((i: any) => {
        if (item.value === i.input) {
            i.cnName = i.input
            i.input = item.label;
        }
    });
    // console.log(layers.value[layeredIndex.value].sliceRules);
}
const handlerInput = (item) => {
    console.log(item);
    layers.value[layeredIndex.value].sliceRules.forEach((i: any) => {
        i.cnName = item

    })
}

const changeRegistration = (value: any) => {
    console.log(value);
    layers.value[layeredIndex.value].sliceRules.forEach(item => {
        if (value === 1) {
            item.cnName = 1
            item.input = "已签约"
        } else {
            item.cnName = 0
            item.input = "未签约"
        }
    })
}
// 获取初始化列表
const initList = async () => {
    const res = await queryLabelList({});
    const root = transformTreeData(res);

    const clonedTree = deepClone(root);
    tableData.value = clonedTree;
    // console.log(tableData.value);

    function deepClone(data) {
        const newData = [];
        for (const item of data) {
            let childrenClone = [];
            if (item.level === 0) {
                item.disabled = true
            }
            if (item.children && item.children.length > 0) {
                if (item.level === 1) {

                    childrenClone = [];
                } else {

                    childrenClone = deepClone(item.children);
                }
            }

            newData.push({ ...item, children: childrenClone });
        }
        return newData;
    }




}

const changeselectType = (item: any) => {
    console.log(item);
    layers.value[layeredIndex.value].sliceRules.forEach((items) => {

        optionsList.value.forEach((i: any) => {
            if (items.input === i.value) {
                // items.cnName = i.value;
                items.cnName = i.label;
                items.input = i.label;
                // console.log("Aaa");
            }
        })
    })
    console.log(layers.value[layeredIndex.value].sliceRules, optionsList.value);
}


function transformTreeData(data: any) {
    if (data.length == 0) return [];
    const treeData = ref<any>([]);
    for (const item of data) {
        treeData.value.push({
            ...item.data,
            children: transformTreeData(item.children),
        });
    }
    return treeData.value
}
const refLabel = ref(null);

const flag = ref(true);
const handleCreateLabel = () => {
    flag.value = false;
    refLabel.value.dialogVisible = true;

}



function goBackAndClearStack() {
    if (window.history.length <= 1) {
        push({ path: "/userPortrait/labelManagement" });
        return false;
    } else {
        push({ path: "/userPortrait/labelManagement" })
    }
    // push({ path: "/userPortrait" });
}
const handleClick = (index: any) => {
    isDowns[index] = !isDowns[index];

}


const {

    treeData,

} = usePowerCustomer(false);


// 获取属性映射列表
const getAttributeList = async () => {

    const res = await getUserAttributeMappingList()
    options.value = res

  console.log(options.value);



}
// 获取客户映射属性
const getCustomerAttribute = async () => {
    const res = await getStatisticalUserAttributeMappingList()
    options4.value = res
    StatisticalRule.value.attributeEnName = res[0].attributeEnName
}
let originLabelSliceList = ref()

const getDetailsList = async () => {
    const Id = route.query.id;
 
    const res = await getLabelInfoById(Id)
 
    seletType.value = res.createType
    activeLable.value =res.updateType
    formInline.updateDays = res.updateDays
    // res.updateType === 1
    if( res.updateType === 1){
        updateStatus.value = true;
    }else{
        updateStatus.value = false;
    }
    

    formInline.labelId = res.id
    formInline.labelNameCn = res.labelNameCn
    formInline.labelNameEn = res.labelNameEn
   formInline.labelRemark = res.labelRemark
   formInline.categoryId = res.parentId
    if(res.createType == "规则匹配类"){
   currentLayerForm.value.sliceName = res.sliceName
   currentLayerForm.value.sliceDescription = res.sliceDescription

    
    const newArr = res.labelSliceList

    newArr.forEach((item: any) => {
     console.log("item",item);
        item.sliceRules = JSON.parse(item.sliceRules)
    
        item.sliceRules.forEach((i: any,index:any) => {
         
            relate.value = i.relate

            console.log("i",i,i.sqlSegment);
            if(i.attributeEnName == "follower_name"){
                   
                    i.input = i.attributeValue
                    i.selectType = 5
            }
            else if(i.attributeEnName == "social_credit_code"){
                i.input = i.attributeValue
                i.selectType = 1
            } 
            else if(i.attributeEnName == "is_sign"){
                i.input = i.attributeValue
                i.selectType = 1
            } 
            else if(i.attributeEnName == "is_open_sea"){
                i.input = i.attributeValue
                i.selectType = 1
            } 
            else if(i.attributeEnName == "area_id"){
                i.input = i.cnName
                i.selectType = 1
            } 
            else if(i.attributeEnName == "ownership" || i.attributeEnName == "registration_status" || i.attributeEnName == "electrical_nature"
                || i.attributeEnName == "industry_id" || i.attributeEnName == "agent_type"
            ){
                i.input = i.cnName
                i.selectType = 1
            } 
            else{
                i.input = i.attributeValue
            }
          
        //    layers.value[layeredIndex.value].sliceRules[index].selectType = 5
              ;
            // if()
            options.value.forEach((enNmae: any) => {

                if (enNmae.attributeEnName === i.attributeEnName) {
                   
                    if(i.attributeEnName === "custom_grade"){
                        
                        if(i.input ==  1){
                            i.input = "A"
                        }else if(i.input == 2){
                            i.input = "B"
                        }else if(i.input == 3){
                            i.input = "C"
                        }else if(i.input == 4){
                            i.input = "D"
                        }

                    }
              
                    i.sqlSegment =enNmae.attributeChName
                 
                    
                }
            })
            inputtype.forEach((element: any) => {
        // console.log(element,item.attributeChName)
        if (element === i.sqlSegment) {
            i.selectType = 0
        }

        selectDown.forEach((element: any) => {
        if (element === i.sqlSegment) {
            i.selectType = 1 
        }
    })
    
    
    })

            // i.input = i.cnName
            if (i.type === "=") {
            i.type = "等于"
        } else if (i.type === "!=") {
            i.type = "不等于"
        } else if ( i.type === ">") {
            i.type = "大于"
        } else if ( i.type === "<") {
            i.type = "小于"
        } else if ( i.type === "like") {
            i.type = "包含"
        } else if ( i.type === "not like") {
            i.type = "不包含"
        }
        })


    })
   
    // newArr.forEach((item: any) => {
       
    //     item.sliceRules.forEach((im: any) => {
    //         console.log("哈哈",im.input);
    //      })
    // })
   layers.value = newArr
   console.log("newArr",layers.value);
//    roundName.value === "1" ? "且" : "或"
   if(relate.value == 1){
    roundName.value = "且"
   }else{
    roundName.value = "或"
   }
    StatisticalRule.value.attributeEnName = res.attributeEnName
    StatisticalRule.value.startValue = res.startValue
    StatisticalRule.value.endValue = res.endValue
    }
    

    if(res.createType  === "统计类"){
        console.log("res",res);
        formInline.sliceDescription = res.labelSliceList[0].sliceDescription
        const newArr = res.labelSliceList
        originLabelSliceList.value = res.labelSliceList
        console.log(originLabelSliceList.value )
        const arr = []
        newArr.forEach((item: any) => {
            
            item.sliceRules = JSON.parse(item.sliceRules)
            item.sliceRules.forEach((im: any) => {
                StatisticalRule. value.type = im.type
                console.log("itemsdas ", im);
                im.serialNumber = item.serialNumber
                im.sliceName = item.sliceName
                arr.push(im)
             })
           
            
        })
  
       
        // console.log("newArr", newArr);
        console.log("item", arr[0].startTime);
        selectTime.value.push(arr[0].startTime)
        selectTime.value.push(arr[1].endTime)
        layerSectionData.value = arr
        // console.log("layerSectionData", layerSectionData.value);
    }


    
   
    if(res.createType  === "挖掘类"){
        formInline.sliceDescription = res.labelSliceList[0].sliceDescription
        selectModle.value = res.labelSliceList[0].sliceName
        console.log("挖掘类哈哈哈", res);

    }
    
 
}

// 使用ref来创建一个响应式的layers数组 ==  labelSliceList
const layers = ref([{ sliceName: "分层1", sliceDescription: formInline.sliceDescription, serialNumber: 1, sliceRules: [] },
{ sliceName: "分层2", sliceDescription: formInline.sliceDescription, serialNumber: 2, sliceRules: [] }]);


const layerForms = ref(layers.value.map(layer => ({
    ...layer,
    sliceName: '',
    sliceDescription: ''
})));

const RuleType = ref("2");
const layerSectionData = ref([{
    sliceName: "分层1",
    startValue: "最大值",
    endValue: "",
    sliceDescription: "",
    serialNumber: 1,
    sliceRules: [{ attributeEnName: "", startValue: "", endValue: "", startTime: "", endTime: "", type: "" }]
}, {
    sliceName: `分层2`,
    startValue: "",
    endValue: "",
    sliceDescription: "",
    serialNumber: 2,
    sliceRules: [{ attributeEnName: "", startValue: "", endValue: "", startTime: "", endTime: "", type: "" }]
}])

watchEffect(() => {
    RuleType.value = StatisticalRule.value.type;
    if (RuleType.value === '1') {
        layerSectionData.value[0].startValue = "0"
        layerSectionData.value[layerSectionData.value.length-1].endValue = "100"
    } else {
        layerSectionData.value[0].startValue = "最小值"
        layerSectionData.value[layerSectionData.value.length-1].endValue = "最大值"
    }
});


const dynamicHeight = ref(null);
 
function handleResize() {
  if (dynamicHeight.value) {
    dynamicHeight.value.style.height = `${window.innerHeight - 160}px`;
  }
}


//添加分层

const sectionData = ref(0)
const removeIndex = ref([])
const addLayered = () => {
     //    判断是否删除的元素


     const newLayer = {
        sliceName: `分层${layers.value.length + 1}`,
        sliceDescription: '',
        serialNumber: layers.value.length + 1,
        sliceRules: []
    };
    if(layers.value.length < 5){
        layers.value.push(newLayer);
        console.log(layers.value)
        layerForms.value.push({ ...newLayer, sliceName: '', sliceDescription: '' });
    }else{
        ElMessage({
            type: 'warning',
            message: '最多添加5层'
        });
    }
}

const sectionCod = ref({
    sliceName: "",
    startValue: "",
    endValue: "",

})
// 添加区间
const handerSection = () => {
    const boject = {
        sliceName: `分层${layerSectionData.value.length + 1}`,
        startValue: "",
        endValue: "",
        sliceDescription: "",
        serialNumber: layerSectionData.value.length + 1,
        sliceRules: [{ attributeEnName: "", startValue: "", endValue: "", startTime: "", endTime: "", type: "" }]
    }

    layerSectionData.value.splice(Math.max(0, layerSectionData.value.length - 1), 0, boject);
    // console.log(layerSectionData.value)




}

const handerSliceName = (item: any, index) => {
    console.log(item)
    layerSectionData.value[index].sliceName = item.sliceName


}

const handerNndValue = (item: any, index) => {
    
    // layerSectionData.value[index].sliceRules[0].endValue = item.endValue

    let arrIdex = index === 0 ? 1 : index - 1
    if(index === 1){
        arrIdex = 1
    }
    layerSectionData.value[arrIdex].startValue = item.endValue
    // sectionCod.value.startValue = item.endValue  0,1 1-1 = 0
}

const handlerStartValue = (item: any, index) => {

    layerSectionData.value[index].sliceRules[0].startValue = item.startValue
    console.log(item.startValue, layerSectionData.value[index].sliceRules[0])
}



const options2 = ref([]);


const layeredIndex = ref(0);
// 添加 属性
const addAttribute = () => {
    selectType.value = 0

    if (layers.value.length > layeredIndex.value) {
        layers.value[layeredIndex.value].sliceRules.push({
            sqlSegment: '',
            cnName: "",
            attributeEnName: '',
            type: "",
            input: '',
            specialAttribute: '',
            tableName: ''

        });

    }
    console.log(layers.value)
    // layeredIndex.value++;
}

// 点击图标添加操作
function addItemOperator() {
    console.log(layers.value[layeredIndex.value])
    selectType.value = 0
    console.log(layeredIndex.value)
    layers.value[layeredIndex.value].sliceRules.push({
        sqlSegment: '',
        attributeEnName: '',
        cnName: "",
        type: "",
        input: '',
        specialAttribute: "",
        tableName: ""
    });

}

// 删除操作
function removeItem() {
    // items.value.splice(index, 1);
 console.log(layeredIndex.value)
    layers.value[layeredIndex.value].sliceRules.splice(layeredIndex.value, 1);
}

const removeLayerItem = (idx: any) => {

    layerSectionData.value.splice(idx, 1);
}
const rules = reactive({
    labelNameCn: [
        { required: true, message: '请输入标签显示名', trigger: ['blur', 'change'] },
        { max: 20, message: '最大只能输入20个字符', trigger: ['change'] },
    ],
    labelNameEn: [
        { required: true, message: '请输入标签英文名称', trigger: ['blur', 'change'] },
        { max: 20, message: '最大只能输入20个字符', trigger: ['change'] }
    ],
    categoryId: [
        { required: true, message: '请选择分类', trigger: ['blur', 'change'] },
    ],
    labelRemark: [
        { required: true, message: '请输入备注', trigger: ['blur', 'change'] },
        { max: 200, message: '最大只能输入200个字符', trigger: ['change'] }
    ]
})
const rules2 = reactive({
    sliceName: [
        { required: true, message: '请输入分层标签', trigger: 'blur' },
    ],
    sliceDescription: [
        { required: true, message: '请输入描述信息', trigger: 'blur' },
    ],
    // input: [
    //     { required: true, message: '请输入', trigger: ['blur', 'change'] },
    // ]
})
const rules3 = reactive({
    attributeEnName: [
        { required: false, message: '请输入分层标签', trigger: ['blur', 'change'] },
    ],
    startValue: [
        { required: false, message: '请输入值', trigger: ['blur', 'change'] },
    ],
    endValue: [
        { required: false, message: '请输入值', trigger: ['blur', 'change'] },
    ],
})



const iscreate = ref(false);
const submit = async (formEl: any, formE2: any, formE3: any) => {
   
  

    if (seletType.value === "规则匹配类") {
        console.log(seletType.value,iscreate.value)
        if (!formEl || !formE2) return;
        layers.value.forEach((item: any) => {
        if (item.sliceRules.length !== 0) {
            item.sliceRules.forEach((item2: any) => {
                console.log(item2.input, item2.type, item2.attributeEnName)
                if (item2.input !== "" && item2.type !== "" && item2.attributeEnName !== "") {
                    // iscreate.value = true;
                } else {
                    ElMessage({
                        message: '用户属性不能为空',
                        type: 'warning',
                    });
                    // iscreate.value = false;
                }
            })
        } else {
            ElMessage({
                message: '请选择用户属性',
                type: 'warning',
            });
            // iscreate.value = false;
        }
    })
        const validateAndProcess = async (form: any) => {
            return new Promise((resolve, reject) => {
                form.validate((valid: boolean, fields: any) => {
                    if (valid) {
                        resolve(true);
                    } else {
                        ElMessage({
                            message: '请检查表单是否有必填项',
                            type: 'warning',
                        });
                        // reject(new Error('表单验证失败'));
                    }
                });
            });
        };

        // 同时验证两个表单
        try {
            await Promise.all([validateAndProcess(formEl), validateAndProcess(formE2)]);

            // 表单验证通过后的逻辑
            const newVal = layers.value.map((item: any) => {
                return {
                    sliceName: item.sliceName,
                    sliceDescription: item.sliceDescription,
                    serialNumber: item.serialNumber,
                    sliceRules: JSON.stringify(item.sliceRules.map((item2: any) => {
                        return {
                            sqlSegment: `${item2.attributeEnName} ${item2.type} '${item2.cnName}'`,
                            type: item2.type,
                            createType: formInline.createType,
                            specialAttribute: item2.specialAttribute,
                            tableName: item2.tableName,
                            attributeEnName: item2.attributeEnName,
                            attributeValue: item2.cnName
                        };
                    })),
                };
            });

            formInline.labelSliceList = newVal;

            const params = {
                categoryId: formInline.categoryId,
                createType: formInline.createType,
                labelNameCn: formInline.labelNameCn,
                labelId:Number(formInline.labelId) ,
                labelNameEn: formInline.labelNameEn,
                labelRemark: formInline.labelRemark,
                labelSliceList: formInline.labelSliceList,
                updateDays: formInline.updateDays,
                updateType: formInline.updateType,
            };

            modifyCustomLabel(params).then(res => {
                initList();

                goBackAndClearStack();
                ElMessage({
                    message: '修改成功',
                    type: 'success',
                });
            }).catch(err => {
                ElMessage({
                    message: '修改失败',
                    type: 'warning',
                });
            });

        } catch (error) {

        }
    }

    if (seletType.value === "统计类") {
       
        if (!formEl || !formE3) return;
        const validateAndProcess = async (form: any) => {
            return new Promise((resolve, reject) => {
                form.validate((valid: boolean, fields: any) => {
                    if (valid) {
                        resolve(true);
                    } else {
                        ElMessage({
                            message: '请检查表单是否有必填项',
                            type: 'warning',
                        });
                        reject(new Error('表单验证失败'));
                    }
                });
            });
        };

        // console.log(layerSectionData)
        // 同时验证两个表单
        try {
           
            await Promise.all([validateAndProcess(formEl), validateAndProcess(formE3)]);
            // console.log(layerSectionData.value)
            // 表单验证通过后的逻辑
         
        let  newVal = layerSectionData.value.map((item: any) => {
              console.log(item)
                return {
                    sliceName: item.sliceName,
                    serialNumber: item.serialNumber,
                    sliceDescription: formInline.sliceDescription,
                    sliceRules:JSON.stringify([{ attributeEnName: StatisticalRule.value.attributeEnName,startValue: item.startValue,endValue: item.endValue,type: StatisticalRule.value.type, startTime: dayjs(selectTime.value[0]).format("YYYY-MM-DD"),endTime: dayjs(selectTime.value[1]).format("YYYY-MM-DD")}])
                    // sliceRules:item.sliceRules.map((item2: any) => {
                    //     return {
                    //         attributeEnName: StatisticalRule.value.attributeEnName,
                    //         startValue: item2.startValue,
                    //         endValue: item2.endValue,
                    //         type: StatisticalRule.value.type,
                    //         startTime: dayjs(selectTime.value[0]).format("YYYY-MM-DD"),
                    //         endTime: dayjs(selectTime.value[1]).format("YYYY-MM-DD")
                    //     };
                    // })
                }

               
            });

//             console.log(layerSectionData.value)
//            let labelSliceList =  originLabelSliceList.value.map((item:any,index:any) => {
//                 return {
//                     ...item,
//                     sliceRules: [layerSectionData.value[index]],

//                 }
//             })
//             let arr = []
//             console.log(labelSliceList)
//             labelSliceList.forEach((item:any) => {
//                 item.sliceRules.forEach((item2:any) => {
//                 arr.push(item2)
//                 })
//             })
// console.log(arr)
            formInline.labelSliceList = newVal;
            // formInline.countQueryType = StatisticalRule.value.type
            const params = {
                categoryId: formInline.categoryId,
                createType: "统计类",
                countDivideType: formInline.countDivideType,
                countQueryType: formInline.countQueryType,
                labelNameCn: formInline.labelNameCn,
                labelId:formInline.labelId,
                labelNameEn: formInline.labelNameEn,
                labelRemark: formInline.labelRemark,
                labelSliceList: formInline.labelSliceList,//数组{labelSliceList: [{sliceRules:[转json]}]}
                updateDays: formInline.updateDays,
                updateType: formInline.updateType,
            };
              console.log(params)
            modifyCustomLabel(params).then(res => {
                initList();
                goBackAndClearStack();
                ElMessage({
                    message: '修改成功',
                    type: 'success',
                });
            }).catch(err => {
                ElMessage({
                    message: '修改失败',
                    type: 'warning',
                });
            });

        } catch (error) {

        }



    }


    if (seletType.value === "挖掘类") {
        if (!formEl) return;
        console.log("aaa")
        await formEl.validate((valid, fields) => {
            if (valid) {
                const params = {
                categoryId: formInline.categoryId,
                createType: formInline.createType,
                labelNameCn: formInline.labelNameCn,
                labelNameEn: formInline.labelNameEn,
                labelId:formInline.labelId,
                labelRemark: formInline.labelRemark,
                labelSliceList: [{serialNumber:1,modelName:selectModle.value,sliceName:selectModle.value,sliceDescription:formInline.sliceDescription}],
                updateDays: formInline.updateDays,
                updateType: formInline.updateType,
            };
            modifyCustomLabel(params).then(res => {
                initList();

                goBackAndClearStack();
                ElMessage({
                    message: '修改成功',
                    type: 'success',
                });
            }).catch(err => {
                ElMessage({
                    message: '修改失败',
                    type: 'warning',
                });
            });
            } else {
                ElMessage({
                    message: '请检查表单是否有必填项',
                    type: 'warning',
                });
            }
        })

    }

};
const updateStatus = ref(true);
const activeLable = ref(null);
// 例行
const handleRoutine = async () => {
    updateStatus.value = true;
    activeLable.value = 1;
    console.log(activeLable.value)
    formInline.updateType = 1


}
// 手动
const handleManual = async () => {
    updateStatus.value = false;
    formInline.updateType = 2
    activeLable.value = 2;
    console.log(activeLable.value)

}


const options = ref([]);

const isDowns = reactive(new Array(options.value.length).fill(false));
const showDetails = reactive(new Array(options.value.length).fill(false)); // 为每个列表项维护一个showDetail状态


// 切换tab
const handlerTab = (index: any) => {

    layeredIndex.value = index
}
// 计算属性，返回当前分层的表单数据
const currentLayerForm = computed(() => ({
    sliceName: layers.value[layeredIndex.value].sliceName,
    sliceDescription: layers.value[layeredIndex.value].sliceDescription,
}));
console.log(currentLayerForm)
const handleDel = (index: any) => {
    // debugger
    console.log(layers.value)
    if(layers.value.length === 1){
        ElMessage({
                        message: '至少保留一个分层',
                        type: 'warning',
                    });
    }else{
        if(layeredIndex.value  === index && layeredIndex.value !== 0){
            layers.value.splice(layeredIndex.value, 1)
            removeIndex.value.push(index)
            removeIndex.value =removeIndex.value.sort((a, b) => a - b)
            layeredIndex.value = index-1
            console.log("bbb",layeredIndex.value,index,removeIndex.value)
        }else{
            layers.value.splice(index, 1)
            removeIndex.value.push(index)
            removeIndex.value =removeIndex.value.sort((a, b) => a - b)
            console.log("aaa",layeredIndex.value,index,removeIndex.value)
            // layeredIndex.value = index-1
        }
        
    }
  
}
const roundName = ref('且')
const handleLevelRelation = () => {
    roundName.value = roundName.value === '且' ? '或' : '且';
}
const handleInput = (index: any) => {
    layers.value[index].sliceName = currentLayerForm.value.sliceName
    formInline.sliceName = currentLayerForm.value.sliceName


}
const handleDescription = (index: any) => {

    layers.value[index].sliceDescription = currentLayerForm.value.sliceDescription
    formInline.sliceDescription = currentLayerForm.value.sliceDescription
}

// 统计类
const handleTotleDescription = () => {

}

const optionsList = ref<any>([])
async function getAllSalesmanListInfo() {
    const res = await getAllSalesmanList()
    if (res && res.length) {
        optionsList.value = res.map((item: any) => {
            return {
                label: item.name,
                value: item.id
            }
        })
    }
}
let areaName
function getareaName(array) {
    array.forEach((item: any) => {
        if (item.id == formInline.areaId) {
            areaName = item.name
        } else if (item.children) {
            getareaName(item.children)
        }
    });
    return areaName
}
const selectType = ref(0)

const getAssetURL = (image: any) => {
    return new URL(`../../../assets/svg/${image}.svg`, import.meta.url).href
}

const scrollTo = (sectionId, item) => {

if (item === '标签规则') {
    activeMenu.value = 1
} else {
    activeMenu.value = 0
}
const targetElement = document.querySelector(`#${sectionId}`);
targetElement.scrollIntoView({ behavior: 'smooth' });
};

const handlerSelectItem = (item: any, index: any) => {
    layers.value[layeredIndex.value].sliceRules[index].attributeEnName = item.attributeEnName
    layers.value[layeredIndex.value].sliceRules[index].sqlSegment = item.attributeChName
    layers.value[layeredIndex.value].sliceRules[index].specialAttribute = item.specialAttribute
    layers.value[layeredIndex.value].sliceRules[index].tableName = item.tableName
    inputtype.forEach((element: any) => {
        // console.log(element,item.attributeChName)
        if (element === item.attributeChName) {
            layers.value[layeredIndex.value].sliceRules[index].selectType = 0
        }
    })
    selectDown.forEach((element: any) => {
        if (element === item.attributeChName) {
            layers.value[layeredIndex.value].sliceRules[index].selectType = 1
            layers.value[layeredIndex.value].sliceRules[index].downTypes = processString(item.attributeEnName) 
        }
    })

   if (item.attributeChName === "所在地区") {
    layers.value[layeredIndex.value].sliceRules[index].selectType = 3
        getareaName(treeData.value)
    } else if (item.attributeChName === "客户状态") {
        layers.value[layeredIndex.value].sliceRules[index].selectType = 4 
    } else if (item.attributeChName === "跟进人姓名") {
        layers.value[layeredIndex.value].sliceRules[index].selectType = 5 
     
    } else if (item.attributeChName === "代理生效日期") {
        layers.value[layeredIndex.value].sliceRules[index].selectType = 6
    } else if (item.attributeChName === "代理结束日期") {
        layers.value[layeredIndex.value].sliceRules[index].selectType = 7
    }
    isDowns[index] = false;
    showDetails[index] = false;

    console.log(layers.value[layeredIndex.value].sliceRules)

    options2.value = []
    const cond = item.attributeMatchingRules.split(',')
    cond.forEach((item: any, index: number) => {
        let str = "";
        if (item === "=") {
            str = "等于"
        } else if (item === "!=") {
            str = "不等于"
        } else if (item === ">") {
            str = "大于"
        } else if (item === "<") {
            str = "小于"
        } else if (item === "like") {
            str = "包含"
        } else if (item === "not like") {
            str = "不包含"
        }

        options2.value.push({ value: item, lable: str })
        // console.log(options2.value)

    })


   
    // if (item.attributeChName === "企业性质") {
    //     downTypes.value = "ownership"
    // } else if (item.attributeChName === "代理类型") {
    //     downTypes.value = "agentType"
    // } else if (item.attributeChName === "客户来源") {
    //     downTypes.value = "customerSource"
    // } else if (item.attributeChName === "登记状态") {
    //     downTypes.value = "registrationStatus"
    // } else if (item.attributeChName === "所属行业") {
    //     downTypes.value = "industry"
    // } else if (item.attributeChName === "用电性质") {
    //     downTypes.value = "electricalNature"
    // } else if (item.attributeChName === "客户分级") {
    //     downTypes.value = "customGrade"
    // } else if (item.attributeChName === "所在地区") {
    //     selectType.value = 3
    //     getareaName(treeData.value)
    // } else if (item.attributeChName === "客户状态") {
    //     selectType.value = 4
    // } else if (item.attributeChName === "跟进人姓名") {
    //     selectType.value = 5
    // } else if (item.attributeChName === "代理生效日期") {
    //     selectType.value = 6
    // } else if (item.attributeChName === "代理结束日期") {
    //     selectType.value = 7
    // }


    // const isMatch = selectDown.includes(item.attributeChName);
    // if (isMatch) {
    //     selectType.value = 1
    // }
    // const isIput = inputtype.includes(item.attributeChName);
    // if (isIput) {
    //     selectType.value = 0
    // }

}
function processString(str) {
  // 检查字符串中是否包含下划线
  if (str.includes("_")) {
    // 找到下划线的位置
    const index = str.indexOf("_");
    // 分割字符串为两部分：下划线前和下划线后（包含下划线后的第一个字符）
    const before = str.substring(0, index);
    const after = str.substring(index + 1);
    // 将下划线后的第一个字母大写，然后与原字符串的下划线前部分拼接
    return before + after.charAt(0).toUpperCase() + (after.length > 1 ? after.substring(1) : '');
  }
  // 如果不包含下划线，则返回原字符串
  return str;
}

// 下拉
const detailTitle = ref('')
const detailEn = ref('')
function toggleDetail(idx, isVisible, item: any) {
    showDetails[idx] = isVisible;
    detailTitle.value = item.attributeChName
    detailEn.value = item.attributeEnName
}

const selectTimeChange = (value: any) => {

    layerSectionData.value.forEach(item => {
        item.sliceRules.forEach(tm => {
            tm.startTime = dayjs(value[0]).format("YYYY-MM-DD")
            tm.endTime = dayjs(value[1]).format("YYYY-MM-DD")

        })
    })
    // const a = dayjs(value[0]).format("YYYY-MM-DD")
    // const b = dayjs(value[1]).format("YYYY-MM-DD")
    console.log(layerSectionData.value)
}
const handerBasic = () => {

}

onMounted(async () => {
    handleResize(); // 初始调用
    window.addEventListener('resize', handleResize);
    await initList()
    // 获取属性映射列表
   
    await  getAttributeList()

    // 客户映射属性
    await getCustomerAttribute()
    await  getAllSalesmanListInfo()
    await getDetailsList()
})


</script>

<style scoped lang="scss">
.addlayer {
    width: 100%;
    height: 50px;
    margin-top: 20px;
    padding-left: 10px;
    background: #e6e6e6;
}
.activeMenu {

color: #00b42a;
}


::v-deep .el-input-group__append {
    background-color: #e6e6e6 !important;
}

.addSection {
    width: 80px;
    cursor: not-allowed;
    margin-top: 20px;
    color: #007bff;
}

.el-divider--horizontal {
    margin: 8px 0 8px 0;
}

.formItem {
    height: 40px !important;
}

.activeLable {
    border: 1px solid #00b42a !important;
    color: #00b42a;
}

.activeLayere {
    border: 1px solid #0678fb !important;
    color: #0678fb;
}

::v-deep .el-select .el-input__wrapper {
    height: 40px;
}

.input-with-select .el-input-group__prepend {
    border: none;
    outline: none;
    //   background-color: var(--el-fill-color-blank);
}

.down {
    position: relative;
    margin-right: 20px;

    .d_r {
        width: 240px;
        height: 100px;
        position: absolute;
        right: -240px;
        top: 40px;
        padding: 10px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .li {
        font-size: 14px;
        color: #403d3d;
        padding: 10px;
        cursor: pointer;
    }
}

.search {
    border: none;
    outline: none;
}

.isDown {
    width: 240px;
    margin-top: 5px;
    height: 260px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 2%;

}

.l_select {
    display: flex;
}

.custom-header {
    .el-checkbox {
        display: flex;
        height: unset;
    }
}

.basic {
    width: 98%;
    min-height: 400px;
    border: 1px solid #e6e6e6;
    border-radius: 6px;
    margin-bottom: 30px;
    // padding: 20px;
    margin: 20px;
    .contant {
        padding: 30px;
    }

    .circle {
        width: 15px;
        height: 15px;
        border-radius: 100%;
        background: #254f7a;
        margin-right: 10px;
    }

    .heander {
        width: 100%;
        line-height: 20px;
        display: flex;
        padding: 15px;
        font-size: 18px;
        font-weight: bold;
        height: 50px;
        background: #e5ebf0;
    }
}

.attribute {
    padding: 20px;
    width: 100%;
    border: 1px solid #e6e6e6;
    .left {
    display: flex;
    align-items: center;
    position: relative;
    .round {
      width: 30px;
      height: 30px;
      border-radius: 50%;
      background-color: white;
      border: 1px solid black;
      font-size: 14px;
      color: black;
      margin: auto;
      position: relative;
      z-index: 3;
      text-align: center;
      line-height: 26px;
    }
    .border {
      position: absolute;
      left: 50%;
      top: 0;
      bottom: 0;
      border-left: 1px solid black;
    }
  }
}

.layeredLevel {
    position: relative;
    margin: 20px 20px 20px 0;
    width: 200px;

    .delete-icon {
        /* 如果使用绝对定位，请确保这个类被应用到图标上 */
        position: absolute;
        right: 10px;
        /* 图标距离按钮右侧的距离 */
        top: 50%;
        /* 图标垂直居中对齐 */
        transform: translateY(-50%);
        /* 向上移动图标自身高度的一半以实现垂直居中 */

    }
}

.layered {
    margin: 20px 20px 20px 0;
    width: 200px;

}

::v-deep .el-textarea__inner {
    height: 250px;
}

.tree-wrapper {
    // padding: 20px;
    border-radius: 6px;
    background-color: #fff;
    box-shadow: 0px 2px 22px rgba(0, 0, 0, 0.06);
    margin-right: 20px;
    overflow-y: auto;
}

.item {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
}

.footer {
    width: 68.5%;
    background-color: #ffffff;
    display: flex;
    justify-content: center;
    padding: 10px 5px;
    z-index: 999;
    position: fixed;
    bottom: 40px;

}

.active {
    border: 1px solid #007bff;
}

.sqlTop {
    width: 240px;
    height: 40px;
    border: 1px solid #e6e6e6;
    background: #f5f7fa;
    color: #807d7d;
    border-radius: 5%;
    cursor: not-allowed;
    padding: 8px;
    display: flex;
    justify-content: space-between;
}

.demo-date-picker {
    display: flex;
    width: 240px !important;
    padding: 0;
    flex-wrap: wrap;
}

.demo-date-picker .block {
    padding: 30px 0;
    text-align: center;
    border-right: solid 1px var(--el-border-color);
    flex: 1;
}

.demo-date-picker .block:last-child {
    border-right: none;
}

::v-deep .el-date-editor--daterange {
    width: 240px !important;
}

.demo-date-picker .demonstration {
    display: block;
    color: var(--el-text-color-secondary);
    font-size: 14px;
    margin-bottom: 20px;
}

.screen {
    padding-top: 8px;
    padding-bottom: 8px;
    margin-left: 15px;
    cursor: pointer;
}



.el-row:last-child {
    margin-bottom: 0;
}

.el-col {
    padding-top: 10px;
    padding-left: 20px;
    border-radius: 4px;
}

.grid-content {
    border-radius: 4px;
    line-height: 50px;
}

.activeMenu {

    color: #00b42a;
}

.manus {
    padding: 10px 0;
    cursor: pointer;
}
</style>