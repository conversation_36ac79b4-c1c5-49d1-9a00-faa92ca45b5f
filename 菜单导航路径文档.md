# 系统菜单导航路径文档

本文档详细记录了系统中所有菜单项的导航路径、层级关系和相关配置信息。

## 菜单结构概览

系统采用多级菜单结构，主要包含以下顶级菜单模块：

1. **首页** (rank: 0)
2. **大屏** (rank: 1) 
3. **客户管理** (rank: 2)
4. **负荷管理** (rank: 3)
5. **中长期交易** (rank: 4)
6. **现货交易** (rank: 5)
7. **批发结算管理** (rank: 6)
8. **零售交易** (rank: 7)
9. **数据管理** (rank: 8)
10. **系统管理** (rank: 9)

---

## 详细菜单路径

### 1. 首页模块 (Home)
**路径**: `/`  
**图标**: `homeFilled`  
**排序**: 0

#### 子菜单:
- **首页**
  - 路径: `/welcome`
  - 组件: `@/views/welcome/index.vue`
  - 显示状态: `showLink: false` (不在菜单中显示)
  - 缓存: `keepAlive: true`

---

### 2. 大屏模块 (BigScreen)
**路径**: `/big-screen`  
**图标**: `ep:monitor`  
**排序**: 1

- **大屏**
  - 路径: `/big-screen`
  - 组件: `@/views/big-screen/index.vue`
  - 缓存: `keepAlive: true`

---

### 3. 客户管理模块 (Customer Management)
**路径**: `/customer-management`  
**图标**: `material-symbols:supervisor-account-rounded`  
**排序**: 2

#### 子菜单:
- **客户档案**
  - 路径: `/customer-record/index`
  - 组件: `@/views/customer-management/customer-record/index.vue`
  - 名称: `CustomerRecord`

- **新增客户** (隐藏菜单项)
  - 路径: `/customer-management/customCreate`
  - 组件: `@/views/customer-management/customCreate.vue`
  - 名称: `CustomerCreate`
  - 显示状态: `showLink: false`

- **编辑客户** (隐藏菜单项)
  - 路径: `/customer-management/customUpdate`
  - 组件: `@/views/customer-management/customUpdate.vue`
  - 名称: `customUpdate`
  - 显示状态: `showLink: false`

- **客户信息** (隐藏菜单项)
  - 路径: `/customer-management/customInfo`
  - 组件: `@/views/customer-management/customInfo.vue`
  - 名称: `CustomerInfomation`
  - 显示状态: `showLink: false`

#### CRM管理子模块:
**路径**: `/crm-management`  
**标题**: `CRM管理`

- **驾驶舱**
  - 路径: `/crm-management/sales-cockpit/index`
  - 组件: `@/views/crm-management/sales-cockpit/index.vue`
  - 名称: `SalesCockpit`
  - 缓存: `keepAlive: false`

- **营销人员管理**
  - 路径: `/salesman/index`
  - 组件: `@/views/customer-management/salesman.vue`
  - 名称: `salesman`

#### 用户画像子模块:
**路径**: `/userPortrait`  
**标题**: `用户画像`

---

### 4. 负荷管理模块 (Load Forecasting)
**路径**: `/load-forecasting`  
**图标**: `ep:trend-charts`  
**排序**: 3

#### 子菜单:
- **负荷数据分析**
  - 路径: `/load-forecasting/analysis`
  - 组件: `@/views/load-forecasting/analysis/index.vue`
  - 名称: `LoadForecastingAnalysis`

- **负荷预测**
  - 路径: `/load-forecasting/forecast`
  - 组件: `@/views/load-forecasting/forecast/index.vue`
  - 名称: `LoadForecastingForecast`

- **用能分析**
  - 路径: `/load-forecasting/load-management`
  - 组件: `@/views/load-forecasting/load-management/index.vue`
  - 名称: `LoadForecastingLoadManagement`

- **用能分析详情** (隐藏菜单项)
  - 路径: `/load-forecasting/load-management/detail`
  - 组件: `@/views/load-forecasting/load-management/detail.vue`
  - 名称: `LoadForecastingLoadManagementDetail`
  - 显示状态: `showLink: false`

- **申报电量管理**
  - 路径: `/load-forecasting/declared-electricity`
  - 组件: `@/views/load-forecasting/declared-electricity/index.vue`
  - 名称: `LoadForecastingDeclaredElectricity`

---

### 5. 中长期交易模块 (Medium and Long Term)
**路径**: `/mAndLTerm`  
**图标**: `material-symbols:supervisor-account-rounded`  
**排序**: 4  
**重定向**: `/mAndLTerm/contractAdmin`

#### 子菜单:
- **合约管理**
  - 路径: `/mAndLTerm/contractAdmin`
  - 组件: `@/views/mAndLTerm/contractAdmin/index.vue`
  - 名称: `contractAdmin`

- **持仓概览**
  - 路径: `/mAndLTerm/positionView`
  - 组件: `@/views/mAndLTerm/positionView/index.vue`
  - 名称: `positionView`

- **D-2交易策略**
  - 路径: `/mAndLTerm/newEnergyD2TradingStrategy`
  - 组件: `@/views/mAndLTerm/newEnergyD2TradingStrategy/index.vue`
  - 名称: `newEnergyD2TradingStrategy`

---

### 6. 现货交易模块 (Spot Trading)
**路径**: `/inPvspotTrading`  
**图标**: `material-symbols:supervisor-account-rounded`  
**排序**: 5  
**重定向**: `/inPvspotTrading/tradStrategy`

#### 子菜单:
- **现货交易策略**
  - 路径: `/inPvspotTrading/tradStrategy`
  - 组件: `@/views/inPvspotTrading/tradStrategy/index.vue`
  - 名称: `tradStrategy`

- **价格预测**
  - 路径: `/spotTrading/priceForecasting`
  - 组件: `@/views/priceForecasting/index.vue`
  - 名称: `priceForecasting`

---

### 7. 批发结算管理模块 (Settlement Management)
**路径**: `/settleManage`  
**图标**: `material-symbols:supervisor-account-rounded`  
**排序**: 6  
**重定向**: `/settleManage/dayMonClean`

#### 子菜单:
- **日清月结**
  - 路径: `/settleManage/dayMonClean`
  - 组件: `@/views/settleManage/dayMonClean/index.vue`
  - 名称: `dayMonClean`

- **结算概览**
  - 路径: `/settleManage/settleSum`
  - 组件: `@/views/settleManage/settleSum/index.vue`
  - 名称: `settleSum`

---

### 8. 零售交易模块 (Retail Trading)
**路径**: `/ratail`  
**图标**: `material-symbols:supervisor-account-rounded`  
**排序**: 7  
**重定向**: `/ratail/monthSum`

#### 子菜单:
- **用户结算**
  - 路径: `/ratail/monthSum`
  - 组件: `@/views/settleManage/monthSum/index.vue`
  - 名称: `monthSum`

- **套餐定制**
  - 路径: `/ratail/setMeal`
  - 组件: `@/views/settleManage/setMeal/index.vue`
  - 名称: `setMeal`

- **套餐编辑** (隐藏菜单项)
  - 路径: `/ratail/setMeal/edit`
  - 组件: `@/views/settleManage/setMeal/edit.vue`
  - 名称: `edit`
  - 显示状态: `showLink: false`

- **添加套餐** (隐藏菜单项)
  - 路径: `/ratail/setMeal/add`
  - 组件: `@/views/settleManage/setMeal/add.vue`
  - 名称: `add`
  - 显示状态: `showLink: false`

- **查看详情** (隐藏菜单项)
  - 路径: `/ratail/setMeal/details`
  - 组件: `@/views/settleManage/setMeal/details.vue`
  - 名称: `details`
  - 显示状态: `showLink: false`

---

### 9. 数据管理模块 (Data Management)
**路径**: `/dataManage`  
**图标**: `material-symbols:supervisor-account-rounded`  
**排序**: 8  
**重定向**: `/dataManage/peakFlatValley`

#### 子菜单:
- **峰平谷**
  - 路径: `/dataManage/peakFlatValley`
  - 组件: `@/views/dataManage/peakFlatValley/index.vue`
  - 名称: `peakFlatValley`

- **大屏数据管理**
  - 路径: `/dataManage/screenDataManage`
  - 组件: `@/views/dataManage/screenDataManage/index.vue`
  - 名称: `screenDataManage`

- **市场数据**
  - 路径: `/dataManage/marketData`
  - 组件: `@/views/marketData/index.vue`
  - 名称: `marketData`

---

### 10. 系统管理模块 (System Management)
**路径**: `/sys-management`  
**图标**: `ant-design:tool-outlined`  
**排序**: 9

#### 子菜单:
- **字典管理**
  - 路径: `/sys-management/dict-management/index`
  - 组件: `@/views/sys-management/dict-management/index.vue`
  - 名称: `DictManagement`

- **地市管理**
  - 路径: `/sys-management/city-management/index`
  - 组件: `@/views/sys-management/city-management/index.vue`
  - 名称: `CityManagement`

---

## 特殊路由 (不参与菜单显示)

### 错误页面模块
**路径**: `/error`  
**图标**: `informationLine`  
**显示状态**: `showLink: false`  
**重定向**: `/error/403`

#### 子页面:
- **403 无权限页面**
  - 路径: `/error/403`
  - 组件: `@/views/error/403.vue`
  - 名称: `403`

- **404 页面不存在**
  - 路径: `/error/404`
  - 组件: `@/views/error/404.vue`
  - 名称: `404`

- **500 服务器错误**
  - 路径: `/error/500`
  - 组件: `@/views/error/500.vue`
  - 名称: `500`

### 其他特殊路由
- **登录页面**
  - 路径: `/login`
  - 组件: `@/views/login/index.vue`
  - 名称: `Login`
  - 显示状态: `showLink: false`

- **重定向页面**
  - 路径: `/redirect/:path(.*)`
  - 组件: `@/layout/redirect.vue`
  - 名称: `Redirect`
  - 显示状态: `showLink: false`

---

## 路由参数说明

### 常用Meta属性
- `title`: 菜单显示名称
- `icon`: 菜单图标
- `rank`: 菜单排序权重 (数值越小越靠前)
- `showLink`: 是否在菜单中显示 (默认true)
- `keepAlive`: 是否缓存页面组件
- `redirect`: 路由重定向地址

### 路由层级关系
- 一级路由: 顶级菜单模块
- 二级路由: 模块下的具体功能页面
- 三级路由: 功能页面的详情或操作页面 (通常设置 `showLink: false`)

### URL参数支持
系统支持通过URL参数传递数据，常见的参数类型包括:
- Query参数: `?param=value`
- Path参数: `/path/:id`
- 动态路由参数: 通过路由配置支持

---

## 权限控制

系统根据用户权限动态显示菜单项，权限控制通过以下方式实现:
1. 生产环境下根据用户功能权限过滤菜单
2. 开发环境下显示所有菜单项
3. 通过 `meta.roles` 配置页面访问权限

---

*文档生成时间: 2025-01-13*  
*系统版本: Vue 3 + Vue Router 4*
