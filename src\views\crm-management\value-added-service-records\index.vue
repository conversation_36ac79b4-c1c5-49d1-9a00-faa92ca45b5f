<template>
  <section>
    <div class="app-search-card">
      <div class="app-form-group">
        <div class="ml-[20px]">
          <span>跟进人：</span>
          <el-select style="width: 140px" v-model="searchInfo.responsibleId" filterable placeholder="请选择">
            <el-option v-for="item in optionsList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="ml-[20px]">
          <span>实施开始时间：</span>
          <el-date-picker style="width: 140px" v-model="searchInfo.implementationDateStart" type="date"
            placeholder="请选择" format="YYYY-MM-DD" value-format="x" />
        </div>
        <div class="ml-[20px]">
          <span>实施结束时间：</span>
          <el-date-picker style="width: 140px" v-model="searchInfo.implementationDateEnd" type="date" placeholder="请选择"
            format="YYYY-MM-DD" value-format="x" />
        </div>
      </div>
      <div class="app-btn-group">
        <el-button class="filter-item" type="primary" @click="getList">查询</el-button>
        <el-button class="filter-item" @click="handleReset">重置</el-button>
      </div>
    </div>
    <div class="app-card mt-[20px] p-[20px] pt-[10px]">
      <div class="mb-[10px]">
        <el-button type="primary" @click="handleCreate">新增</el-button>
      </div>
      <pure-table :columns="columns" border stripe :loading="loading" :data="tableData" :pagination="pagination"
        @page-size-change="onSizeChange" @page-current-change="onCurrentChange">
        <template #nameHeader>
          <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate" :column="columns[1]" />
        </template>
        <template #titleHeader>
          <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate" :column="columns[2]" />
        </template>
        <template #customName="{ row }">
          <a @click="handleEdit(row.id)" style="color: #007bf7">{{
            row.customName
          }}</a>
        </template>
        <template #operation="{ row }">
          <el-button link type="primary" size="small" @click="handleEdit(row.id)">编辑</el-button>
          <el-popconfirm width="220" confirm-button-text="确定" cancel-button-text="取消" :icon="InfoFilled"
            icon-color="#626AEF" title="确认删除？" @confirm="handleDel(row.id)">
            <template #reference>
              <el-button link type="danger" size="small">删除</el-button>
            </template>
          </el-popconfirm>
        </template>
      </pure-table>
    </div>
    <el-dialog v-model="dialogVisible" :title="title" width="60%">
      <el-form ref="ruleFormRef" :model="formInline" :rules="dataFormRules" label-width="160px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="客户：" prop="customId">
              <el-input @click="selectVisible = true" v-model="formInline.customName" placeholder="请选择" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="增值服务项目：" prop="title">
              <el-input maxlength="20" show-word-limit v-model="formInline.title" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="预计金额：" prop="estimatedAmount">
              <el-input maxlength="10" show-word-limit v-model="formInline.estimatedAmount" placeholder="请输入" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="实际金额：" prop="actualAmount">
              <el-input maxlength="10" show-word-limit v-model="formInline.actualAmount" placeholder="请输入" />
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="实施日期：" prop="implementationDate">
              <el-date-picker style="width: 100%" v-model="formInline.implementationDate" type="date" placeholder="请选择"
                format="YYYY/MM/DD" value-format="x" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="跟进人：" prop="responsibleId">
              <el-select style="width: 100%" @change="handleSelectResponsible" v-model="formInline.responsibleId"
                filterable placeholder="请选择">
                <el-option v-for="item in optionsList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="8">
            <el-form-item label="客户经理：" prop="managerId">
              <el-select
                style="width: 100%"
                @change="handleSelectManager"
                v-model="formInline.managerId"
                filterable
                placeholder="请选择"
              >
                <el-option
                  v-for="item in optionsList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col> -->
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="附件：" prop="fileList">
              <el-upload v-model:file-list="fileList" with-credentials :headers="header" :data="uploadData"
                :on-preview="handleDownload" :on-remove="handleRemove" :action="actionUrl"
                :before-upload="beforeUpload">
                <el-button type="primary">点击上传</el-button>
                <template #tip>
                  <div class="el-upload__tip">
                    支持所有格式，且大小不超过10M
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submit(ruleFormRef)">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog width="60%" append-to-body v-model="selectVisible" destroy-on-close title="客户选择">
      <customer-select @change="handleClose" />
    </el-dialog>
  </section>
</template>

<script setup lang="ts">
import TableHeadPopover from "@/components/Core/TableHeadPopover/index.vue";
import {ref, reactive, onMounted, getCurrentInstance, nextTick} from "vue";
import CustomerSelect from "@/components/Core/PowerConsumerSelect/powerConsumer.vue";
import { columns } from "./components/data";
import { delay } from "@pureadmin/utils";
import { InfoFilled } from "@element-plus/icons-vue";
import type { PaginationProps } from "@pureadmin/table";
import type { FormInstance } from "element-plus";
import { ElMessage } from "element-plus";
import type { UploadUserFile } from "element-plus";
import { getUnqueIdApi, getAllUserListApi } from "@/api/user";
import {
  getValueAddedListApi,
  delValueAddedApi,
  getValueAddedByIdApi,
  saveValueAddedApi
} from "@/api/customer-management/index";
import dayjs from "dayjs";
import { CustomerValueAddedListModel } from "@/model/customerModel";
import { useDictOptions } from "@/hooks/dict/useDictOptions";
import { useFileAction } from "@/hooks/fileAction/useFileAction";
defineOptions({
  name: "ValueAddedServiceRecords"
});
import {
  getAllSalesmanList, //获取营销人员列表
} from '@/api'
type UploadType = {
  type: string;
  id: string | number;
};
const uploadData = ref<UploadType>({
  type: "6",
  id: ""
});
const timeSlot = ref<number>(dayjs().valueOf());
// const fileList = ref<UploadUserFile[]>([]);
const ruleFormRef = ref<FormInstance>();
const dialogVisible = ref<boolean>(false);
const selectVisible = ref<boolean>(false);
const title = ref<string>("新增");
const {
  getFileList,
  fileList,
  handleDownload,
  header,
  handleRemove,
  beforeUpload,
  actionUrl
} = useFileAction();
const formMap: CustomerValueAddedListModel = {
  id: undefined,
  title: "",
  customId: undefined,
  customName: "",
  estimatedAmount: undefined,
  actualAmount: undefined,
  implementationDate: undefined,
  responsibleId: undefined,
  responsibleName: "",
  managerId: undefined,
  managerName: "",
  status: 8
};
const formInline = ref<CustomerValueAddedListModel>({
  id: undefined,
  title: "",
  customId: undefined,
  customName: "",
  estimatedAmount: undefined,
  actualAmount: undefined,
  implementationDate: undefined,
  responsibleName: "",
  managerName: "",
  responsibleId: undefined,
  managerId: undefined,
  status: null
});
const loading = ref(false);
const tableData = ref([]);

const searchInfo = ref({
  title: undefined,
  customName: undefined,
  responsibleId: undefined,
  implementationDateStart: undefined,
  implementationDateEnd: undefined,
  pageNo: 1,
  pageSize: 10
});
const checkNum = (rule: any, value: any, callback: any) => {
  if (!value) {
    callback();
  }
  if (!Number(value)) {
    callback(new Error("请输入数字"));
  } else {
    callback();
  }
};
function validateFileList(rule, value, callback) {
  nextTick(() => {
    // console.log(value,fileList.value)
    if (fileList.value.length === 0) {
      callback(new Error('请上传至少一个文件'));
    } else {
      callback();
    }
  })
}
const dataFormRules = {
  title: [
    {
      required: true,
      message: "服务项目是必填项",
      trigger: "blur"
    }
  ],
  customId: [
    {
      required: true,
      message: "客户是必填项",
      trigger: "change"
    }
  ],
  estimatedAmount: [
    {
      validator: checkNum,
      trigger: "blur"
    }
  ],
  actualAmount: [
    {
      validator: checkNum,
      trigger: "blur"
    }
  ],
  // fileList: [
  //   {
  //     validator: validateFileList,
  //     required: true,
  //     message: "跟进内容附件是必填项",
  //     trigger: "blur"
  //   }
  // ]
};
/** 分页配置 */
const pagination = reactive<PaginationProps>({
  pageSize: 10,
  currentPage: 1,
  pageSizes: [10, 20, 40, 50],
  total: 0,
  align: "right",
  background: true,
  small: false
});
async function handleCreate() {
  formInline.value.status = 8;
  fileList.value = [];
  title.value = "新增";
  await getUploadFileId();
  Object.assign(formInline.value, formMap);
  formInline.value.id = uploadData.value.id as string;
  dialogVisible.value = true;
}
async function getUploadFileId() {
  // 生成上传的唯一id
  const res = await getUnqueIdApi();
  uploadData.value.id = res.data;
}
function handleReset() {
  searchInfo.value.title = undefined;
  searchInfo.value.customName = undefined;
  searchInfo.value.responsibleId = undefined;
  searchInfo.value.implementationDateStart = undefined;
  searchInfo.value.implementationDateEnd = undefined;
  timeSlot.value = dayjs().valueOf();
  getList();
}
// 表格筛选
function handleTableUpdate(data) {
  searchInfo.value[data.propKey] = data.value;
  getList();
}
async function handleEdit(id: string) {
  formInline.value.status = null;
  title.value = "编辑";
  dialogVisible.value = true;
  uploadData.value.id = id;
  const res = await getValueAddedByIdApi(id);
  getFileList("6", id);
  formInline.value = { ...res.data };
  formInline.value.implementationDate = Number(
    formInline.value.implementationDate
  );
}

async function handleDel(id: string) {
  await delValueAddedApi(id);
  ElMessage({
    message: "操作成功",
    type: "success"
  });
  getList();
}
function onSizeChange(val) {
  searchInfo.value.pageSize = val;
  getList();
}
function onCurrentChange(val) {
  searchInfo.value.pageNo = val;
  getList();
}
async function getList() {
  loading.value = true;
  const { data } = await getValueAddedListApi(searchInfo.value);
  pagination.total = data ? Number(data.totalCount) : 0;
  tableData.value = data ? data.data : [];
  delay(300).then(() => {
    loading.value = false;
  });
}
async function submit(formEl: FormInstance | undefined) {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      const res = await saveValueAddedApi(formInline.value);
      if (res.code === "200") {
        ElMessage({
          message: "操作成功",
          type: "success"
        });
        getList();
        dialogVisible.value = false;
      } else {
        ElMessage({
          message: res.message,
          type: "error"
        });
      }
    } else {
      console.log("error submit!", fields);
    }
  });
}
const optionsList = ref([]);
// 获取所有用户
async function getUserList() {
  const res = await getAllSalesmanList()
  if (res && res.length) {
    optionsList.value = res.map((item: any) => {
      return {
        label: item.name,
        value: item.id
      }
    })
  }
}
// async function getUserList() {
//   const res = await getAllUserListApi();
//   if (res.data) {
//     optionsList.value = res.data.map(item => {
//       return {
//         label: item.name,
//         value: String(item.id)
//       };
//     });
//   }
// }
function handleSelectResponsible(data) {
  const name = optionsList.value.find(i => i.value == data).label;
  formInline.value.responsibleName = name;
}
function handleSelectManager(data) {
  const name = optionsList.value.find(i => i.value == data).label;
  formInline.value.managerName = name;
}
// 客户确认选择弹框事件
function handleClose(row) {
  selectVisible.value = false;
  formInline.value.customId = row.id;
  formInline.value.customName = row.name;
}

onMounted(() => {
  getUserList();
  getList();
});
</script>

<style lang="scss" scoped></style>
