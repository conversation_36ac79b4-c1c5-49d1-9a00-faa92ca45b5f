<template>
  <div>
    <div class="flex justify-between mb-[20px]">
      <div class="flex">
        <div class="app-form-group">
          <div>
            <span>跟进人姓名：</span>
            <el-input style="width: 140px" clearable v-model="searchInfo.followerName" placeholder="请输入跟进人姓名"
              class="filter-item" />
          </div>
          <div class="ml-[20px]">
            <span>所在区域：</span>
            <el-tree-select style="width: 200px" default-expand-all v-model="searchInfo.areaId" check-strictly
              :props="{ children: 'children', label: 'name', value: 'id' }" placeholder="请选择" :data="treeData"
              :render-after-expand="false" />
          </div>
        </div>
        <div class="app-btn-group">
          <el-button class="filter-item" type="primary" @click="getList">查询</el-button>
          <el-button class="filter-item" @click="handleReset">重置</el-button>
        </div>
      </div>
      <el-button type="primary" @click="handleOpen('新增')">新增</el-button>
    </div>
    <pure-table :columns="columns" border stripe :loading="loading" :data="tableData" :pagination="pagination"
      @page-size-change="onSizeChange" @page-current-change="onCurrentChange">
      <template #nameHeader>
        <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate" :column="columns[1]" />
      </template>
      <template #titleHeader>
        <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate" :column="columns[2]" />
      </template>
      <template #followStageHeader>
        <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate" :filter-options="followStageOptions"
          :column="columns[5]" />
      </template>
      <template #customName="{ row }">
        <a @click="handleEdit(row.id)" style="color: #007bf7">{{
              row.customName
            }}</a>
      </template>
      <template #operation="{ row }">
        <el-button link type="primary" size="small" @click="handleEdit(row.id)">编辑</el-button>
        <el-popconfirm width="220" confirm-button-text="确定" cancel-button-text="取消" :icon="InfoFilled"
          icon-color="#626AEF" title="此操作可能删除关联合同记录，是否删除？" @confirm="handleDel(row.id)">
          <template #reference>
            <el-button link type="danger" size="small">删除</el-button>
          </template>
        </el-popconfirm>
      </template>
    </pure-table>

    <el-dialog v-model="dialogVisible" :title="title" width="60%">
      <el-form ref="ruleFormRef" :rules="dataFormRules" :model="formInline" label-width="160px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="客户：" prop="customName">
              <el-input @click="selectVisible = true" v-model="formInline.customName" placeholder="请选择" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="机会名称：" prop="title">
              <el-input maxlength="30" show-word-limit v-model="formInline.title" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所在区域：" prop="areaId">
              <el-tree-select default-expand-all v-model="formInline.areaId" check-strictly style="width: 100%"
                :props="{ children: 'children', label: 'name', value: 'id' }" placeholder="请选择" :data="treeData"
                @node-click="handleAreaChange" :render-after-expand="false" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="商机描述：" prop="content">
              <el-input maxlength="20" show-word-limit v-model="formInline.content" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="跟进阶段：" prop="followStage">
              <DictSelect v-model="formInline.followStage" :clearable="true" dict-code="followUpSelect" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="跟进人：" prop="followerId">
              <el-select style="width: 100%" @change="handleSelect" v-model="formInline.followerId" filterable
                placeholder="请选择">
                <el-option v-for="item in optionsList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>

        </el-row>
        <el-row>
          <!-- <el-col :span="8">
            <el-form-item label="年用电量：" prop="yearlyElectricityQty">
              <el-input
                v-model="formInline.yearlyElectricityQty"
                maxlength="10"
                show-word-limit
                placeholder="请输入"
              />
            </el-form-item>
          </el-col> -->

          <!-- <el-col :span="8">
            <el-form-item label="营销区域：" prop="marketingAreaName">
              <el-input
                maxlength="10"
                show-word-limit
                v-model="formInline.marketingAreaName"
                placeholder="请输入"
              />
            </el-form-item>
          </el-col> -->
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="合同电量：" prop="contractElectricityQty">
              <el-input v-model="formInline.contractElectricityQty" maxlength="10" show-word-limit placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="预计交易日期：" prop="predictTradeDate">
              <el-date-picker style="width: 100%" v-model="formInline.predictTradeDate" type="date" placeholder="请选择"
                format="YYYY/MM/DD" value-format="YYYY-MM-DD" />
            </el-form-item>
          </el-col>
<!--          <el-col :span="8">-->
<!--            <el-form-item label="已有合同电量：" prop="existElectricityQty">-->
<!--              <el-input maxlength="10" show-word-limit v-model="formInline.existElectricityQty" placeholder="请输入" />-->
<!--            </el-form-item>-->
<!--          </el-col>-->
          <el-col :span="8">
            <el-form-item label="跟进内容：" prop="followContent">
              <el-input v-model="formInline.followContent" maxlength="30" show-word-limit placeholder="请输入"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="跟进时间：" prop="followTime">
              <el-date-picker style="width: 100%" v-model="formInline.followTime" format="YYYY/MM/DD" value-format="x" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submit(ruleFormRef)">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog width="60%" append-to-body v-model="selectVisible" destroy-on-close title="客户选择">
      <customer-select @change="handleClose" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from "vue";
import CustomerSelect from "@/components/Core/PowerConsumerSelect/powerConsumer.vue";
import { columns } from "./data";
import { delay } from "@pureadmin/utils";
import { InfoFilled } from "@element-plus/icons-vue";
import type { PaginationProps } from "@pureadmin/table";
import type { FormInstance } from "element-plus";
import { ElMessage } from "element-plus";
import { getAllUserListApi } from "@/api/user";
import {
  getOpportunityListApi,
  delOpportunityApi,
  getOpportunityByIdApi,
  saveOpportunityApi,
  getAreaUserIdApi
} from "@/api/customer-management/index";
import dayjs from "dayjs";
import { CustomerOpportunityListModel } from "@/model/customerModel";
import TableHeadPopover from "@/components/Core/TableHeadPopover/index.vue";
import { getCityTreeApi } from "@/api/sys/city";
import { useDictOptions } from "@/hooks/dict/useDictOptions";
const props = defineProps({
  selectedValue: {
    type: [String, Number],
    default: ""
  }
});
import {
  getAllSalesmanList, //获取营销人员列表
} from '@/api'
const timeSlot = ref<number>(dayjs().valueOf());
const { followStageOptions } = useDictOptions();
const optionsList = ref([]);
const ruleFormRef = ref<FormInstance>();
const dialogVisible = ref<boolean>(false);
const selectVisible = ref<boolean>(false);
const title = ref<string>("新增");
const treeData = ref([]);
const checkNum = (rule: any, value: any, callback: any) => {
  if (!value) {
    callback();
  }
  if (!Number(value)) {
    callback(new Error("请输入数字"));
  } else {
    callback();
  }
};
const dataFormRules = {
  customName: [
    {
      required: true,
      message: "客户是必填项",
      trigger: "change"
    }
  ],
  title: [
    {
      required: false,
      message: "机会名称是必填项",
      trigger: "change"
    }
  ],
  yearlyElectricityQty: [
    {
      validator: checkNum,
      trigger: "blur"
    }
  ],
  contractElectricityQty: [
    {
      validator: checkNum,
      trigger: "blur"
    }
  ],
  existElectricityQty: [
    {
      validator: checkNum,
      trigger: "blur"
    }
  ]
};
const formDataMap: CustomerOpportunityListModel = {
  id: undefined,
  title: "",
  customId: undefined,
  customName: "",
  areaId: undefined,
  areaName: "",
  content: "",
  followStage: undefined,
  followerId: undefined,
  followerName: "",
  yearlyElectricityQty: undefined,
  contractElectricityQty: undefined,
  marketingAreaName: "",
  predictTradeDate: undefined,
  existElectricityQty: undefined
};
const formInline = ref<CustomerOpportunityListModel>({
  id: undefined,
  title: "",
  customId: undefined,
  customName: "",
  areaId: undefined,
  areaName: "",
  content: "",
  followStage: undefined,
  followerId: undefined,
  followerName: "",
  yearlyElectricityQty: undefined,
  contractElectricityQty: undefined,
  marketingAreaName: "",
  predictTradeDate: undefined,
  existElectricityQty: undefined
});
const loading = ref(false);
const tableData = ref([]);

const searchInfo = ref({
  title: undefined,
  customName: undefined,
  followerName: undefined,
  areaId: undefined,
  followStage: 1,
  pageNo: 1,
  pageSize: 10
});
/** 分页配置 */
const pagination = reactive<PaginationProps>({
  pageSize: 10,
  currentPage: 1,
  pageSizes: [10, 20, 40, 50],
  total: 0,
  align: "right",
  background: true,
  small: false
});

async function getCityTreeData() {
  const tree = await getCityTreeApi();
  treeData.value = tree.data;
}

function handleOpen(type: string) {
  dialogVisible.value = true;
  title.value = type;
  Object.assign(formInline.value, formDataMap);
}
async function handleEdit(id: number) {
  title.value = "编辑";
  dialogVisible.value = true;
  const res = await getOpportunityByIdApi(id);
  formInline.value = { ...res.data };
  formInline.value.followTime = Number(formInline.value.followTime)
}
// 表格筛选
function handleTableUpdate(data) {
  searchInfo.value[data.propKey] = data.value;
  getList();
}
async function handleDel(id: number) {
  await delOpportunityApi(id);
  ElMessage({
    message: "操作成功",
    type: "success"
  });
  getList();
}
function onSizeChange(val) {
  searchInfo.value.pageSize = val;
  getList();
}
function onCurrentChange(val) {
  searchInfo.value.pageNo = val;
  getList();
}
function handleReset() {
  searchInfo.value.title = undefined;
  searchInfo.value.customName = undefined;
  searchInfo.value.followerName = undefined;
  searchInfo.value.followStage = undefined;
  searchInfo.value.areaId = undefined;
  timeSlot.value = dayjs().valueOf();
  getList();
}
async function getList() {
  loading.value = true;
  const { data } = await getOpportunityListApi(searchInfo.value);
  pagination.total = data ? Number(data.totalCount) : 0;
  tableData.value = data ? data.data : [];
  delay(300).then(() => {
    loading.value = false;
  });
}
async function submit(formEl: FormInstance | undefined) {
  // console.log(formInline.value)
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      const res = await saveOpportunityApi(formInline.value);
      if (res.code === "200") {
        ElMessage({
          message: "操作成功",
          type: "success"
        });
        getList();
        dialogVisible.value = false;
      } else {
        ElMessage({
          message: res.message,
          type: "success"
        });
      }
    } else {
      console.log("error submit!", fields);
    }
  });
}
// 获取所有用户
async function getUserList() {
  const res = await getAllSalesmanList()
  if (res && res.length) {
    optionsList.value = res.map((item: any) => {
      return {
        label: item.name,
        value: item.id
      }
    })
  }
}
// async function getUserList() {
//   const res = await getAllUserListApi();
//   if (res.data) {
//     optionsList.value = res.data.map(item => {
//       return {
//         label: item.name,
//         value: String(item.id)
//       };
//     });
//   }
// }
// 地区改变事件
async function handleAreaChange(data) {
  const res = await getAreaUserIdApi(data.id);
  if (res.data) {
    handleSelect(res.data);
    formInline.value.followerId = res.data;
  }
}
function handleSelect(data) {
  const name = optionsList.value.find(i => i.value == data).label;
  formInline.value.followerName = name;
}
// 客户确认选择弹框事件
function handleClose(row) {
  selectVisible.value = false;
  formInline.value.customId = row.id;
  formInline.value.customName = row.name;
}

// 监听tab变化
watch(
  () => props.selectedValue,
  newVal => {
    if (newVal == 0) {
      searchInfo.value.followStage = undefined;
    } else {
      searchInfo.value.followStage = newVal as number;
    }
    getList();
  },
  { immediate: true }
);

onMounted(async () => {
  getUserList();
  await getCityTreeData();
  // getList();
});
</script>

<style lang="scss" scoped></style>
