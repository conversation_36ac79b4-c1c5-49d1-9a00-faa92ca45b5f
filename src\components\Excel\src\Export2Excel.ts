import xlsx from "xlsx-js-style";
import type { WorkBook } from "xlsx";
import type { JsonToSheet, AoAToSheet } from "./typing";

const { utils, writeFile } = xlsx;

const DEF_FILE_NAME = "excel-list.xlsx";

export function jsonToSheetXlsx<T = any>({
  data,
  header,
  filename = DEF_FILE_NAME,
  json2sheetOpts = {},
  write2excelOpts = { bookType: "xlsx" }
}: JsonToSheet<T>) {
  const arrData = [...data];
  if (header) {
    arrData.unshift(header);
    json2sheetOpts.skipHeader = true;
  }

  const worksheet = utils.json_to_sheet(arrData, json2sheetOpts);

  /* add worksheet to workbook */
  const workbook: WorkBook = {
    SheetNames: [filename],
    Sheets: {
      [filename]: worksheet
    }
  };
  /* output format determined by filename */
  writeFile(workbook, filename, write2excelOpts);
  /* at this point, out.xlsb will have been downloaded */
}

export function aoaToSheetXlsx<T = any>({
  data,
  header,
  filename = DEF_FILE_NAME,
  cellSetup = {
    sizing: [],
    style: {}
  },
  write2excelOpts = { bookType: "xlsx" }
}: AoAToSheet<T> & { cellSetup?: { sizing: Array<object>; style: object } }) {
  const arrData = [...data];
  if (header) {
    arrData.unshift(header);
  }
  const worksheet = utils.aoa_to_sheet(arrData); // 数据表

  worksheet["!cols"] = cellSetup.sizing;
  //以下是样式设置，样式设置放在组织完数据之后，xlsx-js-style的核心API就是SheetJS的
  Object.keys(worksheet).forEach(key => {
    // 非!开头的属性都是单元格
    if (!key.startsWith("!")) {
      worksheet[key].s = cellSetup.style;
    }
  });

  // 工作簿
  /* add worksheet to workbook */
  const workbook: WorkBook = {
    SheetNames: [filename],
    Sheets: {
      [filename]: worksheet
    }
  };
  /* output format determined by filename */
  writeFile(workbook, filename, write2excelOpts);
  /* at this point, out.xlsb will have been downloaded */
}


// 定义默认样式
const defaultStyle = {
  font: { color: { rgb: '000000' }, sz: 12 },
  fill: { fgColor: { rgb: 'FFFFFF' } },
  alignment: { horizontal: 'center', vertical: 'center', wrapText: false },
  border: {
    top: { style: 'thin' },
    right: { style: 'thin' },
    bottom: { style: 'thin' },
    left: { style: 'thin' }
  }
};

// 定义样式
const styles = {
  谷: {
    font: { color: { rgb: '0561b7' }, sz: 12 },
    fill: { fgColor: { rgb: '9ce1ff' } },
    alignment: { horizontal: 'center', vertical: 'center', wrapText: false },
    border: {
      top: { style: 'thin' },
      right: { style: 'thin' },
      bottom: { style: 'thin' },
      left: { style: 'thin' }
    }
  },
  峰: {
    font: { color: { rgb: 'df6200' }, sz: 12 },
    fill: { fgColor: { rgb: 'ffdead' } },
    alignment: { horizontal: 'center', vertical: 'center', wrapText: false },
    border: {
      top: { style: 'thin' },
      right: { style: 'thin' },
      bottom: { style: 'thin' },
      left: { style: 'thin' }
    }
  },
  平: {
    font: { color: { rgb: '116240' }, sz: 12 },
    fill: { fgColor: { rgb: 'aef7d8' } },
    alignment: { horizontal: 'center', vertical: 'center', wrapText: false },
    border: {
      top: { style: 'thin' },
      right: { style: 'thin' },
      bottom: { style: 'thin' },
      left: { style: 'thin' }
    }
  },
  尖: {
    font: { color: { rgb: 'f61b1b' }, sz: 12 },
    fill: { fgColor: { rgb: 'fbb4b4' } },
    alignment: { horizontal: 'center', vertical: 'center', wrapText: false },
    border: {
      top: { style: 'thin' },
      right: { style: 'thin' },
      bottom: { style: 'thin' },
      left: { style: 'thin' }
    }
  }
};

export function aoaD_2ToSheetXlsx<T = any>({
                                          data,
                                          header,
                                          filename = DEF_FILE_NAME,
                                          cellSetup = {
                                            sizing: [],
                                            style: {}
                                          },
                                          write2excelOpts = { bookType: "xlsx" }
                                        }: AoAToSheet<T> & { cellSetup?: { sizing: Array<object>; style: object } }) {
  const arrData = [...data];
  if (header) {
    arrData.unshift(header);
  }
  const worksheet = utils.aoa_to_sheet(arrData); // 数据表
  console.log(arrData, worksheet)
  worksheet["!cols"] = cellSetup.sizing;
  //以下是样式设置，样式设置放在组织完数据之后，xlsx-js-style的核心API就是SheetJS的
  // Object.keys(worksheet).forEach(key => {
  //   // 非!开头的属性都是单元格
  //   if (!key.startsWith("!")) {
  //     worksheet[key].s = cellSetup.style;
  //   }
  // });

  arrData.forEach((row, rowIndex) => {
    for (let colIndex = 0; colIndex < row.length; colIndex++) {
      const cellRef = utils.encode_cell({ r: rowIndex, c: colIndex });
      const cell = worksheet[cellRef];

      // 应用默认样式
      let style = defaultStyle;

      // 如果最后一列是字符串类型，根据 valleyPeakType 设置特定样式
      if (row.length > 0 && typeof row[row.length - 1] === 'string') {
        const valleyPeakType = row[row.length - 1];
        // @ts-ignore
        const specificStyle = styles[valleyPeakType] || {};
        style = { ...defaultStyle, ...specificStyle };
      }

      if (cell) {
        cell.s = {
          font: style.font,
          fill: style.fill,
          alignment: style.alignment,
          border: style.border
        };
      } else {
        // 如果单元格不存在，则创建一个新的单元格
        worksheet[cellRef] = {
          v: row[colIndex],
          t: typeof row[colIndex],
          s: {
            font: style.font,
            fill: style.fill,
            alignment: style.alignment,
            border: style.border
          }
        };
      }
    }
  });


  // 工作簿
  /* add worksheet to workbook */
  const workbook: WorkBook = {
    SheetNames: [filename],
    Sheets: {
      [filename]: worksheet
    }
  };
  /* output format determined by filename */
  writeFile(workbook, filename, write2excelOpts);
  /* at this point, out.xlsb will have been downloaded */
}
