<template>
  <div ref="chartRef" :style="{ height, width }" />
</template>

<script setup lang="ts">
import {
  ref,
  computed,
  onMounted,
  watch,
  type Ref,
  PropType,
  nextTick,
  onActivated
} from "vue";
import { useAppStoreHook } from "@/store/modules/app";
import {
  delay,
  useDark,
  useECharts,
  type EchartOptions
} from "@pureadmin/utils";
import * as echarts from "echarts/core";
import type { EChartsOption } from "echarts";
// import { MarkPointComponent, MarkLineComponent } from "echarts/components";
import { triggerWindowResize } from "@/utils/event";
// echarts.use([MarkPointComponent, MarkLineComponent]);
const emit = defineEmits(["update"]);
const props = defineProps({
  height: {
    type: String as PropType<string>,
    default: "100%"
  },
  width: {
    type: String as PropType<string>,
    default: "100%"
  },
  xData: {
    type: Array as PropType<string[] | number[]>,
    default: () => []
  },
  yData: {
    type: Array as PropType<string[] | number[]>,
    default: () => []
  },
  series: {
    type: Array as PropType<Array<object>>,
    default: () => []
  }
});
const { isDark } = useDark();

const theme: EchartOptions["theme"] = computed(() => {
  return isDark.value ? "dark" : "light";
});

const chartRef = ref<HTMLDivElement | null>(null);
const { setOptions, resize } = useECharts(chartRef as Ref<HTMLDivElement>, {
  theme
});

const getOption = (): {
  yAxis: { data: (string[] & {}) | (number[] & {}) | string[] | number[]; type: string };
  xAxis: { name: string; type: string; boundaryGap: [number, number] };
  grid: { bottom: string; right: string };
  legend: { left: string; bottom: string };
  series: (Array<object> & {}) | Array<object>;
  tooltip: { axisPointer: { type: string }; trigger: string };
  dataZoom: {
    left: string;
    bottom: string;
    xAxisIndex: number[];
    start: number;
    show: boolean;
    end: number;
    type: string
  }[];
  addTooltip: boolean
} => {
  return {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow"
      }
    },
    grid: {
      bottom: "22%",
      right: "15%"
    },
    legend: {
      bottom: "5%",
      left: "top",
    },
    xAxis: {
      name: 'MWh',
      type: "value",
      boundaryGap: [0, 0.01]
    },
    yAxis: {
      type: "category",
      data: props.yData
    },
    // 滚动条
    dataZoom: [
      {
        type: "slider",
        xAxisIndex: [0, 1],
        start: 0,
        end: 100,
        bottom: "7%",
        left: "12a%",
        show: true,
      },
    ],
    series: props.series,
    // addTooltip: true
  };
};

watch(
  () => useAppStoreHook().getSidebarStatus,
  () => {
    delay(600).then(() => resize());
  }
);

watch(
  () => props,
  () => setOptions(getOption() as EChartsOption, false, { notMerge: true }),
  {
    immediate: true,
    deep: true
  }
);
onMounted(() => {
  nextTick(() => {
    var myChart = echarts.init(chartRef.value!);
    myChart.on("click", params => {
      console.log(params)
      if(params.data.hasOwnProperty('type')) {
        emit("update", params.data.value);
      } else {
        emit("update", params.data.id);
      }
    });
  });
  delay(300).then(() => resize());
});
onActivated(() => triggerWindowResize());
</script>
