import { request } from '@/utils/request'
// 查询大屏数据
export const queryScreenPageListAPI = (data: any) =>
  request.post({
    url: '/custom/contract/queryBigScreenDataInfo',
    data: data,
  })
// 新增大屏数据
export const addScreenDataInfoAPI = (data: any) =>
  request.post({
    url: '/custom/contract/addBigScreenDataInfo',
    data: data,
  })
// 修改大屏数据
export const modifyScreenDataInfoAPI = (data: any) =>
  request.post({
    url: '/custom/contract/modifyBigScreenDataInfo',
    data: data,
  })
// 删除大屏数据
export const deleteScreenDataInfoAPI = (id: any) =>
  request.delete({
    url: `/custom/contract/delBigScreenDataInfo/{id}`,
  })

// 切换所有数据来源
export const switchAllDataSourceAPI = (data: any) =>
  request.post({
    url: '/custom/contract/switchDataSource',
    data: data,
  })