import { request } from '@/utils/request'

// 新增合同信息
export const contractsAdd = (data: any) => {
  const res = request.post<any>({
    url: '/contracts/add',
    data,
  })
  return res
}
// 分页查询合同信息
export const contractsQueryPage = (data: any) => {
  const res = request.post<any>({
    url: '/contracts/queryPage',
    data,
  })
  return res
}
// 修改合同信息
export const contractsUpdate = (data: any) => {
  const res = request.post<any>({
    url: '/contracts/update',
    data,
  })
  return res
}
// 删除合同信息
export const contractsDelete = (data: any) => {
  const res = request.post<any>({
    url: '/contracts/delete',
    data,
  })
  return res
}
// 电量分解
export const contractseEnergyDecompose = (data: any) => {
  const res = request.post<any>({
    url: '/contracts/energyDecompose',
    data,
  })
  return res
}
// 下载合同模板
export const contractseDownload = () => {
  const res = request.post<any>(
    {
      url: '/contracts/download',
      responseType: 'blob',
    },
    { isTransformResponse: false },
  )
  return res
}
// 导入合同
export const contractseImport = (formData: any) => {
  const res = request.post<any>({
    url: '/contracts/import',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
  return res
}
// 上传附件
export const attachmentUpload = (formData: any) => {
  const res = request.post<any>(
    {
      url: '/attachment/upload',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
    {
      formatDate: true,
    },
  )
  return res
}
// 根据id查询附件列表
export const attachmentGetList = (data: any) => {
  const res = request.post<any>({
    url: `/attachment/getList?ids=${data}`,
  })
  return res
}
// 导出excel
export const contractseDownloadExcel = (data: any) => {
  const res = request.post<any>(
    {
      url: '/contracts/downloadExcel',
      responseType: 'blob',
      data,
    },
    { isTransformResponse: false },
  )
  return res
}
// 下载附件
export const attachmentDownload = (params: any) => {
  const res = request.get<any>(
    {
      url: '/attachment/download',
      responseType: 'blob',
      params,
    },
    { isTransformResponse: false },
  )
  return res
}

// 查询方案列表
export const getProgrammeList = (params: any) => {
  const res = request.get<any>({
    url: `/programme/getProgrammeList`,
    params,
  })
  return res
}
// 查询方案信息
export const getProgrammeContentInfo = (params: any) => {
  const res = request.get<any>({
    url: `/programme/getProgrammeContentInfo`,
    params,
  })
  return res
}
// 新增方案信息
export const programmeAdd = (data: any) => {
  const res = request.post<any>({
    url: `/programme/add`,
    data,
  })
  return res
}
// 修改方案信息
export const programmeUpdate = (data: any) => {
  const res = request.post<any>({
    url: `/programme/update`,
    data,
  })
  return res
}
// 删除方案信息
export const programmeDelete = (data: any) => {
  const res = request.post<any>({
    url: `/programme/delete`,
    data,
  })
  return res
}


// 获取分解信息
export const getDecomposeInfo = (data: any) => {
  const res = request.post<any>({
    url: `/contracts/getDecomposeInfo`,
    data,
  })
  return res
}
// 新增分解信息
export const addDecompose = (data: any) => {
  const res = request.post<any>({
    url: `/contracts/addDecompose`,
    data,
  })
  return res
}
// 修改分解信息
export const updateDecompose = (data: any) => {
  const res = request.post<any>({
    url: `/contracts/updateDecompose`,
    data,
  })
  return res
}

// 分页查询合约明细信息
export const contractDetails = (data: any) => {
  const res = request.post<any>({
    url: `/contractDetails/queryPage`,
    data,
  })
  return res
}
// 根据合约id查询分解信息
export const queryDecomposeByContractId = (data: any) => {
  const res = request.post<any>({
    url: `/contractDetails/queryDecomposeByContractId?contractId=${data}`,
    data,
  })
  return res
}
// 导入合约明细信息
export const importContractEnergyDetail = (formData: any) => {
  const res = request.post<any>({
    url: `/contractDetails/importContractEnergyDetail`,
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    }
  }, {
    formatDate: true,
  },)
  return res
}
