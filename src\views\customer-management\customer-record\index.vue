<template>
  <section>
    <div class="app-search-card" >
      <div class="app-form-group">
        <div class="ml-[15px]">
          <span>签约年份：</span>
          <el-date-picker style="width: 160px" v-model="searchInfo.signYear" type="year" placeholder="请选择" @change="getYearTotal"
            value-format="YYYY" />
        </div>
        <div class="ml-[15px]">
          <span>客户分级：</span>
          <DictSelect style="width: 160px" v-model="searchInfo.customGrade" :clearable="true" dict-code="customGrade" />
        </div>
        <div class="ml-[15px]">
          <span>所属地区：</span>
          <el-tree-select style="width: 160px" default-expand-all v-model="searchInfo.areaId" check-strictly
            :props="{ children: 'children', label: 'name', value: 'id' }" placeholder="请选择" :data="treeData"
            :render-after-expand="false" />
        </div>
        <div class="ml-[15px]">
          <span>行业分类：</span>
          <DictSelect style="width: 160px" v-model="searchInfo.industryId" :clearable="true" dict-code="industry" />
        </div>
      </div>
      <div class="ml-[15px]">
        <span>签约状态：</span>
        <el-select v-model="searchInfo.isSign" placeholder="请选择" clearable style="width: 100px;margin-right: 20px">
          <el-option label="全部" value=""></el-option>
          <el-option label="已签约" :value="true"></el-option>
          <el-option label="未签约" :value="false"></el-option>
        </el-select>
      </div>
      <div class="app-btn-group">
        <el-button class="filter-item" type="primary" @click="getList">查询</el-button>
        <el-button class="filter-item" @click="handleReset">重置</el-button>
      </div>
    </div>
    <div class="app-card mt-[20px]">
      <div class="card-header">
        <div class="font-extrabold">客户签约情况</div>
      </div>
      <div class="p-[20px] flex">
        <div class="customer-record-left">
          <div class="flex">
            <div class="record-item">
              <div>当年签约客户数（个）</div>
              <div class="value">
                 {{ customContractYearTotalMap.signedCount }}
<!--                504-->
              </div>
            </div>
            <div class="record-item">
              <div>当年历史签约客户数（个）</div>
              <div class="value">
                 {{ customContractYearTotalMap.historySignedCount }}
<!--                473-->
              </div>
            </div>
          </div>
          <div class="flex mt-[30px]">
            <div class="record-item">
              <div>当年新增客户数（个）</div>
              <div class="value">
                 {{ customContractYearTotalMap.newSignedCount }}
<!--                31-->
              </div>
            </div>
            <div class="record-item">
              <div>未续签客户数（个）</div>
              <div class="value">
                 {{ customContractYearTotalMap.notRenewedCount }}
<!--                43-->
              </div>
            </div>
          </div>
        </div>
        <div class="customer-record-right">
          <ChartsLine :series="series" y-axis-name1="个" y-axis-name2="MWh" :x-data="xData" width="100%" height="150px" />
        </div>
      </div>
    </div>
    <div class="app-card mt-[20px] p-[20px] pt-[10px]">
      <div class="mb-[10px]">
        <el-button type="primary" @click="handleCreate">新增</el-button>
        <el-button type="primary" @click="handleExport">导出excel</el-button>
      </div>
      
      <pure-table :columns="columns" border stripe :loading="loading" :data="tableData" :pagination="pagination"
        @page-size-change="onSizeChange" @page-current-change="onCurrentChange"
        @selection-change="handleSelectionChange">
        <template #nameHeader>
          <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate" :column="columns[1]" />
        </template>
        <template #monthlyAverageElectricity>
          <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate" :column="columns[2]" />
        </template>
        <template #effectiveDateHeader>
          <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate" :column="columns[3]" />
        </template>
        <template #terminationDateHeader>
          <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate" :column="columns[4]" />
        </template>
        <template #electricityTypeHeader>
          <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate"
            :filter-options="electricityTypeOptions" :column="columns[5]" />
        </template>
        <template #followerNameHeader>
          <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate" :filter-options="optionsList"
            :column="columns[6]" />
        </template>
        <template #industryHeader>
          <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate" :filter-options="industryOptions"
            :column="columns[7]" />
        </template>
        <template #areaIdHeader>
          <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate" :filter-options="treeData"
            :column="columns[8]" />
        </template>
        <template #customerSourceHeader>
          <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate"
            :filter-options="customerSourceOptions" :column="columns[9]" />
        </template>
        <template #customTypeHeader>
          <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate" :filter-options="agentTypeOptions"
            :column="columns[10]" />
        </template>
        <template #statusHeader>
          <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate" :filter-options="statusOptions"
            :column="columns[11]" />
        </template>
        <template #name="{ row }">
          <a @click="handleDetail(row)" style="color: #007bf7">{{
            row.name
          }}</a>
        </template>
        <template #agentType="{ row }">
          <div>
            {{ filterDictText(row.agentType, agentTypeOptions) }}
          </div>
        </template>
        <template #status="{ row }">
          <div>
            {{ filterDictText(row.status, statusOptions) }}
          </div>
        </template>
        <template #customGrade="{ row }">
          <div>
            {{ filterDictText(row.customGrade, customGradeOptions) }}
          </div>
        </template>
        <template #customerSource="{ row }">
          <div>
            {{ filterDictText(row.customerSource, customerSourceOptions) }}
          </div>
        </template>
        <template #operation="{ row }">
          <el-button link type="primary" size="small" @click="handleEdit(row.id, row.followerId)">编辑</el-button>
          <el-button link type="primary" size="small" @click="handleDetail(row)">详情</el-button>
          <el-popconfirm width="220" confirm-button-text="确定" cancel-button-text="取消" :icon="InfoFilled"
            icon-color="#626AEF" title="确认删除？" @confirm="handleDel(row.id)">
            <template #reference>
              <el-button link type="danger" size="small">删除</el-button>
            </template>
          </el-popconfirm>
        </template>
      </pure-table>
      
    </div>
    <el-dialog width="60%" append-to-body destroy-on-close v-model="selectVisible" title="标签选择">
      <tag-select @change="handleChange" />
    </el-dialog>
  </section>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { onMounted,onActivated } from "vue";
import ChartsLine from "./ChartsLine.vue";
import { usePowerCustomer } from "@/hooks/customer/usePowerCustomer";
import { columns } from "../column";
import TagSelect from "@/components/Core/TagSelect/index.vue";
import TableHeadPopover from "@/components/Core/TableHeadPopover/index.vue";
import { getYearTotalApi } from "@/api/customer-management/index";
import dayjs from "dayjs";
defineOptions({
  name: "CustomerRecord"
});
// x轴
const xData = ref([]);
const year = ref(dayjs().format("YYYY"));
const customContractYearTotalMap = ref({
  signedCount: 0,
  historySignedCount: 0,
  newSignedCount: 0,
  notRenewedCount: 0
});
const series = ref([
  {
    type: "bar",
    yAxisIndex: 0,
    name: "签约客户数",
    data: []
  },
  {
    type: "bar",
    yAxisIndex: 0,
    name: "新增客户数",
    data: []
  },
  {
    type: "line",
    yAxisIndex: 1,
    smooth: true,
    showSymbol: false,
    name: "签约电量",
    data: []
  }
]);
async function getYearTotal() {
  const res = await getYearTotalApi(searchInfo.signYear);
  if (res.data) {
    customContractYearTotalMap.value = { ...res.data };
    xData.value = res.data.monthList.map(i => i.month);
    series.value[0].data = res.data.monthList.map(i => i.signedCount);
    series.value[1].data = res.data.monthList.map(i => i.newSignedCount);
    series.value[2].data = res.data.monthList.map(i => i.sumElectricityQty);
  }
  await getList()
}
const {
  handleExport,
  handleTableUpdate,
  handleChange,
  selectVisible,
  timeSlot,
  InfoFilled,
  handleReset,
  handleDel,
  filterDictText,
  customerSourceOptions,
  treeData,
  handleCreate,
  handleEdit,
  tableData,
  loading,
  pagination,
  onSizeChange,
  onCurrentChange,
  handleSelectionChange,
  handleDetail,
  agentTypeOptions,
  statusOptions,
  optionsList,
  searchInfo,
  getList,
  getCityTreeData,
  getUserList,
  customGradeOptions,
  industryOptions,
  electricityTypeOptions
} = usePowerCustomer(false);
onMounted(() => {
  getCityTreeData();
  getUserList();
  getList();
  getYearTotal();
});
onActivated(() => {
   getList();
   getYearTotal();
})
</script>

<style lang="scss" scoped>
.customer-record-left {
  border-right: 1px solid #e4e7ed;

  .record-item {
    width: 260px;
  }

  .value {
    color: var(--el-color-primary);
    font-weight: 700;
    font-size: 20px;
  }
}

.customer-record-right {
  flex: 1;
}
</style>
