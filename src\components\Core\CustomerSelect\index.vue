<template>
  <section>
    <div class="app-content-container container-h">
      <el-tabs v-model="selected">
        <el-tab-pane label="电力用户" name="1">
          <power-consumer @change="handleChange" />
        </el-tab-pane>
        <el-tab-pane label="居间商" name="2">
          <middleman-consumer @change="handleChange" />
        </el-tab-pane>
      </el-tabs>
      <div class="flex justify-end mt-[40px]">
        <el-button type="primary" @click="handleConfirm">确认选择</el-button>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref } from "vue";
import powerConsumer from "./components/powerConsumer.vue";
import middlemanConsumer from "./components/middlemanConsumer.vue";
const selected = ref<string>("1");
const emit = defineEmits(["close"]);

const selectedData = ref({});

// 字典方法
function filterDictText(value, array) {
  return array?.find(i => i.value == String(value))?.label;
}
function handleChange(data) {
  selectedData.value = data;
}
function handleConfirm() {
  emit("close", selectedData.value);
}
</script>

<style lang="scss" scoped></style>
