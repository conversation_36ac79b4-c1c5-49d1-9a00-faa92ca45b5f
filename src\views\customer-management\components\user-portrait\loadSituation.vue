<template>
  <div class="app-list-wrap mt-[20px]">
    <div class="flex items-center justify-between app-list-title">
      <div class="flex items-center">
        <span class="font-bold">负荷及波动情况</span>
        <div class="mx-[20px]">
          <el-radio-group v-model="searchInfo.dateType" @change="handleChange">
            <el-radio :label="1">年</el-radio>
            <el-radio :label="2">月</el-radio>
            <el-radio :label="3">日</el-radio>
          </el-radio-group>
        </div>
        <div v-if="searchInfo.dateType === 1">
          <span class="ml-[15px]">年份：</span>
          <el-date-picker
            style="width: 140px"
            v-model="searchInfo.start"
            type="year"
            valueFormat="x"
            placeholder="请选择"
            @change="getList"
          />
        </div>
        <div v-else-if="searchInfo.dateType === 2">
          <span class="ml-[15px]">月份：</span>
          <el-date-picker
            style="width: 140px"
            v-model="searchInfo.start"
            type="month"
            valueFormat="x"
            placeholder="请选择"
            @change="getList"
          />
        </div>
        <div v-else>
          <span class="ml-[15px]">开始日期：</span>
          <el-date-picker
            style="width: 140px"
            v-model="searchInfo.start"
            type="date"
            valueFormat="x"
            placeholder="请选择"
            @change="getList"
          />
          <span class="ml-[15px]">结束日期：</span>
          <el-date-picker
            style="width: 140px"
            v-model="searchInfo.end"
            type="date"
            valueFormat="x"
            placeholder="请选择"
            @change="getList"
          />
        </div>
      </div>
      <div>单位：MWh，%</div>
    </div>
    <div v-loading="loading" class="p-[20px] flex">
      <!-- 左边 -->
      <div class="loadSituation-left mt-[10px]">
        <div class="flex w-1/1">
          <div
            class="box-item"
            v-for="(item, index) in timePeriodList"
            :key="index"
          >
            <div class="font-bold">{{ item.name }}时段</div>
            <div class="flex mt-[15px] justify-between">
              <div>
                <div>用电</div>
                <div class="value">{{ item.electricityConsumption }}</div>
              </div>
              <div>
                <div>占比</div>
                <div class="value">{{ item.ratio }}</div>
              </div>
            </div>
          </div>
        </div>
        <div>
          <ChartsMergeLine2
            :loadList="lineData.loadList"
            :fluctuateList="lineData.fluctuateList"
            :upDeviationList="lineData.upDeviationList"
            :downDeviationList="lineData.downDeviationList"
            :xData="xData"
            height="450px"
          />
        </div>
      </div>
      <!-- 右边 -->
      <div class="loadSituation-right">
        <el-radio v-model="radio" :label="1">负荷</el-radio>
        <pure-table
          class="mt-[10px]"
          border
          stripe
          height="480"
          :columns="columns"
          :data="maxMinList"
        ></pure-table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import ChartsMergeLine2 from "./ChartsMergeLine2.vue";
import { getPortraitLoadApi } from "@/api/customer-management/index";
import { useRoute } from "vue-router";
import { CustomerPortraitLoadModel } from "@/model/customerModel";
import dayjs from "dayjs";
const loading = ref<boolean>(false);
const { query } = useRoute();
const radio = ref(1);
const searchInfo = ref<CustomerPortraitLoadModel>({
  customId: query.id as string,
  dateType: 1,
  start: dayjs("2023").valueOf(),
  // start: 1696089600000,
  end: dayjs(dayjs().add(1, "day")).valueOf()
});
// x轴
const xData = ref<string[]>(
  new Array(12).fill(item => item).map((_, index) => `${index + 1}月`)
);
const columns = ref([
  {
    label: "月份",
    prop: "date"
  },
  {
    label: "最大值",
    prop: "max"
  },
  {
    label: "最小值",
    prop: "min"
  },
  {
    label: "平均值",
    prop: "avg"
  }
]);
// 时段汇总列表
const timePeriodList = ref([]);
const maxMinList = ref([]);
// 曲线数据
const lineData = ref({
  loadList: [],
  fluctuateList: [],
  upDeviationList: [],
  downDeviationList: []
});
function handleChange(tab) {
  searchInfo.value.dateType = tab;
  columns.value[0].label = tab === 1 ? "月份" : "日期";
  getList();
}

async function getList() {
  loading.value = true;
  const res = await getPortraitLoadApi(searchInfo.value);
  if (res.data) {
    if (res.data.timePeriodList) {
      timePeriodList.value = res.data.timePeriodList;
    }
    if (res.data.maxMinList) {
      maxMinList.value = res.data.maxMinList;
    }
    if (res.data.curveList) {
      xData.value = res.data.curveList.map(i => i.time);
      lineData.value.loadList = res.data.curveList.map(i =>
        i.load !== null ? i.load.toFixed(3) : "-"
      );
      lineData.value.fluctuateList = res.data.curveList.map(i =>
        i.wave !== null ? i.wave.toFixed(3) : "-"
      );
      lineData.value.upDeviationList = res.data.curveList.map(i =>
        i.positiveDeviation !== null ? i.positiveDeviation.toFixed(3) : "-"
      );
      lineData.value.downDeviationList = res.data.curveList.map(i =>
        i.negativeDeviation !== null ? i.negativeDeviation.toFixed(3) : "-"
      );
    }
  } else {
    timePeriodList.value = [
      {
        name: "峰",
        electricityConsumption: "-",
        ratio: "-"
      },
      {
        name: "平",
        electricityConsumption: "-",
        ratio: "-"
      },
      {
        name: "谷",
        electricityConsumption: "-",
        ratio: "-"
      }
    ];
    maxMinList.value = [];
    // xData.value = [];
    lineData.value = {
      loadList: [],
      fluctuateList: [],
      upDeviationList: [],
      downDeviationList: []
    };
  }
  setTimeout(() => {
    loading.value = false;
  }, 300);
}
onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped>
.loadSituation-left {
  width: 60%;

  .box-item {
    width: 33.33%;
    border-right: 1px solid #e4e7ed;
    padding-right: 30px;
    margin-left: 20px;

    &:first-child {
      margin-left: 0;
    }

    &:last-child {
      border: none;
    }

    .value {
      color: var(--el-color-primary);
      font-weight: bold;
    }
  }
}

.loadSituation-right {
  width: 40%;
  margin: 0 20px;
}
</style>
