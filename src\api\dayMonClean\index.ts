import { request } from '@/utils/request'

// 查询日结算列表
export const dailySettlementInfo = (data: any) => {
  const res = request.post<any>({
    url: '/settlementInfo/dailySettlementInfo/list',
    data,
  })
  return res
}
// 查询24小时结算列表
export const hourSettlementInfo = (data: any) => {
  const res = request.post<any>({
    url: '/settlementInfo/hourSettlementInfo/list',
    data,
  })
  return res
}
// 查询月结算列表
export const monthSettlementInfo = (data: any) => {
  const res = request.post<any>({
    url: '/settlementInfo/monthSettlementInfo/list',
    data,
  })
  return res
}
// 导入日结算数据
export const importDailySettlementData = (formData: any) => {
  return request.post({
    url: '/settlementInfo/importDailySettlementData',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}
// 导入24小时结算数据
export const importHourSettlementData = (formData: any) => {
  return request.post({
    url: '/settlementInfo/importHourSettlementData',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}
// 导入月结算数据
export const importMonthSettlementData = (formData: any) => {
  return request.post({
    url: '/settlementInfo/importMonthSettlementData',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}
