import { useUserStore } from "@/store/modules/user";
const userStore = useUserStore();
// 客户状态
const statusOptions = userStore.getDictList.find(
    i => i.code === "customStatus"
).items;
const customStatusOptions = userStore.getDictList.find(
  i => i.code === "fiveCustomStatus"
)?.items;
const customGradeOptions = userStore.getDictList.find(
  i => i.code === "customGrade"
)?.items;
export const columns: TableColumnList = [
  {
    label: "序号",
    width: 80,
    align: "center",
    type: "index"
  },
  {
    label: "客户名称",
    prop: "name",
    width: 250,
    slot: "name"
  },
  // {
  //   label: "客户类型",
  //   prop: "customType",
  //   slot: "customType"
  // },
  {
    label: "所属地区",
    prop: "areaName"
  },
  { label: "客户状态",
    prop: "isSign",
    formatter: ({ isSign }) =>
        isSign == true? "已签约" : "未签约"
  },
  // {
  //   label: "客户经理",
  //   prop: "index4"
  // },
  {
    label: "月均用电量",
    width: 180,
    prop: "annualElectricity"
  },
  {
    label: "客户分级",
    prop: "customGrade",
    formatter: ({ customGrade }) =>
      customGrade !== null
        ? customGradeOptions.find(item => item.value == customGrade)?.label
        : ""
  },
  {
    label: "所属集团",
    prop: "membershipGroup"
  },
  {
    label: "客户渠道",
    prop: "customerSource",
    formatter: ({ customerSource }) =>
        customerSource!== null
            ? userStore.getDictList.find(
                i => i.code === "customerSource"
            )?.items.find(item => item.value == customerSource)?.label
            : ""
  }
];
export const columns1: TableColumnList = [
  {
    label: "序号",
    width: 80,
    align: "center",
    type: "index"
  },
  {
    label: "客户名称",
    prop: "userName",
    width: 250,
    slot: "name"
  },
  // {
  //   label: "客户类型",
  //   prop: "customType",
  //   slot: "customType"
  // },
  {
    label: "所属地区",
    prop: "areaName"
  },
  {
    label: "客户状态",
    prop: "isSign",
    formatter: ({ isSign }) =>
        isSign == true? "已签约" : "未签约"
  },
  // {
  //   label: "客户经理",
  //   prop: "index4"
  // },
  {
    label: "年用电量（MWh）",
    width: 180,
    prop: "annualElectricity"
  },
  {
    label: "客户分级",
    prop: "customGrade",
    formatter: ({ customGrade }) =>
      customGrade !== null
        ? customGradeOptions.find(item => item.value == customGrade)?.label
        : ""
  },
  {
    label: "所属集团",
    prop: "membershipGroup"
  },
  {
    label: "客户渠道",
    prop: "customerSource",
    formatter: ({ customerSource }) =>
        customerSource!== null
            ? userStore.getDictList.find(
                i => i.code === "customerSource"
            )?.items.find(item => item.value == customerSource)?.label
            : ""
  }
];
