<template>
  <section class="custom-battle-wrapper">
    <div class="flex">
      <div class="card-box">
        <div class="card-title">客户结构图</div>
        <div class="card-content">
          <Pie @update="handleUpdate"  :series="pieData1" height="100%" />
        </div>
      </div>
      <div class="card-box ml-[20px]">
        <div class="card-title">客户经理维护客户电量图</div>
        <div class="card-content">
          <Pie2 @change="handleChange"  :series="pieData2" height="100%" />
        </div>
      </div>
    </div>
    <el-dialog v-model="dialogVisible" title="数据详情" width="60%">
      <list :query-id="queryId" :time="searchTime" />
    </el-dialog>
    <el-dialog v-model="dialogVisible1" title="数据详情" width="60%">
      <list1 :query-id="queryId1"  :time="searchTime"/>
    </el-dialog>
  </section>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import Pie from "./components/Pie.vue";
import Pie2 from "./components/Pie2.vue";
import List from "./components/list.vue";
import List1 from "./components/list2.vue";

import {
  getGradeCountApi,
  getElectricitySumApi
} from "@/api/customer-management/index";
defineOptions({
  name: "SalesCockpit"
});
import {
  getAllSalesmanList, //获取营销人员列表
} from '@/api'
const dialogVisible = ref<boolean>(false);
const queryId = ref("");
const dialogVisible1 = ref<boolean>(false);
const queryId1 = ref("");
const props = defineProps({
 
  time: {
    type: String,
    default: ""
  }
});
const searchTime = ref(props.time);
const pieData1 = ref([
  {
    name: "客户结构",
    type: "pie",
    tooltip: {
      formatter: '{a} <br/>{b} : {c}家 ({d}%)',
    },
    radius: "50%",
    data: [],
    emphasis: {
      itemStyle: {
        shadowBlur: 10,
        shadowOffsetX: 0,
        shadowColor: "rgba(0, 0, 0, 0.5)"
      }
    }
  }
]);
const pieData2 = ref([
  {
    name: "客户经理维护客户电量",
    type: "pie",
    tooltip: {
      formatter: '{a} <br/>{b} : {c}MWh ({d}%)',
    },
    radius: "50%",
    data: [],
    emphasis: {
      itemStyle: {
        shadowBlur: 10,
        shadowOffsetX: 0,
        shadowColor: "rgba(0, 0, 0, 0.5)"
      }
    }
  }
]);
async function getList() {
  const data1 = await getGradeCountApi();
  const data2 = await getAllSalesmanList()
  if (data1.data) {
    pieData1.value[0].data = data1.data.map(item => {
      return {
        value: item.customCount,
        name: item.customGradeName,
        id: item.customGradeId
      };
    });
  }
  if (data2) {
    pieData2.value[0].data = data2.map(item => {
      return {
        value: item.annualElectricity,
        name: item.name,
        id: item.id
      };
    });
  }
}
// async function getList() {
//   const data1 = await getGradeCountApi();
//   const data2 = await getElectricitySumApi();
//   if (data1.data) {
//     pieData1.value[0].data = data1.data.map(item => {
//       return {
//         value: item.customCount,
//         name: item.customGradeName,
//         id: item.customGradeId
//       };
//     });
//   }
//   if (data2.data) {
//     pieData2.value[0].data = data2.data.map(item => {
//       return {
//         value: item.sumAnnualElectricity,
//         name: item.followerName,
//         id: item.followerId
//       };
//     });
//   }
// }
function handleUpdate(id) {
  dialogVisible.value = true;
  queryId.value = id;
  console.log("aaa",searchTime.value);
}
function handleChange(id) {
  dialogVisible1.value = true;
  queryId1.value = id;
}
onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped>
.custom-battle-wrapper {
  height: 100%;

  .header-card {
    width: 100%;
    height: 200px;
    padding: 20px;
    box-sizing: border-box;
    border-radius: 6px;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 2px 22px rgba(0, 0, 0, 0.06);
    // margin-bottom: 20px;

    .value {
      font-size: 60px;
      font-weight: 700;
      color: #f26e74;
      text-align: center;
    }

    .name {
      font-size: 20px;
      color: #f26e74;
      text-align: center;
    }
  }

  .card-title {
    font-size: 18px;
    padding-bottom: 5px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }

  .card-box {
    padding: 20px;
    box-sizing: border-box;
    border-radius: 6px;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 2px 22px rgba(0, 0, 0, 0.06);
    width: 49%;
    height: 598px;
    // height: calc(100vh - 206px);

    .card-content {
      height: 100%;
    }
  }
}
</style>
