<template>
  <section class="analysis-container">
    <div class="app-search-card">
      <div class="app-form-group">
        <div class="ml-[20px]">
          <span>日期范围选择：</span>
          <el-date-picker style="width: 140px" v-model="searchInfo.startDate" type="date" placeholder="请选择"
            format="YYYY-MM-DD" value-format="x" />
        </div>
        <div>
          <span class="mx-[5px]">-</span>
          <el-date-picker style="width: 140px" v-model="searchInfo.endDate" type="date" placeholder="请选择"
            format="YYYY-MM-DD" value-format="x" />
        </div>
      </div>
      <div class="app-btn-group">
        <el-button class="filter-item" type="primary" @click="getList">查询</el-button>
        <el-button class="filter-item" @click="handleReset">重置</el-button>
      </div>
    </div>
    <div class="app-card mt-[15px]" v-loading="loading">
      <div class="card-header">
        <div class="flex items-center">
          <div>
            <span>已申报：</span>
            <span class="declared">{{
              summaryData.declaredCount !== null
                ? summaryData.declaredCount
                : "-"
              }}</span>
          </div>
          <div class="ml-[30px]">
            <span>未申报：</span>
            <span class="undeclared">{{
              summaryData.undeclaredCount !== null
                ? summaryData.undeclaredCount
                : "-"
            }}</span>
          </div>
        </div>
      </div>
      <div class="m-[20px] flex justify-between">
        <div class="card-item">
          <div class="name">申报电量（MWh）</div>
          <div class="value">
            {{
              summaryData.declaredTotal !== null
                ? summaryData.declaredTotal
                : "-"
            }}
          </div>
        </div>
        <div class="card-item">
          <div class="name">负荷预测（MWh）</div>
          <div class="value">
            {{
              summaryData.forecastTotal !== null
                ? summaryData.forecastTotal
                : "-"
            }}
          </div>
        </div>
        <div class="card-item">
          <div class="name">实际电量（MWh）</div>
          <div class="value">
            {{ summaryData.realTotal !== null ? summaryData.realTotal : "-" }}
          </div>
        </div>
        <div class="card-item">
          <div class="name">偏差电量（MWh）</div>
          <div class="value">
            {{
              summaryData.deviationTotal !== null
                ? summaryData.deviationTotal
                : "-"
            }}
          </div>
        </div>
        <div class="card-item">
          <div class="name">偏差比例（%）</div>
          <div class="value">
            {{
              summaryData.deviationRate !== null
                ? summaryData.deviationRate
                : "-"
            }}
          </div>
        </div>
        <div class="card-item">
          <div class="name">申报时间</div>
          <div class="value">
            {{
              summaryData.declaredTime !== null ? summaryData.declaredTime : "-"
            }}
          </div>
        </div>
      </div>
      <div class="charts-box">
        <div class="charts-select flex items-center justify-end mr-[30px]">
          <div class="mr-[5px]">偏差率指标：</div>
          <div>
            <el-select v-model="lineType" placeholder="请选择" style="width: 120px" @change="getList">
              <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </div>
          <div class="mx-[6px]">-</div>
          <div>实际电量</div>
        </div>
        <ChartsLine :forecast="lineData.forecast" :real="lineData.real" :declaration="lineData.declaration"
          :deviationRate="lineData.deviationRate" :xData="xData" height="480px" />
      </div>
    </div>
  </section>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import ChartsLine from "./components/ChartsLine.vue";
import {
  getForecastAnalysisDateApi,
  getForecastLoadDeclareApi
} from "@/api/load-forecasting/index";
import dayjs from "dayjs";
defineOptions({
  name: "LoadForecastingDeclaredElectricity"
});
const lineType = ref<number>(1);
const typeOptions = [
  {
    label: "申报曲线",
    value: 1
  },
  {
    label: "预测曲线",
    value: 2
  }
];
const searchInfo = ref({
  startDate: dayjs().subtract(7, "day").valueOf(),
  endDate: dayjs().valueOf()
});
// x轴
const xData = ref<string[]>([]);
const loading = ref<boolean>(false);
// 顶部汇总
const summaryData = ref({
  declaredCount: 0,
  declaredTime: 0,
  declaredTotal: 0,
  deviationRate: 0,
  deviationTotal: 0,
  forecastTotal: 0,
  realTotal: 0,
  undeclaredCount: 0
});
// 曲线数据
const lineData = ref({
  forecast: [
    {
      value: 100,
      time: 1
    },
    {
      value: 110,
      time: 2
    },
    {
      value: 102,
      time: 3
    },
    {
      value: 120,
      time: 4
    },
    {
      value: 124,
      time: 5
    }
  ],
  real: [
    {
      value: 200,
      time: 1
    },
    {
      value: 210,
      time: 2
    },
    {
      value: 202,
      time: 3
    },
    {
      value: 220,
      time: 4
    },
    {
      value: 224,
      time: 5
    }
  ],
  declaration: [
    {
      value: 300,
      time: 1
    },
    {
      value: 410,
      time: 2
    },
    {
      value: 402,
      time: 3
    },
    {
      value: 420,
      time: 4
    },
    {
      value: 424,
      time: 5
    }
  ],
  deviationRate: [
    {
      value: 23,
      time: 1
    },
    {
      value: 21,
      time: 2
    },
    {
      value: 24,
      time: 3
    },
    {
      value: 22,
      time: 4
    },
    {
      value: 26,
      time: 5
    }
  ]
});
async function getNewDate() {
  const res = await getForecastAnalysisDateApi();
  // searchInfo.value.startDate = dayjs(
  //   dayjs(res.data.maxPowerDate).subtract(7, "day")
  // ).valueOf();
  // searchInfo.value.endDate = dayjs(res.data.maxPowerDate).valueOf();
}
async function getList() {
  loading.value = true;
  const res = await getForecastLoadDeclareApi({
    parameter: {
      ...searchInfo.value
    }
  });
  if (res.data.curveList) {
    summaryData.value = { ...res.data.summary };
    xData.value = res.data.curveList.map(i => i.time);
    lineData.value.forecast = res.data.curveList.map(i => i.forecast);
    lineData.value.real = res.data.curveList.map(i => i.real);
    lineData.value.declaration = res.data.curveList.map(i => i.declaration);
    if (lineType.value === 1) {
      lineData.value.deviationRate = res.data.curveList.map(
        i => i.deviationRate1
      );
    } else {
      lineData.value.deviationRate = res.data.curveList.map(
        i => i.deviationRate2
      );
    }
  } else {
    summaryData.value = {
      declaredCount: 0,
      declaredTime: 0,
      declaredTotal: 0,
      deviationRate: 0,
      deviationTotal: 0,
      forecastTotal: 0,
      realTotal: 0,
      undeclaredCount: 0
    };
    xData.value = [];
    lineData.value.forecast = [];
    lineData.value.real = [];
    lineData.value.declaration = [];
    lineData.value.deviationRate = [];
  }
  setTimeout(() => {
    loading.value = false;
  }, 300);
}
async function handleReset() {
  await getNewDate();
  getList();
}
onMounted(async () => {
  await getNewDate();
  getList();
});
</script>

<style lang="scss" scoped>
.analysis-container {
  .analysis-header {
    border-radius: 6px;
    box-shadow: 0px 2px 22px rgba(0, 0, 0, 0.06);
    padding: 20px 5px;
  }

  .card-item {
    width: 25%;
    padding: 10px 15px;
    background: #f5f4fb;
    border-radius: 6px;
    display: flex;
    flex-direction: column;
    // justify-items: center;
    // align-items: center;
    margin-right: 40px;
    font-weight: 600;

    .name {}

    .value {
      font-weight: 700;
      margin-top: 6px;
      color: var(--el-color-primary);
    }

    &:last-child {
      margin-right: 0;
    }
  }

  .declared {
    color: #61c14e;
    font-weight: 600;
  }

  .undeclared {
    color: #d3665f;
    font-weight: 600;
  }

  .charts-box {
    // position: relative;
    // z-index: 9999;
    // .charts-select {
    //   position: absolute;
    //   top: 220px;
    //   right:20px;
    // }
  }
}
</style>
