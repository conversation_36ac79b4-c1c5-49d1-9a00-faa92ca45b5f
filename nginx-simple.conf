# 简单的nginx配置 - 不使用more_clear_headers指令
# 适用于标准nginx安装

server {
    listen 8187;  # 使用您的端口
    server_name 1**********;  # 使用您的IP
    
    # 项目根目录 - 请修改为您的实际路径
    root /var/www/html/dist;
    index index.html index.htm;
    
    # 主要配置
    location / {
        # Vue Router history模式支持
        try_files $uri $uri/ /index.html;
        
        # 直接设置允许iframe嵌入的头部
        add_header X-Frame-Options "ALLOWALL" always;

        # 设置Content-Security-Policy允许所有域名嵌入
        add_header Content-Security-Policy "frame-ancestors *" always;
        
        # 完全开放的CORS配置
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS, HEAD, PATCH" always;
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, Pragma" always;
        add_header Access-Control-Allow-Credentials "true" always;
        add_header Access-Control-Max-Age "86400" always;
        
        # 处理OPTIONS预检请求
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS, HEAD, PATCH";
            add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, Pragma";
            add_header Access-Control-Allow-Credentials "true";
            add_header Access-Control-Max-Age "86400";
            add_header Content-Type "text/plain; charset=utf-8";
            add_header Content-Length 0;
            return 204;
        }
    }
    
    # 静态资源配置
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|map|json)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Frame-Options "ALLOWALL" always;
        add_header Content-Security-Policy "frame-ancestors *" always;
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept" always;
    }
    
    # API代理配置（如果需要）
    location /api/ {
        # 代理到后端服务器
        proxy_pass http://1**********:8187/selling/;
        
        # 代理头部设置
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 设置允许iframe和跨域的头部
        add_header X-Frame-Options "ALLOWALL" always;
        add_header Content-Security-Policy "frame-ancestors *" always;
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization" always;
        add_header Access-Control-Allow-Credentials "true" always;
        
        # 代理超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        proxy_buffering off;
    }
    
    # 禁用访问隐藏文件
    location ~ /\. {
        deny all;
    }
    
    # 错误页面
    error_page 404 /index.html;
    
    # 日志配置
    access_log /var/log/nginx/iframe-app-access.log;
    error_log /var/log/nginx/iframe-app-error.log;
}

# 如果您需要在80端口也提供服务
server {
    listen 80;
    server_name 1**********;
    
    # 重定向到8187端口
    return 301 http://$server_name:8187$request_uri;
}

# 全局nginx配置建议
# 在 /etc/nginx/nginx.conf 的 http 块中确保有以下配置：
#
# http {
#     # 隐藏nginx版本
#     server_tokens off;
#     
#     # 设置客户端请求体大小限制
#     client_max_body_size 50M;
#     
#     # 设置超时时间
#     client_body_timeout 60;
#     client_header_timeout 60;
#     keepalive_timeout 65;
#     send_timeout 60;
#     
#     # 启用gzip压缩
#     gzip on;
#     gzip_vary on;
#     gzip_min_length 1024;
#     gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
# }
