<template>
  <div>
    
    <el-card class="jt-card">
      <div class="header" style="margin-bottom: 20px">
        <div class="header-title">
          <img :src="getAssetURL('title-arrow')" alt="" />
          查询条件
        </div>
      </div>
      <div style="display: flex; justify-content: space-between;">

        <div class="flex flex-wrap gap-4 items-center">
          <span>最新版本计算状态</span>
          <el-select v-model="condition.latestCalculationStatus" placeholder="不限" style="width: 130px"
            @change="selectChangeStatus">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <span>更新方式</span>
          <el-select v-model="condition.updateType" placeholder="不限" style="width: 130px" @change="selectupdataMethod">
            <el-option v-for="item in options1" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <span>标签状态</span>
          <el-select v-model="condition.labelStatus" placeholder="不限" style="width: 130px" @change="selectLabelStatus">
            <el-option v-for="item in options2" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <span>创建方式</span>
          <el-select v-model="condition.createType" placeholder="不限" style="width: 130px" @change="selectCreateType">
            <el-option v-for="item in options3" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div>
          <!-- <el-input v-model="condition.searchKey" placeholder=" 搜索标签显示名/标签名称">
                        <template #append>
                            <el-button @click="handlerSearchKey" type="primary" :icon="Search" />
                        </template>
</el-input> -->
        </div>
      </div>

    </el-card>

    <section class="flex overflow-auto">
      <div class="w-[16%] tree-wrapper mt-[20px]">
        <div class="serach">
          <!-- <el-input v-model="condition.searchKey" style="width: 240px" placeholder="请输入关键字" :prefix-icon="Search" /> -->
          <!-- <el-input v-model="condition.searchKey" style="width: 240px" placeholder="请输入关键字" :prefix-icon="Search"
            @input="onQueryChanged" />
          <el-tree-v2 ref="treeRef" style="max-width: 600px" :data="data" :props="props" :filter-method="filterMethod"
            :height="700" @change="handlerChange" /> -->

          <el-input v-model="filterText" style="width: 100%" placeholder="请输入标签名称" :prefix-icon="Search" @input="searchHandler" />

          <el-tree ref="treeRef" style="max-width: 600px" class="filter-tree" :data="tableData" :props="defaultProps"
              @node-click="handleNodeClick"  />
        </div>
      </div>
      <div class="w-[84%] overflow-auto">
        <div class="w-[98%] tree-wrapper mt-[20px]">
          <el-card class="jt-card">
            <div class="header" style="margin-bottom: 20px">
              <div class="header-title">
                <img :src="getAssetURL('title-arrow')" alt="" />
               {{ name }}
              </div>

            </div>
            <div style="margin: 15px 0 15px 0;">
              <span class="circle" style="margin-right: 12px;"></span>
             <!-- <span >{{ groupStatus === "1"?"上线" :"下线" }} </span> -->
             <span >上线 </span>
             
            </div>
            <!-- <div style="margin: 20px 0 20px 0;">可输入备注</div> -->
            <div class="viewRules" @click="handlerLookRules">查看标签规则</div>
          </el-card>

          <el-card class="jt-card" style="margin-top: 20px;">
            <div class="header" style="margin-bottom: 20px">
              <div class="header-title">
                <img :src="getAssetURL('title-arrow')" alt="" />
                标签人数分布 
              </div>
            </div>
            <div class="header-title">
              <span style="margin-right: 20px;">{{condition.totalNum  }}人</span>
              <span>基准时间:&nbsp;&nbsp; {{ condition.baseExecutionTime? condition.baseExecutionTime :"暂未执行规则" }},&nbsp;&nbsp;&nbsp;&nbsp; 计算完成于:&nbsp;&nbsp; {{ condition.latestCalculationTime?condition.latestCalculationTime :"--" }}</span>
            </div>
            <Echarts :echartsData="analyseOptionDeal" EWidth="100%" EHeight="430px" echartId="analyse"
              @instanceFn="instanceFn1"></Echarts>
          </el-card>
        </div>

      </div>
      
    </section>

    <el-card class="backBtn">

      <el-button style="margin: 20px 0 10px 20px;" @click="goBackAndClearStack">返回</el-button>
    </el-card>
  </div>


</template>

<script setup lang="ts">
import { onActivated, onMounted, ref, watch } from 'vue';
import { addLabelCategory, queryLabelList, modifyLabelCategoryName, delCustomLabel, delLabelCategory, executeRules } from '@/api'
import { Search, Plus, FolderRemove, ArrowDown } from '@element-plus/icons-vue'
import { ElTree  } from 'element-plus'
import { getLabelSliceInfo } from '@/api'

import { useRoute, useRouter } from 'vue-router';

const { push, go } = useRouter();
const route = useRoute();
console.log(route.query,'route.query')
const id = route.query.id;
// console.log(route.query.latestCalculationStatus)
const groupStatus = route.query.name

const condition = ref({
  searchKey: "",
  createType: "",
  labelStatus: "",
  latestCalculationStatus: "",
  updateType: "",
  totalNum:0,
  baseExecutionTime:"",
  latestCalculationTime:""
 
})
// nextTick(() => {
//   // condition.value.baseExecutionTime = "2021-12-31 16:00:00"
// });

const options = [
  {
    value: null,
    label: '不限',
  },
  {
    value: '1',
    label: '成功',
  },
  {
    value: '2',
    label: '等待计算',
  },
  {
    value: '3',
    label: '失败',
  }

]
const options1 = [
  {
    value: null,
    label: '不限',
  },
  {
    value: '2',
    label: '手动',
  },
  {
    value: '1',
    label: '例行',
  }
]
const options2 = [
  {
    value: null,
    label: '不限',
  },
  {
    value: '1',
    label: '上线',
  }
  
]
const options3 = [
  {
    value: null,
    label: '不限',
  },
  {
    value: '规则匹配类',
    label: '规则匹配类',
  },
  {
    value: '统计类',
    label: '统计类',
  },
  {
    value: '挖掘类',
    label: '挖掘类',
  },
]
onMounted(() => {
  name.value = groupStatus.toString()

  initList({})
})
const name = ref('')
const handleNodeClick = async (node:any) => {
  console.log(node,'node')
  name.value = node.labelNameCn
  console.log(node.labelNameCn)
      if (node.children.length == 0) {
        // 这是一个叶子节点，执行你的点击事件逻辑
        // const res = await getLabelSliceInfo({ id: node.id })
        // console.log(res,'res///////')
        getYearTotal(node.id)
      }
    }
const searchKey = ref('')
const selectChangeStatus = (row: any) => {
  initList({ latestCalculationStatus:condition.value.latestCalculationStatus,labelStatus: condition.value.labelStatus,updateType: condition.value.updateType,createType: condition.value.createType,searchKey: condition.value.searchKey })
}
const selectLabelStatus = (row: any) => {
  initList({ latestCalculationStatus:condition.value.latestCalculationStatus,labelStatus: condition.value.labelStatus,updateType: condition.value.updateType,createType: condition.value.createType,searchKey: condition.value.searchKey })
}
const selectupdataMethod = (row: any) => {
  initList({ latestCalculationStatus:condition.value.latestCalculationStatus,labelStatus: condition.value.labelStatus,updateType: condition.value.updateType,createType: condition.value.createType,searchKey: condition.value.searchKey })
}
const selectCreateType = (row: any) => {
  initList({ latestCalculationStatus:condition.value.latestCalculationStatus,labelStatus: condition.value.labelStatus,updateType: condition.value.updateType,createType: condition.value.createType,searchKey: condition.value.searchKey })
}

// 表格数据
const tableData = ref([])
// 获取初始化列表
const initList = async (item: any) => {
  const res = await queryLabelList(item)
  tableData.value = transformTreeData(res) || []

}

onActivated(() => {
  initList({})
})

interface Tree {
  id: string
  label: string
  children?: Tree[]
}

const getAssetURL = (image: any) => {
    return new URL(`../../../assets/svg/${image}.svg`, import.meta.url).href
}

function goBackAndClearStack() {
  if (window.history.length <= 1) {
    push({ path: "/labelManagement" });
    return false;
  } else {
    go(-1);
  }
 
}


function transformTreeData(data: any) {
  if (data.length == 0) return [];
  const treeData = ref<any>([]);
  for (const item of data) {
    treeData.value.push({
      ...item.data,
      children: transformTreeData(item.children),
    });
  }
  return treeData.value
}

interface Tree {
  [key: string]: any
}

const filterText = ref('')
const treeRef = ref<InstanceType<typeof ElTree>>()

const defaultProps = {
  children: 'children',
  label: 'labelNameCn',
  // label: 'label',
}

// watch(filterText, (val) => {
//   treeRef.value!.filter(val)
// })

const filterNode = (value: string, data: Tree) => {
  console.log("aaa",data.labelNameCn.includes(value))
  if (!value) return true
  return data.labelNameCn.includes(value)
}
const yData = ref([])
const xData = ref([])
const seriesData = ref([])
const analyseOptionDeal = ref({
  title: {
    // text: "成交分析",
    // subtext: '单位',
    textStyle: {
      color: '#333', // 文字颜色
      fontSize: 12, // 字体大小
      fontWeight: 'bold', // 字体粗细
    }
  },
  legend: {
  show:true, 
    orient: 'horizontal', // 水平方向
    top: 'bottom',
    left: 'center',
  },
  grid: { // 添加 grid 配置以调整图表内边距
    left: '1%',
    right: '15%',
    bottom: '6%',
    containLabel: true
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  xAxis: {
    type: 'value',
    data: []
  },
  yAxis: {
    type: 'category',
    data: yData
  },
  series: []
})
// 获取echarts实例
const instance1 = ref<any>(null)
const instanceFn1 = (val: any) => {
  instance1.value = val
  instance1.value.on('click', function (param: any) {
    console.log(val, param);
    push(`/userPortrait/labelManagement/labelDetails?sliceId=${param.data.sliceId}&&name=${param.name}`);

  })
}
const formData:any = ref({})
async function getYearTotal(nodeId:any) {
  // seriesData.value = []
  condition.value.totalNum  = 0
  let res:any = ''
  if(nodeId == undefined){
    // seriesData.value = []
    analyseOptionDeal.value.series = []
    res = await getLabelSliceInfo(id);
  }else{
    seriesData.value = []
    analyseOptionDeal.value.series = []
    res = await getLabelSliceInfo(nodeId);
  }
  console.log(res,'res')
  res.forEach((item:any) => {
   console.log('item',item,item.coverNum)
    condition.value.totalNum += Number(item.coverNum) 

  })

   const uerinfor = await queryLabelList({labelId:id})
  //  console.log('res',res[0])
   // 在另一个页面中获取对象
 
   console.log(formData.value, res)

  condition.value.baseExecutionTime = res[0].baseExecutionTimeStr
  condition.value.latestCalculationTime = res[0].latestCalculationTimeStr
  // condition.value.totalNum = res[0].totalNum

  const obj = {
    type:'bar',
    data: res.map(item => ({
      value: item.coverNum, // 或者直接使用 item.coverNum，取决于ECharts版本和配置
      sliceId: item.sliceId
    })),
  
    itemStyle: {
      color: function (params) {
        var colorList = ['#C23531', '#2F4554', '#61A0A8', '#D48265', '#91C7AE', '#749F83', '#CA8622'];
        return colorList[params.dataIndex]; 
      }
    }
  }
 
  
  // 更新yData和xData（如果你还需要它们在其他地方使用）
  yData.value = [...new Set(res.map(item => item.sliceName))]; // 使用Set去重
  // xData.value = res.map(item => item.coverNum);
  // seriesData.value.push(obj)
  analyseOptionDeal.value.series.push(obj)
  console.log('seriesData',"y",yData.value,xData.value,"x",obj)
}

// 跳转详情页
const handlerLookRules = () => {

  push(`/userPortrait/labelManagement/viewLabelRules?id=${id}`);

}

const searchHandler = (item)=>{
 console.log(item)
 initList({searchKey:item})
}
onMounted(() => {
  getYearTotal();
 
})
</script>


<style scoped lang="scss">
.circle {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: rgb(3, 245, 3);
}

.tree-wrapper {
  padding: 20px;
  border-radius: 6px;
  background-color: #fff;
  box-shadow: 0px 2px 22px rgba(0, 0, 0, 0.06);
  margin-right: 20px;
  overflow-y: auto;
}

.viewRules {
  width: 200px;
  border: 1px solid #e8ebf1;
  text-align: center;
  border-radius: 2%;
  height: 50px;
  line-height: 50px;
  cursor: pointer;
}
.backBtn {
  display: flex;
  justify-content: center;
  align-content: center;
 margin-top: 10px;
 position: static;
 bottom: 0;
}
::v-deep  .backBtn > .el-card__body{
  
 padding: 0px;
}
</style>