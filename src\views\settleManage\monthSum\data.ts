import dayjs from "dayjs";
import { useDictOptions } from "@/hooks/dict/useDictOptions";
const { electricityTypeOptions, industryOptions } = useDictOptions();
export const columns = [
  {
    label: "序号",
    width: 80,
    align: "center",
    type: "index"
  },
  {
    label: "零售用户名称",
    width: 140,
    prop: "retailUserName",
    align: "retailUserName",
    headerAlign: "center",
    headerSlot: "retailUserNameHeader",
    searchType: "text"
  },

  {
    label: "用电单元名称",
    width: 140,
    prop: "electricalUnitName",
    headerSlot: "electricalUnitNameHeader",
    align: "center",
    headerAlign: "center",
    searchType: "text",
  },
  {
    label: "用户号",
    width: 120,
    align: "center",
    headerAlign: "center",
    searchType: "text",
    headerSlot: "userCodeHeader",
    prop: "userCode",
  },
  {
    label: "计量点ID",
    width: 120,
    prop: "pointId",
    align: "center",
    headerSlot: "pointIdHeader",
    searchType: "text",
    headerAlign: "center",
  },
  {
    label: "交易类型",
    width: 120,
    prop: "transactionType",
    align: "center",
    headerSlot: "tranTypeHeader",
    searchType: "text",
    headerAlign: "center",
  },
  {
    label: "时段类型",
    width: 120,
    prop: "periodType",
    align: "center",
    headerSlot: "periodHeader",
    searchType: "text",
    headerAlign: "center",
  },
  {
    label: "月份",
    width: 120,
    prop: "month",
    align: "center",
    headerSlot: "monthHeader",
    searchType: "text",
    headerAlign: "center",
  },

  {
    label: "月度合同均价",
    width: 140,
    prop: "averageContractPrice",
    align: "center",
    headerSlot: "averageContractPriceHeader",
    searchType: "text",
    headerAlign: "center",
  },
  {
    label: "用电类别",
    width: 120,
    prop: "electricityUsetype",
    align: "center",
    headerSlot: "electricityUsetypeHeader",
    searchType: "text",
    headerAlign: "center",
  },
  {
    label: "电压等级",
    width: 120,
    prop: "voltageGrade",
    align: "center",
    headerSlot: "voltageGradeHeader",
    searchType: "text",
    headerAlign: "center",
  },
  {
    label: "比例系数K(%)",
    prop: "scale",
    width: 140,
    align: "center",
    headerSlot: "scaleHeader",
    searchType: "text",
    headerAlign: "center",
  },
  {
    label: "代理销售价模式",
    prop: "agentSalesModel",
    align: "center",
    width: 140,
    headerSlot: "agentSalesModelHeader",
    searchType: "text",
    headerAlign: "center",
  },
  {
    label: "计量点用电量",
    width: 140,
    prop: "pointElectricityConsumption",
    align: "center",
    headerSlot: "pointElectricityConsumptionHeader",
    searchType: "text",
    headerAlign: "center",
  },
  {
    label: "零售用户交易价格",
    width: 160,
    prop: "retailTransactionPrice",
    align: "center",
    headerSlot: "retailTransactionPriceHeader",
    searchType: "text",
    headerAlign: "center",
  },
  {
    label: "代理销售费用",
    width: 140,
    prop: "agencySalesFee",
    align: "center",
    headerSlot: "agencySalesFeeHeader",
    searchType: "text",
    headerAlign: "center",
  },
 
];
