# 不使用反向代理的解决方案

## 方案1：修改Vue项目配置（推荐）

### 1.1 修改 public/index.html
在 `public/index.html` 的 `<head>` 标签中添加或修改：

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- 移除或修改这些限制性的meta标签 -->
    <!-- <meta http-equiv="X-Frame-Options" content="deny"> -->
    
    <!-- 添加允许iframe嵌入的配置 -->
    <meta http-equiv="X-Frame-Options" content="ALLOWALL">
    
    <!-- 或者指定特定的父域名 -->
    <!-- <meta http-equiv="Content-Security-Policy" content="frame-ancestors http://***********:9291 https://***********:9291 'self';"> -->
    
    <!-- 允许所有域名嵌入（不推荐生产环境） -->
    <meta http-equiv="Content-Security-Policy" content="frame-ancestors *;">
    
    <title>您的应用标题</title>
</head>
<body>
    <div id="app"></div>
</body>
</html>
```

### 1.2 修改 vite.config.js 或 vue.config.js

#### 如果使用 Vite：
```javascript
// vite.config.js
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],
  server: {
    headers: {
      'X-Frame-Options': 'ALLOWALL',
      'Content-Security-Policy': 'frame-ancestors *'
    }
  },
  preview: {
    headers: {
      'X-Frame-Options': 'ALLOWALL',
      'Content-Security-Policy': 'frame-ancestors *'
    }
  }
})
```

#### 如果使用 Vue CLI：
```javascript
// vue.config.js
module.exports = {
  devServer: {
    headers: {
      'X-Frame-Options': 'ALLOWALL',
      'Content-Security-Policy': 'frame-ancestors *'
    }
  },
  configureWebpack: {
    devServer: {
      headers: {
        'X-Frame-Options': 'ALLOWALL',
        'Content-Security-Policy': 'frame-ancestors *'
      }
    }
  }
}
```

## 方案2：修改后端服务器配置

### 2.1 如果使用 Express.js
```javascript
// app.js 或 server.js
const express = require('express');
const app = express();

// 移除或修改安全头部中间件
app.use((req, res, next) => {
  // 移除限制性头部
  res.removeHeader('X-Frame-Options');
  
  // 设置允许iframe嵌入
  res.setHeader('X-Frame-Options', 'ALLOWALL');
  
  // 设置CSP策略
  res.setHeader('Content-Security-Policy', 'frame-ancestors *');
  
  // 解决跨域问题
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  
  next();
});

// 静态文件服务
app.use(express.static('dist'));

app.listen(8187, () => {
  console.log('Server running on port 8187');
});
```

### 2.2 如果使用 Nginx 直接服务静态文件
```nginx
# /etc/nginx/sites-available/your-app
server {
    listen 8187;
    server_name ***********;
    
    root /path/to/your/dist;  # Vue构建后的dist目录
    index index.html;
    
    # 处理Vue Router的history模式
    location / {
        try_files $uri $uri/ /index.html;
        
        # 移除限制性头部（如果nginx-extras可用）
        # more_clear_headers "X-Frame-Options";
        
        # 设置允许iframe嵌入的头部
        add_header X-Frame-Options "ALLOWALL" always;
        add_header Content-Security-Policy "frame-ancestors *" always;
        
        # 解决跨域问题
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization" always;
        
        # 处理OPTIONS预检请求
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
            add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization";
            add_header Content-Length 0;
            return 204;
        }
    }
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Frame-Options "ALLOWALL" always;
        add_header Content-Security-Policy "frame-ancestors *" always;
    }
}
```

## 方案3：修改构建配置

### 3.1 创建自定义插件移除CSP头部
```javascript
// build/remove-csp-plugin.js
class RemoveCSPPlugin {
  apply(compiler) {
    compiler.hooks.compilation.tap('RemoveCSPPlugin', (compilation) => {
      compilation.hooks.htmlWebpackPluginAfterHtmlProcessing.tapAsync(
        'RemoveCSPPlugin',
        (data, cb) => {
          // 移除限制性的CSP meta标签
          data.html = data.html.replace(
            /<meta[^>]*http-equiv=["']X-Frame-Options["'][^>]*>/gi,
            ''
          );
          data.html = data.html.replace(
            /<meta[^>]*http-equiv=["']Content-Security-Policy["'][^>]*frame-ancestors[^>]*>/gi,
            ''
          );
          
          // 添加允许iframe的meta标签
          data.html = data.html.replace(
            '</head>',
            '  <meta http-equiv="X-Frame-Options" content="ALLOWALL">\n  <meta http-equiv="Content-Security-Policy" content="frame-ancestors *">\n</head>'
          );
          
          cb(null, data);
        }
      );
    });
  }
}

module.exports = RemoveCSPPlugin;
```

### 3.2 在webpack配置中使用插件
```javascript
// vue.config.js
const RemoveCSPPlugin = require('./build/remove-csp-plugin');

module.exports = {
  configureWebpack: {
    plugins: [
      new RemoveCSPPlugin()
    ]
  }
}
```

## 方案4：运行时动态修改

### 4.1 在main.js中添加
```javascript
// src/main.js
import { createApp } from 'vue'
import App from './App.vue'

// 动态移除限制性头部
if (typeof document !== 'undefined') {
  // 移除现有的限制性meta标签
  const existingFrameOptions = document.querySelector('meta[http-equiv="X-Frame-Options"]');
  if (existingFrameOptions) {
    existingFrameOptions.remove();
  }
  
  const existingCSP = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
  if (existingCSP && existingCSP.content.includes('frame-ancestors')) {
    existingCSP.remove();
  }
  
  // 添加允许iframe的meta标签
  const frameOptionsMeta = document.createElement('meta');
  frameOptionsMeta.httpEquiv = 'X-Frame-Options';
  frameOptionsMeta.content = 'ALLOWALL';
  document.head.appendChild(frameOptionsMeta);
  
  const cspMeta = document.createElement('meta');
  cspMeta.httpEquiv = 'Content-Security-Policy';
  cspMeta.content = 'frame-ancestors *';
  document.head.appendChild(cspMeta);
}

createApp(App).mount('#app')
```

## 快速测试方法

### 1. 检查当前响应头
```bash
curl -I http://***********:8187/
```

### 2. 创建测试页面
```html
<!DOCTYPE html>
<html>
<head>
    <title>iframe测试</title>
</head>
<body>
    <h1>测试iframe嵌入</h1>
    <iframe 
        src="http://***********:8187/?hide=true" 
        width="100%" 
        height="600px"
        frameborder="0"
        onload="console.log('iframe加载成功')"
        onerror="console.log('iframe加载失败')">
    </iframe>
    
    <script>
        // 监听iframe消息
        window.addEventListener('message', function(event) {
            console.log('收到iframe消息:', event.data);
        });
    </script>
</body>
</html>
```

### 3. 验证配置
- 打开浏览器开发者工具
- 查看Network标签页的响应头
- 确认没有 `X-Frame-Options: deny`
- 确认有 `X-Frame-Options: ALLOWALL` 或类似配置

## 推荐方案

**最简单的方案**：修改 `public/index.html`，添加：
```html
<meta http-equiv="X-Frame-Options" content="ALLOWALL">
<meta http-equiv="Content-Security-Policy" content="frame-ancestors *;">
```

然后重新构建和部署项目即可。
