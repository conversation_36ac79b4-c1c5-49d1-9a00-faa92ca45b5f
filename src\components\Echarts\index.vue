<template>
  <div class="chart_box" :id="Id">
    <div ref="inner"></div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, watch, ref, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts'
const emit = defineEmits(['instanceFn'])
const props = defineProps({
  echartsData: {
    type: Object,
    default: () => {},
  },
  EWidth: {
    type: String,
    default: '100%',
  },
  EHeight: {
    type: String,
    default: '100%',
  },
  echartId: {
    type: String,
    default: 'echartId',
  },
})
let myChart: any = null
let outer: any = null
let inner = ref()

const EW = ref(props.EWidth)
const EH = ref(props.EHeight)
const Id = props.echartId
let unA: any = null
//参数container为图表盒子节点.charts为图表节点
const chartssize = (container: any, charts: any) => {
  if (!charts) return
  function getStyle(el: any, _: any) {
    if (window.getComputedStyle) {
      return window.getComputedStyle(el, null)
    } else {
      return el.currentStyle
    }
  }
  const wi = getStyle(container, 'width').width
  const hi = getStyle(container, 'height').height
  charts.style.width = wi
  charts.style.height = hi
  return charts
} //动态赋值

const fn = () => {
  if (myChart) {
    myChart.dispose() //销毁
  }
  outer = document.getElementById(Id)
  inner.value = chartssize(outer, inner.value)
  myChart = echarts.init(inner.value)
  myChart.clear()
  const option = props.echartsData
  window.addEventListener('resize', () => {
    myChart.resize()
  })
  option && myChart.setOption(option)
  emit('instanceFn', myChart)
  // myChart.on('mousemove', () => {
  //   console.log(`output->111`, 111)
  // })
}
onMounted(() => {
  fn()
})

window.onresize = function () {
  inner.value = chartssize(outer, inner.value)
}

//监听dom变化
setTimeout(() => {
  const eleZxx = document.getElementById(Id) as any
  const updateFn = throttle((entries: any) => {
    for (const _ of entries) {
      eleZxx.style.width = EW.value
      eleZxx.style.height = EH.value
      inner.value = chartssize(eleZxx, inner.value)
      myChart.resize()
    }
  }, 10)
  const a = new ResizeObserver(updateFn)
  unA = a
  a.observe(document.getElementById(Id) as any)
}, 0)
watch(
  () => props.echartsData,
  () => {
    fn()
  },
  {
    deep: true,
  },
) //更新

//节流函数
function throttle(fn: (T: any) => void, delay: number) {
  let timer: any = null
  // @ts-ignore
  const self = this
  return function () {
    if (!timer) {
      timer = setTimeout(() => {
        // @ts-ignore
        fn.apply(self, arguments)
        timer = null
      }, delay)
    }
  }
}

onBeforeUnmount(() => {
  myChart.clear()
  window.removeEventListener('resize', () => {
    myChart.resize()
  })
  unA.unobserve(document.getElementById(Id))
})
</script>

<style scoped lang="scss">
.chart_box {
  width: v-bind(EW);
  height: v-bind(EH);
}
</style>
