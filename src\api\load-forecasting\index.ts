import { http } from "@/utils/http";
import { baseUrlApi } from "../utils";
import {
  ResponseDetailModel,
  BasicResponseParams,
  BasicPageParams
} from "@/model/baseModel";
import { request } from '@/utils/request'
export type RequestWeatherModel = {
  end: number;
  start: number;
};
export type ResponeWeatherModel = {
  temperature: number;
  time: string;
};
export type RequesForecastParameterModel = {
  parameter: RequesForecastHomeModel;
};
export type RequesForecastHomeModel = {
  startDate: number;
  endDate: number;
  timeType?: number;
  customId?: number | string;
};
export type ResponseForecastModel = {
  curveList: ForecastModel[];
  parameter: RequesForecastHomeModel;
  summary: ForecastSummaryModel;
};
export type ForecastSummaryModel = {
  average: number;
  max: number;
  min: number;
  total: number;
};
export type ForecastModel = {
  forecast: number;
  real: number;
  declaration: number;
  temperature: number;
  time: string;
};
export type ResponseNewDateModel = {
  maxDeclareDate: string;
  maxForecastDate: string;
  maxPowerDate: string;
};
export type ForecastHomeModel = {
  forecast: number;
  real: number;
  deviation: number;
  temperature: number;
  time: string;
};
export type ResponseForecastHomeModel = {
  curveList: ForecastHomeModel[];
  parameter: RequesForecastHomeModel;
  summary: ForecastSummaryHomeModel;
};
export type ForecastSummaryHomeModel = {
  averageDeviationRate: number;
  forecastTotal: number;
  realTotal: number;
  totalDeviationRate: number;
};
// 获取天气曲线
export const getWeatherListApi = (data: RequesForecastHomeModel) => {
  return http.request<ResponseDetailModel<ResponeWeatherModel[]>>(
    "post",
    baseUrlApi("system/weather/query"),
    {
      data
    }
  );
};
// 负荷预测页面查询信息;
export const getForecastHomeApi = (data: RequesForecastParameterModel) => {
  return http.request<ResponseDetailModel<ResponseForecastHomeModel>>(
    "post",
    baseUrlApi("energyForecast/home"),
    {
      data
    }
  );
};
// 有数据的最大日期;
export const getForecastAnalysisDateApi = () => {
  return http.request<ResponseDetailModel<ResponseNewDateModel>>(
    "get",
    baseUrlApi("custom/forecast/analysis")
  );
};
// 负荷预测页面查询信息;
export const getForecastAnalysisApi = (data: RequesForecastParameterModel) => {
  return http.request<ResponseDetailModel<ResponseForecastModel>>(
    "post",
    baseUrlApi("energyForecast/analysis"),
    {
      data
    }
  );
};
// 申报电量管理页面查询信息
export const getForecastLoadDeclareApi = (
  data: RequesForecastParameterModel
) => {
  return http.request<any>("post", baseUrlApi("energyForecast/loadDeclare"), {
    data
  });
};
// 用户负荷管理列表查询信息;
export const getForecastQueryPageApi = data => {
  return http.request<any>("post", baseUrlApi("custom/forecast/queryPage"), {
    data
  });
};
// 统计负荷管理数据完整度
export const getDataIntegrityApi = data => {
  return http.request<any>(
    "post",
    baseUrlApi("custom/forecast/getDataIntegrity"),
    {
      data
    }
  );
};
// 获取用能分析数据明细
export const getForecastDetailApi = data => {
  return http.request<any>("post", baseUrlApi("/custom/forecast/getPowerAnalysisDetailedOne"), {
    data
  });
};
// 户号表计查询;
export const queryCustomAccountApi = customName => {
  return http.request<any>("get", baseUrlApi(`custom/account/queryAccountInfo?customName=${customName}`), {
  });
};
export const getForecasConfigApi = () => {
  return http.request<any>("get", baseUrlApi("custom/forecast/getConfig"));
};
export const saveForecasConfigApi = data => {
  return http.request<any>("post", baseUrlApi("custom/forecast/saveConfig"), {
    data
  });
};
// 保存某天表计电量曲线
export const saveDayPointsApi = data => {
  return http.request<any>("post", baseUrlApi("custom/power/saveDayPoints"), {
    data
  });
};

// 导出某天表计电量曲线
export const exportDayPointsApi = (data: any) => {
  const res = request.post<any>(
      {
        url: '/energyForecast/exportHome',
        responseType: 'blob',
        data,
      },
      { isTransformResponse: false },
  )
  return res
}
