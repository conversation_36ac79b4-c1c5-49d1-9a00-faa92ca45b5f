<template>
  <div>
    <!-- <el-card class="jt-card"></el-card> -->
    <el-card class="jt-card">
      <div class="header" style="margin-bottom: 20px">
        <div class="header-title">
          <img src="@/assets/svg/title-arrow.svg" alt="" />
          查询条件
        </div>
      </div>
      <div style="display: inline-block">
        <MyTab v-model="searchConditionActive" @change="searchConditionChange" :tabs="['营收总览', '交易分析', '收益分析']"></MyTab>
      </div>
      <template v-if="searchConditionActive === '营收总览'">
        日期类型
        <div style="display: inline-block">
          <el-select @change="dateChange" style="margin-left: 20px; width: 200px" class="singleSelect"
            v-model="multipleDate" placeholder="日期类型">
            <el-option v-for="item in ['单日', '多日']" :key="item" :label="item" :value="item" />
          </el-select>
        </div>
        <el-date-picker @change="dateChange" v-if="multipleDate === '单日'" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
          v-model="timeDate" style="margin-left: 15px" :clearable="false" />
        <el-date-picker @change="dateChange" v-if="multipleDate === '多日'" style="width: 228px; margin-left: 15px"
          range-separator="至" format="YYYY-MM-DD" value-format="YYYY-MM-DD" v-model="dayDate" type="daterange"
          start-placeholder="开始日期" end-placeholder="结束日期" />
      </template>

      <template v-else>
        <el-date-picker @change="dateChange" format="YYYY-MM-DD" value-format="YYYY-MM-DD" v-model="timeDate"
          style="margin-left: 15px" :clearable="false" />
      </template>
    </el-card>
    <!-- 3个卡片平分 -->
    <revenue-all v-if="searchConditionActive === '营收总览'" :multipleDate="multipleDate" :total-info="totalInfo"
      :all-data="allData"></revenue-all>
    <trade-ana v-if="searchConditionActive === '交易分析'" :timeDate="timeDate" :total-info="totalInfo"
      :all-data="allData"></trade-ana>
    <revenue-ana v-if="searchConditionActive === '收益分析'" :all-data="allData"></revenue-ana>
  </div>
</template>

<script setup lang="ts">
import { nextTick, onMounted, ref } from 'vue'
import dayjs from 'dayjs' // 引入dayjs
import revenueAll from './revenueAll.vue'
import revenueAna from './revenueAna.vue'
import tradeAna from './tradeAna.vue'
import {
  dailySettlementInfo, //查询日结算列表
  hourSettlementInfo, //查询24小时结算列表
} from '@/api'

// 日期类型改变
function dateChange() {
  getData()
}
// 全部数据
const allData = ref<any>({
})
// 营收总览顶部四个总计
const totalInfo = ref<any>({})

function getData() {
  nextTick(async () => {
    if (searchConditionActive.value === '营收总览') {
      if (multipleDate.value === '单日') {
        const res = await hourSettlementInfo({
          startTime: timeDate.value,
          endTime: timeDate.value
        })
        if (res) {
          allData.value = res.dailySettlementInfoList.sort((a: any, b: any) => a.time - b.time)
        } else {
          allData.value = {}
        }
        const res2 = await dailySettlementInfo({
          startTime: timeDate.value,
          endTime: timeDate.value
        })
        if (res2) {
          totalInfo.value = res2.totalInfo
        } else {
          totalInfo.value = {}
        }
      } else {
        const res = await dailySettlementInfo({
          startTime: dayDate.value[0],
          endTime: dayDate.value[1]
        })
        if (res) {
          allData.value = res.dailySettlementInfoList
          totalInfo.value = res.totalInfo
        } else {
          allData.value = {}
          totalInfo.value = {}
        }
      }
    } else {
      console.log(`output->111`, 111)
      const res = await hourSettlementInfo({
        startTime: timeDate.value,
        endTime: timeDate.value
      })
      if (res) {
        allData.value = res.dailySettlementInfoList.sort((a: any, b: any) => a.time - b.time)
      } else {
        allData.value = {}
      }
      const res2 = await dailySettlementInfo({
        startTime: timeDate.value,
        endTime: timeDate.value
      })
      if (res2) {
        totalInfo.value = res2.totalInfo
      } else {
        totalInfo.value = {}
      }
    }
  })

}
onMounted(() => {
  getData()
})

const searchConditionActive = ref('营收总览')
const multipleDate = ref('单日')
function searchConditionChange(val: string) {
  getData()
}
const timeDate = ref(dayjs().format('YYYY-MM-DD'))
const dayDate = ref([
  dayjs().startOf('month').format('YYYY-MM-DD'),
  dayjs().endOf('month').format('YYYY-MM-DD'),
])
</script>

<style scoped lang="scss"></style>
