<template>
  <div ref="chartRef" :style="{ height, width }" />
</template>

<script setup lang="ts">
import {
  ref,
  computed,
  onMounted,
  watch,
  type Ref,
  PropType,
  nextTick
} from "vue";
import { useAppStoreHook } from "@/store/modules/app";
import {
  delay,
  useDark,
  useECharts,
  type EchartOptions
} from "@pureadmin/utils";
import * as echarts from "echarts/core";
import type { EChartsOption } from "echarts";
const emit = defineEmits(["change"]);
const props = defineProps({
  height: {
    type: String as PropType<string>,
    default: "100%"
  },
  width: {
    type: String as PropType<string>,
    default: "100%"
  },
  xData: {
    type: Array as PropType<string[] | number[]>,
    default: () => []
  },
  yData: {
    type: Array as PropType<string[] | number[]>,
    default: () => []
  },
  series: {
    type: Array as PropType<Array<object>>,
    default: () => []
  }
});
const { isDark } = useDark();

const theme: EchartOptions["theme"] = computed(() => {
  return isDark.value ? "dark" : "light";
});

const chartRef = ref<HTMLDivElement | null>(null);
const { setOptions, resize } = useECharts(chartRef as Ref<HTMLDivElement>, {
  theme
});

const getOption = (): EChartsOption => {
  return {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow"
      }
    },
    grid: {
      bottom: "15%",
      right: "5%"
    },
    legend: {
      bottom: "6%"
    },
    xAxis: {
      type: "value",
      boundaryGap: [0, 0.01]
    },
    yAxis: {
      type: "category",
      data: props.yData
    },
    series: props.series,
    addTooltip: true
  };
};

watch(
  () => useAppStoreHook().getSidebarStatus,
  () => {
    delay(600).then(() => resize());
  }
);

watch(
  () => props,
  () => setOptions(getOption() as EChartsOption, false, { notMerge: true }),
  {
    immediate: true,
    deep: true
  }
);
onMounted(() => {
  nextTick(() => {
    var myChart = echarts.init(chartRef.value!);
    // 给每条数据增加点击事件
    myChart.getZr().on("click", function (params) {
      var pointInPixel = [params.offsetX, params.offsetY];
      if (myChart.containPixel("grid", pointInPixel)) {
        var pointInGrid = myChart.convertFromPixel("grid", pointInPixel);
        let xIndex = pointInGrid[1];
        emit("change", xIndex);
      }
    });
  });
  delay(300).then(() => resize());
});
</script>
