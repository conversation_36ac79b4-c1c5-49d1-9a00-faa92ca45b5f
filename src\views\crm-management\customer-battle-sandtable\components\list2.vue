<template>
  <div>
    <pure-table :columns="columns1" border stripe :loading="loading" :data="tableData" :pagination="pagination"
      @page-size-change="onSizeChange" @page-current-change="onCurrentChange" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from "vue";
import { columns1 } from "./data";
import type { PaginationProps } from "@pureadmin/table";
import { delay } from "@pureadmin/utils";
import { getCustomerListApi } from "@/api/customer-management/index";
import {
  queryPageSaleUser, //分页查询营销人员用户关联
} from '@/api'
const props = defineProps({
  queryId: {
    type: String as PropType<string>,
    default: "0"
  },
  time: {
    type: String,
    default: ""
  }
});
const loading = ref(false);
const tableData = ref([]);
const searchInfo = ref({
  time: props.time ?  props.time : "",
  name: "",
  isSign:true,
  customSaleId: "",
  pageNo: 1,
  pageSize: 10
});
/** 分页配置 */
const pagination = reactive<PaginationProps>({
  pageSize: 10,
  currentPage: 1,
  pageSizes: [10, 20, 40, 50],
  total: 0,
  align: "right",
  background: true,
  small: false
});
function onSizeChange(val) {
  searchInfo.value.pageSize = val;
  getList();
}
function onCurrentChange(val) {
  searchInfo.value.pageNo = val;
  getList();
}
async function getList() {
  loading.value = true;
  const res = await queryPageSaleUser(searchInfo.value);
  pagination.total = res.totalCount ? Number(res.totalCount) : 0;
  tableData.value = res.data ? res.data : [];
  delay(300).then(() => {
    loading.value = false;
  });
}
// async function getList() {
//   loading.value = true;
//   const { data } = await getCustomerListApi(searchInfo.value);
//   pagination.total = data ? Number(data.totalCount) : 0;
//   tableData.value = data ? data.data : [];
//   delay(300).then(() => {
//     loading.value = false;
//   });
// }
onMounted(() => {
  getList();
});
watch(
  () => props.queryId,
  newVal => {
    searchInfo.value.customSaleId = newVal;
    getList();
  },
  { immediate: true }
);
</script>

<style scoped></style>
