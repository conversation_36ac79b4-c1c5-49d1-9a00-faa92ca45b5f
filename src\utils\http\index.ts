import Axios, {
  AxiosInstance,
  AxiosRequestConfig,
  CustomParamsSerializer
} from "axios";
import {
  PureHttpError,
  RequestMethods,
  PureHttpResponse,
  PureHttpRequestConfig
} from "./types.d";
import { stringify } from "qs";
import NProgress from "../progress";
import { getToken, formatToken, removeToken, setToken } from "@/utils/auth";
import { useUserStore, useUserStoreHook } from "@/store/modules/user";
import { ElMessage } from "element-plus";
import { useRouter } from "vue-router";
const router = useRouter();
// 相关配置请参考：www.axios-js.com/zh-cn/docs/#axios-request-config-1
const defaultConfig: AxiosRequestConfig = {
  // 请求超时时间
  timeout: 600000,
  headers: {
    Accept: "application/json, text/plain, */*",
    "Content-Type": "application/json",
    "X-Requested-With": "XMLHttpRequest"
  },
  // 数组格式参数序列化（https://github.com/axios/axios/issues/5142）
  paramsSerializer: {
    serialize: stringify as unknown as CustomParamsSerializer
  }
};

class PureHttp {
  constructor() {
    this.httpInterceptorsRequest();
    this.httpInterceptorsResponse();
  }

  /** token过期后，暂存待执行的请求 */
  private static requests = [];

  /** 防止重复刷新token */
  private static isRefreshing = false;

  /** 初始化配置对象 */
  private static initConfig: PureHttpRequestConfig = {};

  /** 保存当前Axios实例对象 */
  private static axiosInstance: AxiosInstance = Axios.create(defaultConfig);

  /** 重连原始请求 */
  private static retryOriginalRequest(config: PureHttpRequestConfig) {
    return new Promise(resolve => {
      PureHttp.requests.push((token: string) => {
        config.headers["Authorization"] = formatToken(token);
        resolve(config);
      });
    });
  }

  /** 请求拦截 */
  private httpInterceptorsRequest(): void {
    PureHttp.axiosInstance.interceptors.request.use(
      async (config: PureHttpRequestConfig): Promise<any> => {
        // 开启进度条动画
        NProgress.start();
        // 优先判断post/get等方法是否传入回调，否则执行初始化设置等回调
        if (typeof config.beforeRequestCallback === "function") {
          config.beforeRequestCallback(config);
          return config;
        }
        if (PureHttp.initConfig.beforeRequestCallback) {
          PureHttp.initConfig.beforeRequestCallback(config);
          return config;
        }
        /** 请求白名单，放置一些不需要token的接口（通过设置请求白名单，防止token过期后再请求造成的死循环问题） */
        const whiteList = ["/refreshToken", "/login"];
        return whiteList.some(v => config.url.indexOf(v) > -1)
          ? config
          : new Promise(resolve => {
            const data = getToken();
            const { longToken } = useUserStore();
            const { token } = useUserStore();

            config.headers["Authorization"] = token
            config.headers["token"] = token
            config.headers["longToken"] = longToken
            // if (data) {
            //   console.log(`output->parseInt(data.expires)`, data, parseInt(data.expires))
            //   const now = new Date().getTime();
            //   const expired = parseInt(data.expires) - now <= 0;
            //   config.headers["Authorization"] = formatToken(data.accessToken);
            //   config.headers["token"] = token
            //   config.headers["longToken"] = longToken
            //   if (expired) {
            //     if (!PureHttp.isRefreshing) {
            //       PureHttp.isRefreshing = true;
            //       // token过期刷新
            //       useUserStoreHook()
            //         .handRefreshToken({ refreshToken: data.refreshToken })
            //         .then(res => {
            //           const token = res.data.accessToken;
            //           config.headers["Authorization"] = formatToken(token);
            //           config.headers["token"] = token;
            //           config.headers["longToken"] = longToken;
            //           PureHttp.requests.forEach(cb => cb(token));
            //           PureHttp.requests = [];
            //         })
            //         .finally(() => {
            //           PureHttp.isRefreshing = false;
            //         });
            //     }
            //     resolve(PureHttp.retryOriginalRequest(config));
            //   } else {
            //     // console.log(data.accessToken, token)
            //     config.headers["Authorization"] = formatToken(
            //       data.accessToken
            //     );
            //     config.headers["token"] = token;
            //     config.headers["longToken"] = longToken;
            //     resolve(PureHttp.retryOriginalRequest(config));
            //   }
            // } else {
            // }
            resolve(config);
          });
      },
      error => {
        return Promise.reject(error);
      }
    );
  }

  /** 响应拦截 */
  private httpInterceptorsResponse(): void {
    const env = import.meta.env.MODE || 'development'
    const instance = PureHttp.axiosInstance;
    instance.interceptors.response.use(
      (response: PureHttpResponse) => {
        if (response.headers?.authorization) {
          // console.log('刷新Token Http', response.headers, response.config)
          useUserStore().userSetToken(response.headers?.authorization)
          setToken({
            username: useUserStore().username,
            roles: ["admin"],
            accessToken: response.headers?.authorization
          } as any);
            let relativeUrl:string | undefined= ''
            // 使用相对路径
            if (env == 'development') {
                response.config.url = response.config?.url?.replace(/^\/api/, '')
                relativeUrl = response.config?.url?.replace(/^\/api/, '');
            } else {
                response.config.url = response.config?.url?.replace(/^\/selling/, '')
                relativeUrl = response.config?.url?.replace(/^\/selling/, '');
            }
          PureHttp.axiosInstance({...response.config, url: relativeUrl})
        }
        if (
          response.data.message &&
          response.data.message.indexOf("未登录") > -1
        ) {
          ElMessage({
            message: response.data.message,
            type: "error"
          });
          try {
            useUserStoreHook().logOut();
          } catch (err) {
            console.log(err, "err");
          }
        }
        if (response.status == 401) {
          removeToken()
          router.push({ path: "/login" });
        }
        if (response.data.code === "600") {
          ElMessage({
            message: response.data.message,
            type: "error"
          });
        }
        if (response.data.code == "200001") {
          ElMessage({
            message: response.data.message,
            type: "info"
          });
        }
        const $config = response.config;
        // 关闭进度条动画
        NProgress.done();
        // 优先判断post/get等方法是否传入回调，否则执行初始化设置等回调
        if (typeof $config.beforeResponseCallback === "function") {
          $config.beforeResponseCallback(response);
          return response.data;
        }
        if (PureHttp.initConfig.beforeResponseCallback) {
          PureHttp.initConfig.beforeResponseCallback(response);
          return response.data;
        }
        return response.data;
      },
      (error: PureHttpError) => {
        const $error = error;
        $error.isCancelRequest = Axios.isCancel($error);
        // 关闭进度条动画
        NProgress.done();
        // 所有的响应异常 区分来源为取消请求/非取消请求
        return Promise.reject($error);
      }
    );
  }

  /** 通用请求工具函数 */
  public request<T>(
    method: RequestMethods,
    url: string,
    param?: AxiosRequestConfig,
    axiosConfig?: PureHttpRequestConfig
  ): Promise<T> {
    const config = {
      method,
      url,
      ...param,
      ...axiosConfig
    } as PureHttpRequestConfig;

    // 单独处理自定义请求/响应回调
    return new Promise((resolve, reject) => {
      PureHttp.axiosInstance
        .request(config)
        .then((response: undefined) => {
          resolve(response);
        })
        .catch(error => {
          console.log(error)
          if (error.response?.status === 401) {
            // ElMessage.error({message:'您的登录已过期，请重新登录！', grouping: true})
            // token 过期不提醒，直接跳转到登录页面
            removeToken()
            // useUserStore().userLogoutTimeout(() => {
            //     router.replace('/login')
            // })
            useUserStoreHook().logOut();
          }
          reject(error);
        });
    });
  }

  /** 单独抽离的post工具函数 */
  public post<T, P>(
    url: string,
    params?: AxiosRequestConfig<T>,
    config?: PureHttpRequestConfig
  ): Promise<P> {
    return this.request<P>("post", url, params, config);
  }

  /** 单独抽离的get工具函数 */
  public get<T, P>(
    url: string,
    params?: AxiosRequestConfig<T>,
    config?: PureHttpRequestConfig
  ): Promise<P> {
    return this.request<P>("get", url, params, config);
  }
}

export const http = new PureHttp();
