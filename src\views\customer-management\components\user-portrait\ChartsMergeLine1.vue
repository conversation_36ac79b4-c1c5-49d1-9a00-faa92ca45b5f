<template>
  <div ref="chartRef" :style="{ height, width }" />
</template>
<script lang="ts">
import { defineComponent, PropType, ref, Ref, watch, onMounted } from "vue";
import { useECharts, delay, type EchartOptions } from "@pureadmin/utils";
import * as echarts from "echarts/core";
import { useAppStoreHook } from "@/store/modules/app";
import dayjs, { Dayjs } from "dayjs";
export default defineComponent({
  props: {
    width: {
      type: String as PropType<string>,
      default: "100%"
    },
    height: {
      type: String as PropType<string>,
      default: "calc(100vh - 210px)"
    },
    yAxisName: {
      type: String as PropType<string>,
      default: ""
    },
    xData: {
      type: Array as PropType<string[]>,
      default: () => []
    },
    series: {
      type: Array as PropType<Array<object>>,
      default: () => []
    },
    settleCostList: {
      type: Array as PropType<Array<object>>,
      default: () => []
    },
    settleElectList: {
      type: Array as PropType<Array<object>>,
      default: () => []
    },
    settlePriceList: {
      type: Array as PropType<Array<object>>,
      default: () => []
    },
    settleAvgList: {
      type: Array as PropType<Array<object>>,
      default: () => []
    },
    startDate: {
      type: Dayjs,
      default: ""
    },
    endDate: {
      type: Dayjs,
      default: ""
    }
  },
  setup(props) {
    const chartRef = ref<HTMLDivElement | null>(null);
    const { setOptions, resize } = useECharts(chartRef as Ref<HTMLDivElement>);
    //图表定位
    const chartGridTop = 60;
    const chartGridHeight = 150;
    const getOption = () => {
      return {
        tooltip: {
          trigger: "axis",
          // axisPointer: {
          //   type: 'shadow',
          //   label: {
          //     show: true,
          //     backgroundColor: '#21BD9B',
          //   },
          // },
          formatter: function (params) {
            //数据单位格式化
            // console.log(params, 'params');
            let relVal = params[0].name; //x轴名称
            relVal += "<div style='min-width:180px'>";
            for (let i = 0, l = params.length; i < l; i++) {
              if (params[i].value !== "-" && params[i].value !== null) {
                relVal +=
                  "<span  style='display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:" +
                  params[i].color +
                  ";'>" +
                  '<span  style="display:block;padding-left:15px;margin-top:-4px">' +
                  params[i].seriesName +
                  " : " +
                  params[i].value.toFixed(3) +
                  "</span>" +
                  "</span>" +
                  "<br>";
              } else if (params[i].value === "-") {
                relVal +=
                  "<span  style='display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:" +
                  params[i].color +
                  ";'>" +
                  '<span  style="display:block;padding-left:15px;margin-top:-4px">' +
                  params[i].seriesName +
                  " : " +
                  params[i].value.toFixed(3) +
                  "</span>" +
                  "</span>" +
                  "<br>";
              } else {
                relVal +=
                  "<span  style='display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:" +
                  params[i].color +
                  ";'>" +
                  '<span  style="display:block;padding-left:15px;margin-top:-4px">' +
                  params[i].seriesName +
                  " : " +
                  "-" +
                  "</span>" +
                  "</span>" +
                  "<br>";
              }
            }
            relVal += "</div>";
            return relVal;
          }
        },
        //坐标轴指示器（axisPointer）的全局公用设置
        axisPointer: {
          type: "shadow",
          link: {
            xAxisIndex: "all"
          }
        },
        grid: [
          makeGrid(chartGridTop, chartGridHeight),
          makeGrid(chartGridTop + (chartGridHeight + 60), chartGridHeight)
        ],
        legend: {
          right: "center",
          top: "4%",
          textStyle: {
            fontSize: 14
          },
          width: "100%"
        },
        xAxis: [makeXAxis(0), makeXAxis(1)],
        yAxis: [makeYAxis(0, 0), makeYAxis(1, 1), makeYAxis(1, 2)],
        series: [
          makeGridData(
            0,
            0,
            "line",
            "结算电费",
            props.settleCostList,
            "#5087ec"
          ),
          makeGridData(
            1,
            1,
            "line",
            "结算电量",
            props.settleElectList,
            "#68bbc4"
          ),
          makeGridData(
            1,
            2,
            "bar",
            "结算电价",
            props.settlePriceList,
            "#ef8683"
          ),
          makeGridData(1, 2, "line", "推送均价", props.settleAvgList, "#83daad")
        ]
      };
    };
    //直角坐标系内绘图网格
    function makeGrid(top, height, opt?) {
      return echarts.util.merge(
        {
          left: 90,
          right: "5%",
          top: top,
          height: height
        },
        opt || {},
        true
      );
    }

    //X轴生成器
    function makeXAxis(gridIndex, opt?) {
      return echarts.util.merge(
        {
          type: "category",
          gridIndex: gridIndex,
          axisLabel: {
            // x轴标签

            fontSize: 14
          },
          axisLine: {
            // x轴线
            lineStyle: {
              opacity: 0.15
            }
          },
          // boundaryGap: false,
          axisTick: {
            // x轴刻度
            show: false
          },
          splitLine: {
            show: true,
            lineStyle: {
              // opacity: 0.15,
            }
          },
          data: props.xData
        },
        opt || {},
        true
      );
    }

    //Y轴生成器
    function makeYAxis(gridIndex, yAxisIndex, opt?) {
      return echarts.util.merge(
        {
          scale: true,
          boundaryGap: false,
          name: yAxisIndex === 0 ? "元" : yAxisIndex === 1 ? "MWh" : "元/MWh",
          gridIndex: gridIndex,
          nameTextStyle: {
            padding: [0, 40, 0, 0],
            fontSize: 12
          },
          type: "value",
          axisLine: {
            lineStyle: {
              opacity: 0.2
            }
          },

          axisTick: {
            show: false
          },
          axisLabel: {
            fontSize: 14
          },
          splitLine: {
            lineStyle: {
              // opacity: 0.15,
            }
          }
        },
        opt || {},
        true
      );
    }

    //数据生成器
    function makeGridData(
      xAxisIndex,
      yAxisIndex,
      chartType,
      chartName,
      chartData,
      color,
      opt?
    ) {
      return echarts.util.merge(
        {
          type: chartType,
          name: chartName,
          xAxisIndex: xAxisIndex,
          yAxisIndex: yAxisIndex,
          smooth: true,
          showSymbol: false,
          itemStyle: {
            color
          },
          barMaxWidth: "20",
          markPoint: {
            label: {
              formatter: params => params.value
            },
            data: [
              { type: "max", name: "Max" },
              { type: "min", name: "Min" }
            ]
          },
          data: chartData
        },
        opt || {},
        true
      );
    }
    watch(
      () => useAppStoreHook().getSidebarStatus,
      () => {
        delay(600).then(() => resize());
      }
    );
    watch(
      () => props,
      () => setOptions(getOption() as EchartOptions, false, { notMerge: true }),
      {
        immediate: true,
        deep: true
      }
    );
    onMounted(() => {
      delay(300).then(() => resize());
    });

    return { chartRef };
  }
});
</script>
