export interface loginFormI {
  username: string
  password: string
}
export interface dictionaryPaI {
  dictCode: string
  dictDesc: string
  dictName: string
  id: string
  serialNumber: number
}
export interface dictionaryI {
  dictItemCode: string
  dictItemDesc: string
  dictItemValue: string
  serialNumber: number
}
export type dictionaryPaListI = dictionaryPaI[]
export type dictionaryListI = dictionaryI[]
