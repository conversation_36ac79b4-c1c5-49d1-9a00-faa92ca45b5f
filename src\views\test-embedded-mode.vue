<template>
  <div class="embedded-mode-test">
    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <span>嵌入模式测试页面</span>
        </div>
      </template>

      <div class="test-content">
        <el-row :gutter="20">
          <el-col :span="12">
            <h3>当前状态</h3>
            <el-descriptions :column="1" border>
              <el-descriptions-item label="嵌入模式">
                <el-tag :type="debugInfo.isEmbedded ? 'success' : 'info'">
                  {{ debugInfo.isEmbedded ? "已启用" : "未启用" }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="localStorage值">
                <code>{{ debugInfo.storageValue || "未设置" }}</code>
              </el-descriptions-item>
              <el-descriptions-item label="当前URL">
                <code style="word-break: break-all">{{
                  debugInfo.currentUrl
                }}</code>
              </el-descriptions-item>
              <el-descriptions-item label="hide参数">
                <el-tag :type="debugInfo.hasHideParam ? 'warning' : 'info'">
                  {{ debugInfo.hasHideParam ? debugInfo.hideParamValue : "无" }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="样式状态">
                <el-tag :type="hasEmbeddedStyles ? 'success' : 'warning'">
                  {{ hasEmbeddedStyles ? "已应用嵌入样式" : "使用默认样式" }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="重定向路径">
                <el-tag
                  :type="redirectInfo.hasRedirectPath ? 'warning' : 'info'"
                >
                  {{
                    redirectInfo.hasRedirectPath
                      ? redirectInfo.redirectPath
                      : "无"
                  }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </el-col>

          <el-col :span="12">
            <h3>测试操作</h3>
            <div class="test-buttons">
              <el-button type="primary" @click="enableEmbedded">
                启用嵌入模式
              </el-button>
              <el-button type="danger" @click="disableEmbedded">
                禁用嵌入模式
              </el-button>
              <el-button type="warning" @click="toggleEmbedded">
                切换模式
              </el-button>
              <el-button @click="refreshDebugInfo"> 刷新状态 </el-button>
            </div>

            <div class="test-links" style="margin-top: 20px">
              <h4>测试链接</h4>
              <div>
                <el-link type="primary" :href="normalUrl" target="_blank">
                  正常模式链接
                </el-link>
              </div>
              <div style="margin-top: 10px">
                <el-link type="success" :href="embeddedUrl" target="_blank">
                  嵌入模式链接 (?hide=true)
                </el-link>
              </div>
            </div>
          </el-col>
        </el-row>

        <el-divider />

        <div class="instructions">
          <h3>测试说明</h3>
          <ol>
            <li>
              点击"嵌入模式链接"或在URL后添加 <code>?hide=true</code> 参数
            </li>
            <li>观察页面头部和侧边栏是否被隐藏</li>
            <li>刷新页面，验证状态是否保持</li>
            <li>导航到其他页面，验证嵌入模式是否持续生效</li>
            <li>使用"禁用嵌入模式"按钮可以退出嵌入模式</li>
          </ol>
        </div>

        <el-divider />

        <div class="debug-info">
          <h3>调试信息</h3>
          <el-input
            v-model="debugInfoJson"
            type="textarea"
            :rows="8"
            readonly
            placeholder="调试信息将显示在这里"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { ElMessage } from "element-plus";
import { useSettingStoreHook } from "@/store/modules/settings";
import {
  isEmbeddedMode,
  enableEmbeddedMode,
  disableEmbeddedMode,
  toggleEmbeddedMode,
  getEmbeddedModeDebugInfo,
  cleanHideParamFromUrl
} from "@/utils/embeddedMode";
import { getRedirectDebugInfo } from "@/utils/loginRedirect";

const settingStore = useSettingStoreHook();
const debugInfo = ref(getEmbeddedModeDebugInfo());
const redirectInfo = ref(getRedirectDebugInfo());

const debugInfoJson = computed(() => {
  return JSON.stringify(debugInfo.value, null, 2);
});

const normalUrl = computed(() => {
  const url = new URL(window.location.href);
  url.searchParams.delete("hide");
  return url.toString();
});

const embeddedUrl = computed(() => {
  const url = new URL(window.location.href);
  url.searchParams.set("hide", "true");
  return url.toString();
});

const hasEmbeddedStyles = computed(() => {
  // 检查是否应用了嵌入模式样式
  return settingStore.embeddedMode && debugInfo.value.isEmbedded;
});

const enableEmbedded = () => {
  enableEmbeddedMode();
  settingStore.setEmbeddedMode(true);
  refreshDebugInfo();
  ElMessage.success("嵌入模式已启用");
};

const disableEmbedded = () => {
  disableEmbeddedMode();
  settingStore.setEmbeddedMode(false);
  refreshDebugInfo();
  ElMessage.success("嵌入模式已禁用");
};

const toggleEmbedded = () => {
  const newState = toggleEmbeddedMode();
  settingStore.setEmbeddedMode(newState);
  refreshDebugInfo();
  ElMessage.success(`嵌入模式已${newState ? "启用" : "禁用"}`);
};

const refreshDebugInfo = () => {
  debugInfo.value = getEmbeddedModeDebugInfo();
  redirectInfo.value = getRedirectDebugInfo();
};

onMounted(() => {
  // 初始化时清理URL参数（如果需要）
  if (new URLSearchParams(window.location.search).has("hide")) {
    setTimeout(() => {
      cleanHideParamFromUrl();
    }, 1000);
  }

  // 定期刷新调试信息
  setInterval(refreshDebugInfo, 2000);
});
</script>

<style scoped>
.embedded-mode-test {
  padding: 20px;
}

.test-card {
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.test-buttons .el-button {
  width: 100%;
}

.test-links div {
  margin-bottom: 5px;
}

.instructions ol {
  padding-left: 20px;
}

.instructions li {
  margin-bottom: 8px;
}

code {
  background-color: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: "Courier New", monospace;
}
</style>
