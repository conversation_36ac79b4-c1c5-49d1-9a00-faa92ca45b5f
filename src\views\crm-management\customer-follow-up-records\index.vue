<template>
  <section>
    <div class="app-search-card">
      <div class="app-form-group">
        <div class="ml-[20px]">
          <span>跟进人：</span>
          <el-select style="width: 140px" v-model="searchInfo.followerId" filterable placeholder="请选择">
            <el-option v-for="item in optionsList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="ml-[20px]">
          <span>跟进开始时间：</span>
          <el-date-picker style="width: 140px" v-model="searchInfo.followTimeStart" type="date" placeholder="请选择"
            format="YYYY-MM-DD" value-format="x" />
        </div>
        <div class="ml-[20px]">
          <span>跟进结束时间：</span>
          <el-date-picker style="width: 140px" v-model="searchInfo.followTimeEnd" type="date" placeholder="请选择"
            format="YYYY-MM-DD" value-format="x" />
        </div>
      </div>
      <div class="app-btn-group">
        <el-button class="filter-item" type="primary" @click="getList">查询</el-button>
        <el-button class="filter-item" @click="handleReset">重置</el-button>
      </div>
    </div>
    <div class="app-card mt-[20px] p-[20px] pt-[10px]">
      <div class="mb-[10px]">
        <el-button type="primary" @click="handleCreate">新增</el-button>
      </div>
      <pure-table :columns="columns" border stripe :loading="loading" :data="tableData" :pagination="pagination"
        @page-size-change="onSizeChange" @page-current-change="onCurrentChange" @sort-change="handlerChange">
        <template #nameHeader>
          <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate" :column="columns[1]" />
        </template>
        <template #titleHeader>
          <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate" :column="columns[2]" />
        </template>
        <template #typeHeader>
          <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate" :filter-options="followTypeOptions"
            :column="columns[3]" />
        </template>
        <template #electricityHeader>
          <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate" :filter-options="followTypeOptions"
            :column="columns[4]" />
        </template>
        <template #customName="{ row }">
          <a @click="handleEdit(row.id)" style="color: #007bf7">{{
            row.customName
          }}</a>
        </template>
        <template #operation="{ row }">
          <el-button link type="primary" size="small" @click="handleEdit(row.id)">编辑</el-button>
          <el-popconfirm width="220" confirm-button-text="确定" cancel-button-text="取消" :icon="InfoFilled"
            icon-color="#626AEF" title="此操作将删除联动的商机记录，是否删除" @confirm="handleDel(row.id)">
            <template #reference>
              <el-button link type="danger" size="small">删除</el-button>
            </template>
          </el-popconfirm>
        </template>
      </pure-table>
    </div>
    <el-dialog v-model="dialogVisible" :title="title" width="60%">
      <el-form ref="ruleFormRef" :rules="dataFormRules" :model="formInline" label-width="120px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="客户：" prop="customName">
              <el-input @click="selectVisible = true" v-model="formInline.customName" placeholder="请选择" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="跟进内容：" prop="title">
              <el-input maxlength="30" show-word-limit v-model="formInline.title" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="跟进形式：" prop="type">
              <DictSelect v-model="formInline.type" :clearable="true" dict-code="followType" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="跟进人：" prop="followerId">
              <el-select style="width: 100%" @change="handleSelect" v-model="formInline.followerId" filterable
                         placeholder="请选择">
                <el-option v-for="item in optionsList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="陪同人员：" prop="entourage">
              <el-input maxlength="8" show-word-limit v-model="formInline.entourage" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="跟进时间：" prop="followTime">
              <el-date-picker style="width: 100%" v-model="formInline.followTime" type="date" placeholder="请选择"
                              format="YYYY/MM/DD" value-format="x" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="绿电需求：" prop="electricityDemand">
              <el-input maxlength="10" show-word-limit v-model="formInline.electricityDemand" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="其它意向需求：" prop="otherDemand">
              <el-input maxlength="20" show-word-limit v-model="formInline.otherDemand" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="创建时间：" prop="createTime">
              <el-date-picker disabled value-format="x" format="YYYY-MM-DD HH:mm:ss" type="date" :model-value="Number(formInline.createTime)"  placeholder="暂无创建时间">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="跟进内容附件：" prop="fileList">
              <el-upload v-model:file-list="fileList" with-credentials :headers="header" :data="uploadData"
                :on-preview="handleDownload" :on-remove="handleRemove" :action="actionUrl"
                :before-upload="beforeUpload">
                <el-button type="primary"> 点击上传</el-button>
                <template #tip>
                  <div class="el-upload__tip">
                    支持所有格式，且大小不超过10M
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submit(ruleFormRef)">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog width="60%" append-to-body v-model="selectVisible" destroy-on-close title="客户选择">
      <customer-select @change="handleClose" />
    </el-dialog>
  </section>
</template>

<script setup lang="ts">
import {ref, reactive, onMounted, getCurrentInstance, nextTick} from "vue";
import CustomerSelect from "@/components/Core/PowerConsumerSelect/powerConsumer.vue";
import { columns } from "./components/data";
import { delay } from "@pureadmin/utils";
import { InfoFilled } from "@element-plus/icons-vue";
import type { PaginationProps } from "@pureadmin/table";
import type { FormInstance } from "element-plus";
import { ElMessage } from "element-plus";
import {
  getFollowRecordListApi,
  delFollowRecordApi,
  getFollowRecordByIdApi,
  saveFollowRecordApi
} from "@/api/customer-management/index";
import type { UploadUserFile } from "element-plus";
import TableHeadPopover from "@/components/Core/TableHeadPopover/index.vue";
import { CustomerFollowListModel } from "@/model/customerModel";
import { getUnqueIdApi, getAllUserListApi } from "@/api/user";
import dayjs from "dayjs";
import { useDictOptions } from "@/hooks/dict/useDictOptions";
import { useFileAction } from "@/hooks/fileAction/useFileAction";
defineOptions({
  name: "CustomerFollowUpRecords"
});
import {
  getAllSalesmanList, //获取营销人员列表
} from '@/api'
type UploadType = {
  type: string;
  id: number;
};
const timeSlot = ref<number>(dayjs().valueOf());
const { followTypeOptions } = useDictOptions();
const {
  getFileList,
  handleDownload,
  header,
  handleRemove,
  beforeUpload,
  actionUrl,
  fileList
} = useFileAction();
const ruleFormRef = ref<FormInstance>();
const uploadData = ref<UploadType>({
  type: "5",
  id: undefined
});

const handlerChange = (item:any) => {
  console.log("aaaaa",item)
  searchInfo.value.sort = []
  let order;
  if(item.order.includes("desc")){
    order = "desc"
  }else{
    order = "asc"
  }
  searchInfo.value.sort.push(item.prop,order)
  getList()
}
const dialogVisible = ref<boolean>(false);
const selectVisible = ref<boolean>(false);
function validateFileList(rule, value, callback) {
  nextTick(() => {
    console.log(value,fileList.value)
    if (fileList.value.length === 0) {
      callback(new Error('请上传至少一个文件'));
    } else {
      callback();
    }
  })
}
const dataFormRules = {
  customName: [
    {
      required: true,
      message: "客户是必填项",
      trigger: "change"
    }
  ],
  title: [
    {
      required: true,
      message: "跟进内容是必填项",
      trigger: "blur"
    }
  ],
  type: [
    {
      required: true,
      message: "跟进形式是必填项",
      trigger: "blur"
    }
  ],
  followerId: [
    {
      required: true,
      message: "跟进人是必填项",
      trigger: "blur"
    }
  ],
  followTime: [
    {
      required: true,
      message: "跟进时间是必填项",
      trigger: "blur"
    }
  ],
  // fileList: [
  //   {
  //     validator: validateFileList,
  //     required: true,
  //     message: "跟进内容附件是必填项",
  //     trigger: "blur"
  //   }
  // ]
};
const title = ref<string>("新增");
const formMap: CustomerFollowListModel = {
  id: undefined,
  title: "",
  customId: undefined,
  customName: "",
  type: undefined,
  electricityDemand: "",
  otherDemand: "",
  followerId: undefined,
  followerName: "",
  followTime: undefined,
  entourage: "",
  status: 8
};
const formInline = ref<CustomerFollowListModel>({
  id: undefined,
  title: "",
  customId: undefined,
  customName: "",
  type: undefined,
  electricityDemand: "",
  otherDemand: "",
  followTime: undefined,
  followerId: undefined,
  followerName: "",
  entourage: "",
  status: null
});
const loading = ref(false);
const tableData = ref([]);
const searchInfo = ref({
  title: undefined,
  customName: undefined,
  followerId: undefined,
  followTimeStart: undefined,
  followTimeEnd: undefined,
  type: undefined,
  pageNo: 1,
  sort: [],
  pageSize: 10
});
/** 分页配置 */
const pagination = reactive<PaginationProps>({
  pageSize: 10,
  currentPage: 1,
  pageSizes: [10, 20, 40, 50],
  total: 0,
  align: "right",
  background: true,
  small: false
});
async function handleCreate() {
  formInline.value.status = 8;
  fileList.value = [];
  title.value = "新增";
  await getUploadFileId();
  Object.assign(formInline.value, formMap);
  formInline.value.id = uploadData.value.id;
  dialogVisible.value = true;
}
async function getUploadFileId() {
  // 生成上传的唯一id
  const res = await getUnqueIdApi();
  uploadData.value.id = res.data;
}
function handleReset() {
  searchInfo.value.title = undefined;
  searchInfo.value.customName = undefined;
  searchInfo.value.followerId = undefined;
  searchInfo.value.type = undefined;
  searchInfo.value.followTimeStart = undefined;
  searchInfo.value.followTimeEnd = undefined;
  timeSlot.value = dayjs().valueOf();
  getList();
}
async function handleEdit(id: number) {
  formInline.value.status = null;
  title.value = "编辑";
  dialogVisible.value = true;
  uploadData.value.id = id;
  const res = await getFollowRecordByIdApi(id);
  getFileList("5", id);
  formInline.value = { ...res.data };
  formInline.value.followTime = Number(formInline.value.followTime);
  formInline.value.createTime = Number(formInline.value.createTime);
}

async function handleDel(id: string) {
  await delFollowRecordApi(id);
  ElMessage({
    message: "操作成功",
    type: "success"
  });
  getList();
}
function onSizeChange(val) {
  searchInfo.value.pageSize = val;
  getList();
}
function onCurrentChange(val) {
  searchInfo.value.pageNo = val;
  getList();
}
// 跟进人下拉
const optionsList = ref([]);
// 获取所有用户
async function getUserList() {
  const res = await getAllSalesmanList()
  if (res && res.length) {
    optionsList.value = res.map((item: any) => {
      return {
        label: item.name,
        value: item.id
      }
    })
  }
}
// async function getUserList() {
//   const res = await getAllUserListApi();
//   if (res.data) {
//     optionsList.value = res.data.map(item => {
//       return {
//         label: item.name,
//         value: String(item.id)
//       };
//     });
//   }
// }
function handleSelect(data) {
  const name = optionsList.value.find(i => i.value == data).label;
  formInline.value.followerName = name;
}
async function getList() {
  loading.value = true;
  const { data } = await getFollowRecordListApi(searchInfo.value);
  pagination.total = data ? Number(data.totalCount) : 0;
  tableData.value = data ? data.data : [];
  delay(300).then(() => {
    loading.value = false;
  });
}
async function submit(formEl: FormInstance | undefined) {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      const res = await saveFollowRecordApi(formInline.value);
      if (res.code === "200") {
        ElMessage({
          message: "操作成功",
          type: "success"
        });
        getList();
        dialogVisible.value = false;
      } else {
        ElMessage({
          message: res.message,
          type: "success"
        });
      }
    } else {
      console.log("error submit!", fields);
    }
  });
}
// 客户确认选择弹框事件
function handleClose(row) {
  selectVisible.value = false;
  formInline.value.customId = row.id;
  formInline.value.customName = row.name;
}
// 表格筛选
function handleTableUpdate(data) {
  searchInfo.value[data.propKey] = data.value;
  getList();
}

onMounted(() => {
  getUserList();
  getList();
});
</script>

<style lang="scss" scoped></style>
