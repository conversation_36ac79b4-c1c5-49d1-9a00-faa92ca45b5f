<template>
    <div ref="chartRef" :style="{ height, width }" />
  </template>
  
  <script setup lang="ts">
  import {
    ref,
    computed,
    onMounted,
    watch,
    type Ref,
    PropType,
    nextTick
  } from "vue";
  import { useAppStoreHook } from "@/store/modules/app";
  import {
    delay,
    useDark,
    useECharts,
    type EchartOptions
  } from "@pureadmin/utils";
  import * as echarts from "echarts/core";
  import type { EChartsOption } from "echarts";
  import { MarkPointComponent, MarkLineComponent } from "echarts/components";
  echarts.use([MarkPointComponent, MarkLineComponent]);
  const emit = defineEmits(["update"]);
  const props = defineProps({
    height: {
      type: String as PropType<string>,
      default: "100%"
    },
    width: {
      type: String as PropType<string>,
      default: "100%"
    },
    xData: {
      type: Array as PropType<string[] | number[]>,
      default: () => []
    },
    yData: {
      type: Array as PropType<string[] | number[]>,
      default: () => []
    },
    yAxisName1: {
      type: String as PropType<string>,
      default: ""
    },
    yAxisName2: {
      type: String as PropType<string>,
      default: ""
    },
    series: {
      type: Array as PropType<Array<object>>,
      default: () => []
    }
  });
  const { isDark } = useDark();
  
  const theme: EchartOptions["theme"] = computed(() => {
    return isDark.value ? "dark" : "light";
  });
  
  const chartRef = ref<HTMLDivElement | null>(null);
  const { setOptions, resize } = useECharts(chartRef as Ref<HTMLDivElement>, {
    theme
  });
  
  const getOption = (): EChartsOption => {
    return {
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "shadow"
        }
      },
      grid: {
        bottom: "12%",
        right: "10%"
      },
      legend: {
        top: "5%"
      },
      xAxis: {
        type: "category",
        data: props.xData
      },
      yAxis: [
        {
          type: "value",
          name: props.yAxisName1,
          boundaryGap: [0, 0.01]
        },
        {
          type: "value",
          name: props.yAxisName2,
          boundaryGap: [0, 0.01]
        }
      ],
      series: props.series,
      addTooltip: true
    };
  };
  
  watch(
    () => useAppStoreHook().getSidebarStatus,
    () => {
      delay(600).then(() => resize());
    }
  );
  
  watch(
    () => props,
    () => setOptions(getOption() as EChartsOption),
    {
      immediate: true,
      deep: true
    }
  );
  onMounted(() => {
    delay(300).then(() => resize());
  });
  </script>
  