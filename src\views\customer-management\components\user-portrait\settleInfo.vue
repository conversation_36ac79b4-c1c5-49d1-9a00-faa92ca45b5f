<template>
  <div class="app-list-wrap mt-[20px]">
    <div class="flex items-center justify-between app-list-title">
      <div>
        <span class="font-bold">结算信息</span>
        <span class="ml-[15px]">年份：</span>
        <el-date-picker
          style="width: 140px"
          v-model="year"
          valueFormat="YYYY"
          type="year"
          @change="getList"
          placeholder="请选择"
        />
      </div>
      <div>单位：元，MWh，元/MWh</div>
    </div>
    <div class="p-[20px] flex">
      <div class="user-portrait-settleInfo-left">
        <div class="box">
          <div class="box-title">电费</div>
          <div class="box-detail">
            <div class="flex ml-[20px]">
              <div class="box-item">
                <div>年汇总</div>
                <div class="value">
                  {{ filterValue(electricityCost.yearSum) }}
                </div>
              </div>
              <div class="box-item ml-[40px]">
                <div>平均值</div>
                <div class="value">{{ filterValue(electricityCost.avg) }}</div>
              </div>
            </div>
            <div class="flex ml-[20px]">
              <div class="box-item">
                <div>最大值</div>
                <div class="value">{{ filterValue(electricityCost.max) }}</div>
              </div>
              <div class="box-item ml-[40px]">
                <div>最小值</div>
                <div class="value">{{ filterValue(electricityCost.min) }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="box mt-[20px]">
          <div class="box-title">电量</div>
          <div class="box-detail">
            <div class="flex ml-[20px]">
              <div class="box-item">
                <div>年汇总</div>
                <div class="value">
                  {{ filterValue(electricitySum.yearSum) }}
                </div>
              </div>
              <div class="box-item ml-[40px]">
                <div>平均值</div>
                <div class="value">{{ filterValue(electricitySum.avg) }}</div>
              </div>
            </div>
            <div class="flex ml-[20px]">
              <div class="box-item">
                <div>最大值</div>
                <div class="value">{{ filterValue(electricitySum.max) }}</div>
              </div>
              <div class="box-item ml-[40px]">
                <div>最小值</div>
                <div class="value">{{ filterValue(electricitySum.min) }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="box mt-[20px]">
          <div class="box-title">电价</div>
          <div class="box-detail">
            <div class="flex ml-[20px]">
              <div class="box-item">
                <div>年汇总</div>
                <div class="value">
                  {{ filterValue(electricityPrice.yearSum) }}
                </div>
              </div>
              <div class="box-item ml-[40px]">
                <div>平均值</div>
                <div class="value">{{ filterValue(electricityPrice.avg) }}</div>
              </div>
            </div>
            <div class="flex ml-[20px]">
              <div class="box-item">
                <div>最大值</div>
                <div class="value">{{ filterValue(electricityPrice.max) }}</div>
              </div>
              <div class="box-item ml-[40px]">
                <div>最小值</div>
                <div class="value">{{ filterValue(electricityPrice.min) }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="user-portrait-settleInfo-right">
        <ChartsMergeLine1
          :settleCostList="lineData.settleCostList"
          :settleElectList="lineData.settleElectList"
          :settlePriceList="lineData.settlePriceList"
          :settleAvgList="lineData.settleAvgList"
          :xData="xData"
          height="500px"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { getPortraitSettlementApi } from "@/api/customer-management/index";
import ChartsMergeLine1 from "./ChartsMergeLine1.vue";
import { useRoute } from "vue-router";
const { query } = useRoute();
const year = ref("2023");
// x轴
const xData = ref<string[]>(
  new Array(12).fill(item => item).map((_, index) => `${index + 1}月`)
);
const electricitySum = ref({
  avg: "2.3190362903225807",
  max: "4.735",
  min: "0.016",
  yearSum: "575.121"
});
///结算电费
const electricityCost = ref({
  avg: "2.3190362903225807",
  max: "4.735",
  min: "0.016",
  yearSum: "575.121"
});
///结算电价
const electricityPrice = ref({
  avg: "2.3190362903225807",
  max: "4.735",
  min: "0.016",
  yearSum: "575.121"
});
// 曲线数据
const lineData = ref({
  settleCostList: [],
  settleElectList: [],
  settlePriceList: [],
  settleAvgList: []
});
async function getList() {
  const res = await getPortraitSettlementApi({
    parameter: {
      customId: query.id as string,
      year: year.value
    }
  });
  if (res.data) {
    xData.value = res.data.curveList.map(i => i.time);
    lineData.value.settleElectList = res.data.curveList.map(i => i.electricity);
    lineData.value.settleCostList = res.data.curveList.map(i => i.fee);
    lineData.value.settlePriceList = res.data.curveList.map(i => i.price);
    electricitySum.value = { ...res.data.electricity };
    electricityCost.value = { ...res.data.fee };
    electricityPrice.value = { ...res.data.price };
  } else {
    lineData.value.settleElectList = [];
    lineData.value.settleCostList = [];
    lineData.value.settlePriceList = [];
    electricitySum.value = {
      avg: "-",
      max: "-",
      min: "-",
      yearSum: "-"
    };
    electricityCost.value = {
      avg: "-",
      max: "-",
      min: "-",
      yearSum: "-"
    };
    electricityPrice.value = {
      avg: "-",
      max: "-",
      min: "-",
      yearSum: "-"
    };
  }
}
function filterValue(value) {
  if (![null, undefined, "-"].includes(value)) {
    return Number(value).toFixed(3);
  } else {
    return "-";
  }
}
onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped>
.user-portrait-settleInfo-left {
  border-right: 1px solid #e4e7ed;
  padding-right: 20px;

  .box {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #e4e7ed;
    padding-bottom: 20px;

    .box-title {
      background: #f7f7fc;
      writing-mode: vertical-lr;
      border-radius: 2px;
      text-align: center;
      padding: 25px 5px;
      letter-spacing: 1em;
      color: var(--el-color-primary);
    }

    .box-detail {
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .box-item {
        width: 140px;
        .value {
          color: var(--el-color-primary);
          font-weight: 800;
        }
      }
    }
  }
}

.user-portrait-settleInfo-right {
  width: 100%;
}
</style>
