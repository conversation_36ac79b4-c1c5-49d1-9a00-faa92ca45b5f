import { defineStore } from "pinia";
import { store } from "@/store";
import { routerArrays } from "@/layout/types";
import { resetRouter } from "@/router";
import { storageSession } from "@pureadmin/utils";
import { refreshTokenApi } from "@/api/user";
import { RefreshTokenResult } from "@/api/user";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";
import { getAllDictListApi } from "@/api/sys/dict";
import { GetDictListModel } from "@/model/dictModel";
import { reqLogin, reqUserInfo, reqDictionaryPa, reqLogout, checkApplicationLock } from '@/api'
import {
  type DataInfo,
  setToken,
  removeToken,
  sessionKey,
  getToken
} from "@/utils/auth";
import { loginOutApi } from "@/api/user";
import { usePermissionStoreHook } from "@/store/modules/permission";

import router from '@/router'
import { get } from "sortablejs";

let env = import.meta.env.VITE_APP_ENV || 'development'
console.log('env', env)
// TODO 部署生产环境时注释下面这行代码
env = 'development'

// console.log(router)

export const useUserStore = defineStore({
  id: "pure-user",
  state: () => ({
    // 用户名
    username:
      storageSession().getItem<DataInfo<number>>(sessionKey)?.username ?? "",
    // 页面级别权限
    roles: storageSession().getItem<DataInfo<number>>(sessionKey)?.roles ?? [],
    // 字典信息
    dictList: [],
    loginUserTenantList: [{ tenantId: '', tenantName: '' }], //用户登录的租户列表
    token: '',
    systemName: '',
    dictionaryPa: [],
    hasChange: false,
    // Last fetch time
    lastUpdateTime: 0,
    tenantId: '',
    userId: '',
    longToken: '',
    isLock: false,
    contractInfor:{}
  }),
  getters: {
    getDictList(): GetDictListModel[] {
      return this.dictList;
    },
    getLastUpdateTime(): number {
      return this.lastUpdateTime;
    },
    getContractInfor(){
      return this.contractInfor
    }
  },
  actions: {
    setUserId(userId: any) {
      this.userId = userId
    },
    setContractInfor(item:any){
        this.contractInfor = item
    },
    setLoginUserTenantList(loginUserTenantList: any) {
      this.loginUserTenantList = loginUserTenantList
    },
    async setDictionaryPa() {
      this.dictionaryPa = await reqDictionaryPa()
    },
    async userLogoutTimeout(callback: () => void) {
      removeToken()
      usePermissionStoreHook().clearAllCachePage()
      await this.$reset()
      callback()
    },
    async userLogin(data: any, callback: (resList: any[]) => void) {
      const res = await reqLogin(data)
      this.loginUserTenantList = res.loginUserTenantList
      callback(res.loginUserTenantList)
    },
    userSetToken(token: string) {
      this.token = token
    },
    userSetLongToken(longToken: string) {
      this.longToken = longToken
    },
    async setUserInfoAndToken(data: any, callback: () => any) {
      const { systemName, token, username, longToken, systemCode } = data
      this.userSetToken(token)
      this.userSetLongToken(longToken)
      this.token = token
      this.systemName = systemName | callback()
      this.systemCode = systemCode
      if (env == 'production') {
        const res = await checkApplicationLock({
          systemCode: systemCode
        })
        this.isLock = res
      }
      setToken({
        username: username,
        roles: ["admin"],
        accessToken: token
      } as any);
    },
    async stateReqUserInfo() {
      try {
        const result = await reqUserInfo()
        this.username = result.name
        this.systemName = result.tenantName
        if (env == 'production') {
          const res = await checkApplicationLock({
            systemCode: 'shengchandaping'
          })
          this.isLock = res
        }
        setToken({
          username: this.username,
          roles: ["admin"],
          accessToken: this.token
        } as any);
        return this.username
      } catch (error) {
        this.$reset()
        // 使用工具函数处理登录重定向
        const { handleLoginRequired } = await import("@/utils/loginRedirect");
        handleLoginRequired(router);
      }
    },
    /** 存储用户名 */
    SET_USERNAME(username: string) {
      this.username = username;
    },
    /** 存储角色 */
    SET_ROLES(roles: Array<string>) {
      this.roles = roles;
    },
    // 字典
    async setDictList() {
      const list = await getAllDictListApi();
      this.dictList = list.data ? list.data : [];
    },
    setLastUpdateTime() {
      this.lastUpdateTime = new Date().getTime();
    },
    /** 前端登出（不调用接口） */
    async logOut() {
      await router.push("login");
      // console.log('logOut', router.push)
      this.username = "";
      this.roles = [];
      removeToken();
      if (localStorage.getItem("dictList")) {
        localStorage.removeItem("dictList");
      }
      useMultiTagsStoreHook().handleTags("equal", [...routerArrays]);
      resetRouter();

      localStorage.removeItem("user-info");

      await reqLogout()

      if (navigator.userAgent.indexOf("Firefox") != -1 || navigator.userAgent.indexOf("Chrome") != -1) {
        window.location.href = "";
        window.close();
      } else {
        window.opener = null;
        window.open("", "_self");
        window.close();
      }

      // return new Promise((resolve, reject) => {
      //   loginOutApi(getToken().accessToken)
      //     .then(data => {
      //       console.log(`output->data`, data)
      //       if (data) resolve(data);
      //       this.username = "";
      //       this.roles = [];
      //       removeToken();
      //       if (localStorage.getItem("dictList")) {
      //         localStorage.removeItem("dictList");
      //       }
      //       useMultiTagsStoreHook().handleTags("equal", [...routerArrays]);
      //       resetRouter();
      //       router.push("/login");
      //     })
      //     .catch(error => {
      //       reject(error);
      //     });
      // });
    },
    /** 刷新`token` */
    async handRefreshToken(data) {
      return new Promise<RefreshTokenResult>((resolve, reject) => {
        refreshTokenApi(data)
          .then(data => {
            if (data) {
              setToken(data.data);
              resolve(data);
            }
          })
          .catch(error => {
            reject(error);
          });
      });
    }
  },
  persist: [
    {
      paths: ['token'],
      storage: localStorage,
      key: 'TOKEN',
    },
    {
      paths: ['longToken'],
      storage: localStorage,
      key: 'LONGTOKEN',
    },
  ],
});

export function useUserStoreHook() {
  return useUserStore(store);
}
