# 简单的nginx反向代理配置
# 解决iframe嵌入和跨域问题

server {
    listen 80;
    server_name localhost;  # 或者您的域名/IP
    
    # 反向代理到目标系统
    location / {
        # 代理到目标服务器
        proxy_pass http://***********:8187;
        
        # 基本代理头部设置
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 关键：移除目标服务器的限制性头部
        proxy_hide_header X-Frame-Options;
        proxy_hide_header Content-Security-Policy;
        
        # 重新设置允许iframe嵌入的头部
        add_header X-Frame-Options "ALLOWALL" always;
        
        # 解决跨域问题
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control" always;
        add_header Access-Control-Allow-Credentials "true" always;
        
        # 处理OPTIONS预检请求
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
            add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control";
            add_header Access-Control-Max-Age 3600;
            add_header Content-Type "text/plain; charset=utf-8";
            add_header Content-Length 0;
            return 204;
        }
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 缓冲设置
        proxy_buffering on;
        proxy_buffer_size 8k;
        proxy_buffers 8 8k;
    }
}

# 如果需要HTTPS支持，添加这个server块
server {
    listen 443 ssl;
    server_name localhost;
    
    # SSL证书配置（请替换为您的证书路径）
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    location / {
        proxy_pass http://***********:8187;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https;
        
        # 移除限制性头部
        proxy_hide_header X-Frame-Options;
        proxy_hide_header Content-Security-Policy;
        
        # 允许iframe嵌入
        add_header X-Frame-Options "ALLOWALL" always;
        
        # CORS配置
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control" always;
        
        # HTTPS安全头部
        add_header Strict-Transport-Security "max-age=31536000" always;
    }
}
