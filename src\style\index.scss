@import "./transition";
@import "./element-plus";
@import "./sidebar";
@import "./dark";

/* 自定义全局 CssVar */
:root {
  /* 左侧菜单展开、收起动画时长 */
  --pure-transition-duration: 0.3s;
}

/* 灰色模式 */
.html-grey {
  filter: grayscale(100%);
}

/* 色弱模式 */
.html-weakness {
  filter: invert(80%);
}

// 页面表格主体
.app-content-container {
  padding: 20px;
  opacity: 1;
  border-radius: 6px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 2px 22px rgba(0, 0, 0, 0.06);
  margin-top: 20px;
  overflow: auto;
}

.container-h {
  height: calc(100vh - 179px);
}

// 查询条件
.app-func-bar {
  min-height: 70px;
  padding: 0 20px;
  padding-bottom: 0;
  border-radius: 6px;
  box-shadow: 0px 2px 22px rgba(0, 0, 0, 0.06);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.app-list-wrap {
  // padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;

  .app-list-title {
    padding: 10px;
    background: #f7f7f7;
    border-bottom: 1px solid #e4e7ed;
  }
}

// 顶部搜索
.app-search-card {
  display: flex;
  border-radius: 6px;
  background: #fff;
  box-shadow: 0px 2px 22px rgba(0, 0, 0, 0.06);
  padding: 15px 5px;
}

.app-card {
  border-radius: 6px;
  background: #fff;
  box-shadow: 0px 2px 22px rgba(0, 0, 0, 0.06);

  .card-header {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #e4e7ed;
    padding: 20px;
    padding-bottom: 10px;
  }
}

.app-form-group {
  display: flex;
  align-items: center;
  margin-right: 15px;

  &>div {
    display: flex;
    align-items: center;

    span {
      color: rgba(96, 98, 102, 1);
    }

    .filter-item {
      width: 220px;
    }

    &.el-input {
      margin-right: 15px;
    }

    &.el-input:last-child {
      margin-right: 0;
    }
  }
}

.no-border {
  border: none !important;
}

.el-input-group__append {
  background-color: #254f7a !important;
}

//el-card头部样式
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .header-title {
    display: flex;
    align-items: center;

    img {
      margin-right: 4px;
    }

    color: #303133;
    /* 文字/18加粗 */
    font-size: 16px;
    font-weight: 700;
    line-height: 22px;
  }
}

.dialog {
  .el-dialog__header {
    margin-right: 0;
    background-color: #254F7A !important;

    .el-dialog__title {
      color: #fff !important;
    }

    .el-dialog__headerbtn {
      .el-dialog__close {
        color: #fff !important;
      }
    }
  }
}