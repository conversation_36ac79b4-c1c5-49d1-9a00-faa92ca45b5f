<!-- 用户画像 -->
<template>
  <div>
    <BasicInfo :formInline="formInline" />
    <SettleInfo />
    <LoadSituation />
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  formInline: {
    type: Object,
    default: () => {}
  }
});
import BasicInfo from "./basicInfo.vue";
import SettleInfo from "./settleInfo.vue";
import LoadSituation from "./loadSituation.vue";
</script>

<style scoped></style>
