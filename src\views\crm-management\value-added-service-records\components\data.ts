import dayjs from "dayjs";

export const columns: TableColumnList = [
  {
    label: "序号",
    width: 80,
    align: "center",
    type: "index"
  },
  {
    label: "客户名称",
    slot: "customName",
    prop: "customName",
    headerSlot: "nameHeader",
    searchType: "text"
  },
  {
    label: "增值服务项目",
    prop: "title",
    headerSlot: "titleHeader",
    searchType: "text",
    width: 180
  },
  {
    label: "实施日期",
    sortable: true,
    prop: "implementationDate",
    width: 160,
    formatter: ({ implementationDate }) =>
      !["0", 0, undefined, null].includes(implementationDate)
        ? dayjs(Number(implementationDate)).format("YYYY-MM-DD")
        : ""
  },
  {
    label: "跟进人",
    prop: "responsibleName"
  },
  {
    label: "预计金额",
    sortable: true,
    prop: "estimatedAmount"
  },
  {
    label: "实际金额",
    sortable: true,
    prop: "actualAmount"
  },
  // {
  //   label: "客户经理",
  //   prop: "managerName"
  // },
  {
    label: "操作",
    width: "120",
    fixed: "right",
    slot: "operation"
  }
];
