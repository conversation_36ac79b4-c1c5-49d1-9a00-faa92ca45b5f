<template>
    <div class="app-card">
        <div class="card-header">
            <div>新增</div>
            <div>
                <el-button @click="goBackAndClearStack">返回</el-button>
                <el-button type="primary" @click="submit(ruleFormRef)">保存</el-button>
            </div>
        </div>
        <div class="m-[20px]">
            <div class="font-bold mb-[10px]">基本信息</div>
            <el-form ref="ruleFormRef" :rules="dataFormRules" :model="condition" label-width="120px">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="套餐名称:" prop="packageName">
                            <el-input maxlength="50" show-word-limit v-model="condition.packageName"
                                placeholder="请输入" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="套餐类型:" prop="packageType">
                            <el-input maxlength="50" show-word-limit v-model="condition.packageType"
                                placeholder="请输入" />
                        </el-form-item>
                    </el-col>

                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="可选购买时间:">
                            <el-date-picker v-model="condition.purchaseTimeList" type="monthrange" range-separator="至"
                                start-placeholder="开始时间" end-placeholder="结束时间" style="width: 100%" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="最短购买时间:">
                            <el-select v-model="condition.minPurchaseTime" placeholder="请选择" style="width: 100%">
                                <el-option v-for="item in options" :key="item.value" :label="item.label"
                                    :value="item.value"  />
                            </el-select>
                        </el-form-item>
                    </el-col>

                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="偏差考核方式:">
                            <el-input maxlength="50" show-word-limit v-model="condition.deviationAssessmentMethod"
                                placeholder="请输入" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="是否有绿电:">
                            <el-select v-model="condition.greenElectricity" placeholder="请选择"  style="width: 100%"  @change="changeElectricity">
                                <el-option v-for="item in options2" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>

                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="套餐描述:" prop="packageDescription">
                            <el-input maxlength="100" show-word-limit v-model="condition.packageDescription"
                                placeholder="请输入" />
                        </el-form-item>
                    </el-col>


                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="参数设置:" prop="argumentSet">
                            <el-input maxlength="100" show-word-limit v-model="condition.argumentSet"
                                placeholder="请输入" />
                        </el-form-item>
                    </el-col>


                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item prop="icon" label="上传图片">
                            <el-upload v-model:file-list="fileList"  :limit="1" with-credentials :headers="header"   :before-upload="beforeUpload"
                               :on-preview="handleDownload" :on-remove="handleRemove2" :data="uploadData"
                                :action="actionUr2" :on-success="handlerSuccess"  >
                                <el-button  @click="handlerUpload"  type="primary"> 点击上传</el-button>
                                <template #tip>
                                    <div class="el-upload__tip">
                                        支持所有格式，且大小不超过10M
                                    </div>
                                </template>
                            </el-upload>
                        </el-form-item>
                    </el-col>


                </el-row>





            </el-form>



            <div>
                <div class="font-bold mt-[20px] mb-[10px]">套餐内容</div>

                <p style="margin: 10px 0;">售电公司收益模式计算公式</p>
                <div style="border: 1px solid #ccc">
                    <Toolbar style="border-bottom: 1px solid black" :editor="editorRef" :defaultConfig="toolbarConfig"
                        :mode="mode" />
                    <Editor style="height: 200px;" v-model="condition.computeFormula" :defaultConfig="editorConfig"
                        :mode="mode" @onCreated="handleCreated" @onChange="handleChange" />
                </div>

            </div>

            <div>


                <div style="margin: 10px 0; display: flex;justify-content: space-between;">
                    <div>售电公司收益模式计算公式说明</div>
                    <div @click="handlerAdd(1, 2)"><img :src="getAssetURL('u138')" alt="" width="30px" height="30px"
                            style="margin-right: 10px ;cursor: pointer;" /></div>
                </div>
                <div style="border: 1px solid #ccc">
                    <Toolbar style="border-bottom: 1px solid black" :editor="editorRef2" :defaultConfig="toolbarConfig2"
                        :mode="mode2" />
                    <Editor style="height: 200px;" v-model="condition.computeFormulaExplain"
                        :defaultConfig="editorConfig" :mode="mode" @onCreated="(editor) => editorRef2 = editor"
                        @onChange="handleChange" />

                </div>

            </div>
            <div v-for=" (item, index) in arrList" :key="index">

                <!-- <el-input v-model="item.title"  type="text" maxlength="20" style="width: 240px" placeholder="请输入标题" /> -->
                <div style="margin: 20px 0; display: flex;justify-content: space-between;">
                    <div>
                        <el-input v-model="item.title" style="width: 240px" maxlength="30" placeholder="请输入标题"
                            show-word-limit type="text" />
                    </div>
                    <div style="display: flex;">
                        <img @click="handlerUp(item, index)" :src="getAssetURL('u152')" alt="" width="25px"
                            height="25px" style="margin-right: 15px ;cursor: pointer;" />
                        <img @click="handlerDw(item, index)" :src="getAssetURL('u149')" alt="" width="25px"
                            height="25px" style="margin-right: 15px ;cursor: pointer;" />
                        <img @click="handlerAdd(item, index)" :src="getAssetURL('u138')" alt="" width="25px"
                            height="25px" style="margin-right: 15px ;cursor: pointer;" />
                        <img @click="handleRm(item, index)" :src="getAssetURL('u158')" alt="" width="20px" height="20px"
                            style="margin-right: 10px ;cursor: pointer;" />
                    </div>

                </div>
                <div style="border: 1px solid #ccc">
                    <Toolbar style="border-bottom: 1px solid black" :editor="item.editor"
                        :defaultConfig="toolbarConfig2" :mode="mode2" />
                    <Editor style="height: 200px;" v-model="item.content" :defaultConfig="editorConfig" :mode="mode"
                        @onCreated="(editor) => handleCreated2(item, editor)" @onChange="handleChange" />

                </div>

            </div>

        </div>


    </div>
</template>
<script setup lang="ts">
import { ref, onMounted, shallowRef, onBeforeUnmount, onActivated } from "vue";
import TagSelect from "@/components/Core/TagSelect/index.vue";
import type { FormInstance } from "element-plus";
import { CustomerResultModel } from "@/model/customerModel";
import { useRouter, useRoute } from "vue-router";
import { ElMessage, dayjs } from "element-plus";
import { Plus } from '@element-plus/icons-vue'
import { useFileAction } from "@/hooks/fileAction/useFileAction";
import { getUnqueIdApi, getAllUserListApi } from "@/api/user";
const {
    getFileList,
    handleDownload,
    header,
    handleRemove,
    handleRemove2,
    actionUrl,
    actionUr2,
    fileList
} = useFileAction();
import { usePowerCustomer } from "@/hooks/customer/usePowerCustomer";
import '@wangeditor/editor/dist/css/style.css' // 引入 css

import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import { createEditor, IEditorConfig, IToolbarConfig, DomEditor } from "@wangeditor/editor";
defineOptions({
    name: "CustomerCreate"
});
import {
    getAllSalesmanList, //获取营销人员列表
    addSalePackage
} from '@/api'
const imageUrl = ref('')
const route = useRoute();
const optionsList = ref<any>([])
async function getAllSalesmanListInfo() {
    const res = await getAllSalesmanList()
    if (res && res.length) {
        optionsList.value = res.map((item: any) => {
            return {
                label: item.name,
                value: item.id
            }
        })
    }

}
const maxLength = 400;

const mode = ref('edit')
const mode2 = ref('edit')
const ruleFormRef = ref<FormInstance>();
const { push, go } = useRouter();
const { query } = useRoute();

const maxSize = 10 * 1024 * 1024;
// 定义允许上传的图片文件扩展名
const allowedImageExtensions = ['jpg', 'jpeg', 'png', 'gif'];
const handlerUpload = ()=>{
    getUploadFileId()
}
// 上传文件大小限制和类型检查
function beforeUpload(file) {
  return new Promise((resolve, reject) => {
    // 检查文件大小
    if (file.size <= maxSize) {
      // 检查文件类型
      const fileExtension = file.name.split('.').pop().toLowerCase();
      console.log("dsadasdsa", file.name.split('.').pop().toLowerCase(),fileExtension);
      if (allowedImageExtensions.includes(fileExtension)) {
        
        resolve(file);
      } else {
        ElMessage({
          message: "只能上传图片文件（如jpg, jpeg, png, gif）",
          type: "error"
        });
        reject();
      }
    } else {
      ElMessage({
        message: "文件大小超过限制",
        type: "error"
      });
      reject();
    }
  });
}
const dataFormRules = {
    packageName: [
        {
            required: true,
            message: "套餐名称不能为空",
            trigger: "blur"
        }
    ],
    packageType: [
        {
            required: true,
            message: "套餐类型不能为空",
            trigger: "blur"
        }
    ]


};
type UploadType = {
  type: string;
  id: number;
};
const {
    getCityTreeData,

    getUserList
} = usePowerCustomer(false);

const  handlerSuccess = (res: any) => {
   
    uploadData.value.id = res.data.businessId;
   
}
function getUploadFileId() {
  // 生成上传的唯一id
//   getUnqueIdApi().then(res => {
//     uploadData.value.id = res.data;
// });


}
const uploadData = ref<any>({
  type: "9",
  id: ""
});


const condition = ref<any>({
    packageName: "",
    packageType: "",
    packageDescription: "",
    argumentSet: "",
    computeFormulaExplain: "",
    computeFormula: "",
    purchaseTimeList: "",
    minPurchaseTime: "",
    deviationAssessmentMethod: "",
    greenElectricity: "",
    isGreenElectricity: "",
    photoId: "",
    
    modeExplainList: [],
});
const changeElectricity = (item:any)=>{
    if(item == "1"){
        condition.value.isGreenElectricity = 1
    }else{
        condition.value.isGreenElectricity = 0
 }
 console.log(item,"22",condition.value.isGreenElectricity)
}
onActivated(() => {
    condition.value.packageName = ""
    condition.value.packageType = ""
    condition.value.packageDescription = ""
    condition.value.argumentSet = ""
    condition.value.computeFormulaExplain = ""
    condition.value.computeFormula = ""
    condition.value.minPurchaseTime = ""
    condition.value.purchaseTimeList = ""
    condition.value.deviationAssessmentMethod = ""
    condition.value.greenElectricity = ""
    condition.value.isGreenElectricity = ""
    condition.value.photoId = ""
    fileList.value = []
    condition.value.modeExplainList = []
})

const options = [
    {
        value: '1月',
        label: '1月',
    },
    {
        value: '2月',
        label: '2月',
    },
    {
        value: '3月',
        label: '3月',
    },
    {
        value: '4月',
        label: '4月',
    },
    {
        value: '5月',
        label: '5月',
    },
    {
        value: '6月',
        label: '6月',
    },
    {
        value: '7月',
        label: '7月',
    },
    {
        value: '8月',
        label: '8月',
    },
    {
        value: '9月',
        label: '9月',
    },
    {
        value: '10月',
        label: '10月',
    },
    {
        value: '11月',
        label: '11月',
    },
    {
        value: '一年',
        label: '一年',
    },
    {
        value: '两年',
        label: '两年',
    }, {
        value: '三年',
        label: '三年',
    }
]

const options2 = [
    {
        value: '1',
        label: '是',
    },
    {
        value: '0',
        label: '否',
    }

]
const formInline = ref<CustomerResultModel>({

    basic: {
        name: "",
        isSign: false,
        ownership: undefined,
        areaId: "",
        followerId: "",
        formerName: "",
        annualElectricity: undefined,
        customGrade: undefined,
        industryId: undefined,
        terminationDate: undefined,
        effectiveDate: undefined,
        status: 0,
        socialCreditCode: "",
        bankName: "",
        bankAccount: "",
        registeredCapital: "",
        membershipGroup: "",
        greenDemand: "",
        followerName: "",
        description: "",
        isOpenSea: undefined,
        customIdentity: 1,
        registrationNo: "",
        legalRepresentative: "",
        businessTerm: "",
        businessRegistrationDate: undefined,
        issueDate: undefined,
        registrationAuthority: "",
        registrationStatus: undefined,
        registeredAddress: "",
        businessScope: "",
        electricalNature: undefined,
        mainBusiness: "",
        monthlyAverageElectricity: undefined
    },
    electricity: {
        agentType: undefined,
        annualElectricity: undefined,
        customerSource: undefined,
        greenDemand: ""
    },
    middleman: {
        annualProxyElectricity: undefined
    },
    tagNames: "",
    tagList: [],
    contactList: [
        {
            id: undefined,
            sex: undefined,
            name: "",
            role: "",
            post: "",
            birthday: undefined,
            mobile: "",
            fixedTelephone: "",
            fax: "",
            wechat: "",
            qq: "",
            email: "",
            customName: "",
            customId: undefined
        }
    ]
});


const getAssetURL = (image: any) => {
    return new URL(`../../../assets/svg/${image}.png`, import.meta.url).href
}


const isSubmit = ref(true)
const handleChange = (newHtml) => {
    // console.log(newHtml.getText(), 'newHtml');
    if (newHtml.getText().length > maxLength) {
        ElMessage({
            message: "文本框最大长度不能超过200字符",
            type: "error"
        });
        isSubmit.value = false;
    } else {
        isSubmit.value = true;
    }
};
async function submit(formEl: FormInstance | undefined) {
    const newArr = []
    // console.log("Aaaaaa", arrList)
    if (!formEl) return;
    await formEl.validate(async (valid, fields) => {

        if (valid) {
      

            arrList.value.forEach(item => {
             
                newArr.push({ title: item.title, content: item.content })
            })
            let time = []
            condition.value.purchaseTimeList?.forEach(item => {
                console.log("aaa", item,dayjs(item).format("YYYY-MM"))
             
                time.push(dayjs(item).format("YYYY-MM"))
              

             })
           
            condition.value.modeExplainList = newArr
            console.log("bbb", condition.value.isGreenElectricity)
            addSalePackage({
                packageName: condition.value.packageName,
                packageType: condition.value.packageType,
                packageDescription: condition.value.packageDescription,
                argumentSet: condition.value.argumentSet,
                computeFormulaExplain: condition.value.computeFormulaExplain,
                computeFormula: condition.value.computeFormula,
                modeExplainList: condition.value.modeExplainList,
                purchaseTimeList:time || [],
                minPurchaseTime: condition.value.minPurchaseTime,
                greenElectricity: condition.value.isGreenElectricity ,
                deviationAssessmentMethod: condition.value.deviationAssessmentMethod,
                photoId: uploadData.value.id ||"",
            }).then(res => {

                goBackAndClearStack()
                ElMessage({
                    message: "新增套餐成功",
                    type: "success"
                });
            }).catch(err => {
                ElMessage({
                    message: "新增套餐失败",
                    type: "error"
                });
            });


        } else {
            ElMessage({
                message: "请检查表单是否有必填项",
                type: "error"
            });
        }
    });
}

// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef()
const editorRef2 = shallowRef()

const imageExtensionList = ['png', 'jpg', 'jpeg', 'svg']
// 上传图片
const uploadFile = (file: any) => {
    const fileExtension = file.name.split('.').pop()
    const size = file.size / 1024 / 1024
    if (!imageExtensionList.includes(fileExtension)) {
        ElMessage.warning('只能上传图片文件')
        return
    }
    if (size > 1) {
        ElMessage.warning('文件大小不得超过1M')
        return
    }
    const formData = new FormData()
    formData.append('file', file.raw)
    //   uploadIcon(formData)
    //     .then((res) => {
    //       classifyForm.attachmentId = res.busId
    //       classifyForm.icon = res.url
    //     })
    //     .catch((e) => {
    //       console.log(e)
    //     })
}

const toolbarConfig = {
    excludeKeys: ['group-image', 'group-video', "headerSelect", "group-more-style", "emotion", "insertTable", "codeBlock", "divider", "undo", "redo", "fullScreen", "blockquote", "color", "bgColor", "fontSize", "fontFamily", "lineHeight", "todo", "group-justify", "group-indent", "insertLink"]
}
const toolbarConfig2 = {
    excludeKeys: ['group-image', 'group-video', "headerSelect", "group-more-style", "emotion", "insertTable", "codeBlock", "divider", "undo", "redo", "fullScreen", "blockquote", "color", "bgColor", "fontSize", "fontFamily", "lineHeight", "todo", "group-justify", "group-indent", "insertLink"]
}

const editorConfig = {
    placeholder: '请输入内容...',
    hoverbarKeys: {
        text: {
            menuKeys: [

            ]
        },


    }
}


// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
    const editor = editorRef.value
    const editor2 = editorRef2.value
    if (editor == null || editor2) return
    editor.destroy()
})

const handleCreated = (editor) => {
    editorRef.value = editor

}
const handleCreated2 = (item, editor) => {
    item.editor = editor
}


const handlerAdd = (item, index) => {
    arrList.value.splice(index + 1, 0, {
        content: "",
        title: ""
    })
}
const handleRm = (item, index) => {
    arrList.value.splice(index, 1)
}
const handlerUp = (item, index) => {
    arrList.value.splice(index - 1, 0, item)
    arrList.value.splice(index + 1, 1)
    console.log(arrList.value, item)
}
const handlerDw = (item, index) => {
    arrList.value.splice(index + 2, 0, item)
    arrList.value.splice(index, 1)
}
const arrList = ref<any>([])

function handleSelect(data) {
    const name = optionsList.value.find(i => i.value == data).label;
    formInline.value.basic.followerName = name;
}

function goBackAndClearStack() {
    if (window.history.length <= 1) {
        push({ path: "/setMeal" });
        return false;
    } else {
        go(-1);
    }
}
onActivated(() => {
    condition.value.packageName = ""
    condition.value.packageType = ""
    condition.value.packageDescription = ""
    condition.value.argumentSet = ""
    condition.value.computeFormulaExplain = ""
    condition.value.computeFormula = ""
    condition.value.modeExplainList = []
    arrList.value = []


})
onMounted(() => {
    formInline.value.basic.isOpenSea = query.isOpenSea === "true" ? true : false;
    //    const aa = JSON.parse(localStorage.getItem("obj")) 
    //    console.log(aa.aa)
    getCityTreeData();
    getUserList();
    getAllSalesmanListInfo()

    
});
</script>
<style lang="scss" scoped>
.contacts-form {
    margin-top: 20px;
    border-bottom: 1px solid #e4e7ed;
    padding-bottom: 20px;

    &:last-of-type {
        border: none;
    }
}

.wangeditor-container .w-e-toolbar,
.wangeditor-container .w-e-text-container {
    all: unset;
    /* 取消任何继承的样式 */
}

.main-content {
    height: 100%;
    padding-bottom: 24px;
}

.compiler-container {
    margin-bottom: 20px;
    /* 在容器之间添加一些间距 */
}

::v-deep .el-form-item_label {
    text-align: righty !important;
}

.avatar-uploader .avatar {
    width: 178px;
    height: 178px;
    display: block;
}

::v-deep .avatar-uploader .el-upload {
    border: 1px dashed black;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
    border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    text-align: center;
}

::v-deep .el-form-item__label{
    font-weight: 700
}
</style>