<template>
    <div>
        <el-dialog width="45%" v-model="dialogVisible" title="通过哪种方式进行标签的创建？" center>
            <el-row>
                <el-col :span="12">
                    <div class="box" @click="clickLabel('规则匹配类')">
                        <img src="/src/assets/svg/u7.png" alt="">
                        <div class="contant">
                            <p style="font-weight: bold; margin-bottom: 8px;">规则匹配类</p>
                            <p>自定义每个标签值的名称以及计算规则，将人群划分为多个分层</p>
                        </div>
                    </div>

                </el-col>
                <el-col :span="12">
                    <div class="box" @click="handleStatistics('统计类')">
                        <img src="/src/assets/svg/u14.png" alt="">
                        <div class="contant">
                            <p style="font-weight: bold; margin-bottom: 8px;">统计类</p>   
                            <p>将用户数据统计的计算结果作为标签值</p>

                                
                        </div>
                    </div>

                </el-col>
            </el-row>
            <el-row >
                <el-col :span="12">
                    <div class="box" @click="handleExcavate('挖掘类')">
                        <img src="/src/assets/svg/u19.png" alt="">
                        <div class="contant">
                            <p style="font-weight: bold; margin-bottom: 8px;">挖掘类</p>
                            <p>根据数据挖掘模型指定标签值</p>


                        </div>
                    </div>

                </el-col>

            </el-row>
        </el-dialog>

    </div>
</template>

<script setup lang="ts">
import {  ref, watch } from "vue";
import { useRouter } from "vue-router";
const dialogVisible = ref<boolean>(false);
const { push } = useRouter();
const createtype = ref<string>("");
    const emit = defineEmits(["changeType"]);
    const props = defineProps({
    isShow: {
        type: Boolean,
        default: true
    },type: {
        type: String,
        default: ""
    }
});

// watch(() => props.isShow, (newValue, oldValue) => {
//     dialogVisible.value = true
    
//     // 在这里执行你想要的操作
// });
    // console.log(props.isShow);
// const handleCreateLabel = (type:any) => { 
//     createtype.value = type;
//     dialogVisible.value = true;
//     emit("changeType", "规则匹配类");
// }

// 点击统计
const  handleStatistics = (types:any) => {
    
   if(props.type == "创建"){
    push(`/userPortrait/labelManagement/createUserTags?type=${types}`);
    emit("changeType", "统计类");
    dialogVisible.value = false;
   }

   if(props.type == "编辑"){
    push(`/userPortrait/labelManagement/editLabelRules?type=${types}`);
    emit("changeType", "统计类");
    dialogVisible.value = false;
   }
  
   
}
// 点击挖掘
const handleExcavate = (types:any)=>{
    if(props.type == "创建"){
        dialogVisible.value = false;
        push(`/userPortrait/labelManagement/createUserTags?type=${types}`);
        emit("changeType", "挖掘类");
    }
    if(props.type == "编辑"){
    push(`/userPortrait/labelManagement/editLabelRules?type=${types}`);
    emit("changeType", "规则匹配类");
    dialogVisible.value = false;
   }
  
    
   
}
const clickLabel = (types:any) => {
    if(props.type == "创建"){
        
        push(`/userPortrait/labelManagement/createUserTags?type=${types}`);
        emit("changeType", "挖掘类");
        dialogVisible.value = false;
    }
    if(props.type == "编辑"){
    push(`/userPortrait/labelManagement/editLabelRules?type=${types}`);
    emit("changeType", "挖掘类");
    dialogVisible.value = false;
   }
   
    // emit("changeType", "规则匹配类");
   
}
defineExpose({
    dialogVisible
});
</script>

<style lang="scss" scoped>
.box {
    width: 80%;
    height: 100px;
    border: 1px solid #ccc;
    display: flex;
    cursor: pointer;

    img {
        width: 50px;
        height: 55px;
        margin: auto 15px;
    }
    .contant{
        margin: 15px 0;
    }
}
.box:hover{
    border: 1px solid #009688;
}

.el-row {
    margin-bottom: 20px;
}

.el-row:last-child {
    margin-bottom: 0;
}

.el-col {
    border-radius: 4px;
}

.grid-content {
    border-radius: 4px;
    min-height: 36px;
}
</style>