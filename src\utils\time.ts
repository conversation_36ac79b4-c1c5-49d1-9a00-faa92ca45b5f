//封装一个函数:获取一个结果:当前早上|上午|下午|晚上
export function getTime(): string {
  let message = ''
  //通过内置构造函数Date
  const hours = new Date().getHours()
  //情况的判断
  if (hours <= 9) {
    message = '早上'
  } else if (hours <= 12) {
    message = '上午'
  } else if (hours <= 18) {
    message = '下午'
  } else {
    message = '晚上'
  }
  return message
}
export interface getTopRightElementReturn {
  month: number
  day: number
  week: string
  hours: string | number
  minutes: string | number
  ch: string
}
export function getTopRightElement(): getTopRightElementReturn {
  // 获取当前月和日
  const date = new Date()
  const month = date.getMonth() + 1
  const day = date.getDate()
  // 获取当前星期
  const week = ['日', '一', '二', '三', '四', '五', '六'][date.getDay()]
  // 获取小时数带0
  const hours = date.getHours() < 10 ? '0' + date.getHours() : date.getHours()
  // 获取小时数带0
  const minutes =
    date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
  const ch = getTime()
  return {
    month,
    day,
    week,
    hours,
    minutes,
    ch,
  }
}
// 使用时间戳获取第二天的日期
export function getTomorrow(date: Date): string {
  const tomorrow = new Date(date.getTime() + 24 * 60 * 60 * 1000)
  return Fnyyyymmdd(tomorrow)
}
// 通过日期返回一个月的日期数组
export function getMonthDays(date: any): string[] {
  date = new Date(date)
  const year = date.getFullYear()
  let month = date.getMonth() + 1
  const d = new Date(year, month, 0)
  const days = d.getDate()
  const arr = []
  for (let i = 1; i <= days; i++) {
    i = i.toString().padStart(2, '0') as any
    month = month.toString().padStart(2, '0') as any
    arr.push(month + '-' + i)
  }
  return arr
}
// 通过日期返回一个月的日期数组带year
export function getMonthDaysYear(date: any): string[] {
  date = new Date(date)
  const year = date.getFullYear()
  let month = date.getMonth() + 1
  const d = new Date(year, month, 0)
  const days = d.getDate()
  const arr = []
  for (let i = 1; i <= days; i++) {
    i = i.toString().padStart(2, '0') as any
    month = month.toString().padStart(2, '0') as any
    arr.push(year + '-' + month + '-' + i)
  }
  return arr
}
// YYYY-MM-DD 传入Date类型，返回字符串
export function Fnyyyymmdd(date: Date) {
  return (
    date.toLocaleDateString().split('/')[0] +
    '-' +
    date.toLocaleDateString().split('/')[1].padStart(2, '0') +
    '-' +
    date.toLocaleDateString().split('/')[2].padStart(2, '0')
  )
}
