<template>
  <div>
    <pure-table
      :columns="columns"
      :data="tableData"
      :pagination="pagination"
      @page-size-change="onSizeChange"
      @page-current-change="onCurrentChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from "vue";
import { columns } from "./data";
import type { PaginationProps } from "@pureadmin/table";
import { delay } from "@pureadmin/utils";
import { getCustomerListApi } from "@/api/customer-management";
const props = defineProps({
  queryId: {
    type: String as PropType<string>,
    default: "0"
  },
  tags: {
    type: Array,
    default: () => []
  },
  type: {
    type: Number as PropType<number>,
    default: 1
  }
});
const loading = ref(false);
const tableData = ref([]);
const selectType = ref(1);
const searchInfo = ref({
  name: "",
  customGrade: "",
  tags: undefined,
  isSign: true,
  customIdentity: 1,
  pageNo: 1,
  pageSize: 10
});
/** 分页配置 */
const pagination = reactive<PaginationProps>({
  pageSize: 10,
  currentPage: 1,
  pageSizes: [10, 20, 40, 50],
  total: 0,
  align: "right",
  background: true,
  small: false
});
function onSizeChange(val) {
  searchInfo.value.pageSize = val;
  getList();
}
function onCurrentChange(val) {
  searchInfo.value.pageNo = val;
  getList();
}
async function getList() {
  loading.value = true;
  const { data } = await getCustomerListApi(searchInfo.value);
  pagination.total = data ? Number(data.totalCount) : 0;
  tableData.value = data ? data.data : [];
  delay(300).then(() => {
    loading.value = false;
  });
}
onMounted(() => {
  // getList();
});
watch(
  () => props.queryId,
  newVal => {
    searchInfo.value.tags = undefined;
    searchInfo.value.customGrade = newVal;
    getList();
  },
  { immediate: true }
);
watch(
  () => props.type,
  newVal => {
    selectType.value = newVal;
    if (selectType.value !== 1) {
      searchInfo.value.customGrade = undefined;
    }
  },
  { immediate: true }
);
watch(
  () => props.tags,
  newVal => {
    if (selectType.value !== 1) {
      searchInfo.value.customGrade = undefined;
    }
    searchInfo.value.tags = newVal;
    getList();
  },
  { immediate: true }
);
</script>

<style scoped></style>
