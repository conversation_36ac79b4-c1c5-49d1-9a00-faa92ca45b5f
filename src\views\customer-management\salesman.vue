<template>
  <section>
    <el-card class="jt-card">
      <div class="header">
        <div>
          用户名称：<el-input style="margin-right: 10px;width: 15%" v-model="customerlName" placeholder="用户名称">
        </el-input>
          <!--          地区：-->
          <!--          <el-tree-select @node-click="handleAreaChange" style="margin-right: 10px; width: 180px" default-expand-all-->
          <!--            v-model="areaId" check-strictly :props="{ children: 'children', label: 'name', value: 'id' }"-->
          <!--            placeholder="请选择" :data="treeData" :render-after-expand="false" />-->
          用户来源：
          <!--          <DictSelect style="margin-right: 10px; width: 15%" v-model="customerSource" :clearable="true"-->
          <!--            dict-code="customerSource" />-->
          <el-select v-model="customerSource" :clearable="true" style="margin-right: 10px; width: 15%"
                     placeholder="用户来源">
            <el-option v-for="item in customerSourceOptions" :key="item.value" :label="item.label" :value="item.label" />
          </el-select>
          营销人员：<el-select clearable @change="handleSelect" style="margin-right: 10px; width: 15%" class="singleSelect"
                              v-model="followerId" placeholder="营销人员">
          <el-option v-for="item in optionsList" :key="item.value" :label="item.label" :value="item.label" />
        </el-select>
          授权时间：<el-date-picker v-model="authorizationTime" format="YYYY.MM" value-format="YYYY.MM" type="month" placeholder="选择日期" style="margin-right: 10px" />
        </div>
        <div style="width: 15%; display: flex; justify-content: flex-end;">
          <el-button @click="searchClick" type="primary" style="margin-right: 10px">
            查询
          </el-button>
          <el-button @click="resetClick" type="success" style="margin-right: 10px">
            重置
          </el-button>
        </div>
      </div>
    </el-card>

    <el-card class="jt-card" style="margin-top: 20px;">
      <el-row justify="space-between">
        <el-col :span="6">
          <div style="display: flex;justify-content: flex-start;">
            <el-button @click="settingClick" type="primary" style="margin-right: 10px">
              营销人员配置
            </el-button>
            <el-button :disabled="!outerSelect.length" @click="updateClick" type="primary" style="margin-right: 10px">
              选中营销人员修改
            </el-button>
            <el-button :disabled="!outerSelect.length" @click="updateSalesmanClick" type="primary" style="margin-right: 10px">
              选中用户来源修改
            </el-button>
            <el-button :disabled="!outerSelect.length" @click="() => {
              timeUpdate_Visible = true
              authorisationTimeSelect = []
            }" type="primary" style="margin-right: 10px">
              选中授权时间修改
            </el-button>
          </div>
        </el-col>
        <el-col :span="3">
          <div style="display: flex;justify-content: flex-end;">
            <el-upload class="daoru" style="margin-left: 10px" action="" :http-request="detailUploadFunction"
                       :on-change="detailguideInto" :show-file-list="false">
              <el-button :icon="Upload" type="primary" style="margin-right: 10px">导入</el-button>
            </el-upload>
            <el-button @click="Download" type="primary" :icon="Edit">
              导出
            </el-button>
          </div>
        </el-col>
      </el-row>
      <el-table stripe :header-cell-style="{
            borderColor: '#DCDFE6',
            color: '#1D2129',
            backgroundColor: '#F2F3F5',
          }" :data="outerTableData" border style="width: 100%; margin: 20px 0" height="500"
                @selection-change="outerTableChange">
        <el-table-column type="selection" width="55" />
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="customName" label="用户名称" />
        <!--        <el-table-column prop="areaName" label="地区" />-->
        <el-table-column prop="userChannel" label="用户渠道">
          <!--          <template #default="scope">-->
          <!--            <div>-->
          <!--              {{ filterDictText(scope.row.customerSource, customerSourceOptions) }}-->
          <!--            </div>-->
          <!--          </template>-->
        </el-table-column>
        <!--        <el-table-column prop="annualElectricity" label="年用电量" />-->
        <el-table-column prop="saleName" label="营销人员" />
        <el-table-column prop="authorizationTime" label="授权时间">
          <template #default="{row}">
            {{ row.authorisationStartTime }}-{{ row.authorisationEndTime }}
          </template>
        </el-table-column>
      </el-table>
      <el-pagination v-model:current-page="outerCurrentPage" v-model:page-size="outerPageSize" :page-count="outerPageCount"
                     :page-sizes="[10, 20, 50, 100, '全部']" layout="->,total, prev, pager, next,sizes" :total="outerTotal"
                     @size-change="outerHandleSizeChange" @current-change="outerHandleCurrentChange" />
    </el-card>

    <!-- 选中营销人员修改 -->
    <el-dialog class="dialog" v-model="update_Visible" title="请选择更改的营销人员" style="width: 50%">
      <div>
        <el-table highlight-current-row :header-cell-style="{
            borderColor: '#DCDFE6',
            color: '#1D2129',
            backgroundColor: '#F2F3F5',
          }" :data="inner1TableData" border style="width: 100%; margin: 20px 0" height="440"
                  @current-change="inner1TableChange">
          <el-table-column type="index" label="序号" width="200" />
          <el-table-column prop="name" label="人员" />
        </el-table>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="info" @click="update_VisibleCancle">
            取消
          </el-button>
          <el-button type="primary" @click="update_VisibleConfirm">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 选中用户来源修改 -->
    <el-dialog class="dialog" v-model="salesmanUpdate_Visible" title="请选择更改的用户来源" style="width: 50%">
      <div>
        <el-table highlight-current-row :header-cell-style="{
            borderColor: '#DCDFE6',
            color: '#1D2129',
            backgroundColor: '#F2F3F5',
          }" :data="inner3TableData" border style="width: 100%; margin: 20px 0" height="440"
                  @current-change="inner3TableChange">
          <el-table-column type="index" label="序号" width="200" />
          <el-table-column prop="label" label="用户来源" />
        </el-table>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="info" @click="sourceUpdate_VisibleCancle">
            取消
          </el-button>
          <el-button type="primary" @click="sourceUpdate_VisibleConfirm">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 授权时间修改 -->
    <el-dialog class="dialog" v-model="timeUpdate_Visible" title="修改授权时间" style="width: 30%">
      <el-row :justify="'center'">
        <el-form-item label="授权时间">
          <el-date-picker v-model="authorisationTimeSelect" type="daterange" value-format="YYYY.MM.DD" format="YYYY.MM.DD" placeholder="选择日期"
                          style="width: 100%" />
        </el-form-item>
      </el-row>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="info" @click="() => {
            timeUpdate_Visible = false
          }">
            取消
          </el-button>
          <el-button type="primary" @click="authorizationTimeUpdate_VisibleConfirm">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 营销人员配置 -->
    <el-dialog class="dialog" v-model="setting_Visible" title="营销人员配置" style="width: 50%">
      <div>
        <el-button @click="addClick" type="primary" style="margin-bottom: 10px">
          新增
        </el-button>
        <el-table highlight-current-row stripe :header-cell-style="{
            borderColor: '#DCDFE6',
            color: '#1D2129',
            backgroundColor: '#F2F3F5',
          }" :data="inner2TableData" border style="width: 100%; margin: 20px 0" height="440">
          <el-table-column type="index" label="序号" width="200" />
          <el-table-column prop="name" label="人员" />
          <el-table-column label="操作" width="180">
            <template #default="scope">
              <div style="display: flex; ">
                <div @click="editClick(scope.row)" style="
                  cursor: pointer;
                  color: #254f7a;
                  display: flex;
                  align-items: center;
                ">
                  <img style="margin-right: 5px; width: 15px" :src="getAssetURL('Edit_light')" alt="" />
                  修改
                </div>
                <div @click="closeClick(scope.row)" style="
                  cursor: pointer;
                  color: #f53f3f;
                  display: flex;
                  align-items: center;
                  margin-left: 20px;
                ">
                  <img style="margin-right: 5px; width: 15px" :src="getAssetURL('close')" alt="" />
                  删除
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button type="info" @click="setting_VisibleCancle">
            取消
          </el-button>
          <el-button type="primary" @click="setting_VisibleConfirm">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog top="30vh" class="dialog" v-model="saleman_Visible" title="营销人员配置" style="width: 30%">
      <div>
        人员名称： <el-input @input="() =>
            saleman.length > 20
              ? (saleman =
                saleman.slice(0, 20))
              : (saleman = saleman)
            " style="width: 70%;" v-model="saleman"></el-input>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button type="info" @click="saleman_VisibleCancle">
            取消
          </el-button>
          <el-button type="primary" @click="saleman_VisibleConfirm">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>


    <el-dialog top="30vh" class="dialog" v-model="closeVisible" title="提示" style="width: 30%;">
      <div>
        <div style="
            text-align: center;
            line-height: 200px;
            height: 200px;
            font-size: 16px;
          ">
          是否确认删除？
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button class="no-border" type="info" @click="closeVisible = false">取消</el-button>
          <el-button type="primary" @click="closeVisibleConfirm">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </section>
</template>

<script setup lang='ts'>
import { ref, onMounted } from 'vue'
import { usePowerCustomer } from "@/hooks/customer/usePowerCustomer";
import { getCustomerListApi } from "@/api/customer-management/index";
import {
  addSalesman, //添加营销人员
  batchUpdateSaleUser, //批量修改营销人员用户关联
  getAllSalesmanList, //获取营销人员列表
  updateSalesman, //修改营销人员
  queryPageSaleUser,
  batchUpdateSourceUser,
  marketerExportAPI,
  marketerImportAPI,
  marketerPageAPI,
  marketerBatchUpdateAPI, //分页查询营销人员用户关联
} from '@/api'
import { ElMessage, ElLoading } from 'element-plus';
import {Edit, Upload} from "@element-plus/icons-vue";

// 删除弹窗
function closeClick(data: any) {
  rowData.value = data
  saleman.value = data.name
  closeVisible.value = true
}
const closeVisible = ref<any>()
async function closeVisibleConfirm() {
  const loading = ElLoading.service({ text: '正在删除...' })
  await updateSalesman({
    id: rowData.value.id,
    name: saleman.value,
    status: 9
  }).then(async (res) => {
    await getAllSalesmanListInfo()
    await queryPageSaleUserInfo()
  }).finally(() => {
    loading.close()
    closeVisible.value = false
  })
}

const saleman_Visible = ref<any>(false)
function saleman_VisibleCancle() {
  saleman.value = ''
  saleman_Visible.value = false
}
async function saleman_VisibleConfirm() {
  if (!saleman.value) return ElMessage.info('请输入人员名称！')
  if (isEdit.value) {
    await updateSalesman({
      id: rowData.value.id,
      name: saleman.value,
      status: 0
    }).then((res) => {
      saleman_Visible.value = false
      isEdit.value = false
    })
  } else {
    await addSalesman({
      name: saleman.value
    }).then((res) => {
      saleman_Visible.value = false
      isEdit.value = false
    })
  }
  await getAllSalesmanListInfo()
}

const saleman = ref<any>()
async function queryPageSaleUserInfo() {
  outerTableData.value = []
  const res = await marketerPageAPI({
    saleName: followerId.value,
    customName: customerlName.value,
    userChannel: customerSource.value,
    pageNo: outerCurrentPage.value,
    pageSize: maxFlag.value ? outerPageSizeMax.value : outerPageSize.value,
    authorisationTime: authorizationTime.value
  })
  console.log(`output->res`, res)
  if (res) {
    outerTableData.value = res.data
    outerTotal.value = Number(res.totalCount) || 0
    outerCurrentPage.value = res.pageNo || 1
    outerPageCount.value = Number(res.totalPage) || 0
  }
}

const optionsList = ref<any>([])
async function getAllSalesmanListInfo() {
  const res = await getAllSalesmanList()
  if (res && res.length > 0) {
    optionsList.value = res.map((item: any) => {
      return {
        label: item.name,
        value: item.id
      }
    })
  }
  inner2TableData.value = res || []
  inner1TableData.value = res || []
}

function getCustomerSource() {
  inner3TableData.value = customerSourceOptions
}

const {
  getCityTreeData,
  treeData,
  filterDictText,
  customerSourceOptions
} = usePowerCustomer(false);
console.log(`output->`, filterDictText, customerSourceOptions)
const getAssetURL = (image: any) => {
  return new URL(`../../assets/svg/${image}.svg`, import.meta.url).href
}
// 营销人员配置
function settingClick() {
  setting_Visible.value = true
}
const setting_Visible = ref<any>(false)
function setting_VisibleCancle() {
  setting_Visible.value = false
}
function setting_VisibleConfirm() {
  setting_Visible.value = false
}
// 营销人员修改
function updateClick() {
  update_Visible.value = true
}

function updateSalesmanClick() {
  salesmanUpdate_Visible.value = true
}

const update_Visible = ref<any>(false)
const salesmanUpdate_Visible = ref<any>(false)
function update_VisibleCancle() {
  update_Visible.value = false
}
async function update_VisibleConfirm() {
  if (!inner1TableSelect.value) return ElMessage.info('请选择营销人员！')
  let Array = outerSelect.value.map((item: any) => {
    return {
      ...item,
      // customSaleId: inner1TableSelect.value.id,
      saleName: inner1TableSelect.value.name,
    }
  })
  await marketerBatchUpdateAPI(Array)
  await queryPageSaleUserInfo()
  update_Visible.value = false
}
function sourceUpdate_VisibleCancle() {
  salesmanUpdate_Visible.value = false
}
async function sourceUpdate_VisibleConfirm() {
  if (!inner3TableSelect.value) return ElMessage.info('请选择用户来源！')
  let Array = outerSelect.value.map((item: any) => {
    return {
      ...item,
      userChannel: inner3TableSelect.value.label,
    }
  })
  // console.log('sourceUpdate_VisibleConfirm', Array)
  await marketerBatchUpdateAPI(Array)
  await queryPageSaleUserInfo()
  salesmanUpdate_Visible.value = false
}

const authorisationTimeSelect = ref([])
const timeUpdate_Visible = ref(false)

async function authorizationTimeUpdate_VisibleConfirm() {
  if (authorisationTimeSelect.value.length == 0) return ElMessage.info('请选择授权时间！')
  let Array = outerSelect.value.map((item: any) => {
    return {
      ...item,
      authorisationStartTime: authorisationTimeSelect.value[0],
      authorisationEndTime: authorisationTimeSelect.value[1]
    }
  })
  // console.log('sourceUpdate_VisibleConfirm', Array)
  await marketerBatchUpdateAPI(Array)
  await queryPageSaleUserInfo()
  timeUpdate_Visible.value = false
}

let outerSelect = ref<any>([])
// 表格多选事件
function outerTableChange(data) {
  outerSelect.value = data
  console.log(`output->data`, data)
}
// 外面表格
const outerTableData = ref<any>([])
// 分页
const outerCurrentPage = ref<any>(1)
const outerPageSize = ref<any>(10)
const outerTotal = ref<any>(1)
const outerPageCount = ref<any>(1)

const maxFlag = ref(false)
const outerPageSizeMax = ref<any>(214748)

function outerHandleSizeChange(val: any) {
  if (Number.isNaN(val)) {
    maxFlag.value = true
    outerPageSize.value = '全部'
  } else {
    maxFlag.value = false
    outerPageSize.value = val
  }
  queryPageSaleUserInfo()
}
function outerHandleCurrentChange(val) {
  outerCurrentPage.value = val
  queryPageSaleUserInfo()
}

// 内部1表格

let inner1TableSelect = ref<any>()
let inner3TableSelect = ref<any>()
const inner1TableData = ref<any>([])
const inner3TableData = ref<any>([])
function inner1TableChange(data) {
  inner1TableSelect.value = data
  console.log(`output->data`, data)
}
function inner3TableChange(data) {
  inner3TableSelect.value = data
  console.log(`output->data`, data)
}

// 查询
function searchClick() {
  queryPageSaleUserInfo()
}
// 重置
function resetClick() {
  customerlName.value = ''
  areaId.value = ''
  followerId.value = ''
  customerSource.value = ''
  authorizationTime.value = ''
  queryPageSaleUserInfo()
}

// 内部表格2
function addClick() {
  saleman.value = ''
  saleman_Visible.value = true
}
const inner2TableData = ref<any>([
])
const isEdit = ref<any>(false)
const rowData = ref<any>()
function editClick(data) {
  isEdit.value = true
  saleman_Visible.value = true
  saleman.value = data.name
  rowData.value = data
}

// 用户名称
const customerlName = ref<any>()

// 地区id
const areaId = ref<any>()
// 地区改变事件
async function handleAreaChange(data) {
}
function handleSelect(data) {
}
// 用户来源
const customerSource = ref<any>()

// 授权时间
const authorizationTime = ref<any>()

// 营销人员
const followerId = ref<any>()

// 导入
let detailfileList = ref<any>()
function detailUploadFunction() { }
function detailguideInto(file: any) {
  const fileExtension = file.name.split('.').pop()
  const size = file.size / 1024 / 1024
  if (fileExtension !== 'xlsx' && fileExtension !== 'xls') {
    ElMessage.warning('只能上传excel文件')
    detailfileList.value = []
    return
  }
  if (size > 10) {
    ElMessage.warning('文件大小不得超过10M')
    detailfileList.value = []
    return
  }
  detailfileList.value = [file]
  detailhandleUpload()
}

// 上传函数
const detailhandleUpload = () => {
  if (detailfileList.value.length === 0) {
    ElMessage.warning('未选择文件')
  } else {
    const formData = new FormData()
    formData.append('file', detailfileList.value[0].raw)
    marketerImportAPI(formData)
        .then(() => {
          // ElMessage.success('上传成功')
          // contractDetailsInfo()
          getAllSalesmanListInfo()
          queryPageSaleUserInfo()
          getCustomerSource()
        })
        .catch((e) => {
          console.log(e)
        })
  }
}

// 导出数据
function Download() {
  const loading = ElLoading.service({ text: '正在下载...' })
  marketerExportAPI({
    contractStartTime: 1,
  })
      .then((data) => {
        const blob = new Blob([data])
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = '合约.xlsx'
        link.click()
        window.URL.revokeObjectURL(url)
        loading.close()
      })
      .catch(() => {
        ElMessage.error('下载失败')
        loading.close()
      })
}

onMounted(() => {
  // getCityTreeData();
  getAllSalesmanListInfo()
  queryPageSaleUserInfo()
  getCustomerSource()
});

</script>

<style scoped lang="scss">
.my-dialog {
  margin-top: 30vh !important;
}
</style>