export default {
  path: "/load-forecasting",
  meta: {
    icon: "ep:trend-charts",
    title: "负荷管理",
    rank: 3
  },
  children: [
    // LoadForecastingForecast
    {
      path: "/load-forecasting/analysis",
      name: "LoadForecastingAnalysis",
      component: () => import("@/views/load-forecasting/analysis/index.vue"),
      meta: {
        // keepAlive: true,
        title: "负荷数据分析",
        //icon: "ep:histogram"
      }
    },
    {
      path: "/load-forecasting/forecast",
      name: "LoadForecastingForecast",
      component: () => import("@/views/load-forecasting/forecast/index.vue"),
      meta: {
        // keepAlive: true,
        title: "负荷预测",
        //icon: "ep:data-line"
      }
    },
    {
      path: "/load-forecasting/load-management",
      name: "LoadForecastingLoadManagement",
      component: () =>
        import("@/views/load-forecasting/load-management/index.vue"),
      meta: {
        // keepAlive: true,
        title: "用能分析",
        //icon: "ant-design:project-outlined"
      }
    },
    {
      path: "/load-forecasting/load-management/detail",
      name: "LoadForecastingLoadManagementDetail",
      component: () =>
        import("@/views/load-forecasting/load-management/detail.vue"),
      meta: {
        // keepAlive: true,
        title: "用能分析详情",
        //icon: "ant-design:project-outlined",
        showLink: false
      }
    },
    {
      path: "/load-forecasting/declared-electricity",
      name: "LoadForecastingDeclaredElectricity",
      component: () =>
        import("@/views/load-forecasting/declared-electricity/index.vue"),
      meta: {
        // keepAlive: true,
        title: "申报电量管理",
        //icon: "ep:scale-to-original"
      }
    }
  ]
};
