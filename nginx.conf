# 完全移除所有限制的nginx配置
# 彻底解决跨域和iframe嵌入问题

server {
    listen 80;
    server_name _;  # 匹配所有域名
    
    # 项目根目录 - 请修改为您的实际路径
    root /var/www/html/dist;
    index index.html index.htm;
    
    # 全局配置：移除所有限制
    location / {
        # Vue Router history模式支持
        try_files $uri $uri/ /index.html;
        
        # 完全移除所有iframe和CSP限制
        more_clear_headers "X-Frame-Options";
        more_clear_headers "Content-Security-Policy";
        more_clear_headers "X-Content-Type-Options";
        more_clear_headers "X-XSS-Protection";
        
        # 允许所有iframe嵌入
        add_header X-Frame-Options "ALLOWALL" always;
        
        # 完全开放的CORS配置
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "*" always;
        add_header Access-Control-Allow-Headers "*" always;
        add_header Access-Control-Allow-Credentials "true" always;
        add_header Access-Control-Max-Age "86400" always;
        
        # 处理所有OPTIONS预检请求
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "*";
            add_header Access-Control-Allow-Headers "*";
            add_header Access-Control-Allow-Credentials "true";
            add_header Access-Control-Max-Age "86400";
            add_header Content-Type "text/plain; charset=utf-8";
            add_header Content-Length 0;
            return 204;
        }
    }
    
    # 静态资源配置
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|map)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        
        # 静态资源也移除所有限制
        more_clear_headers "X-Frame-Options";
        more_clear_headers "Content-Security-Policy";
        
        add_header X-Frame-Options "ALLOWALL" always;
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "*" always;
        add_header Access-Control-Allow-Headers "*" always;
    }
    
    # API代理配置（如果需要）
    location /api/ {
        # 代理到后端服务器 - 请修改为您的后端地址
        proxy_pass http://***********:8187/selling/;
        
        # 代理头部设置
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 移除后端可能设置的限制性头部
        proxy_hide_header X-Frame-Options;
        proxy_hide_header Content-Security-Policy;
        proxy_hide_header X-Content-Type-Options;
        proxy_hide_header X-XSS-Protection;
        
        # 重新设置完全开放的头部
        add_header X-Frame-Options "ALLOWALL" always;
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "*" always;
        add_header Access-Control-Allow-Headers "*" always;
        add_header Access-Control-Allow-Credentials "true" always;
        
        # 代理超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 禁用访问隐藏文件
    location ~ /\. {
        deny all;
    }
    
    # 错误页面
    error_page 404 /index.html;
}

# 如果需要HTTPS支持
server {
    listen 443 ssl http2;
    server_name _;
    
    # SSL证书配置 - 请替换为您的证书路径
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    root /var/www/html/dist;
    index index.html index.htm;
    
    location / {
        try_files $uri $uri/ /index.html;
        
        # HTTPS环境下也完全移除限制
        more_clear_headers "X-Frame-Options";
        more_clear_headers "Content-Security-Policy";
        more_clear_headers "Strict-Transport-Security";
        
        add_header X-Frame-Options "ALLOWALL" always;
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "*" always;
        add_header Access-Control-Allow-Headers "*" always;
        add_header Access-Control-Allow-Credentials "true" always;
        
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "*";
            add_header Access-Control-Allow-Headers "*";
            add_header Access-Control-Allow-Credentials "true";
            add_header Content-Length 0;
            return 204;
        }
    }
    
    # HTTPS静态资源
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|map)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        more_clear_headers "X-Frame-Options";
        more_clear_headers "Content-Security-Policy";
        add_header X-Frame-Options "ALLOWALL" always;
        add_header Access-Control-Allow-Origin "*" always;
    }
}

# HTTP自动跳转到HTTPS（可选）
# server {
#     listen 80;
#     server_name _;
#     return 301 https://$server_name$request_uri;
# }
