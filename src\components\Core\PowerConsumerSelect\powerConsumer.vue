<template>
  <section>
    <div class="app-func-bar">
      <div class="app-form-group">
        <div>
          <span>用户名称：</span>
          <el-input
            clearable
            v-model="searchInfo.name"
            placeholder="请输入用户名称"
            class="filter-item"
          />
        </div>
      </div>
      <div class="app-btn-group">
        <el-button class="filter-item" type="primary" @click="getList"
          >查询</el-button
        >
        <el-button class="filter-item" @click="handleReset">重置</el-button>
      </div>
    </div>
    <pure-table
      :columns="columns"
      border
      stripe
      :data="tableData"
      :loading="loading"
      :pagination="pagination"
      :highlight-current-row="true"
      @row-click="handleRowClick"
      @page-size-change="onSizeChange"
      @page-current-change="onCurrentChange"
    >
      <template #agentType="{ row }">
        <div>
          {{ filterDictText(row.agentType, agentTypeOptions) }}
        </div>
      </template>
      <template #status="{ row }">
        <div>
          {{ filterDictText(row.status, statusOptions) }}
        </div>
      </template>
      <template #customerSource="{ row }">
        <div>
          {{ filterDictText(row.customerSource, customerSourceOptions) }}
        </div>
      </template>
    </pure-table>
    <div class="flex justify-end mt-[40px]">
      <el-button type="primary" @click="handleConfirm">确认选择</el-button>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { columns } from "./data";
import { useUserStore } from "@/store/modules/user";
import type { PaginationProps } from "@pureadmin/table";
import { getCustomerListApi } from "@/api/customer-management/index";
import { delay } from "@pureadmin/utils";
import { useDictOptions } from "@/hooks/dict/useDictOptions";
const emit = defineEmits(["change"]);
const loading = ref(false);
const { agentTypeOptions, statusOptions, customerSourceOptions } =
  useDictOptions();
const tableData = ref([]);
const selectedData = ref([]);
const searchInfo = reactive({
  name: "",
  pageNo: 1,
  pageSize: 10
});
/** 分页配置 */
const pagination = reactive<PaginationProps>({
  pageSize: 10,
  currentPage: 1,
  pageSizes: [10, 20, 40, 50],
  total: 0,
  align: "right",
  background: true,
  small: false
});

async function getList() {
  loading.value = true;
  const { data } = await getCustomerListApi(searchInfo);
  pagination.total = data ? Number(data.totalCount) : 0;
  tableData.value = data ? data.data : [];
  delay(300).then(() => {
    loading.value = false;
  });
}

function handleReset() {
  searchInfo.name = "";
  getList();
}

function onSizeChange(val) {
  searchInfo.pageSize = val;
  getList();
}

function onCurrentChange(val) {
  searchInfo.pageNo = val;
  getList();
}
function handleRowClick(row) {
  selectedData.value = row;
}
function handleConfirm() {
  emit("change", selectedData.value);
}
// 字典方法
function filterDictText(value, array) {
  return array.find(i => i.value == String(value))?.label;
}

onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped></style>
