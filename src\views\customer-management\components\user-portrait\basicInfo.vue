<template>
  <div class="app-list-wrap">
    <div class="app-list-title">
      <span class="font-bold">基本情况</span>
    </div>
    <div class="p-[20px]">
      <el-descriptions :column="4">
        <el-descriptions-item label="行业分类：">{{
          dataForm.basic.industryId
            ? filterDictText(dataForm.basic.industryId, industryOptions)
            : "-"
        }}</el-descriptions-item>
        <!-- <el-descriptions-item label="所属区域：">{{
          dataForm.basic.areaName
        }}</el-descriptions-item> -->
        <el-descriptions-item label="所属地区：">{{
          dataForm.basic.areaName
        }}</el-descriptions-item>
        <el-descriptions-item label="开始签约月份~结束签约月份：">{{
          dataForm.basic.signMonthPeriod
        }}</el-descriptions-item>
        <el-descriptions-item label="当前年绑定期限：">{{
          dataForm.basic.thisYearBindPeriod
        }}</el-descriptions-item>
        <el-descriptions-item label="套餐名称：">{{
          dataForm.basic.priceModel
        }}</el-descriptions-item>
      </el-descriptions>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, watch, ref, computed } from "vue";
import { CustomerResultModel } from "@/model/customerModel";
import { useDictOptions } from "@/hooks/dict/useDictOptions";
import dayjs from "dayjs";
const { industryOptions } = useDictOptions();
const date = computed(() => {
  if (
    dataForm.value.basic.effectiveDate &&
    dataForm.value.basic.terminationDate
  ) {
    return (
      dayjs(Number(dataForm.value.basic.effectiveDate)).format("YYYY-MM") +
      "~" +
      dayjs(Number(dataForm.value.basic.terminationDate)).format("YYYY-MM")
    );
  } else {
    return "-";
  }
});
const props = defineProps({
  formInline: {
    type: Object,
    default: () => {}
  }
});
// 字典方法
function filterDictText(value, array) {
  return array.find(i => i.value == String(value))?.label;
}
const dataForm = ref<CustomerResultModel>({
  basic: {
    name: "",
    ownership: undefined,
    areaId: "",
    followerId: "",
    formerName: "",
    annualElectricity: undefined,
    customGrade: undefined,
    industryId: undefined,
    terminationDate: undefined,
    effectiveDate: undefined,
    signMonthPeriod: undefined,
    thisYearBindPeriod: undefined,
    status: undefined,
    socialCreditCode: "",
    bankName: "",
    bankAccount: "",
    registeredCapital: "",
    membershipGroup: "",
    greenDemand: "",
    followerName: "",
    description: "",
    isOpenSea: undefined,
    customIdentity: 1,
    registrationNo: "",
    legalRepresentative: "",
    businessTerm: "",
    businessRegistrationDate: undefined,
    issueDate: undefined,
    registrationAuthority: "",
    registrationStatus: undefined,
    registeredAddress: "",
    businessScope: "",
    electricalNature: undefined,
    mainBusiness: "",
    priceModel:"",
    monthlyAverageElectricity: undefined
  },
  electricity: {
    agentType: undefined,
    annualElectricity: undefined,
    customerSource: undefined,
    greenDemand: ""
  },
  contactList: []
});
watch(
  () => props.formInline,
  newVal => {
    dataForm.value.basic = newVal.basic;
    dataForm.value.contactList = newVal.contactList;
  },
  { deep: true }
);
onMounted(() => {
  console.log(props);

  if (props.formInline && props.formInline.basic) {
    dataForm.value.basic = props.formInline.basic;
  }
});
</script>

<style scoped></style>
