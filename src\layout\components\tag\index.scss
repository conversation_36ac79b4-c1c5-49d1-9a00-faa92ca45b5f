@keyframes schedule-in-width {
  from {
    width: 0;
  }

  to {
    width: 100%;
  }
}

@keyframes schedule-out-width {
  from {
    width: 100%;
  }

  to {
    width: 0;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@keyframes close {
  from {
    transform: translate(-50%, -50%);
  }

  to {
    transform: translate(0, -50%);
  }
}

.tags-view {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  height: 28px;
  font-size: 14px;
  color: var(--el-text-color-primary);
  transition: all 0.3s;
  margin-left: 251px;
  padding-top: 10px;
  box-sizing: border-box;
  width: calc(100% - 251px);

  .scroll-item {
    position: relative;
    display: inline-block;
    height: 28px;
    padding: 0 10px;
    margin-right: 4px;
    line-height: 28px;
    cursor: pointer;
    border-radius: 2px 2px 0 0;
    box-shadow: 0 0 1px #888;
    transition: all 0.4s;

    .el-icon-close {
      position: absolute;
      top: 50%;
      font-size: 10px;
      color: var(--el-color-primary);
      color: #39486d;
      cursor: pointer;
      transition: font-size 0.2s;
      transform: translate(-50%, -50%);
    }

    &.is-closable:not(:first-child) {
      &:hover {
        padding-right: 18px;

        &:not(.is-active) {
          .el-icon-close {
            animation: close 200ms ease-in forwards;
          }
        }
      }
    }
  }

  a {
    padding: 0 10px;
    color: #606266;
    text-decoration: none;
  }

  .scroll-container {
    position: relative;
    flex: 1;
    padding: 5px 0;
    padding-left: 24px;
    overflow: hidden;
    white-space: nowrap;

    .tab {
      position: relative;
      float: left;
      overflow: visible;
      white-space: nowrap;
      list-style: none;
      transition: transform 0.5s ease-in-out;

      .scroll-item {
        transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

        &:nth-child(1) {
          margin-left: 5px;
        }
      }
    }
  }

  /* 右键菜单 */
  .contextmenu {
    position: absolute;
    padding: 5px 0;
    margin: 0;
    font-size: 13px;
    font-weight: normal;
    color: var(--el-text-color-primary);
    white-space: nowrap;
    list-style-type: none;
    background: #fff;
    border-radius: 4px;
    outline: 0;
    box-shadow: 0 2px 8px rgb(0 0 0 / 15%);

    li {
      display: flex;
      align-items: center;
      width: 100%;
      padding: 7px 12px;
      margin: 0;
      cursor: pointer;

      &:hover {
        color: var(--el-color-primary);
      }

      svg {
        display: block;
        margin-right: 0.5em;
      }
    }
  }
}

.el-dropdown-menu {
  li {
    display: flex;
    align-items: center;
    width: 100%;
    margin: 0;
    cursor: pointer;

    svg {
      display: block;
      margin-right: 0.5em;
    }
  }
}

.el-dropdown-menu__item:not(.is-disabled):hover {
  color: #606266;
  background: #f0f0f0;
}

:deep(.el-dropdown-menu__item) i {
  margin-right: 10px;
}

:deep(.el-dropdown-menu__item--divided) {
  margin: 1px 0;
}

.el-dropdown-menu__item--divided::before {
  margin: 0;
}

.el-dropdown-menu__item.is-disabled {
  cursor: not-allowed;
}

.scroll-item.is-active {
  position: relative;
  color: #fff;
  border-radius: 2px 2px 0 0;
  box-shadow: 0 0 1px var(--el-color-primary);
  background: rgba(16, 80, 126, 0.08);

  &:not(:first-child) {
    padding-right: 18px;
  }

  .el-icon-close {
    transform: translate(0, -50%);
  }

  a {
    /* color: var(--el-color-primary) !important; */
    color: #214494;
  }
}

.arrow-left,
.arrow-right,
.arrow-down {
  position: relative;
  width: 40px;
  height: 38px;
  color: var(--el-text-color-primary);

  svg {
    position: absolute;
    left: 50%;
    width: 20px;
    height: 20px;
    transform: translate(-50%, 50%);
  }
}

.arrow-left {
  box-shadow: 5px 0 5px -6px #ccc;

  &:hover {
    cursor: w-resize;
  }
}

.arrow-right {
  border-right: 0.5px solid #ccc;
  box-shadow: -5px 0 5px -6px #ccc;

  &:hover {
    cursor: e-resize;
  }
}

/* 卡片模式下鼠标移入显示蓝色边框 */
.card-in {
  color: var(--el-color-primary);

  a {
    color: var(--el-color-primary);
  }
}

/* 卡片模式下鼠标移出隐藏蓝色边框 */
.card-out {
  color: #666;
  border: none;

  a {
    color: #666;
  }
}

/* 灵动模式 */
.schedule-active {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
}

/* 灵动模式下鼠标移入显示蓝色进度条 */
.schedule-in {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: var(--el-color-primary);
  animation: schedule-in-width 200ms ease-in;
}

/* 灵动模式下鼠标移出隐藏蓝色进度条 */
.schedule-out {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--el-color-primary);
  animation: schedule-out-width 200ms ease-in;
}
