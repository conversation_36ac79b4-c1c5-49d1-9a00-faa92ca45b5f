import dayjs from "dayjs";
// 电力用户列头
export const columns: TableColumnList = [
  {
    label: "序号",
    width: 80,
    align: "center",
    type: "index"
  },
  {
    label: "用户名称",
    width: 250,
    prop: "name"
  },
  {
    label: "年用电量",
    prop: "annualElectricity"
  },
  {
    label: "代理结束日期",
    prop: "terminationDate",
    width: 120,
    formatter: ({ terminationDate }) =>
      !["0", 0, undefined, null].includes(terminationDate)
        ? dayjs(Number(terminationDate)).format("YYYY-MM-DD")
        : ""
  },
  // {
  //   label: "用电类型",
  //   prop: "useType"
  // },
  {
    label: "跟进人",
    prop: "followerName"
  },
  {
    label: "所在地区",
    prop: "areaName"
  },
  {
    label: "用户来源",
    prop: "customerSource",
    slot: "customerSource"
  },
  // {
  //   label: "最后跟进时间",
  //   prop: "followTime",
  //   width: 160,
  //   formatter: ({ createTime }) =>
  //     dayjs(createTime).format("YYYY-MM-DD HH:mm:ss")
  // },
  {
    label: "代理类型",
    prop: "agentType",
    slot: "agentType"
  },
  {
    label: "签约状态",
    prop: "status",
    slot: "status"
  }
];
// 居间商列头
export const columns1: TableColumnList = [
  {
    label: "序号",
    width: 80,
    type: "index"
  },
  {
    label: "企业名称",
    prop: "name"
  },
  {
    label: "行政区域",
    prop: "areaName"
  },
  {
    label: "年度代理电量(MWh)",
    prop: "annualProxyElectricity"
  },
  {
    label: "联系信息",
    prop: "contact"
  }
];
