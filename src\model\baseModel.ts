export interface BasicResponseParams {
  data: Object;
  code: string;
  message: string;
}

export interface ResponseDetailModel<T> {
  code: string;
  message: string;
  data: T;
}

export interface BasicPageParams<T> {
  totalCount: string | number;
  totalPage: string | number;
  pageNo?: number;
  pageSize?: number;
  data: T[];
}

export type BasicFetchResult<T> = BasicResponseParams & {
  data: BasicPageParams<T>;
};

// 分页查询参数
export interface BaseRequestParams {
  pageNo?: number;
  pageSize?: number;
}
