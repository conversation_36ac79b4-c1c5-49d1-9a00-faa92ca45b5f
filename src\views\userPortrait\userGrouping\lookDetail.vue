<template>
    <div>
        
        <!-- <el-button style="margin: 20px 0 10px 20px;" @click="goBackAndClearStack">返回</el-button> -->
            <div style="display: flex;height: 100%;">
                <div class="w-[15%] tree-wrapper " ref="dynamicHeight">
                <div style="display: flex;padding: 10px;">
                    <img src="@/assets/svg/u626.png" alt="" style="width: 36px;height: 30px;">
                    <div style="line-height: 30px;">基础信息与分群规则</div>
                </div>
                <el-space wrap>

                </el-space>
                <div style="margin-left: 36px;">
                    <div class="manus" @click="scrollTo('basic', '基础信息')" :class="{ activeMenu: activeMenu === 0 }">基础信息
                    </div>
                    <div class="manus" @click="scrollTo('rules', '分群规则')" :class="{ activeMenu: activeMenu === 1 }">分群规则
                    </div>
                </div>

            </div>
            <div class="tree-wrapper  w-[85%]" ref="dynamicHeight">
                <div class="w-[100%]">
                    <div class="header" style="margin-bottom: 50px;position: fixed;z-index: 99;height: 50px;background-color: #ffffff;width: 72%;top: 118px;padding: 0 20px;"  >
                        <div class="header-title">
                            <img :src="getAssetURL('title-arrow')"  alt="" />
                            基础信息与分群规则
                        </div>

                    </div>
                    <div class="spacer" style="height: 50px;"></div>
                    <el-scrollbar>
                        <div class="basic" id="basic">
                            <div class="heander">
                                <div class="circle"></div>
                                <div>基础信息</div>
                            </div>
                            <div class="contant">
                                <el-form  :inline="true" :model="formInline" ref="ruleFormRef" :rules="rules">
                                    <el-form-item label="分群显示名" prop="groupNameCn">
                                        <el-input v-model="formInline.groupNameCn" placeholder="请输入标签显示名" clearable disabled/>
                                    </el-form-item>
                                    <el-form-item label="分群名称" prop="groupNameEn">
                                        <el-input v-model="formInline.groupNameEn" placeholder="请输入英文标签显示名称" disabled>
                                            <template #prepend>user_group_</template>
                                        </el-input>
                                    </el-form-item>
                                    <!-- <el-form-item label="分类" prop="categoryId">
                                        <el-tree-select default-expand-all v-model="formInline.categoryId"
                                            check-strictly style="width: 100%"
                                            :props="{ children: 'children', label: 'labelNameCn', value: 'id' }"
                                            placeholder="请选择" :data="tableData" :render-after-expand="false" />
                                        <el-tree-select v-model="value" :data="options5" :render-after-expand="false"
                                        style="width: 240px" />
                                    </el-form-item> -->
                                    <br />
                                    <el-form-item label="备注" prop="groupRemark">
                                        <el-input type="textarea" v-model="formInline.groupRemark" disabled
                                            style="width: 850px;"></el-input>
                                    </el-form-item>
                                </el-form>
                            </div>
                        </div>
                        <div class="basic" style="margin-top: 20px;" id="rules">
                            <div class="heander">
                                <div class="circle"></div>
                                <div>分群规则</div>
                            </div>
                            <div class="contant">
                                <!-- <div>创建方式</div>

                                <el-button style="margin: 20px 0; display: flex; box-sizing: border-box;"
                                    @click="handleCreateLabel">

                                    <el-image src="/src/assets/svg/rules.svg" alt="" />
                                    <span style="margin: 10px;">规则匹配类</span>
                                    <el-image src="/src/assets/svg/reset.svg" alt="" />
                                </el-button>
                                <p>在全部用户中,将满足以下条件的用户分为2个分层，系统会按照一下自定义分层的顺序进行用户匹配,同一用户会被优先匹配在顺序靠前的分层</p>
                                <div>

                                    <el-button v-for="(item, index) in layers" :key="index" round size="large"
                                        class="layeredLevel el-icon--right" :class="{activeLayere:layeredIndex === index}" @click="handlerTab(index)" style="border: 1px solid black;">

                                        <span>{{ layers[index].sliceName }}</span>

                                        <el-icon class="delete-icon" @click="handleDel(index)">
                                            <Delete />
                                        </el-icon>
                                    </el-button>
                                    <el-button round size="large" @click="addLayered" class="layered">
                                        <el-icon>
                                            <Plus />
                                        </el-icon>添加分层
                                    </el-button>
                                </div> -->
                                <el-form :model="formInline" label-width="auto" style="width: 100%; "  ref="ruleFormRef2"  :rules="rules2">
                                    <!-- <el-form-item prop="sliceName">
                                        <el-input v-model="formInline.sliceName" maxlength="10"
                                            placeholder="请输入分层标题" show-word-limit @input="handleInput"
                                            type="text" />
                                    </el-form-item>
                                    <el-form-item prop="sliceDescription">
                                      
                                        <el-input type="textarea" v-model="formInline.sliceDescription"  placeholder="请输入描述信息"
                                            @input="handleDescription"  maxlength="200"  show-word-limit></el-input>
                                    </el-form-item> -->
                                    <div class="attribute">
                                        <div style="display: flex;justify-content: space-between; ">
                                            <p>用户属性满足</p> <el-button type="primary" @click="addAttribute" disabled>
                                                <el-icon>
                                                    <Plus />
                                                </el-icon>添加</el-button>
                                        </div>
                                        <div style="margin: 10px 0 10px 0;">
                                            <el-form-item label="规则描述" prop="labelNameCn">
                                        <el-input v-model="formInline.rulesDescription" placeholder="请输入描述规则" clearable disabled />
                                    </el-form-item>
                                        </div>

                                        <div style="display: flex;justify-content: space-between;">
                                            <div class="left" v-if="layers[layeredIndex].sliceRules.length > 1">
                                                <div class="round" >
                                                {{ roundName }} 
                                                </div>
                                                <div class="border"></div>
                                            </div>
                                            <div style="width: 90%;"> 
                                        <div v-for="(item, idx) in layers[layeredIndex].sliceRules" :key="idx"
                                            class="item">
                                            <div class="l_select">
                                                <el-form-item prop="categoryId" style="margin-right: 20px;">
                                                    <el-tree-select default-expand-all v-model="item.id" check-strictly disabled
                                                        style="width: 100%"
                                                        :props="{ children: 'children', label: 'labelNameCn', value: 'id' }"
                                                        placeholder="请选择" :data="tableData" :render-after-expand="false"
                                                        @change="handlerChange($event,item)" />
                                                    <!-- <el-tree-select v-model="value" :data="options5" :render-after-expand="false"
                                        style="width: 240px" /> -->
                                                </el-form-item>
                                                <el-form-item prop="type" class="formItem">
                                                    <el-select v-model="item.type" placeholder="请选择"
                                                        style="width: 160px; margin-right: 20px; height: 40px;" disabled>
                                                        <el-option v-for="item in options2" :key="item.value"
                                                            :label="item.label" :value="item.value" />
                                                    </el-select>
                                                </el-form-item>
                                                <el-select v-model="item.attributeValue" placeholder="请选择" style="width: 240px" disabled>
                                                    <el-option v-for="item in item.options9" :key="item.value"
                                                        :label="item.sliceName" :value="item.id" />
                                                </el-select>
                                            </div>
                                            <div style="display: flex;">
                                                <el-button type="primary" :icon="CirclePlus" disabled
                                                    @click="addItemOperator()"></el-button>
                                                <el-button type="primary" :icon="Delete" @click="removeItem(item,idx)" disabled/>
                                            </div>
                                        </div>
                                    </div> 
                                    </div> 

                                    </div>

                                </el-form>

                                <div style="margin-top: 30px ">
                                    <!-- <p style="margin: 20px 0 20px 0;">更新方式</p> -->
                                    <div style="display: flex;">
                                        
                                        <el-button style="margin-right: 10px; " @click="handleRoutine" :class="{activeLable:activeLable === 1}" disabled>
                                            <img v-if="activeLable === 1" :src="getAssetURL('routine')"  style="margin-right: 10px;" />
                                            <img  v-else :src="getAssetURL('routinegray')" style="margin-right: 10px;" />
                                            例行</el-button>
                                        <el-button @click="handleManual" :class="{activeLable:activeLable === 2}" disabled>
                                          
                                            <img  v-if="activeLable === 2" :src="getAssetURL('manualgreen')"   style="margin-right: 10px;" />
                                            <img   v-else :src="getAssetURL('manual')"  style="margin-right: 10px;" />
                                            手动</el-button>
                                    </div>
                                    <div style="margin-top: 20px;">
                                        <div v-if="updateStatus">
                                            <!-- <el-input v-model="formInline.updateDays" style="width: 160px" />  -->
                                            按 <el-input-number v-model="formInline.updateDays" style="width: 160px"
                                                :min="1" :max="30" disabled/>
                                            天计算 <span style="color:#bcbcbc ;">分群每日凌晨更新</span>
                                        </div>
                                        <!-- <div v-else>标签生成后，点击“更新”进行数据的更新</div> -->

                                    </div>
                                </div>
                            </div>
                        </div>
                    </el-scrollbar>
                    <div class="spacer" style="height: 50px;"></div>
                </div>
                <div class="footer">
                    <!-- <el-button type="primary" style="margin-right: 20px;" @click="submit(ruleFormRef,ruleFormRef2)">保存</el-button> -->
                    <el-button @click="goBackAndClearStack">返回</el-button>
                </div>

            </div>
            </div>
           
            <createLabel ref="refLabel"></createLabel>
    </div>

</template>
<script setup lang="ts">
import { onMounted, ref, watchEffect } from 'vue';
import { Plus, CirclePlus, Delete, Search, Avatar, ArrowDown, Pointer } from "@element-plus/icons-vue";
import { reactive } from 'vue'
import createLabel from './components/createLabel.vue'
import { getCustomGroupInfoById, queryLabelList, queryGroupLabelList, modifyGroup,getLabelSliceList } from '@/api'

const ruleFormRef = ref<FormInstance>()
const ruleFormRef2 = ref<FormInstance>()
import { useRoute, useRouter } from "vue-router";
import { ElMessage, FormInstance } from 'element-plus';
const { push, go} = useRouter();
const route = useRoute();

const formInline = reactive({
    createType: '规则创建',
    groupNameCn: '',
    groupNameEn: '',
    groupRemark: '',
    groupRules: '',
    labelIdList: [],
    rulesDescription: '',
    updateDays: 1,
    updateType: 1,
    groupId:"",

})
// 下拉框搜索内容
const searchData = ref('')

// onMounted(() => {
    
// })
const dynamicHeight = ref(null);
function handleResize() {
  if (dynamicHeight.value) {
    dynamicHeight.value.style.height = `${window.innerHeight - 160}px`;
  }
}
const getAssetURL = (image: any) => {
    return new URL(`../../../assets/svg/${image}.svg`, import.meta.url).href
}

const tableData = ref([])
// 获取初始化列表
const initList = async () => {
    const res = await queryLabelList({});
    const root = transformTreeData(res);
    // seletType.value = res.createType

    const clonedTree = deepClone(root);
    tableData.value = clonedTree;
    console.log(tableData.value);

    function deepClone(data) {
        const newData = [];
        for (const item of data) {
            let childrenClone = [];
            if (item.level === 0 || item.level === 1) {
                item.disabled = true
            }
            if (item.children && item.children.length > 0) {


                childrenClone = deepClone(item.children);

            }

            newData.push({ ...item, children: childrenClone });
        }
        return newData;
    }




}
function transformTreeData(data: any) {
    if (data.length == 0) return [];
    const treeData = ref<any>([]);
    for (const item of data) {
        treeData.value.push({
            ...item.data,
            children: transformTreeData(item.children),
        });
    }
    return treeData.value
}
const refLabel = ref(null);

const handleCreateLabel = () => {
    refLabel.value.handleCreateLabel("createMode")

}

const relate = ref(1)
const getDetailsList = async () => {
    const id = route.query.id;
    console.log(id,'id')
    const res = await getCustomGroupInfoById(id)
    console.log(res)
    activeLable.value =res.updateType
    formInline.updateDays = res.updateDays
    // res.updateType === 1
    if( res.updateType === 1){
        updateStatus.value = true;
    }else{
        updateStatus.value = false;
    }
    formInline.groupNameEn = res.groupNameEn
    formInline.groupNameCn = res.groupNameCn
    formInline.groupRemark = res.groupRemark
    formInline.rulesDescription = res.rulesDescription
    formInline.groupId = res.id
    

    console.log(res,'res/////////----------------------')
   const newArr = JSON.parse(res.groupRules)
   console.log(newArr)
   layers[layeredIndex.value]
   newArr.map(async (item: any) => {
    
    console.log(item,'item')
    const res = await getLabelSliceList(item.labelId)
     relate.value = item.relate
    item.options9 = res
    console.log(item.options9,'item.options9')
    // res.map(data => {
    //     // if(data.id){}
    //     data
    // })
    layers.value[0].sliceRules.push(
        {
            id:item.labelId,
        type:item.type === '='?'等于':'不等于',
        attributeValue:item.attributeValue,
        options9:res
        }
    )
    if(relate.value == 1){
    roundName.value = "且"
   }else{
    roundName.value = "或"
   }
})

//   const res = await getLabelSliceList(id)
   
console.log('list',layers.value[0].sliceRules)

}

function goBackAndClearStack() {
    if (window.history.length <= 1) {
        push({ path: "/labelManagement" });
        return false;
    } else {
        go(-1);
    }
    // push({ path: "/userPortrait" });
}
const handleClick = (index: any) => {
    isDowns[index] = !isDowns[index];

}

const form = reactive({
    name: '',
    region: '',
    date1: '',
    date2: '',
    delivery: false,
    type: [],
    resource: '',
    desc: '',
})

// 映射属性
const roundName = ref('且')
const handleLevelRelation = () => {
    roundName.value = roundName.value === '且' ? '或' : '且';
}

// 获取属性映射列表
const getAttributeList = async () => {
    
    const res = await queryGroupLabelList()
    options.value = res
    // console.log(res)



}



// 使用ref来创建一个响应式的layers数组 ==  labelSliceList
const layers = ref([{sliceRules: []}]);
// 使用watch监听layers的深层变化

//添加分层
const layeredIndex = ref(0);


const addLayered = () => {

    // 向layers数组前面添加一个新元素
    layers.value.push({sliceRules: [] });

}



const options2 = ref([
    { value: '!=', label: '不等于' },
    { value: '=', label: '等于' },
    // 更多选项...
]);

// 添加 属性
const addAttribute = () => {
    layers.value[layeredIndex.value].sliceRules.push({
        labelNameCn: '',
        labelNameEn: '',
        type: "",
        labelId: '',  
        attributeValue:'',
        options9:[]
    });
}

// 点击图标添加操作
function addItemOperator() {
    console.log(layeredIndex.value)
    layers.value[layeredIndex.value].sliceRules.push({
        labelNameCn: '',
        labelNameEn: '',
        labelId:'',
        type: "",
        attributeValue:''
    
    });
}

// 删除操作
function removeItem(item,idx) {
    // items.value.splice(index, 1);
  
    layers.value[layeredIndex.value].sliceRules.splice(idx, 1);
}

const rules = reactive({
    groupNameCn: [
        { required: true, message: '请输入标分群显示名', trigger: ['blur', 'change'] },
    ],
    groupNameEn: [
        { required: true, message: '请输入分群名称', trigger: ['blur', 'change'] },
    ],
    groupRemark: [
        { required: true, message: '请选择分类', trigger: ['blur', 'change'] },
    ]
})
const rules2 = reactive({
    sliceName: [
        { required: true, message: '请输入分层标签', trigger: ['blur', 'change'] },
    ],
    sliceDescription: [
        { required: true, message: '请输入描述信息', trigger: ['blur', 'change'] },
    ],
   
})

//创建用户标签


const submit = async (formEl: any, formE2: any) => {
    // 检查两个表单元素是否存在
    if (!formEl ) return;

    // 定义一个函数来处理表单验证和后续逻辑
    const validateAndProcess = async (form: any) => {
        return new Promise((resolve, reject) => {
            form.validate((valid: boolean, fields: any) => {
                if (valid) {
                    resolve(true);
                } else {
                    ElMessage({
                        message: '请检查表单是否有必填项',
                        type: 'warning',
                    });
                    reject(new Error('表单验证失败'));
                }
            });
        });
    };

    // 同时验证两个表单
    try {
        await Promise.all([validateAndProcess(formEl)]);
        formInline.labelIdList = []    
        // 表单验证通过后的逻辑
        const newVal = layers.value.map((item: any) => {
            console.log(item);
     
            return {
                sliceRules: JSON.stringify(item.sliceRules.map((item2: any) => {
                    console.log(item2);
                    formInline.labelIdList.push(item2.id);
                    if(item2.type === '等于'){
                        item2.type = '='
                    }else if(item2.type === '不等于'){
                        item2.type = '!='
                    }
                    return {
                        
                        labelId: item2.id,
                        type: item2.type,
                        attributeValue: item2.attributeValue,
                        relate:"1"

                      
                    };
                })),
            };
        });
        console.log(newVal);

        formInline.groupRules= newVal[0].sliceRules

        const params = {
            createType: formInline.createType,
            groupNameCn: formInline.groupNameCn,
            groupNameEn: formInline.groupNameEn,
            groupRemark: formInline.groupRemark,
            groupId:formInline.groupId,
            groupRules: formInline.groupRules,
            labelIdList: formInline.labelIdList,
            rulesDescription: formInline.rulesDescription,
            updateDays: formInline.updateDays,
            updateType: formInline.updateType,
        };

        modifyGroup(params).then(res => {
            initList();
            goBackAndClearStack();
            ElMessage({
                message: '修改成功',
                type: 'success',
            });
        }).catch(err => {
            ElMessage({
                message: '修改失败',
                type: 'warning',
            });
        });

    } catch (error) {
        // 如果有表单验证失败，这里会捕获到错误
        // console.error('表单验证失败:', error);
    }
};
const updateStatus = ref(true);
const activeLable = ref(1);
// 例行
const handleRoutine = async () => {
    updateStatus.value = true;
    activeLable.value = 1;
    formInline.updateType = 1


}
// 手动
const handleManual = async () => {
    updateStatus.value = false;
    formInline.updateType = 2
    activeLable.value = 2;
   
}
const options9 = ref([])
const  getLabelSliceLists  =  async(id:any,item:any)=>{
    const res = await getLabelSliceList(id)
    item.options9 = res
}
const handlerChange = (id:any,item:any) => {
    item.type = '';
    item.attributeValue = '';
    item.options9 = []
    getLabelSliceLists(id,item)

}

const options = ref([]);

const isDowns = reactive(new Array(options.value.length).fill(false));
const showDetails = reactive(new Array(options.value.length).fill(false)); // 为每个列表项维护一个showDetail状态
const activeMenu = ref(0)
const scrollTo = (sectionId, item) => {
    // console.log(sectionId, item)
    if (item === '分群规则') {
        activeMenu.value = 1
    } else {
        activeMenu.value = 0
    }

    const targetElement = document.querySelector(`#${sectionId}`);
    console.log(targetElement, 'targetElement')
    targetElement.scrollIntoView({ behavior: 'smooth' });
}
// 切换tab
const handlerTab = (index: any) => {

    layeredIndex.value = index
}
const handleDel = (index: any) => {
    layers.value.splice(index, 1)
}

const handlerSelectItem = (item: any, index: any) => {
   
    layers.value[layeredIndex.value].sliceRules[index].labelNameCn = item.labelNameCn
    layers.value[layeredIndex.value].sliceRules[index].labelNameEn = item.labelNameEn
    layers.value[layeredIndex.value].sliceRules[index].labelId = item.id
    //  console.log(item,layers.value)
    isDowns[index] = false;
    showDetails[index] = false;

}
// 下拉
const detailTitle = ref('')
function toggleDetail(idx, isVisible, item: any) {
    showDetails[idx] = isVisible;
    detailTitle.value = item
}


onMounted(async() => {
    handleResize(); // 初始调用
    window.addEventListener('resize', handleResize);
   await initList()
    // 获取属性映射列表
    await getAttributeList()
    await getDetailsList()
})

</script>

<style scoped lang="scss">
.addlayer {
    width: 100%;
    height: 50px;
    margin-top: 20px;
    padding-left: 10px;
    background: #e6e6e6;
}

::v-deep .el-input-group__append {
    background-color: #e6e6e6 !important;
}

.addSection {
    width: 80px;
    cursor: pointer;
    margin-top: 20px;
    color: #007bff;
}

.el-divider--horizontal {
    margin: 8px 0 8px 0;
}

.formItem {
    height: 40px !important;
}

.activeLable {
    border: 1px solid #00b42a !important;
    color: #00b42a;
}

.activeLayere {
    border: 1px solid #0678fb !important;
    color: #0678fb;
}

::v-deep .el-select .el-input__wrapper {
    height: 40px;
}

.input-with-select .el-input-group__prepend {
    border: none;
    outline: none;
    //   background-color: var(--el-fill-color-blank);
}

.down {
    position: relative;
    margin-right: 20px;

    .d_r {
        width: 240px;
        height: 100px;
        position: absolute;
        right: -240px;
        top: 40px;
        padding: 10px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .li {
        font-size: 14px;
        color: #403d3d;
        padding: 10px;
        cursor: pointer;
    }
}

.search {
    border: none;
    outline: none;
}

.isDown {
    width: 240px;
    margin-top: 5px;
    height: 260px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 2%;

}

.l_select {
    display: flex;
}

.custom-header {
    .el-checkbox {
        display: flex;
        height: unset;
    }
}

.basic {
    width: 98%;
    min-height: 400px;
    border: 1px solid #e6e6e6;
    border-radius: 6px;
    margin-bottom: 30px;
    // padding: 20px;
    margin: 20px;
    .contant {
        padding: 30px;
    }

    .circle {
        width: 15px;
        height: 15px;
        border-radius: 100%;
        background: #254f7a;
        margin-right: 10px;
    }

    .heander {
        width: 100%;
        line-height: 20px;
        display: flex;
        padding: 15px;
        font-size: 18px;
        font-weight: bold;
        height: 50px;
        background: #e5ebf0;
    }
}

.attribute {
    padding: 20px;
    width: 100%;
    border: 1px solid #e6e6e6;
    .left {
    display: flex;
    align-items: center;
    position: relative;
    .round {
      width: 30px;
      height: 30px;
      border-radius: 50%;
      background-color: white;
      border: 1px solid black;
      font-size: 14px;
      color: black;
      margin: auto;
      position: relative;
      z-index: 3;
      text-align: center;
      line-height: 26px;
    }
    .border {
      position: absolute;
      left: 50%;
      top: 0;
      bottom: 0;
      border-left: 1px solid black;
    }
  }
}

.layeredLevel {
    position: relative;
    margin: 20px 20px 20px 0;
    width: 200px;

    .delete-icon {
        /* 如果使用绝对定位，请确保这个类被应用到图标上 */
        position: absolute;
        right: 10px;
        /* 图标距离按钮右侧的距离 */
        top: 50%;
        /* 图标垂直居中对齐 */
        transform: translateY(-50%);
        /* 向上移动图标自身高度的一半以实现垂直居中 */

    }
}

.layered {
    margin: 20px 20px 20px 0;
    width: 200px;

}

::v-deep .el-textarea__inner {
    height: 250px;
}

.tree-wrapper {
    // padding: 20px;
    border-radius: 6px;
    background-color: #fff;
    box-shadow: 0px 2px 22px rgba(0, 0, 0, 0.06);
    margin-right: 20px;
    overflow-y: auto;
}

.item {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
}

.footer {
    width: 68.5%;
    background-color: #ffffff;
    display: flex;
    justify-content: center;
    padding: 10px 5px;
    z-index: 999;
    position: fixed;
    bottom: 40px;

}

.active {
    border: 1px solid #007bff;
}

.sqlTop {
    width: 240px;
    height: 40px;
    border: 1px solid #e6e6e6;
    color: #807d7d;
    border-radius: 5%;
    cursor: pointer;
    padding: 8px;
    display: flex;
    justify-content: space-between;
}

.demo-date-picker {
    display: flex;
    width: 240px !important;
    padding: 0;
    flex-wrap: wrap;
}

.demo-date-picker .block {
    padding: 30px 0;
    text-align: center;
    border-right: solid 1px var(--el-border-color);
    flex: 1;
}

.demo-date-picker .block:last-child {
    border-right: none;
}

::v-deep .el-date-editor--daterange {
    width: 240px !important;
}

.demo-date-picker .demonstration {
    display: block;
    color: var(--el-text-color-secondary);
    font-size: 14px;
    margin-bottom: 20px;
}

.screen {
    padding-top: 8px;
    padding-bottom: 8px;
    margin-left: 15px;
    cursor: pointer;
}



.el-row:last-child {
    margin-bottom: 0;
}

.el-col {
    padding-top: 10px;
    padding-left: 20px;
    border-radius: 4px;
}

.grid-content {
    border-radius: 4px;
    line-height: 50px;
}

.activeMenu {

    color: #00b42a;
}

.manus {
    padding: 10px 0;
    cursor: pointer;
}
</style>