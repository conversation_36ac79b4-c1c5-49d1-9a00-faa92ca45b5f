<template>
  <div class="step-box">
    <div v-for="(item, index) in props.stepList" :key="index" :class="index + 1 <= props.active ? 'active' : ''"
      @click="clickBtn(index + 1)">
      <span>{{ item }}</span>
    </div>
  </div>
</template>
<script lang="ts" setup>
const props = withDefaults(
  // 接收父组件传来的数据
  defineProps<{
    active: number
    stepList: string[]
  }>(),
  // 设置默认值
  {
    active: () => 1,
    stepList: () => ['Step 1', 'Step 2', 'Step 3'],
  },
)
const emit = defineEmits(['clickChoose'])
const clickBtn = (index: number) => {
  emit('clickChoose', index)
}
</script>
<style lang="scss" scoped>
.step-card {
  margin-bottom: 20px;

  :deep(.el-card__header) {
    border: none;
  }
}

.step-box {
  width: 100%;
  height: 40px;
  background: #fff;
  display: flex;
  box-sizing: border-box;
}

.step-box div {
  flex: 1;
  padding: 0 20px;
  line-height: 40px;
  background: #f2f5f7;
  display: inline-block;
  position: relative;
  margin-right: 30px;
}

.step-box div span {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.step-box div::after {
  content: '';
  display: block;
  border-top: 20px solid #f2f5f7;
  border-bottom: 20px solid #f2f5f7;
  border-left: 20px solid #fff;
  position: absolute;
  right: -20px;
  top: 0;
}

.step-box div::after {
  content: '';
  display: block;
  border-top: 20px solid transparent;
  border-bottom: 20px solid transparent;
  border-left: 20px solid #f2f5f7;
  position: absolute;
  right: -20px;
  top: 0;
  z-index: 10;
}

.step-box div::before {
  content: '';
  display: block;
  border-top: 20px solid #f2f5f7;
  border-bottom: 20px solid #f2f5f7;
  border-left: 20px solid #fff;
  position: absolute;
  left: -20px;
  top: 0;
}

.step-box div:first-child {
  padding: 0 20px 0 40px;
}

.step-box div:first-child::before {
  display: none;
}

.step-box div:last-child {
  padding: 0 40px 0 20px;
}

.step-box div:last-child::after {
  display: none;
}

.step-box .active {
  background: #254f7a;
  color: #ffffff;
}

.step-box .active::after {
  border-left-color: #254f7a;
}

.step-box .active::before {
  border-top-color: #254f7a;
  border-bottom-color: #254f7a;
}
</style>
