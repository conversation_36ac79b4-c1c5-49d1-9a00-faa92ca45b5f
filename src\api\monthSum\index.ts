import {request} from "@/utils/request";

// 导入价格申报单 /agencySaleFee/importPriceDeclarationForm
export const importPriceDeclarationForm = (formData: any) => {
  return request.post({
    url: "/agencySaleFee/importPriceDeclarationForm",
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  });
};
export const importSalePackageBinding = (formData: any) => {
  return request.post({
    url: "/agencySaleFee/importSalePackageBinding",
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  });
};
// 导入用户电量单 /agencySaleFee/importUserElectricityForm
export const importUserElectricityForm = (formData: any) => {
  return request.post({
    url: "/agencySaleFee/importUserElectricityForm",
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  });
};
//  导入结算单
export const importSettlementstatementm = (formData: any) => {
  return request.post({
    url: "/userStatement/importUserStatement",
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  });
};

//获取所有电压等级
export const queryVoltageGrade = () => {
  return request.get({
    url: `/userStatement/queryVoltageGrade`
  });
};
// 获取所有用电类别 get请求
export const queryElectricityUsetype = () => {
  return request.get({
    url: `/userStatement/queryElectricityUsetype`
  });
};

//根据零售用户名称查询用户结算单
export const queryRetailUserName = (data: any) => {
  return request.post({
    url: "/userStatement/queryUserStatementByUserName",
    data
  });
};
//条件查询零售用户
export const getRetailUserlist = (data: any) => {
  return request.post({
    url: "/userStatement/queryRetailUserName",
    data
  });
};
// 导入偏差考核费明细表 /deviationCost/importDeviationCostForm
export const importDeviationCostForm = (formData: any) => {
  return request.post({
    url: "/deviationCost/importDeviationCostForm",
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  });
};
// 分页查询代理销售费用信息 /agencySaleFee/queryPage
export const agencyQueryPage = (data: any) => {
  return request.post({
    url: "/agencySaleFee/queryPage",
    data
  });
};
// 获取结算单列表
export const getStatementlist = (data: any) => {
  return request.post({
    url: "/userStatement/queryUserStatementByPage",
    data
  });
};
// 查询代理销售套餐 /agencySaleFee/queryAgentSalePackage/{agencySaleFeeId}
export const agencyQueryAgentSalePackage = (agencySaleFeeId: any) => {
  return request.get({
    url: `/agencySaleFee/queryAgentSalePackage/${agencySaleFeeId}`
  });
};
// 分页查询偏差费用信息 /deviationCost/queryPage
export const deviationQueryPage = (data: any) => {
  return request.post({
    url: "/deviationCost/queryPage",
    data
  });
};
// 保存代理销售费用 /agencySaleFee/saveAgencySaleFee
export const agencySaveAgencySaleFee = (data: any) => {
  return request.post({
    url: "/agencySaleFee/saveAgencySaleFee",
    data
  });
};
// 保存偏差费用 /deviationCost/saveDeviationCost
export const deviationSaveDeviationCost = (data: any) => {
  return request.post({
    url: "/deviationCost/saveDeviationCost",
    data
  });
};


// 导出费用申报
export const exportDataList = (data: any) => {
  const res = request.post<any>(
      {
        url: '/agencySaleFee/exportAgencySaleFee',
        responseType: 'blob',
        data,
      },
      { isTransformResponse: false },
  )
  return res
}

// 结算电费分析导出
export const exportFeeDataListAPI = (data: any) => {
  const res = request.post<any>(
      {
        url: '/userStatement/exportUserStatementInfo',
        responseType: 'blob',
        data,
      },
      { isTransformResponse: false },
  )
  return res
}
// 量价费用导出模版 
export const exportDataList1 = (data: any) => {
  const res = request.post<any>(
      {
        url: '/deviationCost/exportDeviationCost',
        responseType: 'blob',
        data,
      },
      { isTransformResponse: false },
  )
  return res

}
// 用户结算电量预览
export const previewOfUserInvoicesAPI  = (data: any) => {
  return request.post<any>(
      {
        url: '/agencySaleFee/userElectricityPreview',
        responseType: 'blob',
        data,
      },
      {isTransformResponse: false},
  )
}
//偏差费用导入数据预览
export const previewOfDeviationCostAPI = (data: any) => {
  return request.post<any>(
      {
        url: '/agencySaleFee/deviationCostPreview',
        responseType: 'blob',
        data,
      },
      {isTransformResponse: false},
  )
}

//价格申报数据预览
export const previewOfPriceDeclarationAPI = (data: any) => {
  return request.post<any>(
      {
        url: '/agencySaleFee/priceDeclarationPreview',
        responseType: 'blob',
        data,
      },
      {isTransformResponse: false},
  )
}

//用户套餐数据预览
export const previewOfSalePackageBindingAPI = (data: any) => {
  return request.post<any>(
      {
        url: '/agencySaleFee/userPackagePreview',
        responseType: 'blob',
        data,
      },
      {isTransformResponse: false},
  )
}

// 导出用户结算单模板
export const exportUserStatementTemplateAPI = () => {
  const res = request.post<any>(
      {
        url: '/userStatement/exportUserStatement',
        responseType: 'blob',
      },
      {isTransformResponse: false},
  )
  return res
}

