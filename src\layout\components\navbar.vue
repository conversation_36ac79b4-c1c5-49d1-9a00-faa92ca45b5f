<script setup lang="ts">
import mixNav from "./sidebar/mixNav.vue";
import { useNav } from "@/layout/hooks/useNav";
import Breadcrumb from "./sidebar/breadCrumb.vue";
import topCollapse from "./sidebar/topCollapse.vue";
import LogoutCircleRLine from "@iconify-icons/ri/logout-circle-r-line";
import Setting from "@iconify-icons/ri/settings-3-line";


const {
  layout,
  device,
  logout,
  onPanel,
  pureApp,
  username,
  userAvatar,
  userAvatarNew,
  avatarsStyle,
  toggleSideBar,
  title
} = useNav();

import {reactive, ref} from 'vue'
import { WarningFilled } from '@element-plus/icons-vue'
import {getTime, getTopRightElement, getTopRightElementReturn} from "@/utils/time";
import {toRefs} from "vue-demi";
const centerDialogVisible = ref(false)
let { month, minutes, week, day, ch, hours } = toRefs(
    reactive(getTopRightElement()),
)

const onLine = ref()
setInterval(() => {
  month.value = getTopRightElement().month
  minutes.value = getTopRightElement().minutes
  week.value = getTopRightElement().week
  day.value = getTopRightElement().day
  ch.value = getTopRightElement().ch
  hours.value = getTopRightElement().hours
}, 1000)
</script>

<template>
  <div class="navbar bg-[#fff] shadow-sm shadow-[rgba(0, 21, 41, 0.08)] dark:shadow-[#0d0d0d]">
    <topCollapse v-if="device === 'mobile'" class="hamburger-container" :is-active="pureApp.sidebar.opened"
      @toggleClick="toggleSideBar" />
    <div class="header-logo-wrapper">
      <img src="/src/assets/images/BigScreen/stateGrid.png" alt="logo" style="width: 56px;height: 56px" />
      <span class="logo-title">{{ title }}</span>
    </div>
    <!-- <Breadcrumb
      v-if="layout !== 'mix' && device !== 'mobile'"
      class="breadcrumb-container"
    /> -->

    <mixNav v-if="layout === 'mix'" />

    <div v-if="layout === 'vertical'" class="vertical-header-right">
      <div class="time">
        {{ month }}月{{ day }}日 星期{{ week }} {{ ch }}{{ hours }}:
        {{ minutes }}
      </div>
      <!-- 退出登录 -->
      <el-dropdown trigger="click">
        <!-- <span class="select-none el-dropdown-link navbar-bg-hover">
          <img :src="userAvatar" :style="avatarsStyle" />
          <p v-if="username" class="dark:text-white">{{ username }}</p>
        </span> -->
        <span class="select-none el-dropdown-link navbar-bg-hover">
<!--          <img :src="userAvatar" :style="avatarsStyle" />-->
          <img :src="userAvatarNew" style="width: 24px; height: 24px; margin: 0px 10px; border-radius: 50%;">
          <p v-if="username" class="text-white">{{ username }}</p>
        </span>
        <template #dropdown>
          <el-dropdown-menu class="logout">
            <!-- <el-dropdown-item @click="logout"> -->
            <el-dropdown-item @click="centerDialogVisible = true">
              <IconifyIconOffline :icon="LogoutCircleRLine" style="margin: 5px" />
              <span class="pt-[3px]">退出系统</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
  <!-- 退出系统 -->
  <el-dialog title="退出系统" v-model="centerDialogVisible" width="30%" align-center center class="dialog"
    :append-to-body="true">
    <div style="
        text-align: center;
        line-height: 200px;
        height: 200px;
        font-size: 16px;
      ">
      您确认退出系统吗?
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="info" @click="centerDialogVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="logout">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.navbar {
  width: 100%;
  height: 59px;
  overflow: hidden;
  background: #254f7a;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .hamburger-container {
    float: left;
    height: 100%;
    line-height: 64px;
    cursor: pointer;
  }

  .header-logo-wrapper {
    display: flex;
    align-items: center;
    margin-left: 20px;

    img {
      width: 32px;
      height: 32px;
    }

    .logo-title {
      font-size: 20px;
      margin-left: 16px;
      font-weight: 700;
      color: #fff;
    }
  }

  .vertical-header-right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-right: 10px;
    min-width: 300px;
    height: 64px;
    color: #000000d9;

    .el-dropdown-link {
      display: flex;
      align-items: center;
      justify-content: space-around;
      height: 64px;
      padding: 10px;
      color: #000000d9;
      cursor: pointer;

      p {
        font-size: 14px;
      }

      img {
        width: 22px;
        height: 22px;
        border-radius: 50%;
      }
    }
  }

  .breadcrumb-container {
    float: left;
    margin-left: 16px;
  }
}

.logout {
  width: 120px;

  ::v-deep(.el-dropdown-menu__item) {
    display: inline-flex;
    // flex-wrap: wrap;
    min-width: 100%;
  }
}

.select-none.el-dropdown-link.navbar-bg-hover:hover {
  background-color: #254f7a;
}

.time {
  margin-right: 10px;
  font-size: 16px;
  color: white;
}
</style>
