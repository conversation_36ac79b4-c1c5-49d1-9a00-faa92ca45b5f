/**
 * 嵌入模式工具函数
 * 用于管理系统的嵌入模式状态
 */

const EMBEDDED_MODE_KEY = 'system-embedded-mode';

/**
 * 检查当前是否处于嵌入模式
 * @returns {boolean} 是否为嵌入模式
 */
export function isEmbeddedMode(): boolean {
  return localStorage.getItem(EMBEDDED_MODE_KEY) === 'true';
}

/**
 * 启用嵌入模式
 */
export function enableEmbeddedMode(): void {
  localStorage.setItem(EMBEDDED_MODE_KEY, 'true');
  console.log('嵌入模式已启用');
}

/**
 * 禁用嵌入模式
 */
export function disableEmbeddedMode(): void {
  localStorage.removeItem(EMBEDDED_MODE_KEY);
  console.log('嵌入模式已禁用');
}

/**
 * 切换嵌入模式状态
 * @returns {boolean} 切换后的状态
 */
export function toggleEmbeddedMode(): boolean {
  const currentMode = isEmbeddedMode();
  if (currentMode) {
    disableEmbeddedMode();
  } else {
    enableEmbeddedMode();
  }
  return !currentMode;
}

/**
 * 从URL参数检测并设置嵌入模式
 * @param url 要检测的URL，默认为当前页面URL
 * @returns {boolean} 是否检测到hide参数
 */
export function detectAndSetEmbeddedModeFromUrl(url?: string): boolean {
  const targetUrl = url || window.location.href;
  const urlParams = new URLSearchParams(new URL(targetUrl).search);
  const hideParam = urlParams.get('hide');
  
  if (hideParam === 'true') {
    enableEmbeddedMode();
    return true;
  }
  
  return false;
}

/**
 * 清理URL中的hide参数
 * @param replaceHistory 是否替换浏览器历史记录，默认为true
 */
export function cleanHideParamFromUrl(replaceHistory: boolean = true): void {
  const url = new URL(window.location.href);
  if (url.searchParams.has('hide')) {
    url.searchParams.delete('hide');
    const cleanUrl = url.toString();
    
    if (replaceHistory) {
      window.history.replaceState({}, document.title, cleanUrl);
    } else {
      window.history.pushState({}, document.title, cleanUrl);
    }
    
    console.log('已清理URL中的hide参数');
  }
}

/**
 * 获取嵌入模式的调试信息
 * @returns {object} 调试信息对象
 */
export function getEmbeddedModeDebugInfo() {
  return {
    isEmbedded: isEmbeddedMode(),
    storageKey: EMBEDDED_MODE_KEY,
    storageValue: localStorage.getItem(EMBEDDED_MODE_KEY),
    currentUrl: window.location.href,
    hasHideParam: new URLSearchParams(window.location.search).has('hide'),
    hideParamValue: new URLSearchParams(window.location.search).get('hide')
  };
}
