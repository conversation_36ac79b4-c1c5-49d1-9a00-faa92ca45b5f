import dayjs from "dayjs";
export const contractColumns: TableColumnList = [
  {
    label: "序号",
    width: 80,
    type: "index"
  },
  {
    label: "姓名",
    prop: "name"
  },
  {
    label: "性别",
    prop: "sex",
    formatter: ({ sex }) => (sex !== null ? (sex === 1 ? "男" : "女") : "")
  },
  {
    label: "角色",
    prop: "role"
  },
  {
    label: "部门职务",
    prop: "post"
  },
  {
    label: "手机号码",
    prop: "mobile"
  },
  {
    label: "固定电话",
    prop: "fixedTelephone"
  },
  {
    label: "传真号码",
    prop: "fax"
  },
  {
    label: "电子邮箱",
    prop: "email"
  }
];
export const pointColumns: TableColumnList = [
  {
    label: "序号",
    width: 80,
    type: "index"
  },
  {
    label: "户号",
    prop: "accountNo"
  },
  {
    label: "计量点ID",
    prop: "meterCode"
  },
  {
    label: "计量点名称",
    prop: "meterName"
  },
  {
    label: "状态",
    prop: "status",
    formatter:()=>'有效'
  },
  {
    label: "备注",
    prop: "remark"
  }
];
