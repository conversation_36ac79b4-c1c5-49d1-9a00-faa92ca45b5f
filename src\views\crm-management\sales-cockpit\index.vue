<template>

  <section>
    <div class="app-search-card" style="margin-bottom: 20px">
      <div class="app-form-group">
        <div class="ml-[20px]">
          <span>选择年份：</span>
          <el-date-picker style="width: 140px" v-model="searchInfo.time" type="year" placeholder="请选择"
            format="YYYY" value-format="YYYY" />
        </div>
      </div>
      <div class="app-btn-group">
        <el-button class="filter-item" type="primary" @click="handlerSearch">查询</el-button>
        <el-button class="filter-item" @click="handleReset">重置</el-button>
      </div>
    </div>
    <div class="sales-cockpit-wrapper">
      <div class="left-card">
        <div>
          <div class="card-title p-[20px]">年度累计销售额（MWh）</div>
          <div class="value">{{ totalCount.toFixed(2) }}</div>
          <div class="name">年度累计销售额</div>
        </div>
        <div class="mt-[50px]">
          <battle-sandTable  :time="searchInfo.time"/>
        </div>
      </div>

      <div class="right-card">
        <div class="sales-card-box">
          <div class="card-title">年度销售排行榜</div>
          <div class="card-content">
            <Bar :series="barSeries" :y-data="yData" @update="handleContractChange" height="100%" />
          </div>
        </div>

        <div class="sales-card-box mt-[20px]">
          <div class="card-title">商机转化漏斗</div>
          <div class="card-content">
            <!-- <Funnel :series="series" :total-series="totalSeries" :legend-data="legendData" @change="handleChange"
            height="100%" /> -->
            <div class="funnel-box">
              <img src="/src/assets/images/BigScreen/funel.png" class="h-[170px] w-[320px]" alt="" />
              <div class="legend-box">
                <div class="content" v-for="(item, index) in funnelSeries" :key="index" @click="handleChange(item.id)">
                  <div class="label">{{ item.name }}</div>
                  <div class="value">({{ item.value }})</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <el-dialog v-model="contractDialogVisible" title="合同详情" width="80%">
        <contract-list :query-id="queryId"  :time="searchInfo.time"/>
      </el-dialog>

      <el-dialog v-model="dialogVisible" title="商机详情" :time="searchInfo.time" width="80%">
        <opportunity-list :query-type="queryType" />
      </el-dialog>
    </div>
  </section>
</template>

<script setup lang="ts">
import {ref, onMounted, watch, reactive, onActivated} from "vue";
import Bar from "./components/Bar.vue";
import Funnel from "./components/Funnel.vue";
import opportunityList from "./components/opportunityList.vue";
import contractList from "./components/contractList.vue";
import BattleSandTable from "@/views/crm-management/customer-battle-sandtable/index.vue";
import {
  getOpportunityTotalApi,
  getContractSumApi
} from "@/api/customer-management/index";
import { max } from 'lodash-es'

import {
  getTotalTop4Api,
} from "@/api/customer-management/index";

const funnelSeries = ref([]);
async function getList1() {
  const res = await getTotalTop4Api();
  if (res.data) {
    funnelSeries.value = res.data.map(item => {
      return {
        name: item.name,
        value: item.count,
        id: item.id
      };
    });
  }
}


defineOptions({
  name: "SalesCockpit"
});
import {
  getAllSalesmanListByTime, //获取营销人员列表
} from '@/api'
import dayjs from "dayjs";
const dialogVisible = ref<boolean>(false);
const contractDialogVisible = ref<boolean>(false);
const barSeries = ref([
  {
    name: "销售额",
    type: "bar",
    markPoint: {
      symbolRotate: -90,
      data: [
        {
          type: "max",
          name: "Max"
        },
        { type: "min", name: "Min" }
      ]
    },
    markLine: {
      data: [{ type: "average", name: "Avg" }]
    },
    data: []
  }
]);
const yData = ref([]);
const totalCount = ref<number>(0);
const legendData = ref([]);
const list = ref([]);
const queryType = ref("");
const queryId: any = ref();
const series = ref([]);
const totalSeries = ref([]);
async function getList() {
  //  console.log('searchInfo', searchInfo.time);
  const array = await getAllSalesmanListByTime(searchInfo.time );
  if (array) {
    yData.value = array?.map(i => i.name);
    barSeries.value[0].data = array?.map(item => {
      return {
        value: item.annualElectricity,
        id: Number(item.id)
      };
    });

    totalCount.value = array.reduce((pre, current) => {
      return (pre || 0) + (Number(current.annualElectricity) || 0);
    }, 0);
  } else {
    totalCount.value = 0;
    barSeries.value[0].data = [];
  }
  const res = await getOpportunityTotalApi();
  if (res.data) {
    list.value = res.data;
    // legendData.value = res.data.map(i => i.name);
    series.value = res.data.map(item => {
      return {
        name: `${item.name}(${item.count})`,
        value: item.count,
        id: item.id
      };
    });
    // legendData.value = series.value.map(i => `${i.name}(${i.value})`)
    // console.log(legendData.value,'legendData.value');
    const maxValue = max(series.value.map(i => i.value))
    if (maxValue) {
      const num = Number((maxValue / 3).toFixed(0)) + maxValue % 3
      const arr = series.value.map((item, index) => {
        return {
          name: item.name,
          value: (index + 1) * num,
          id: item.id
        }
      }).map(i => i.value).reverse()
      totalSeries.value = series.value.map((item, index) => {
        return {
          name: item.name,
          value: arr[index],
          id: item.id
        }
      })
    }
  }
}

const searchInfo = reactive({
  time: dayjs().format("YYYY"),
});

const handleReset = ()=>{
  searchInfo.time = "";
  getList();
  getList1();
}
const handlerSearch = ()=>{
  console.log(searchInfo.time,'searchInfo')

  getList();
  getList1();
}
// async function getList() {
//   const array = await getContractSumApi();
//   if (array.data) {
//     yData.value = array.data?.map(i => i.entryName);
//     barSeries.value[0].data = array.data?.map(item => {
//       return {
//         value: item.sumElectricityQty,
//         id: item.entryId
//       };
//     });
//     totalCount.value = array.data.reduce((pre, current) => {
//       return pre + current.sumElectricityQty;
//     }, 0);
//   } else {
//     totalCount.value = 0;
//     barSeries.value[0].data = [];
//   }
//   const res = await getOpportunityTotalApi();
//   if (res.data) {
//     list.value = res.data;
//     // legendData.value = res.data.map(i => i.name);
//     series.value = res.data.map(item => {
//       return {
//         name: `${item.name}(${item.count})`,
//         value: item.count,
//         id: item.id
//       };
//     });
//     // legendData.value = series.value.map(i => `${i.name}(${i.value})`)
//     // console.log(legendData.value,'legendData.value');
//     const maxValue = max(series.value.map(i => i.value))
//     if (maxValue) {
//       const num = Number((maxValue / 3).toFixed(0)) + maxValue % 3
//       const arr = series.value.map((item, index) => {
//         return {
//           name: item.name,
//           value: (index + 1) * num,
//           id: item.id
//         }
//       }).map(i => i.value).reverse()
//       totalSeries.value = series.value.map((item, index) => {
//         return {
//           name: item.name,
//           value: arr[index],
//           id: item.id
//         }
//       })
//     }
//   }
// }
async function handleChange(id) {
  dialogVisible.value = true;
  queryType.value = id;
}

async function handleContractChange(id) {
  // console.log(id)
  contractDialogVisible.value = true;
  queryId.value = id;
 
}

onMounted(() => {
  getList();
  getList1();
});
onActivated(() => {
  getList();
  getList1();
})
</script>

<style lang="scss" scoped>
.sales-cockpit-wrapper {
  height: 100%;
  display: flex;

  .left-card {
    width: 50%;
    height: 200px;
    // padding: 20px;
    box-sizing: border-box;
    border-radius: 6px;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 2px 22px rgba(0, 0, 0, 0.06);
    margin-bottom: 20px;
    margin-right: 20px;

    .value {
      font-size: 60px;
      font-weight: 700;
      color: #f26e74;
      text-align: center;
    }

    .name {
      font-size: 20px;
      color: #f26e74;
      text-align: center;
    }
  }

  .right-card {
    width: calc(50% - 20px);
    margin-right: 20px;
  }

  .card-title {
    font-size: 18px;
    padding-bottom: 5px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }

  .sales-card-box {
    padding: 20px;
    box-sizing: border-box;
    border-radius: 6px;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 2px 22px rgba(0, 0, 0, 0.06);
    width: 100%;
    height: 400px;
    // height: calc(100vh - 426px);

    .card-content {
      height: 100%;
    }
  }
}

.funnel-box {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 40px;
  margin-top: 50px;

  .legend-box {
    // position: absolute;
    // top: -5px;
    // right: 40px;
    color: #000;
    font-size: 14px;

    .content {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 10px;
      cursor: pointer;

      &:first-child {
        margin-top: 0;
      }

      .value {
        color: #0e07da;
        font-size: 12px;
      }
    }
  }
}
</style>
