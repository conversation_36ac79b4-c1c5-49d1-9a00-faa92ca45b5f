import { request } from '@/utils/request'

// 持仓概览
export const overviewOfHoldings = (data: any) => {
  const res = request.post<any>({
    url: '/contractDetails/overviewOfHoldings',
    data,
  })
  return res
}
// 新增市场化信息
export const addHolding = (data: any) => {
  const res = request.post<any>({
    url: '/contracts/addHolding',
    data,
  })
  return res
}
// 修改市场化信息
export const updateHolding = (data: any) => {
  const res = request.post<any>({
    url: '/contracts/updateHolding',
    data,
  })
  return res
}
// 持仓分析
export const getHoldingViewInfo = (data: any) => {
  const res = request.post<any>({
    url: '/contractDetails/getHoldingViewInfo',
    data,
  })
  return res
}
