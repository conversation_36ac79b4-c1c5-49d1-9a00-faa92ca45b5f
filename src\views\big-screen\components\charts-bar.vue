<template>
  <div ref="chartRef" :style="{ height, width }" />
</template>

<script setup lang="ts">
import {
  ref,
  computed,
  onMounted,
  watch,
  type Ref,
  PropType,
  nextTick,
  onActivated
} from "vue";
import { useAppStoreHook } from "@/store/modules/app";
import {
  delay,
  useDark,
  useECharts,
  type EchartOptions
} from "@pureadmin/utils";
import * as echarts from "echarts/core";
import { triggerWindowResize } from "@/utils/event";
import type { EChartsOption } from "echarts";
import { MarkPointComponent, MarkLineComponent } from "echarts/components";
echarts.use([MarkPointComponent, MarkLineComponent]);
const emit = defineEmits(["update"]);
const props = defineProps({
  height: {
    type: String as PropType<string>,
    default: "100%"
  },
  width: {
    type: String as PropType<string>,
    default: "100%"
  },
  xData: {
    type: Array as PropType<string[] | number[]>,
    default: () => []
  },
  yData: {
    type: Array as PropType<string[] | number[]>,
    default: () => []
  },
  xAxisName: {
    type: String as PropType<string>,
    default: "MWh"
  },
  series: {
    type: Array as PropType<string[] | number[]>,
    default: () => []
  }
});
const { isDark } = useDark();

const theme: EchartOptions["theme"] = computed(() => {
  return isDark.value ? "dark" : "light";
});

const chartRef = ref<HTMLDivElement | null>(null);
const { setOptions, resize } = useECharts(chartRef as Ref<HTMLDivElement>, {
  theme
});

const getOption = (): EChartsOption => {
  return {
    tooltip: {
      show: true,
      formatter: function (params) {
        // console.log(params);

        let res =
          '<div><h2 style="font-size: 14px;">' +
          params.name +
          "</h2>" +
          '<p style="font-size: 12px;">' +
          "代理电量" +
          "：" +
          params.value +
          "<div>";
        return res;
      }
    },
    grid: {
      bottom: "6%",
      left: "20%",
      right: "16%"
    },
    legend: {
      show: false
    },
    xAxis: {
      type: "value",
      name: props.xAxisName,
      nameTextStyle: {
        fontSize: 12
      },
      boundaryGap: [0, 0.01],
      axisLine: {
        show: true,
        lineStyle: {
          color: "#456192"
        }
      },
      axisLabel: {
        color: "#fff",
        opacity: 0.8
      },
      splitNumber: 3,
      splitLine: {
        show: false
      }
    },
    yAxis: {
      type: "category",
      axisLine: {
        show: true,
        lineStyle: {
          color: "#456192"
        }
      },
      axisLabel: {
        color: "#fff",
        opacity: 0.8
      },
      axisTick: {
        show: false
      },
      data: props.yData
    },
    // series: props.series,
    series: [
      {
        name: "XXX",
        type: "pictorialBar",
        symbol:
          "image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADoAAAA6CAMAAADWZboaAAAAZlBMVEUAAABe3uVe3+Vf3uVf3+Zf3uVg3+Zg3+Zf3+Vi4OZh4OZg3+Z86/Bh3+Zi4Odj4Odi4OZ86/B76/B86/Bj4ed56+9x5+xn4umB7/N87PB36e+A7/N+7fF/7vJ/7vJ+7fGA7/OB7/PReX+lAAAAIXRSTlMABQkVDREmIhk3MR10LEFFPHh7cUprXE35h2XnqMLAp+mHAG9cAAAB5ElEQVRIx83WjU7CMBQFYIoiKMqU/XUboHv/l/Tce7t2XamDNSacETEmX86tlK2rx4py150o+MstMBLwWRfHKo6JCVxLnvmFGBjFQ58oF1//sUZhGy/ClSTWObgnL4O+bkeN4nY2okfNMbkRt9/vtxz8InoTsWplJSCzFxPmO8+GpSIByX3YQAuGDWtRKhKjCnxDXhF6Z4yxnZ20Wgko7BMRDmxtSGVaI4kdTIgb+zTYoJQlIMlDlmUFgrcDWWC201qSayqlTkiCddWWeV62VU0YlnpRi9VOKaSUsiyq/N0krwq2Ugt7lVpZl5BfHNiytjagMi+XYp0kCR45hMlivVQrE/uU5pXSrCB5bM6d1t2lOZItMqmliT3q5uVxqxzyW/ccfYLNKx7ZTeykMvNyac2yt2Fbc61MHLSC0rwoxbiNdlQ3GBm1NLHQsHUrtEXppR/ljNpW6DbSCoqlFiVoN6YdaFlgsSFVPs1BdT8OaB5QyQzVcaqWDows/zepxR8ObLglTrdtCRVuRNj4Rrxh+//0ke2f8KVL+Kon3GCSbmsJN9OUW3j6g0Ns+LgCij2u0h+Sghc8mlMPBMgdx5DFh59VmOVHrvmDnoNxCz3J7MFWsMuaLyR089xz/xhlfijvwutR8gv3zk6BLUUeCgAAAABJRU5ErkJggg==",
        symbolSize: [30, 30],
        symbolOffset: [15, 0],
        symbolPosition: "end",
        z: 12,
        itemStyle: {
          color: "#fff"
        },
        data: props.series
      },
      {
        name: "条",
        type: "bar",
        showBackground: true,
        backgroundStyle: {
          borderRadius: 20,
          opacity: 0.55
        },
        yAxisIndex: 0,
        data: props.series,
        barWidth: 8,
        // align: left,
        itemStyle: {
          barBorderRadius: 20,
          color: {
            type: "linear",
            x: 1,
            y: 0,
            x2: 0,
            y2: 0,
            colorStops: [
              {
                offset: 0,
                color: "#2F91FF" //  0%  处的颜色
              },
              {
                offset: 0.03,
                color: "#296bb6" //  93%  处的颜色
              },
              {
                offset: 1,
                color: "#2FA1FF" //  100%  处的颜色2F91FF
              }
            ],
            global: false //  缺省为  false
          }
        }
      }
    ]
  };
};

watch(
  () => useAppStoreHook().getSidebarStatus,
  () => {
    delay(600).then(() => resize());
  }
);

watch(
  () => props,
  () => setOptions(getOption() as EChartsOption),
  {
    immediate: true,
    deep: true
  }
);
onMounted(() => {
  nextTick(() => {
    var myChart = echarts.init(chartRef.value!);
    myChart.on("click", params => {
      emit("update", params.data.id);
    });
  });
});
onActivated(() => triggerWindowResize());
</script>
