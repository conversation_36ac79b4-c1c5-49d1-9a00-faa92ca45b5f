<template>
    <el-table-v2
      fixed
      :columns="columns"
      :data="tableData"
      :header-height="[50, 40, 50]"
      :width="1700"
      :height="400"
    >
      <template #header="props">
        <customized-header v-bind="props" />
      </template>
    </el-table-v2>
  </template>
  <script lang="tsx" setup>
  import { TableV2FixedDir, TableV2Placeholder } from 'element-plus'
  import type { FunctionalComponent } from 'vue'
  import type {
    HeaderClassNameGetter,
    TableV2CustomizedHeaderSlotParam,
  } from 'element-plus'

  const props = defineProps({
  tableData: {
    type: Array as any,
    default: []
  }
});
  const generateColumns = (length = 10, prefix = 'column-', props?: any) =>
    Array.from({ length }).map((_, columnIndex) => ({
      ...props,
      key: `${prefix}${columnIndex}`,
      dataKey: `${prefix}${columnIndex}`,
      title: `Column ${columnIndex}`,
      width: 150,
    }))
  
  
  const columns = [{ title: "零售用户名称", dataKey: "retailUserName", width: 220, }, { title: "月份", dataKey: "month", width: 150, }, { title: "结算电量", dataKey: "settlementEnergy1", width: 150 }, { title: "代理销售费", dataKey: "agentSaleFee1", width: 150 }
  , { title: "用电量", dataKey: "settlementEnergy2", width: 150 }, { title: "代理销售费", dataKey: "agentSaleFee2", width: 150 }, { title: "用电量", dataKey: "settlementEnergy3", width: 150 }, { title: "代理销售费", dataKey: "agentSaleFee3", width: 150 }
]
//   const data = generateData(columns, 200)
  

  
  const CustomizedHeader: FunctionalComponent<
    TableV2CustomizedHeaderSlotParam
  > = ({ cells, columns, headerIndex }) => {
    if (headerIndex === 2) return cells
  
    const groupCells = [] as typeof cells
    let width = 0
    let idx = 0
  
    columns.forEach((column, columnIndex) => {
      if (column.placeholderSign === TableV2Placeholder)
        groupCells.push(cells[columnIndex])
      else {
        width += cells[columnIndex].props!.column.width
        idx++
  
        const nextColumn = columns[columnIndex + 1]
        if (
          columnIndex === columns.length - 1 ||
          nextColumn.placeholderSign === TableV2Placeholder ||
          idx === (headerIndex === 0 ? 4 : 2)
        ) {
           
       
          groupCells.push(
            <div
              class="flex items-center justify-center custom-header-cell"
              role="columnheader"
              style={{
                ...cells[columnIndex].props!.style,
                width: `${width}px`,
              }}
            >
              {cells[columnIndex].props!.column.title}
            </div>
          )
          width = 0
          idx = 0
        }
      }
    })
    return groupCells
  }
  

  </script>
  
  <style lang="scss">
  .el-el-table-v2__header-row .custom-header-cell {
    border-right: 1px solid var(--el-border-color);
  }
  
  .el-el-table-v2__header-row .custom-header-cell:last-child {
    border-right: none;
  }
  
  .el-primary-color {
    background-color: var(--el-color-primary);
    color: var(--el-color-white);
    font-size: 14px;
    font-weight: bold;
  }
  
  .el-primary-color .custom-header-cell {
    padding: 0 4px;
  }

  </style>