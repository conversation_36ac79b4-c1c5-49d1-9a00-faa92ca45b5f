<template>
  <div style="margin-top: 20px">
    <el-row :gutter="20">
      <el-col :span="15">
        <el-card>
          <div class="header">
            <div class="header-title">
              <img :src="getAssetURL('title-arrow')" alt="" />
              现货出清分析
            </div>
          </div>
          <Echarts :echartsData="optionDeal1" EWidth="100%" EHeight="550px" echartId="settleSumInfo1"></Echarts>
          <!-- <Echarts :echartsData="optionDeal2" EWidth="100%" EHeight="550px" echartId="settleSumInfo2"></Echarts> -->
        </el-card>
      </el-col>
      <el-col :span="9">
        <el-card style="height: 612px;">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-card class="jt-card">
                <div class="jt-card-content">
                  <div class="jt-card-content-item">
                    <div style="color: #606266; line-height: 22px; font-size: 14px">
                      总收入(元):
                    </div>
                    <div style="
                        line-height: 24px;
                        font-size: 24px;
                        margin-top: 15px;
                      ">
                      {{ totalElectricityCost }}
                    </div>
                    <div style="
                        color: #606266;
                        line-height: 22px;
                        font-size: 14px;
                        margin-top: 15px;
                      ">
                      较昨日对比
                    </div>
                  </div>
                  <div>
                    <img :src="getAssetURL('coin')" alt="" />
                    <div style="margin-top: 15px" v-if="totaldiffValue < 0">
                      <el-icon color="green">
                        <CaretBottom />
                      </el-icon>
                      <span style="color: green">
                        {{ totaldiffValue ? (totaldiffValue).toFixed(2) : 0 }}
                      </span>
                    </div>
                    <div style="margin-top: 15px" v-else>
                      <el-icon color="red">
                        <CaretTop />
                      </el-icon>
                      <span style="color: red">
                        {{ totaldiffValue ? (totaldiffValue).toFixed(2) : 0 }}
                      </span>
                    </div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card class="jt-card">
                <div class="jt-card-content">
                  <div class="jt-card-content-item">
                    <div style="color: #606266; line-height: 22px; font-size: 14px">
                      度电收益(元/MWh):
                    </div>
                    <div style="
                        line-height: 24px;
                        font-size: 24px;
                        margin-top: 15px;
                      ">
                      {{ settlementAveragePrice }}
                    </div>
                    <div style="
                        color: #606266;
                        line-height: 22px;
                        font-size: 14px;
                        margin-top: 15px;
                      ">
                      较昨日对比
                    </div>
                  </div>
                  <div>
                    <img :src="getAssetURL('flag14')" alt="" />
                    <div style="margin-top: 15px" v-if="dudiandiffValue < 0">
                      <el-icon color="green">
                        <CaretBottom />
                      </el-icon>
                      <span style="color: green">
                        {{ dudiandiffValue ? (dudiandiffValue).toFixed(2) : 0 }}
                      </span>
                    </div>
                    <div style="margin-top: 15px" v-else>
                      <el-icon color="red">
                        <CaretTop />
                      </el-icon>
                      <span style="color: red">
                        {{ dudiandiffValue ? (dudiandiffValue).toFixed(2) : 0 }}
                      </span>
                    </div>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
          <el-table :data="tableData" class="table" :cell-style="{ borderRight: 'none', color: '#1D2129' }"
            :header-cell-style="{
      borderColor: '#DCDFE6',
      color: '#1D2129',
      backgroundColor: '#F2F3F5',
    }" border style="width: 100%; margin: 20px 0">
            <el-table-column prop="type" label="类型" />
            <el-table-column prop="electricQuantity" label="电量" />
            <el-table-column prop="electrovalence" label="电价" />
            <el-table-column prop="electricCharge" label="电费" />
          </el-table>
        </el-card>
        <!-- <el-card style="margin-top: 20px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-card class="jt-card">
                <div class="jt-card-content">
                  <div class="jt-card-content-item">
                    <div style="color: #606266; line-height: 22px; font-size: 14px">
                      D-2日原始预测准确率(%):
                    </div>
                    <div style="line-height: 24px; font-size: 24px"></div>
                  </div>
                  <img :src="getAssetURL('lingxingpro14')" alt="" />
                </div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card class="jt-card">
                <div class="jt-card-content">
                  <div class="jt-card-content-item">
                    <div style="color: #606266; line-height: 22px; font-size: 14px">
                      D-1日申报预测准确率(%):
                    </div>
                    <div style="line-height: 24px; font-size: 24px"></div>
                  </div>
                  <img :src="getAssetURL('miaozhun14')" alt="" />
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-card> -->
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { echartsConfigBottom } from '@/utils/echartsConfig'
import { CaretTop, CaretBottom } from '@element-plus/icons-vue'
import { watch, ref } from 'vue'

const getAssetURL = (image: any) => {
  return new URL(`../../../assets/svg/${image}.svg`, import.meta.url).href
}

import {
  dailySettlementInfo, //查询日结算列表
  hourSettlementInfo, //查询24小时结算列表
} from '@/api'
import dayjs from 'dayjs';

const props = defineProps({
  allData: {
    default: {}
  },
  totalInfo: {
    default: {}
  },
  timeDate: {
  }
})
// 总营收
const totalElectricityCost = ref<any>()
// 总度电收入
const settlementAveragePrice = ref<any>()

// 总营收较昨日
const totaldiffValue = ref<any>(0)
// 总总度电收入较昨日
const dudiandiffValue = ref<any>(0)

// test
const tableData = ref<any>([
  {
    type: '中长期',
    electricQuantity: '',
    electrovalence: '',
    electricCharge: '',
  },
  {
    type: '日前市场',
    electricQuantity: '',
    electrovalence: '',
    electricCharge: '',
  },
  {
    type: '实时市场',
    electricQuantity: '',
    electrovalence: '',
    electricCharge: '',
  },
  {
    type: '偏差回收',
    electricQuantity: '',
    electrovalence: '',
    electricCharge: '',
  },
  // {
  //   type: '超额回收',
  //   electricQuantity: '',
  //   electrovalence: '',
  //   electricCharge: '',
  // },
])
const option1 = ref<any>({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      crossStyle: {
        color: '#999',
      },
    },
  },
  dataZoom: [
    {
      type: 'inside',
      show: true,
      relatime: true,
    },
    {
      bottom: 25,
      height: 20,
      show: true,
      type: 'slider',
      borderColor: '#ccc',
      borderRadius: 0,
      selectedDataBackground: {
        areaStyle: {
          color: '#D0D8E2',
        },
      },
      showDetail: false,
      brushSelect: false,
    },
  ],
  xAxis:
  {
    type: 'category',
    data: [],
    axisPointer: {
      type: 'shadow',
    },
  },
  yAxis: [
    {
      type: 'value',
      name: '电量（MWh）',
    },
    {
      type: 'value',
      name: '电价（元/MWh）',
    },
  ],
  series: [
    {
      name: '合同电量',
      type: 'bar',
      tooltip: {
        valueFormatter: function (value: number) {
          return (value || '--') + ' MWh'
        },
      },
      data: [],
    },
    {
      name: '日前需求电量',
      type: 'bar',
      tooltip: {
        valueFormatter: function (value: number) {
          return (value || '--') + ' MWh'
        },
      },
      data: [],
    },
    {
      name: '实际用电量',
      type: 'bar',
      tooltip: {
        valueFormatter: function (value: number) {
          return (value || '--') + ' MWh'
        },
      },
      data: [],
    },
    {
      name: '合同均价',
      yAxisIndex: 1, //设置y轴归属
      type: 'line',
      tooltip: {
        valueFormatter: function (value: number) {
          return (value || '--') + ' 元/MWh'
        },
      },
      data: [],
    },
    {
      name: '日前市场平均电价',
      yAxisIndex: 1, //设置y轴归属
      type: 'line',
      tooltip: {
        valueFormatter: function (value: number) {
          return (value || '--') + ' 元/MWh'
        },
      },
      data: [],
    },
    {
      name: '实时市场平均电价',
      yAxisIndex: 1, //设置y轴归属
      type: 'line',
      tooltip: {
        valueFormatter: function (value: number) {
          return (value || '--') + ' 元/MWh'
        },
      },
      data: [],

    },
  ],
})
const optionDeal1 = echartsConfigBottom(option1.value)
const option2 = ref<any>({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      crossStyle: {
        color: '#999',
      },
    },
  },
  xAxis:
  {
    type: 'category',
    data: [],
    axisPointer: {
      type: 'shadow',
    },
  },
  yAxis:
  {
    type: 'value',
    name: '电量（MWh）',
  },
  series: [
    {
      name: 'D-2日原始预测电量',
      type: 'bar',
      tooltip: {
        valueFormatter: function (value: number) {
          return (value || '--') + ' MWh'
        },
      },
      data: [],
    },
    {
      name: 'D-2日策略修正电量',
      type: 'bar',
      tooltip: {
        valueFormatter: function (value: number) {
          return (value || '--') + ' MWh'
        },
      },
      data: [],
    },
    {
      name: 'D-1日申报修正电量',
      type: 'bar',
      tooltip: {
        valueFormatter: function (value: number) {
          return (value || '--') + ' MWh'
        },
      },
      data: [],
    }
  ],
})
const optionDeal2 = echartsConfigBottom(option2.value)
watch(
  () => [props.allData, props.totalInfo],
  (newVal: any) => {
    option1.value.xAxis.data = []
    option2.value.xAxis.data = []
    option1.value.series[0].data = []
    option1.value.series[1].data = []
    option1.value.series[2].data = []
    option1.value.series[3].data = []
    option1.value.series[4].data = []
    option1.value.series[5].data = []
    totalElectricityCost.value = newVal[1]?.totalElectricityCost
    settlementAveragePrice.value = newVal[1]?.settlementAveragePrice
    option1.value.xAxis.data = newVal[0]?.map((item: any) => item.time)
    option2.value.xAxis.data = newVal[0]?.map((item: any) => item.time)
    option1.value.series[0].data = newVal[0]?.map((item: any) => item.contractQuantity)
    option1.value.series[1].data = newVal[0]?.map((item: any) => item.dayaheadDemandQuantity)
    option1.value.series[2].data = newVal[0]?.map((item: any) => item.realtimeClearingQuantity)
    option1.value.series[3].data = newVal[0]?.map((item: any) => item.contractUnitPrice)
    option1.value.series[4].data = newVal[0]?.map((item: any) => item.dayaheadMarketAvgCost)
    option1.value.series[5].data = newVal[0]?.map((item: any) => item.realtimeMarketAvgCost)
    // option2.value.series[0].data = newVal[0]?.map((item: any) => item.)
    // option2.value.series[1].data = newVal[0]?.map((item: any) => item.)
    // option2.value.series[2].data = newVal[0]?.map((item: any) => item.)
    tableData.value[0].electricQuantity = newVal[1]?.contractQuantity
    tableData.value[0].electrovalence = newVal[1]?.contractUnitPrice
    tableData.value[0].electricCharge = newVal[1]?.contractElectricityCost
    tableData.value[1].electricQuantity = newVal[1]?.dayaheadDemandQuantity
    tableData.value[1].electricCharge = newVal[1]?.dayaheadtestiationElectricityCost
    tableData.value[2].electricQuantity = newVal[1]?.realtimeClearingQuantity
    tableData.value[2].electricCharge = newVal[1]?.realtimetestiationElectricityCost
    tableData.value[3].electricCharge = newVal[1]?.testiationRecoveryElectricityCost
  },
  {
    immediate: true
  }
)
watch(() => [props.timeDate, props.totalInfo], async (newVal: any) => {
  const yestoday = dayjs(newVal[0]).add(-1, 'day').format('YYYY-MM-DD')
  const yestodayInfo = await dailySettlementInfo({
    startTime: yestoday,
    endTime: yestoday,
  })
  totaldiffValue.value = totalElectricityCost.value - yestodayInfo?.totalInfo?.totalElectricityCost
  dudiandiffValue.value = settlementAveragePrice.value - yestodayInfo?.totalInfo?.settlementAveragePrice
  const res = await hourSettlementInfo({
    startTime: newVal[0],
    endTime: newVal[0],
  })
  tableData.value[1].electrovalence = res.dayaheadMarketPrice
  tableData.value[2].electrovalence = res.realMarketPrice
  tableData.value[3].electrovalence = res.testiationRecoveryPrice
  console.log(`output->res`, res)
},
  {
    immediate: true
  }
)
</script>

<style scoped lang="scss">
.jt-card-content {
  display: flex;
  justify-content: space-between;
  margin: 20px;

  .jt-card-content-item {
    height: 60px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
}
</style>
