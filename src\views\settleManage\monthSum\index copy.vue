<template>
  <div>

    <el-card class="jt-card" style="margin-top: 20px">

      <MyTab v-model="tabActive" @change="searchConditionChange" :tabs="['用户结算单', '量价费申报', '结算电量分析']"
        style="margin-bottom: 15px">
      </MyTab>
    </el-card>
    <el-card class="jt-card" style="margin-top: 20px" v-if="tabActive === '量价费申报'">
      <div class="header" style="margin-bottom: 20px">
        <div class="header-title">
          <img src="@/assets/svg/title-arrow.svg" alt="" />
          量价费填报
        </div>
      </div>
      <MyTab v-model="active" @change="searchConditionChange" :tabs="['代理销售费', '偏差费用']" style="margin-bottom: 15px">
      </MyTab>
      <div class="footers">
        <div class="footers-left">
          月份选择:
          <!-- 空格占位 -->
          &nbsp; &nbsp;
          <el-date-picker format="YYYY-MM" value-format="YYYY-MM" v-model="timeDate" key="day" type="month"
            :clearable="false"></el-date-picker>
          &nbsp; &nbsp;&nbsp; 客户名称: &nbsp; &nbsp;
          <el-input v-model="inputValue" style="width: 200px" maxlength="12" />
          <el-button type="success" :icon="Search" style="margin-left: 10px" @click="searchWatch">
            查询
          </el-button>
        </div>
        <div class="footers-right" v-if="active === '代理销售费'">
          <el-row style="display: flex; justify-content: center; align-items: center;flex-direction: column">
            <el-upload :http-request="tradingCenterUploadFunction" class="upload" action="" :show-file-list="false"
                       :on-change="tradingCenterUploadFile4"
                       style="margin-bottom: 5px; display: flex; align-items: center;">
              <el-button :icon="Upload" type="primary">用户套餐导入</el-button>
            </el-upload>
            <el-button
                @click="previewUpload('用户套餐预览')"
                type="primary"
                :icon="Upload"
            >
              用户套餐预览
            </el-button>
          </el-row>
          <el-row style="display: flex; align-items: center;flex-direction: column;margin-left: 5px">
            <el-upload :http-request="tradingCenterUploadFunction" class="upload" action="" :show-file-list="false"
                       :on-change="tradingCenterUploadFile1" style="margin-bottom: 5px; display: flex; align-items: center">
              <el-button :icon="Upload" type="primary">价格申报导入</el-button>
            </el-upload>
            <el-button
                @click="previewUpload('价格申报预览')"
                type="primary"
                :icon="Upload"
            >
              价格申报预览
            </el-button>
          </el-row>
          <!-- 同上 -->
          <el-row style="display: flex; align-items: center;flex-direction: column;margin-left: 5px">
            <el-upload style="margin-bottom: 5px; display: flex; align-items: center"
                       :http-request="tradingCenterUploadFunction" class="upload" action="" :show-file-list="false"
                       :on-change="tradingCenterUploadFile2">
              <el-button :icon="Upload" type="primary">用户电量导入</el-button>
            </el-upload>
            <el-button
                @click="previewUpload('用户电量预览')"
                type="primary"
                :icon="Upload"
            >
              用户电量预览
            </el-button>
          </el-row>

          <!-- <el-button :icon="Edit" type="primary">导出</el-button> -->
        </div>
        <div class="footers-right" v-else>
          <el-row style="display: flex; align-items: center;flex-direction: column;margin-left: 5px">
            <el-upload :http-request="tradingCenterUploadFunction" class="upload" action="" :show-file-list="false"
                       :on-change="tradingCenterUploadFile4"
                       style="margin-bottom: 5px; display: flex; align-items: center;">
              <el-button :icon="Upload" type="primary">用户套餐导入</el-button>
            </el-upload>
            <el-button
                @click="previewUpload('用户套餐预览')"
                type="primary"
                :icon="Upload"
            >
              用户套餐预览
            </el-button>
          </el-row>
          <!-- 同上 -->
          <el-row style="display: flex; align-items: center;flex-direction: column;margin-left: 5px">
            <el-upload style="margin-bottom: 5px; display: flex; align-items: center"
                       :http-request="tradingCenterUploadFunction" class="upload" action="" :show-file-list="false"
                       :on-change="tradingCenterUploadFile3">
              <el-button :icon="Upload" type="primary">偏差考核费明细表导入</el-button>
            </el-upload>
            <el-button
                @click="previewUpload('偏差考核费明细表预览')"
                type="primary"
                :icon="Upload"
            >
              偏差考核费明细表预览
            </el-button>
          </el-row>

          <!-- <el-button :icon="Edit" type="primary">导出</el-button> -->
        </div>
      </div>
    </el-card>
    <el-card class="jt-card" style="margin-top: 20px" v-if="tabActive === '用户结算单'">
      <div class="header" style="margin-bottom: 20px">
        <div class="header-title">
          <img src="@/assets/svg/title-arrow.svg" alt="" />
          用户结算单
        </div>
      </div>

      <div class="footers">
        <div class="footers-left">
          月份选择:
          <!-- 空格占位 -->
          &nbsp; &nbsp;
          <el-date-picker format="YYYY-MM" value-format="YYYY-MM" v-model="timeDate2" key="day" type="month"
            :clearable="false" @change="handlerTime"></el-date-picker>
          &nbsp; &nbsp;&nbsp;
        </div>
        <div class="footers-right">

          <!-- 同上 -->
              <el-upload style="margin-bottom: 5px; display: flex; align-items: center"
                         :http-request="tradingCenterUploadFunction" class="upload" action="" :show-file-list="false"
                         :on-change="tradingCenterUploadFile5">
                <el-button :icon="Upload" type="primary">用户结算单导入</el-button>
              </el-upload>


          <!-- <el-button :icon="Edit" type="primary">导出</el-button> -->
        </div>

      </div>
    </el-card>

    <el-dialog title="数据预览" v-model="dataPreviewDialogVisible" width="90%">
      <TemplatePreview :fileId="templateId" ref="templatePreviewRef" />
    </el-dialog>
    <el-card class="jt-card" style="margin-top: 20px" v-if="tabActive === '结算电量分析'">
      <div class="header" style="margin-bottom: 20px">
        <div class="header-title">
          <img src="@/assets/svg/title-arrow.svg" alt="" />
          用户结算单
        </div>
      </div>

      <div class="footers">
        <div class="app-search-card">
          <div class="app-form-group">
            <div class="ml-[15px]">
              <span>年份：</span>
              <el-date-picker style="width: 160px" v-model="searchAnalysis.year" type="year" placeholder="请选择"
                @change="getYearTotal" value-format="YYYY" />
            </div>
            <div class="ml-[15px]">
              <span>月份：</span>
              <!-- <el-date-picker v-model="searchAnalysis.months"    type="months"
                 :clearable="false" style="width: 160px"></el-date-picker> -->
              <el-select v-model="searchAnalysis.months" filterable placeholder="请选择" style="width: 180px" multiple
                collapse-tags>
                <el-option v-for="(item, index) in selectMonths" :key="index" :label="item.month" :value="item.value" />
              </el-select>
            </div>

            <div class="ml-[15px]">
              <span>用户渠道：</span>
              <!-- <DictSelect style="margin-right: 10px; width: 180px" v-model="searchAnalysis.userChannel"
                :clearable="true" dict-code="customerSource" :identification="identification" :multiple="true"/> -->
              <el-select v-model="searchAnalysis.userChannel" filterable placeholder="请选择"
                style="width: 240px ;margin-right: 10px" multiple collapse-tags>
                <el-option v-for="(item, index) in dictData" :key="index" :label="item.label" :value="item.value" />
              </el-select>
            </div>
            <div>
              <span>用户类别：</span>
              <el-select v-model="searchAnalysis.electricityUsetype" filterable placeholder="请选择" style="width: 200px"
                multiple collapse-tags>
                <el-option v-for="(item, index) in selectUsetype" :key="index" :label="item" :value="item" />
              </el-select>
            </div>
          </div>
          <div class="ml-[15px]">
            <span>电压等级：</span>
            <el-select v-model="searchAnalysis.voltageGrade" filterable placeholder="请选择" style="width: 160px" multiple
              collapse-tags>
              <el-option v-for="(item, index) in selectVoltageGrade" :key="index" :label="item" :value="item" />
            </el-select>
          </div>
          <div class="ml-[15px]">
            <span>零售用户：</span>
            <el-select v-model="searchAnalysis.retailUsers" filterable placeholder="请选择" style="width: 160px">
              <el-option v-for="item in retailData" :key="item" :label="item" :value="item.value" />
            </el-select>
          </div>

        </div>
      </div>
      <div class="search ">
        <el-button class="filter-item" type="primary" @click="handlerData">查询</el-button>
        <el-button class="filter-item" @click="handleReset">重置</el-button>
      </div>
    </el-card>

    <el-card class="jt-card" style="margin-top: 20px; position: relative" v-if="tabActive === '用户结算单'">
      <div style="margin-bottom: 10px;margin-left: 5px;">
        <span>电量: MWh</span> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <span>价格: 元 / MWh</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <span>费用: 元</span>
      </div>
      <pure-table :columns="columns" border stripe :loading="loading" :data="sumTableData" :pagination="pagination"
        @page-size-change="onSizeChange" @page-current-change="onCurrentChange">
        <template #retailUserNameHeader>
          <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate" :column="columns[1]" />
        </template>
        <template #electricalUnitNameHeader>
          <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate" :column="columns[2]" />
        </template>
        <template #userCodeHeader>
          <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate" :column="columns[3]" />
        </template>
        <template #pointIdHeader>
          <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate" :column="columns[4]" />
        </template>
        <template #tranTypeHeader>
          <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate" :column="columns[5]" />
        </template>
        <template #periodHeader>
          <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate" :column="columns[6]" />
        </template>
        <!-- <template #monthHeader>
          <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate" :column="columns[7]" />
        </template> -->
        <template #electricityUsetypeHeader>
          <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate" :column="columns[9]" />
        </template>
        <template #voltageGradeHeader>
          <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate" :column="columns[10]" />
        </template>
        <template #scaleHeader>
          <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate" :column="columns[11]" />
        </template>
        <template #agentSalesModelHeader>
          <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate" :column="columns[12]" />
        </template>

      </pure-table>
      <!-- 弹窗 -->
      <el-dialog v-model="dialogVisible" title="套餐推荐" width="1000" :before-close="handleClose">
        <el-form :model="dialogForm" :inline="true">
          <el-form-item label="当前套餐" label-width="160">
            <el-input v-model="dialogForm.one" disabled></el-input>
          </el-form-item>
          <el-form-item label="当前比例系数" label-width="160">
            <el-input v-model="dialogForm.two" disabled></el-input>
          </el-form-item>
          <el-form-item label="当前套餐代理销售费" label-width="160">
            <el-input v-model="dialogForm.three" disabled></el-input>
          </el-form-item>
          <el-form-item label="推荐套餐" label-width="160">
            <el-input v-model="dialogForm.four" disabled></el-input>
          </el-form-item>
          <el-form-item label="推荐比例系数" label-width="160">
            <el-input v-model="dialogForm.five" disabled></el-input>
          </el-form-item>
          <el-form-item label="推荐套餐代理销售费" label-width="160">
            <el-input v-model="dialogForm.six" disabled></el-input>
          </el-form-item>
        </el-form>
        <Echarts :echartsData="option1" EWidth="100%" EHeight="450px" echartId="monthSumSelected"></Echarts>
        <template #footer>
          <div style="display: flex; justify-content: center">
            <el-row :gutter="50">
              <el-col :span="12" style="display: flex; justify-content: center">
                <el-button @click="dialogVisible = false" type="primary">
                  确认
                </el-button>
              </el-col>
              <el-col :span="12" style="display: flex; justify-content: center">
                <el-button @click="dialogVisible = false">取消</el-button>
              </el-col>
            </el-row>
          </div>
        </template>
      </el-dialog>
    </el-card>
    <el-card class="jt-card" style="margin-top: 20px; position: relative"
      v-if="active === '代理销售费' && tabActive === '量价费申报'">
      <div style="margin-left: 10px; right: 20px; position: absolute">
        <el-button type="primary" @click="agencySaveAgencySaleFeeFn">
          保存
        </el-button>
        <el-button @click="Download" type="primary">
          导出
        </el-button>
      </div>


      <div style="margin-bottom: 15px">
        <el-form :model="firstForm" :inline="true">
          <el-form-item label="常规交易上限价:" label-width="140">
            <el-input v-model="firstForm.one" maxlength="10" @input="firstForm.one = firstForm.one.replace(
              /^([0-9-]\d*\.?\d{0,3})?.*$/,
              '$1',
            )"></el-input>
          </el-form-item>
          <el-form-item label="售电公司常规交易均价:" label-width="160">
            <el-input v-model="firstForm.two" maxlength="10" @input="firstForm.two = firstForm.two.replace(
              /^([0-9-]\d*\.?\d{0,3})?.*$/,
              '$1',
            )"></el-input>
          </el-form-item>
          <!-- <el-form-item label="批发市场交易上限价:" label-width="160">
            <el-input v-model="firstForm.three" maxlength="10" @input="firstForm.three = firstForm.three.replace(
              /^([0-9-]\d*\.?\d{0,3})?.*$/,
              '$1',
            )"></el-input>
          </el-form-item> -->
          <el-form-item label="当月国网代理购电价:" label-width="160">
            <el-input v-model="firstForm.four" maxlength="10" @input="firstForm.four = firstForm.four.replace(
              /^([0-9-]\d*\.?\d{0,3})?.*$/,
              '$1',
            )"></el-input>
          </el-form-item>
          <el-form-item label="售电侧常规交易均价:" label-width="160">
            <el-input v-model="firstForm.five" maxlength="10" @input="firstForm.five = firstForm.five.replace(
              /^([0-9-]\d*\.?\d{0,3})?.*$/,
              '$1',
            )"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div style="margin-bottom: 10px;margin-left: 5px;">
        <span>电量: MWh</span> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <span>价格: 元 / MWh</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <span>费用: 元</span>
      </div>
      <!-- 写一个表格 -->
      <el-table :data="tableData1" :cell-style="{ borderRight: 'none', color: '#1D2129' }" :header-cell-style="{
        borderColor: '#DCDFE6',
        color: '#1D2129',
        backgroundColor: '#F2F3F5'
      }" border @row-click="rowHandleClick" style="width: 100%; overflow: auto">
        <!-- 序号 -->
        <el-table-column type="index" label="序号" width="80"></el-table-column>
        <el-table-column prop="retailUserName" label="零售用户名称" width="120"></el-table-column>
        <el-table-column prop="packageName" label="套餐名称" width="120"></el-table-column>
        <el-table-column prop="electricalUnitName" label="用电单元名称" width="120"></el-table-column>
        <el-table-column prop="userCode" label="用户号" width="120"></el-table-column>
        <el-table-column prop="pointId" label="计量点ID" width="120"></el-table-column>
        <el-table-column prop="averageContractPrice" label="月度合同均价" width="120"></el-table-column>
        <el-table-column prop="electricityUsetype" label="用电类别" width="120"></el-table-column>
        <el-table-column prop="voltageGrade" label="电压等级" width="120"></el-table-column>
        <el-table-column prop="scale" label="比例系数K(%)" width="120"></el-table-column>
        <el-table-column prop="agentSalesModelStr" label="代理销售价模式" width="140"></el-table-column>
        <el-table-column prop="pointElectricityConsumption" label="计量点用电量" width="120"></el-table-column>
        <el-table-column prop="retailTransactionPrice" label="零售用户交易价格" width="140"></el-table-column>
        <el-table-column prop="agencySalesFee" label="代理销售费用" width="140"></el-table-column>
        <!-- <el-table-column prop="ispackage" label="是否套餐推荐"  width="120">
          <template #default="scope">
            <el-tag :type="scope.row.ispackage === '是' ? 'success' : 'danger'" disable-transitions>{{
              scope.row.ispackage ? "有推荐" : "无推荐" }}</el-tag>
          </template>
        </el-table-column> -->
      </el-table>
      <el-pagination v-if="tableData1 && tableData1.length" v-model:current-page="electricRateCurrentPage1"
        v-model:page-size="electricRatePageSize1" :page-sizes="[10, 20, 50, 100]"
        layout="->,total, prev, pager, next,sizes" :total="electricRateTotal1" @size-change="electricRateChange1"
        @current-change="electricRateCurrentChange1" />
      <!-- 弹窗 -->
      <el-dialog v-model="dialogVisible" title="套餐推荐" width="1000" :before-close="handleClose">
        <el-form :model="dialogForm" :inline="true">
          <el-form-item label="当前套餐" label-width="160">
            <el-input v-model="dialogForm.one" disabled></el-input>
          </el-form-item>
          <el-form-item label="当前比例系数" label-width="160">
            <el-input v-model="dialogForm.two" disabled></el-input>
          </el-form-item>
          <el-form-item label="当前套餐代理销售费" label-width="160">
            <el-input v-model="dialogForm.three" disabled></el-input>
          </el-form-item>
          <el-form-item label="推荐套餐" label-width="160">
            <el-input v-model="dialogForm.four" disabled></el-input>
          </el-form-item>
          <el-form-item label="推荐比例系数" label-width="160">
            <el-input v-model="dialogForm.five" disabled></el-input>
          </el-form-item>
          <el-form-item label="推荐套餐代理销售费" label-width="160">
            <el-input v-model="dialogForm.six" disabled></el-input>
          </el-form-item>
        </el-form>
        <Echarts :echartsData="option1" EWidth="100%" EHeight="450px" echartId="monthSumSelected"></Echarts>
        <template #footer>
          <div style="display: flex; justify-content: center">
            <el-row :gutter="50">
              <el-col :span="12" style="display: flex; justify-content: center">
                <el-button @click="dialogVisible = false" type="primary">
                  确认
                </el-button>
              </el-col>
              <el-col :span="12" style="display: flex; justify-content: center">
                <el-button @click="dialogVisible = false">取消</el-button>
              </el-col>
            </el-row>
          </div>
        </template>
      </el-dialog>
    </el-card>

    <el-card class="jt-card" style="margin-top: 20px; position: relative" v-if="tabActive === '结算电量分析'">
      <el-table :data="tableData" style="width: 100%" height="600" show-summary :summary-method="getSummaries"
      v-loading="loading"
      element-loading-text="正在加载数据..."
    :element-loading-spinner="svg"
    element-loading-svg-view-box="-10, -10, 50, 50"
    element-loading-background="rgba(122, 122, 122, 0.8)">
        <el-table-column prop="retailUserName" label="零售用户名称" width="150" />
        <el-table-column label="月份" width="220">
          <template #default="scope">
            <span :class="{ fw: scope.row.month === '小计' }">{{ scope.row.month }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="preYears">
          <el-table-column label="结算电量">
            <template #default="scope">
              <span :class="{ fw: scope.row.month === '小计' }">{{ scope.row.settlementEnergy1 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="agentSaleFee1" label="代理销售费">
            <template #default="scope">
              <span :class="{ fw: scope.row.month === '小计' }">{{ scope.row.agentSaleFee1 }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column :label="follYear">
          <el-table-column prop="settlementEnergy2" label="用电量">
            <template #default="scope">
              <span :class="{ fw: scope.row.month === '小计' }">{{ scope.row.settlementEnergy2 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="agentSaleFee2" label="代理销售费">
            <template #default="scope">
              <span :class="{ fw: scope.row.month === '小计' }">{{ scope.row.agentSaleFee2 }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="同比">
          <el-table-column  label="用电量" >
            <template #default="scope">
              <span :class="{ fw: scope.row.month === '小计' }">{{ scope.row.settlementEnergy3 }}</span>
            </template>
          </el-table-column>
          <el-table-column  label="代理销售费" >
            <template #default="scope">
              <span :class="{ fw: scope.row.month === '小计' }">{{ scope.row.agentSaleFee3 }}</span>
            </template>
          </el-table-column>
        </el-table-column>
      </el-table>
      <!-- <el-table-v2 :columns="col" :data="tableData" :width="1600" :height="400" :footer-height="50" fixed>
        <template #footer
      ><div
      
      >
      
        <el-row style="text-align: left">
    <el-col :span="3">总计</el-col>
    <el-col :span="1"></el-col>
    <el-col :span="3">{{ sum.sumEnergy1 }}</el-col>
    <el-col :span="3">{{ sum.sumSaleFee1 }}</el-col>
    <el-col :span="3">{{ sum.sumEnergy2 }}</el-col>
    <el-col :span="3">{{ sum.sumSaleFee2 }}</el-col>
    <el-col :span="3">{{ sum.sumEnergy3 }}</el-col>
    <el-col :span="3">{{ sum.sumSaleFee3 }}</el-col>
    
  </el-row>
      
      
      </div>
    </template>
      </el-table-v2> -->
      <!-- 弹窗 -->
      <el-dialog v-model="dialogVisible" title="套餐推荐" width="1000" :before-close="handleClose">
        <el-form :model="dialogForm" :inline="true">
          <el-form-item label="当前套餐" label-width="160">
            <el-input v-model="dialogForm.one" disabled></el-input>
          </el-form-item>
          <el-form-item label="当前比例系数" label-width="160">
            <el-input v-model="dialogForm.two" disabled></el-input>
          </el-form-item>
          <el-form-item label="当前套餐代理销售费" label-width="160">
            <el-input v-model="dialogForm.three" disabled></el-input>
          </el-form-item>
          <el-form-item label="推荐套餐" label-width="160">
            <el-input v-model="dialogForm.four" disabled></el-input>
          </el-form-item>
          <el-form-item label="推荐比例系数" label-width="160">
            <el-input v-model="dialogForm.five" disabled></el-input>
          </el-form-item>
          <el-form-item label="推荐套餐代理销售费" label-width="160">
            <el-input v-model="dialogForm.six" disabled></el-input>
          </el-form-item>
        </el-form>
        <Echarts :echartsData="option1" EWidth="100%" EHeight="450px" echartId="monthSumSelected"></Echarts>
        <template #footer>
          <div style="display: flex; justify-content: center">
            <el-row :gutter="50">
              <el-col :span="12" style="display: flex; justify-content: center">
                <el-button @click="dialogVisible = false" type="primary">
                  确认
                </el-button>
              </el-col>
              <el-col :span="12" style="display: flex; justify-content: center">
                <el-button @click="dialogVisible = false">取消</el-button>
              </el-col>
            </el-row>
          </div>
        </template>
      </el-dialog>
    </el-card>
    <!-- 第二个弹窗 -->
    <el-card class="jt-card" style="margin-top: 20px; position: relative"
      v-if="active === '偏差费用' && tabActive === '量价费申报'">
      <!-- <div style="margin-left: 10px; right: 20px; position: absolute">
      <el-button type="primary" style="margin-left: 10px; right: 20px; position: absolute"
        @click="deviationSaveDeviationCostFn">
        保存
      </el-button>
      <el-button @click="Download" type="primary">
          导出
        </el-button>
      </div> -->
      <div style="margin-left: 10px; right: 20px; position: absolute">
        <el-button type="primary" @click="deviationSaveDeviationCostFn">
          保存
        </el-button>
        <el-button @click="Download1" type="primary">
          导出
        </el-button>
      </div>
      <el-form :model="secondForm" :inline="true">
        <el-form-item label="总用电量:">
          <el-input v-model="secondForm.one" maxlength="10" @input="secondForm.one = secondForm.one.replace(
            /^([0-9-]\d*\.?\d{0,3})?.*$/,
            '$1',
          )"></el-input>
        </el-form-item>
        <el-form-item label="合同电量:">
          <el-input v-model="secondForm.two" maxlength="10" @input="secondForm.two = secondForm.two.replace(
            /^([0-9-]\d*\.?\d{0,3})?.*$/,
            '$1',
          )"></el-input>
        </el-form-item>
      </el-form>
      <!-- 表格 -->
      <el-table :data="secondTable" class="table" :cell-style="{ borderRight: 'none', color: '#1D2129' }"
        :header-cell-style="{
          borderColor: '#DCDFE6',
          color: '#1D2129',
          backgroundColor: '#F2F3F5'
        }" border style="width: 100%; margin: 20px 0">
        <!-- 序号 -->
        <el-table-column type="index" label="序号"></el-table-column>
        <el-table-column prop="retailUserName" label="零售用户名称"></el-table-column>
        <el-table-column prop="month" label="结算月份"></el-table-column>
        <el-table-column prop="electricityConsumption" label="用电量"></el-table-column>
        <el-table-column prop="deviationAssessmentFee" label="偏差考核费"></el-table-column>
        <el-table-column prop="deviationAssessmentMethod" label="偏差考方式"></el-table-column>
        <el-table-column prop="transactionType" label="交易类型"></el-table-column>
        <!-- <el-table-column prop="deviationAssessmentFee" label="偏差考核费"></el-table-column> -->
      </el-table>
      <el-pagination v-if="secondTable && secondTable.length" :current-page="electricRateCurrentPage2"
        :page-size="electricRatePageSize2" :page-sizes="[10, 20, 50, 100]" layout="->,total, prev, pager, next,sizes"
        :total="electricRateTotal2" @size-change="electricRateChange2" @current-change="electricRateCurrentChange2" />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { echartsConfigBottom, echartsConfig } from "@/utils/echartsConfig";
import TableHeadPopover from "@/components/Core/TableHeadPopover/index.vue";
import {ref, onMounted, watch, reactive, computed, h, VNode, nextTick} from "vue";
import { useUserStore } from "@/store/modules/user";
import {Upload, Search, Bottom} from "@element-plus/icons-vue";
import dayjs from "dayjs"; // 引入dayjs
import { ElLoading, ElMessage, ElMessageBox, TableColumnCtx } from "element-plus"
import { columns } from "./data";
import { delay } from "@pureadmin/utils";
import type { PaginationProps } from "@pureadmin/table";
import TemplatePreview from '@/components/TemplatePreview/index.vue'
import virTable from "./virTable.vue";
import {
  importPriceDeclarationForm, //导入价格申报单
  importDeviationCostForm, //导入偏差费用表
  importUserElectricityForm, //导入用户电量表
  agencyQueryPage, //代理销售费
  agencyQueryAgentSalePackage, //点击查询
  deviationQueryPage,
  agencySaveAgencySaleFee,
  deviationSaveDeviationCost,
  importSalePackageBinding,
  exportDataList,
  exportDataList1,
  importSettlementstatementm,
  getStatementlist,
  queryVoltageGrade,
  queryElectricityUsetype,
  queryRetailUserName,
  getRetailUserlist,
  previewOfUserInvoicesAPI,
  previewOfSalePackageBindingAPI,
  previewOfPriceDeclarationAPI,
  previewOfDeviationCostAPI,
  exportFeeDataListAPI
} from "@/api";
import {downloadElectrictyTemplateAPI} from "@/api/customer-management";
// #region 第一个table
const timeDate = ref(dayjs().format("YYYY-MM"));
const timeDate2 = ref(dayjs().format("YYYY-MM"));
const active = ref("代理销售费");
const tabActive = ref("用户结算单");
const fileList = ref<any>([]);
  const svg = `
        <path class="path" d="
          M 30 15
          L 28 17
          M 25.61 25.61
          A 15 15, 0, 0, 1, 15 30
          A 15 15, 0, 1, 1, 27.99 7.5
          L 15 15
        " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
      `
//用电类别下拉选项
const selectUsetype = ref([]);
const selectVoltageGrade = ref([]);
const retailData = ref([]);
const selectRetailUsers = ref([]);
const identification = ref('结算电量分析');
const sum = ref({
  sumEnergy1: 0,//用电量1
  sumEnergy2: 0,
  sumEnergy3: 0,
  sumSaleFee1: 0,//代理费用
  sumSaleFee2: 0,
  sumSaleFee3: 0,

})
const preYears = ref("");
const follYear = ref("");
const userStore = useUserStore();
const dictData = computed(() => userStore.getDictList.find(i => i.code == "customerSource")?.items.map(item => {
  return {
    label: item.label,
    value: Number(item.value)
  }
}))
console.log("dictDatadasdas", dictData.value);
const searchAnalysis = ref({
  year: dayjs("2023").format("YYYY"),
  years: [],
  months: ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'],
  userChannel: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14],//用户渠道
  electricityUsetype: [],//用电类别
  voltageGrade: [],//电压等级
  retailUsers: [],//零售用户


});

const col = [{ title: "零售用户名称", dataKey: "retailUserName", width: 220, }, { title: "月份", dataKey: "month", width: 150, }, { title: "结算电量", dataKey: "settlementEnergy1", width: 150 }, { title: "代理销售费", dataKey: "agentSaleFee1", width: 150 }
  , { title: "用电量", dataKey: "settlementEnergy2", width: 150 }, { title: "代理销售费", dataKey: "agentSaleFee2", width: 150 }, { title: "用电量", dataKey: "settlementEnergy3", width: 150 }, { title: "代理销售费", dataKey: "agentSaleFee3", width: 150 }
]
const generateColumns = (length = 10, prefix = 'column-', props?: any) =>
  Array.from({ length }).map((_, columnIndex) => ({
    ...props,
    // key: `${prefix}${columnIndex}`,
    dataKey: `${prefix}${columnIndex}`,
    title: `结算电量 ${columnIndex}`,
    width: 150,
  }))

const generateData = (
  columns: ReturnType<typeof generateColumns>,
  length = 200,
  prefix = 'row-'
) =>
  Array.from({ length }).map((_, rowIndex) => {
    return columns.reduce(
      (rowData, column, columnIndex) => {
        rowData[column.dataKey] = `Row ${rowIndex} - Col ${columnIndex}`
        return rowData
      },
      {
        id: `${prefix}${rowIndex}`,
        parentId: null,
      }
    )
  })

const columns1 = generateColumns(10)

const data = generateData(columns1, 1000)
console.log("data", data, "columns1", columns1);




const tableData = ref([])
  



// interface SummaryMethodProps<T = Product> {
//   columns: TableColumnCtx<T>[]
//   data: T[]
// }
// const getSummaries = (param: SummaryMethodProps) => {
//   const { columns, data } = param
//   const sums = []
//   data.forEach((item) => {
//     // console.log("value", value);
//     if(item.month === "小计"){
//       sum.value.sumEnergy1 += parseFloat(Number(item.settlementEnergy1).toFixed(2)) 
//       sum.value.sumSaleFee1 += parseFloat(Number(item.agentSaleFee1).toFixed(2)) 

//       sum.value.sumEnergy2 += parseFloat(Number(item.settlementEnergy2).toFixed(2)) 
//       sum.value.sumSaleFee2 += parseFloat(Number(item.agentSaleFee2).toFixed(2)) 


//       sum.value.sumEnergy3 += parseFloat(Number(item.settlementEnergy3).toFixed(2)) 
//       sum.value.sumSaleFee3 += parseFloat(Number(item.agentSaleFee3).toFixed(2)) 
    
//     }
//   })
//   columns.forEach((column, index) => {
//     if (index === 0) {
//       sums[index] = h('div', { style: {} }, [
//         '合计',
//       ])
//       return
//     }
//     // const values = data.map((item) => Number(item[column.property]))
//     // console.log("dsdasdas", data,"列", column);
   

//     if (index === 1) {
//       sums[index] = ""
//     }
//     if (index === 2) {
//       sums[index] = sum.value.sumEnergy1 }
//       if (index === 3) {
//       sums[index] = sum.value.sumSaleFee1}
//       if (index === 4) {
//       sums[index] = sum.value.sumEnergy2 }
//       if (index === 5) {
//       sums[index] = sum.value.sumSaleFee2}
//       if (index === 6) {
//       sums[index] = sum.value.sumEnergy3 }
//       if (index === 7) {
//       sums[index] = sum.value.sumSaleFee3}

//   })


//   return sums
// }


const selectMonths = ref([{ month: '一月', value: '01' },
{ month: '二月', value: '02' },
{ month: '三月', value: '03' },
{ month: '四月', value: '04' },
{ month: '五月', value: '05' },
{ month: '六月', value: '06' },
{ month: '七月', value: '07' },
{ month: '八月', value: '08' },
{ month: '九月', value: '09' },
{ month: '十月', value: '10' },
{ month: '十一月', value: '11' },
{ month: '十二月', value: '12' },
]);
const searchInfo = ref({
  month: timeDate2.value,
  agentSalesModel: '',
  electricalUnitName: '',
  electricityUsetype: ' ',
  periodType: ' ',
  pointId: '',
  retailUserName: '',
  scale: '',
  transactionType: ' ',
  userCode: '',
  voltageGrade: '',
  pageNo: 1,
  pageSize: 10
});
const loading = ref(false);
const timeSlot = ref<number>(dayjs().valueOf());
function tradingCenterUploadFunction() { }

const firstForm = ref({
  one: "",
  two: "",
  three: "",
  four: "",
  five: ""
});
const tableData1 = ref([]);
const sumTableData = ref([]);

const getVoltageGrade = async () => {
  const res = await queryVoltageGrade();
  selectVoltageGrade.value = res
  searchAnalysis.value.voltageGrade = res

}
const getretailData = () => {
}
const queryElectricityUsetypes = async () => {
  const res = await queryElectricityUsetype();
  selectUsetype.value = res
  searchAnalysis.value.electricityUsetype = res
  // console.log("selectUsetype",res);
}
const getPageUsersList = async () => {
  const res = await getRetailUserlist({ electricityUsetype: searchAnalysis.value.electricityUsetype, voltageGrade: searchAnalysis.value.voltageGrade, userChannel: searchAnalysis.value.userChannel });
  retailData.value = res;
  searchAnalysis.value.retailUsers = res
}
// 获取结算分析列表
const getSumAnalysisList = async () => {
  try {
    searchAnalysis.value.years.push(searchAnalysis.value.year)
    searchAnalysis.value.years.push(Number(searchAnalysis.value.year) + 1)
    const res = await queryRetailUserName({ year: searchAnalysis.value.years, month: searchAnalysis.value.months, retailUserName: searchAnalysis.value.retailUsers });
    if(res){
      loading.value = false
      preYears.value =searchAnalysis.value.year + "年";
      follYear.value = Number(searchAnalysis.value.year) + 1 + "年"
    }
    res.forEach(item => {
      item.forEach(item1 => {
        tableData.value.push(item1)
      })
    })
    tableData.value.forEach(item => {
      if (item.month === "小计" && item.settlementEnergy1) {
        sum.value.sumEnergy1 += Number(item.settlementEnergy1)
        sum.value.sumEnergy1 = parseFloat(sum.value.sumEnergy1.toFixed(2));
      }
      if (item.month === "小计" && item.settlementEnergy2) {
        sum.value.sumEnergy2 += Number(item.settlementEnergy2)
        sum.value.sumEnergy2 += parseFloat(sum.value.sumEnergy2.toFixed(2));
      }
      if (item.month === "小计" && item.settlementEnergy3) {
        sum.value.sumEnergy3 += Number(item.settlementEnergy3)
        sum.value.sumEnergy3 += parseFloat(sum.value.sumEnergy3.toFixed(2));
      }
      if (item.month === "小计" && item.agentSaleFee1) {
        sum.value.sumSaleFee1 += Number(item.agentSaleFee1)
        sum.value.sumSaleFee1 += parseFloat(sum.value.sumSaleFee1.toFixed(2));
      }
      if (item.month === "小计" && item.agentSaleFee2) {
        sum.value.sumSaleFee2 += Number(item.agentSaleFee2)
        sum.value.sumSaleFee2 += parseFloat(sum.value.sumSaleFee2.toFixed(2));
      }
      if (item.month === "小计" && item.agentSaleFee3) {
        sum.value.sumSaleFee3 += Number(item.agentSaleFee3)
        sum.value.sumSaleFee3 += parseFloat(sum.value.sumSaleFee3.toFixed(2));
      }
    })
  } finally {
    loading.value = false
  }

  // console.log("获取结算分析列表", tableData.value);
}
onMounted(async () => {
  preYears.value = searchAnalysis.value.year + "年"
  follYear.value = Number(searchAnalysis.value.year) + 1 + "年"

  // console.log("searchAnalysis", searchAnalysis.value,dictData.value);
  await agencyQueryPageFn();
  await getList();
  await getVoltageGrade()
  await queryElectricityUsetypes()
  await getPageUsersList()
  
  nextTick(() => {
    setTimeout(() => {
      getSumAnalysisList()
    }, 3000)
    
  })
  
});
// 当前页
const electricRateCurrentPage1 = ref<any>(1);
// 当前显示条数
const electricRatePageSize1 = ref<any>(10);
// 总页数
const electricRateTotal1 = ref<any>(20);
function agencyQueryPageFn() {
  if (!electricRateCurrentPage1.value) electricRateCurrentPage1.value = 1;
  if (!electricRatePageSize1.value) electricRatePageSize1.value = 10;
  agencyQueryPage({
    month: timeDate.value,
    pageNo: electricRateCurrentPage1.value,
    pageSize: electricRatePageSize1.value,
    retailUserNameLike: inputValue.value
  }).then(res => {
    tableData1.value = res.data;
    electricRateCurrentPage1.value = res.pageNo;
    electricRatePageSize1.value = res.pageSize;
    electricRateTotal1.value = +res.totalCount;
  });
}

async function getList() {
  try {
    loading.value = true;
    const res = await getStatementlist(searchInfo.value);
    pagination.total = res ? Number(res.totalCount) : 0;
    sumTableData.value = res ? res.data : [];
    console.log("data", sumTableData.value);
  } finally {
    loading.value = false;
  }
}

const handlerTime = () => {
  searchInfo.value.month = timeDate2.value;
  // console.log("AAAa", timeDate2.value,)

  getList()
}


function onSizeChange(val) {
  searchInfo.value.pageSize = val;
  getList();
}
function onCurrentChange(val) {
  searchInfo.value.pageNo = val;
  getList();
}
const option1 = ref({
  xAxis: {
    type: "category",
    axisLabel: {
      interval: 0,
      formatter: function (value) {
        //x轴的文字改为竖版显示
        var str = value.split("");
        return str.join("\n");
      }
    },
    data: []
  },
  yAxis: [
    {
      type: "value",
      name: "套餐代理销售费",
      min: 0,
      axisLabel: {
        formatter: "{value} 元"
      }
    },
    {
      type: "value",
      name: "当前套餐代理销售费",
      min: 0,
      axisLabel: {
        formatter: "{value} 元"
      }
    }
  ],
  series: [
    {
      name: "套餐代理销售费",
      data: [],
      type: "bar"
      // 在后面加上单位元
    },
    {
      name: "当前套餐代理销售费",
      type: "line",
      yAxisIndex: 1,
      data: []
    }
  ]
});
echartsConfig(option1.value);

function rowHandleClick(row: any) {
  dialogVisible.value = true;
  console.log(row);
  agencyQueryAgentSalePackage(row.id).then(res => {
    dialogForm.value = {
      one: res.currentAgentSalesModelStr,
      two: res.currentScale,
      three: res.currentAgencySalesFee,
      four: res.recommendAgentSalesModelStr,
      five: res.recommendScale,
      six: res.recommendAgencySalesFee
    };
    //  寻找最大值
    const lineArr = res.agentSalePackageVOList.map(
      () => dialogForm.value.three
    );
    // console.log("line", lineArr);
    option1.value.series.forEach(item => {
      if (item.name === "当前套餐代理销售费") {
        item.data = lineArr;
      }
    });
    option1.value.xAxis.data = res.agentSalePackageVOList.map(
      item => item.agentSalesModelStr
    );
    res.agentSalePackageVOList.forEach(item => {
      option1.value.series[0].data.push(Number(item.agencySalesFee));
    });
  });
  return;
  if (row.ispackage) {
    dialogVisible.value = true;
    console.log(row);
    agencyQueryAgentSalePackage(row.id).then(res => {
      console.log("chanxun", res);
      console.log("@@@@", dialogForm.value);
      dialogForm.value = {
        one: res.currentAgentSalesModelStr,
        two: res.currentScale,
        three: res.currentAgencySalesFee,
        four: res.recommendAgentSalesModelStr,
        five: res.recommendScale,
        six: res.recommendAgencySalesFee
      };
      console.log("@@@@", dialogForm.value);
    });
    return;
  }
  // 暂无推荐
  else {
    ElMessage.warning("暂无推荐");
  }
}
const dialogVisible = ref(false);
const dialogForm = ref({
  one: "",
  two: "",
  three: "",
  four: "",
  five: "",
  six: ""
});
function tradingCenterUploadFile1(file: any) {
  const fileExtension = file.name.split(".").pop();
  const size = file.size / 1024 / 1024;
  if (fileExtension !== "xlsx" && fileExtension !== "xls") {
    ElMessage.warning("只能上传excel文件");
    fileList.value = [];
    return;
  }
  if (size > 10) {
    ElMessage.warning("文件大小不得超过10M");
    fileList.value = [];
    return;
  }
  fileList.value = [file];
  handleUpload1();
}
// 上传函数
const handleUpload1 = () => {
  if (fileList.value.length === 0) {
    ElMessage.warning("未选择文件");
  } else {
    const formData = new FormData();
    formData.append("file", fileList.value[0].raw);
    importPriceDeclarationForm(formData)
      .then(() => {
        ElMessage.success("上传成功");
        // 更新数据
      })
      .catch((e: any) => {
        console.log(e);
      });
  }
};
function tradingCenterUploadFile4(file: any) {
  const fileExtension = file.name.split(".").pop();
  const size = file.size / 1024 / 1024;
  if (fileExtension !== "xlsx" && fileExtension !== "xls") {
    ElMessage.warning("只能上传excel文件");
    fileList.value = [];
    return;
  }
  if (size > 10) {
    ElMessage.warning("文件大小不得超过10M");
    fileList.value = [];
    return;
  }
  fileList.value = [file];
  handleUpload4();
}

/** 分页配置 */
const pagination = reactive<PaginationProps>({
  pageSize: 10,
  currentPage: 1,
  pageSizes: [10, 20, 40, 50],
  total: 0,
  align: "right",
  background: true,
  small: false
});


const getYearTotal = (item: any) => {

  
  

}
// 表格筛选
function handleTableUpdate(data) {
  searchInfo.value[data.propKey] = data.value;

  getList();
}

const handleUpload4 = () => {
  if (fileList.value.length === 0) {
    ElMessage.warning("未选择文件");
  } else {
    const formData = new FormData();
    formData.append("file", fileList.value[0].raw);
    importSalePackageBinding(formData)
      .then(() => {
        ElMessage.success("上传成功");
        // 更新数据
        agencyQueryPageFn()
      })
      .catch((e: any) => {
        console.log(e);
      });
  }
};

const tradingCenterUploadFile2 = (file: any) => {
  const fileExtension = file.name.split(".").pop();
  const size = file.size / 1024 / 1024;
  if (fileExtension !== "xlsx" && fileExtension !== "xls") {
    ElMessage.warning("只能上传excel文件");
  }
  if (size > 10) {
    ElMessage.warning("文件大小不得超过10M");
  } else {
    fileList.value = [file];
    handleUpload2();
  }
};
const tradingCenterUploadFile5 = (file: any) => {
  const fileExtension = file.name.split(".").pop();
  const size = file.size / 1024 / 1024;
  if (fileExtension !== "xlsx" && fileExtension !== "xls") {
    ElMessage.warning("只能上传excel文件");
  }
  if (size > 10) {
    ElMessage.warning("文件大小不得超过10M");
  } else {
    fileList.value = [file];
    handleUpload5();
  }
};


function handleUpload2() {
  if (fileList.value.length === 0) {
    ElMessage.warning("未选择文件");
  } else {
    const formData = new FormData();
    formData.append("file", fileList.value[0].raw);
    importUserElectricityForm(formData)
      .then(() => {
        ElMessage.success("上传成功");
        // 更新数据
      })
      .catch();
  }
}
function handleUpload5() {
  if (fileList.value.length === 0) {
    ElMessage.warning("未选择文件");
  } else {
    const formData = new FormData();
    formData.append("file", fileList.value[0].raw);
    importSettlementstatementm(formData)
      .then(() => {
        ElMessage.success("上传成功");
        // 更新数据
      })
      .catch();
  }
}
function electricRateChange1() {
  agencyQueryPageFn();
}
function electricRateCurrentChange1() {
  agencyQueryPageFn();
}
function agencySaveAgencySaleFeeFn() {
  ElMessageBox.alert("是否要保存并更新数据？(请谨慎操作)", "提示", {
    confirmButtonText: "确认",
    callback: (action: any) => {
      if (action === "confirm") {
        agencySaveAgencySaleFee({
          month: timeDate.value,
          pageNo: electricRateCurrentPage1.value,
          pageSize: electricRatePageSize1.value,
          retailUserNameLike: inputValue.value,
          regularTradingCeilingPrice: firstForm.value.one,
          regularTradingAveragePrice: firstForm.value.two,
          wholesaleMarketCeilingPrice: firstForm.value.three,
          stateGridAgencyPrice: firstForm.value.four,
          gatdfRegularTradingPrice: firstForm.value.five
        }).then(() => {
          agencyQueryPageFn();
          ElMessage.success("保存并更新数据成功");
        });
      }
    }
  });
}
// #endregion

// #region 第二个table
const secondForm = ref({
  one: "",
  two: "",
  three: "",
  four: "",
  five: ""
});
function deviationSaveDeviationCostFn() {
  ElMessageBox.alert("是否要保存并更新数据？(请谨慎操作)", "提示", {
    confirmButtonText: "确认",
    callback: (action: any) => {
      if (action === "confirm") {
        deviationSaveDeviationCost({
          month: timeDate.value,
          pageNo: electricRateCurrentPage2.value,
          pageSize: electricRatePageSize2.value,
          retailUserNameLike: inputValue.value,
          totalElectricityConsumption: secondForm.value.one,
          differentialElectricityFee: secondForm.value.two
        }).then(() => {
          deviationQueryPageFn();
          ElMessage.success("保存并更新数据成功");
        });
      }
    }
  });
}
function tradingCenterUploadFile3(file: any) {
  const fileExtension = file.name.split(".").pop();
  const size = file.size / 1024 / 1024;
  if (fileExtension !== "xlsx" && fileExtension !== "xls") {
    ElMessage.warning("只能上传excel文件");
  } else if (size > 10) {
    ElMessage.warning("文件大小不得超过10M");
  } else {
    fileList.value = [file];
    handleUpload3();
  }
}
function handleUpload3() {
  if (fileList.value.length === 0) {
    ElMessage.warning("未选择文件");
  } else {
    const formData = new FormData();
    formData.append("file", fileList.value[0].raw);
    importDeviationCostForm(formData)
      .then(() => {
        ElMessage.success("上传成功");
        // 更新数据
      })
      .catch();
  }
}
const secondTable = ref([]);
// 当前页
const electricRateCurrentPage2 = ref<any>(1);
// 当前显示条数
const electricRatePageSize2 = ref<any>(10);
// 总页数
const electricRateTotal2 = ref<any>(20);
function deviationQueryPageFn() {
  deviationQueryPage({
    month: timeDate.value,
    pageNo: electricRateCurrentPage2.value,
    pageSize: electricRatePageSize2.value,
    retailUserNameLike: inputValue.value
  }).then(res => {
    console.log("res", res);
    secondTable.value = res.data;
    electricRateCurrentPage2.value = res.pageNo;
    electricRatePageSize2.value = res.pageSize;
    electricRateTotal2.value = +res.totalCount;
  });
}
function searchConditionChange(val) {
  console.log("搜索条件", active.value, val);
  if (val === "偏差费用") {
    deviationQueryPageFn();
  } else {
    agencyQueryPageFn();
  }
}



function electricRateChange2(val) {
  electricRatePageSize2.value = val;
  deviationQueryPageFn();
}
function electricRateCurrentChange2(val) {
  electricRateCurrentPage2.value = val;
  deviationQueryPageFn();
}

// #endregion
const inputValue = ref("");
function searchWatch() {
  if (active.value === "代理销售费") {
    agencyQueryPageFn();
  } else if (active.value === "偏差费用") {
    deviationQueryPageFn();
  }
}
const handlerChannel = (item: any) => {
  console.log("啊哈哈哈哈哈哈哈", item, searchAnalysis.value.userChannel)
}


// 导出数据
function Download() {
  const loading = ElLoading.service({ text: '正在下载...' })

  exportDataList({
    month: timeDate.value,
    retailUserNameLike: inputValue.value
  })
    .then((data) => {
      const blob = new Blob([data])
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `代理销售费.xlsx`
      link.click()
      window.URL.revokeObjectURL(url)
      loading.close()
    })
    .catch(() => {
      ElMessage.error('下载失败')
      loading.close()
    })
}
function Download1() {
  const loading = ElLoading.service({ text: '正在下载...' })

  exportDataList1({
    month: timeDate.value,
    retailUserNameLike: inputValue.value
  })
    .then((data) => {
      const blob = new Blob([data])
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `量价费申报.xlsx`
      link.click()
      window.URL.revokeObjectURL(url)
      loading.close()
    })
    .catch(() => {
      ElMessage.error('下载失败')
      loading.close()
    })
}

const handlerData = () => {
  getSumAnalysisList()
}

const handleReset = () => {
  searchAnalysis.value.year = dayjs("2023").format("YYYY")
  searchAnalysis.value.years = []
  searchAnalysis.value.months = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12']
  searchAnalysis.value.userChannel = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14]
  searchAnalysis.value.electricityUsetype = []
  searchAnalysis.value.voltageGrade = []
  searchAnalysis.value.retailUsers = []
  getSumAnalysisList()
}

const dataPreviewDialogVisible = ref(false)
const templateId = ref()
const templatePreviewRef = ref()

const previewUpload = (row) => {
  console.log('预览row', row)
  templateId.value = '数据预览'
  dataPreviewDialogVisible.value = true
  nextTick(() => {
    if (row == '用户套餐预览') {
      previewOfSalePackageBindingAPI({month: timeDate.value}).then((res) => {
        templatePreviewRef.value.initShow(res)
      })
    } else if (row == '价格申报预览') {
      previewOfPriceDeclarationAPI({month: timeDate.value}).then((res) => {
        templatePreviewRef.value.initShow(res)
      })

    } else if (row == '用户电量预览') {
      previewOfUserInvoicesAPI({month: timeDate.value}).then((res) => {
        templatePreviewRef.value.initShow(res)
      })

    }  else if (row == '偏差考核费明细表预览') {
      previewOfDeviationCostAPI({month: timeDate.value}).then((res) => {
        templatePreviewRef.value.initShow(res)
      })
    }
  })
}
</script>

<style scoped lang="scss">
.fw {
  font-weight: bold;
}

.footers {
  display: flex;
  justify-content: space-between;

  .footers-left {
    display: flex;
    align-items: center;
    height: 40px;
    line-height: 40px;
  }

  .footers-right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 40px;
    line-height: 40px;

    el-button {
      margin-left: 10px;
    }
  }
}

.search {
  margin-top: 10px;
  margin-left: 10px;
}
</style>
