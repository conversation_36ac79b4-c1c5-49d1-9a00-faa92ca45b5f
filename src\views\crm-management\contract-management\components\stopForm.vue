<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      title="合同终止"
      destroy-on-close
      width="60%"
    >
      <el-form
        ref="ruleFormRef"
        :model="formInline"
        :rules="dataFormRules"
        label-width="160px"
      >
        <el-row>
          <el-col :span="8">
            <el-form-item label="终止类型：" prop="stopType">
              <DictSelect
                v-model="formInline.stopType"
                :clearable="true"
                dict-code="stopType"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="终止生效日期：" prop="effectiveTime">
              <el-date-picker
                style="width: 100%"
                v-model="formInline.effectiveTime"
                type="date"
                placeholder="请选择"
                value-format="x"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="终止原因：" prop="reason">
              <el-input
                type="textarea"
                maxlength="30"
                show-word-limit
                v-model="formInline.reason"
                placeholder="请输入"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="相关附件：">
              <el-upload
                v-model:file-list="fileList"
                with-credentials
                :headers="header"
                :data="uploadData"
                :on-preview="handleDownload"
                :on-remove="handleRemove"
                :action="actionUrl"
                :before-upload="beforeUpload"
              >
                <el-button type="primary">点击上传</el-button>
                <template #tip>
                  <div class="el-upload__tip">
                    支持所有格式，且大小不超过10M
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submit(ruleFormRef)">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "StopForm"
});
import { ref } from "vue";
import type { FormInstance } from "element-plus";
import { ElMessage } from "element-plus";
import type { UploadUserFile } from "element-plus";
import { StopContractListModel } from "@/model/customerModel";
import { stopContractApi } from "@/api/customer-management/index";
import { cloneDeep } from "lodash-es";
import { useFileAction } from "@/hooks/fileAction/useFileAction";
const emit = defineEmits(["update"]);
const ruleFormRef = ref<FormInstance>();
const {
  getFileList,
  handleDownload,
  header,
  handleRemove,
  beforeUpload,
  actionUrl
} = useFileAction();
const uploadData = ref({
  type: "4",
  id: ""
});
const fileList = ref<UploadUserFile[]>([]);
const dialogVisible = ref<boolean>(false);
const formInline = ref<StopContractListModel>({
  stopType: undefined,
  reason: "",
  contractId: undefined,
  effectiveTime: undefined
});
const dataFormMap = cloneDeep(formInline.value);

function handleCreate(id) {
  fileList.value = [];
  uploadData.value.id = id;
  dialogVisible.value = true;
  Object.assign(formInline.value, dataFormMap);
  formInline.value.contractId = id;
  // getFileList("4", id);
}
const dataFormRules = {
  stopType: [
    {
      required: true,
      message: "终止类型是必填项",
      trigger: "change"
    }
  ],
  effectiveTime: [
    {
      required: true,
      message: "终止生效日期是必填项",
      trigger: "change"
    }
  ]
};
async function submit(formEl: FormInstance | undefined) {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      const res = await stopContractApi(formInline.value);
      if (res.code === "200") {
        ElMessage({
          message: "操作成功",
          type: "success"
        });
        emit("update");
        // getList();
        dialogVisible.value = false;
      } else {
        ElMessage({
          message: res.message,
          type: "error"
        });
      }
    } else {
      console.log("error submit!", fields);
    }
  });
}
defineExpose({
  handleCreate
});
</script>

<style lang="scss" scoped></style>
