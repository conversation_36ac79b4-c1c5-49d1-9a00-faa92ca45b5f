<template>
  <section class="forecast-container">
    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane label="短期预测" name="0"></el-tab-pane>
      <el-tab-pane label="中长期预测" name="1"></el-tab-pane>
    </el-tabs>
    <div class="app-search-card">
      <div class="app-form-group">
        <div class="ml-[20px]" v-if="activeName === '0'">
          <span>日期选择：</span>
          <el-date-picker style="width: 140px" v-model="searchInfo.startDate" type="date" placeholder="请选择"
            :clearable="false" format="YYYY-MM-DD" value-format="x" />
        </div>
        <div class="ml-[20px]" v-if="activeName === '1'">
          <span>日期范围选择：</span>
          <el-date-picker style="width: 140px" v-model="searchInfo.startDate" type="date" :clearable="false"
            placeholder="请选择" format="YYYY-MM-DD" value-format="x" />
        </div>
        <div v-if="activeName === '1'">
          <span class="mx-[5px]">-</span>
          <el-date-picker style="width: 140px" v-model="searchInfo.endDate" type="date" :clearable="false"
            placeholder="请选择" format="YYYY-MM-DD" value-format="x" />
        </div>
        <div class="ml-[20px]">
          <span>数据范围：</span>
          <DictSelect style="width: 120px" v-model="dataRange" dict-code="dataRange" />
        </div>
        <div class="ml-[20px]" v-if="dataRange === 2">
          <el-input @click="selectVisible = true" v-model="customName" placeholder="请选择客户" />
        </div>
      </div>
      <div class="app-btn-group">
        <el-button class="filter-item" type="primary" @click="getForecastHome">查询</el-button>
        <el-button class="filter-item" @click="handleReset">重置</el-button>
      </div>
    </div>
    <div class="app-card mt-[15px]" v-loading="loading">
      <div class="card-header">
        <div>
          <div class="flex items-center">
            <div v-show="activeName == '1'">
              <el-radio-group v-model="searchInfo.timeType" @change="handleDateModeChange">
                <el-radio-button :label="1">24点</el-radio-button>
                <el-radio-button :label="2">日</el-radio-button>
              </el-radio-group>
            </div>
            <el-tag class="ml-[10px]" type="success">{{ dataUpdateInfo.startDate }}至{{ dataUpdateInfo.endDate
              }}数据预测已完成</el-tag>
          </div>
        </div>

        <div>
          <el-button v-if="false">人工调整设置</el-button>
          <el-button @click="Download" type="primary" :icon="Edit">
            导出
          </el-button>
          <el-button type="primary" @click="handleDetail">预测配置</el-button>
        </div>
      </div>
      <div class="flex justify-between mt-[20px] mx-[20px]">
        <div class="card-item">
          <div class="name">预测总电量（MWh）</div>
          <div class="value">
            {{ summaryData.forecastTotal ? summaryData.forecastTotal : "-" }}
          </div>
        </div>
        <div class="card-item">
          <div class="name">实际总电量（MWh）</div>
          <div class="value">
            {{ summaryData.realTotal ? summaryData.realTotal : "-" }}
          </div>
        </div>
        <div class="card-item">
          <div class="name">总偏差率（%）</div>
          <div class="value">
            {{
              summaryData.totalDeviationRate
                ? summaryData.totalDeviationRate
                : "-"
            }}
          </div>
        </div>
        <div class="card-item">
          <div class="name">平均偏差率（%）</div>
          <div class="value">
            {{
              summaryData.averageDeviationRate
                ? summaryData.averageDeviationRate
                : "-"
            }}
          </div>
        </div>
      </div>
      <div v-if="activeName === '1' && searchInfo.timeType === 2">
        <ChartsLine :x-data="xData" :loadList="electSeries[0].data" :elecList="electSeries[1].data"
          :deviation-series="deviationSeries[0].data" height="520px" />
      </div>
      <div v-else>
        <ChartsMergeLine :x-data="xData" :loadList="electSeries[0].data" :elecList="electSeries[1].data"
          :deviation-series="deviationSeries[0].data" :temperatureSeries="temperatureSeries[0].data" height="760px" />
      </div>
    </div>
    <el-dialog destroy-on-close v-model="dialogVisible" title="预测配置" width="30%">
      <el-form ref="ruleFormRef" :model="formInline" :rules="dataFormRules" label-width="160px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="实际电量数据源：">
              <DictSelect disabled v-model="dataSource" :clearable="false" dict-code="electDataSource" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="算法类型：">
              <DictSelect disabled v-model="dataType" :clearable="false" dict-code="computeType" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="样本周期：" prop="parameterValue">
              <DictSelect :clearable="false" v-model="formInline.parameterValue" dict-code="forecastSamplePeriod" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submit(ruleFormRef)">
            提交
          </el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog width="60%" append-to-body v-model="selectVisible" destroy-on-close title="客户选择">
      <customer-select @change="handleClose" />
    </el-dialog>
  </section>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from "vue";
import dayjs from "dayjs";
import ChartsLine from "./components/ChartsLine.vue";
import ChartsMergeLine from "./components/ChartsMergeLine.vue";

import {
  getForecastHomeApi,
  getForecastAnalysisDateApi,
  getForecasConfigApi,
  saveForecasConfigApi, exportDayPointsApi
} from "@/api/load-forecasting/index";
import CustomerSelect from "@/components/Core/PowerConsumerSelect/powerConsumer.vue";
import type { TabsPaneContext, FormInstance } from "element-plus";
import {ElLoading, ElMessage} from "element-plus";
import {Edit} from "@element-plus/icons-vue";

defineOptions({
  name: "LoadForecastingForecast"
});
const ruleFormRef = ref<FormInstance>();
const selectVisible = ref<boolean>(false);
const dataSource = ref("用户分时电量");
const dataType = ref("均值算法");
const customName = ref<string>("");
const loading = ref<boolean>(false);
// 顶部汇总
const summaryData = ref({
  averageDeviationRate: 0,
  forecastTotal: 0,
  realTotal: 0,
  totalDeviationRate: 0
});
const dataFormRules = {
  dataSource: [
    {
      required: true,
      message: "实际电量数据源是必填项",
      trigger: "blur"
    }
  ],
  type: [
    {
      required: true,
      message: "算法类型是必填项",
      trigger: "change"
    }
  ],
  parameterValue: [
    {
      required: true,
      message: "样本周期是必填项",
      trigger: "change"
    }
  ]
};
const xData = ref<string[]>([]);
const dataRange = ref<number>(0);
const searchInfo = ref({
  startDate: dayjs().valueOf(),
  endDate: dayjs().valueOf(),
  timeType: 1,
  customId: 0
});
const dataUpdateInfo = ref({
  startDate: dayjs().format('YYYY-MM-DD'),
  endDate: dayjs().format('YYYY-MM-DD'),
})
const dialogVisible = ref<boolean>(false);
const groupOptions = ref([]);
const formInline = ref({
  id: undefined,
  parameterName: undefined,
  parameterValue: undefined,
  remark: undefined
});
// 电量曲线
const electSeries = ref([
  {
    type: "line",
    showSymbol: false,
    smooth: true,
    name: "负荷预测",
    markPoint: {
      label: {
        formatter: params => params.value
      },
      data: [
        { type: "max", name: "Max" },
        { type: "min", name: "Min" }
      ]
    },
    data: [100, 105, 103, 104, 107]
  },
  {
    type: "line",
    showSymbol: false,
    smooth: true,
    name: "实时电量",
    markPoint: {
      label: {
        formatter: params => params.value
      },
      data: [
        { type: "max", name: "Max" },
        { type: "min", name: "Min" }
      ]
    },
    data: [110, 115, 113, 124, 117]
  }
]);
// 偏差率曲线
const deviationSeries = ref([
  {
    type: "bar",
    name: "偏差率",
    markPoint: {
      label: {
        formatter: params => params.value
      },
      data: [
        { type: "max", name: "Max" },
        { type: "min", name: "Min" }
      ]
    },
    data: [100, 105, 103, 104, 107]
  }
]);
// 气温曲线
const temperatureSeries = ref([
  {
    type: "line",
    name: "气温",
    showSymbol: false,
    smooth: true,
    markPoint: {
      label: {
        formatter: params => params.value
      },
      data: [
        { type: "max", name: "Max" },
        { type: "min", name: "Min" }
      ]
    },
    data: [100, 105, 103, 104, 107]
  }
]);
const activeName = ref<string>("0");
async function getForecastHome() {
  loading.value = true;
  if (activeName.value === "0") {
    searchInfo.value.endDate = searchInfo.value.startDate;
  }
  if (dataRange.value === 0) {
    searchInfo.value.customId = 0;
  }
  const res = await getForecastHomeApi({
    parameter: {
      ...searchInfo.value
    }
  });
  dataUpdateInfo.value.startDate = dayjs(Number(res.data.parameter.startDate)).format('YYYY-MM-DD');
  dataUpdateInfo.value.endDate = dayjs(Number(res.data.parameter.endDate)).format('YYYY-MM-DD');
  if (res.data.curveList) {
    summaryData.value = { ...res.data.summary };
    xData.value = res.data.curveList.map(i => i.time);
    electSeries.value[0].data = res.data.curveList.map(i => i.forecast);
    electSeries.value[1].data = res.data.curveList.map(i => i.real);
    deviationSeries.value[0].data = res.data.curveList.map(i => i.deviation);
    temperatureSeries.value[0].data = res.data.curveList.map(
      i => i.temperature
    );
  } else {
    summaryData.value = {
      averageDeviationRate: 0,
      forecastTotal: 0,
      realTotal: 0,
      totalDeviationRate: 0
    };
    xData.value = [];
    electSeries.value[0].data = [];
    electSeries.value[1].data = [];
    deviationSeries.value[0].data = [];
    temperatureSeries.value[0].data = [];
  }
  setTimeout(() => {
    loading.value = false;
  }, 300);
}
async function handleDetail() {
  const res = await getForecasConfigApi();
  formInline.value = { ...res.data };
  formInline.value.parameterValue = Number(res.data.parameterValue);
  dialogVisible.value = true;
}
async function submit(formEl: FormInstance | undefined) {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      const res = await saveForecasConfigApi(formInline.value);
      if (res.code === "200") {
        ElMessage({
          message: "操作成功",
          type: "success"
        });
        dialogVisible.value = false;
      } else {
        ElMessage({
          message: res.message,
          type: "success"
        });
      }
    } else {
      console.log("error submit!", fields);
    }
  });
}
// 客户确认选择弹框事件
function handleClose(row) {
  selectVisible.value = false;
  searchInfo.value.customId = row.id;
  customName.value = row.name;
}
const handleClick = (tab: TabsPaneContext) => {
  activeName.value = tab.index;
  searchInfo.value.timeType = 1;
  getForecastHome();
};
function handleDateModeChange(value) {
  searchInfo.value.timeType = value;
  getForecastHome();
}
async function handleReset() {
  searchInfo.value.timeType = 1;
  searchInfo.value.customId = 0;
  dataRange.value = 0;
  customName.value = "";
  await getNewDate();
  getForecastHome();
}
async function getNewDate() {
  // const res = await getForecastAnalysisDateApi();
  // searchInfo.value.startDate = dayjs(res.data.maxPowerDate).valueOf();
  // searchInfo.value.endDate = dayjs(res.data.maxPowerDate).valueOf();
  // dataUpdateInfo.value.startDate= dayjs(res.data.maxPowerDate).startOf('month').format('YYYY-MM-DD');
  // dataUpdateInfo.value.endDate= dayjs(res.data.maxPowerDate).format('YYYY-MM-DD');
}
onMounted(async () => {
  await getNewDate();
  getForecastHome();
});
// watch(
//   () => activeName.value,
//   newVal => {
//     if (newVal === "0") {
//       searchInfo.value.endDate = searchInfo.value.startDate;
//     }
//   },
//   { immediate: true }
// );
// 导出数据
function Download() {
  const loading = ElLoading.service({ text: '正在下载...' })
  if (activeName.value === "0") {
    searchInfo.value.endDate = searchInfo.value.startDate;
  }
  if (dataRange.value === 0) {
    searchInfo.value.customId = 0;
  }
  exportDayPointsApi({
    parameter: {
      ...searchInfo.value
    }
  })
      .then((data) => {
        const blob = new Blob([data])
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `负荷预测-${activeName.value == 0 ? '短期' : '中长期' }预测导出.xlsx`
        link.click()
        window.URL.revokeObjectURL(url)
        loading.close()
      })
      .catch(() => {
        ElMessage.error('下载失败')
        loading.close()
      })
}
</script>

<style lang="scss" scoped>
.forecast-container {
  .forecast-header {
    border-radius: 6px;
    box-shadow: 0px 2px 22px rgba(0, 0, 0, 0.06);
    padding: 20px 5px;
  }

  .card-item {
    width: 25%;
    padding: 10px 15px;
    background: #f5f4fb;
    border-radius: 6px;
    display: flex;
    flex-direction: column;
    // justify-items: center;
    // align-items: center;
    margin-right: 40px;
    font-weight: 600;

    .name {}

    .value {
      font-weight: 700;
      margin-top: 6px;
      color: var(--el-color-primary);
    }

    &:last-child {
      margin-right: 0;
    }
  }
}
</style>
