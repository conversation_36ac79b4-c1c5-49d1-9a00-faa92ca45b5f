<template>
    <div>
        <el-card class="jt-card">
            <div class="header" style="margin-bottom: 20px">
                <div class="header-title">
                    <img :src="getAssetURL('title-arrow')" />
                    <!-- <el-image src="@src/assets/svg/title-arrow.svg" alt="" /> -->
                    查询条件
                </div>
            </div>
            <div style="display: flex; justify-content: space-between;">

                <div class="flex flex-wrap gap-4 items-center">
                    <span>最新版本计算状态</span>
                    <el-select v-model="condition.latestCalculationStatus" placeholder="不限" style="width: 130px"
                        @change="selectChangeStatus">
                        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                    <span>更新方式</span>
                    <el-select v-model="condition.updateType" placeholder="不限" style="width: 130px"
                        @change="selectupdataMethod">
                        <el-option v-for="item in options1" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                    <span>标签状态</span>
                    <el-select v-model="condition.labelStatus" placeholder="不限" style="width: 130px"
                        @change="selectLabelStatus">
                        <el-option v-for="item in options2" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                    <span>创建方式</span>
                    <el-select v-model="condition.createType" placeholder="不限" style="width: 130px"
                        @change="selectCreateType">
                        <el-option v-for="item in options3" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </div>
                <div>
                    <el-input v-model="condition.searchKey" placeholder=" 搜索标签显示名/标签名称">
                        <template #append>
                            <el-button @click="handlerSearchKey" type="primary" :icon="Search" />
                        </template>
                    </el-input>
                </div>
            </div>

        </el-card>

        <el-card class="jt-card" style="margin-top: 20px;">
            <div class="header" style="margin-bottom: 20px">
                <div class="header-title">
                    <img :src="getAssetURL('title-arrow')" />
                    标签列表
                </div>
                <div>
                    <el-button style="margin-left: 20px" @click="handleCreatlevel">
                        <b>创建一级类别</b>
                    </el-button>
                    <el-button style="margin-left: 20px" @click="handleCategory" v-if="!isAdd">
                        <b>创建类别 </b>

                    </el-button>
                    <el-button style="margin-left: 20px" @click="handleSave" v-else-if="isAdd">
                        <b>保存类别</b>
                    </el-button>
                    <el-button style="margin-left: 20px" @click="handleList">
                        <b>列目显示</b>
                    </el-button>
                    <el-button style="margin-left: 20px" type="primary" @click="handleCreateLabel">
                        <b>创建标签</b>
                    </el-button>
                    <!-- <el-button style="margin-left: 20px" type="primary" @click="handlerDetails(1)">
                        <b>创建标签</b>
                    </el-button>  -->
                </div>
            </div>
            <el-table :data="tableData" style="width: 100%; margin-bottom: 20px" row-key="id" border :header-cell-style="{
                borderColor: '#DCDFE6',
                color: '#1D2129',
                backgroundColor: '#F2F3F5',
                textAlign: 'center'
            }">
                <el-table-column fixed="left" prop="date" label="全部标签" width="350" align="center">
                    <template #default="scope">
                        <el-checkbox v-model="scope.row.isChecked" size="large" style="margin-right: 15px;" />

                        <el-icon v-if="scope.row.level === 0 || scope.row.level === 1">
                            <FolderRemove />
                        </el-icon>

                        <el-icon v-if="scope.row.level === 2" :class="{ fcolor: scope.row.level === 2 }"
                            @click="handlerDetails(scope.row)">
                            <Discount />
                        </el-icon>
                        <el-input
                            v-if="inputVisible && scope.row.isEditing && (scope.row.level === 1 || scope.row.level === 0)"
                            ref="InputRef" v-model="scope.row.labelNameCn" class="w-20 input_value" size="small"
                            @keyup.enter="handleInputConfirm(scope.row)" @blur="handleInputConfirm(scope.row)"
                            maxlength="10" />

                        <el-button
                            v-if="!inputVisible && !scope.row.isEditing && (scope.row.level === 1 || scope.row.level === 0)"
                            class="button-new-tag btn-tag" :class="{ fcolor: scope.row.level === 2 }" size="large"
                            @click="showInput(scope.row)">
                            {{ scope.row.labelNameCn }}
                        </el-button>
                        <el-button
                            v-if="inputVisible && !scope.row.isEditing && (scope.row.level === 1 || scope.row.level === 0)"
                            class="button-new-tag btn-tag" :class="{ fcolor: scope.row.level === 2 }" size="large"
                            @click="showInput(scope.row)">
                            {{ scope.row.labelNameCn }}
                        </el-button>
                        <!-- <el-button >
                           
                        </el-button > -->
                        <el-tooltip  v-if="scope.row.level === 2" 
                          :content="scope.row.labelNameCn" effect="light"  placement="top">
                            <el-button  @click="handlerDetails(scope.row)" class="button-new-tag btn-tag" :class="{ fcolor: scope.row.level === 2 }" > 
                                <span style="display: inline-block; max-width: 130px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
      {{ scope.row.labelNameCn }}
    </span>
                            </el-button>
                        </el-tooltip>
                        <!-- <el-button v-if="inputVisible && isAdd && scope.row.isEditing ==false && scope.row.level === 2" class="button-new-tag btn-tag" :class="{fcolor:scope.row.level === 2}"  size="small" @click="showInput(scope.row)">
                            {{ scope.row.labelNameCn }}
                        </el-button> -->
                        <el-icon v-if="!inputVisible && isAdd && scope.row.level == 0"
                            style="color: red; cursor: pointer;" @click="subordinate(scope.row)">
                            <Plus />
                        </el-icon>

                    </template>
                </el-table-column>
                <el-table-column :prop="item.value" v-for="(item, index) in tableDatas" :key="index" :label="item.label"
                    align="center">
                    <template #default="scope">
                        <span class="circle"
                            v-if="scope.row.latestCalculationStatus === 1 && item.value === 'latestCalculationStatus'"></span>
                        <span class="grey"
                            v-if="(scope.row.latestCalculationStatus === 2 || scope.row.latestCalculationStatus === 3) && item.value === 'latestCalculationStatus'"></span>
                        <span
                            v-if="scope.row.latestCalculationStatus === 1 && item.value === 'latestCalculationStatus'">成功</span>
                        <span
                            v-if="scope.row.latestCalculationStatus === 2 && item.value === 'latestCalculationStatus'">等待计算</span>
                        <span
                            v-if="scope.row.latestCalculationStatus === 3 && item.value === 'latestCalculationStatus'">失败</span>
                        <span v-if="scope.row.updateType === 1 && item.value === 'updateType'">
                            <el-icon>
                                <VideoPlay />
                            </el-icon>
                        </span>
                        <span v-if="scope.row.updateType === 1 && item.value === 'updateType'" class="routine">例行</span>
                        <span v-if="scope.row.updateType === 2 && item.value === 'updateType'"
                            @click="handlerUpata(scope.row)" class="handler">
                            <el-icon>
                                <Pointer />
                            </el-icon>
                        </span>
                        <span v-if="scope.row.updateType === 2 && item.value === 'updateType'"
                            @click="handlerUpata(scope.row)" class="point" style="color:green ;">手动</span>
                        <spa class="circle" v-if="scope.row.labelStatus !== null && item.value === 'labelStatus'">
                        </spa>
                        <span v-if="scope.row.labelStatus === 1 && item.value === 'labelStatus'">上线</span>
                        <span v-if="scope.row.labelStatus === 2 && item.value === 'labelStatus'">下线</span>


                    </template>
                </el-table-column>

                <el-table-column align="center" fixed="right" label="操作" width="200">
                    <template #default="scope">
                        <div style="display: flex;justify-content: center;">
                            <div @click="onRemove(scope.row)" style="margin-right: 15px;display: flex;
                  align-items: center; color: #f53f3f; cursor: pointer"
                                v-if="scope.row.level === 0 || scope.row.level === 1">
                                <img :src="getAssetURL('delete')" style="margin-right: 2px" />
                                删除
                            </div>
                            <div v-else style="display: flex;">
                                <div @click="handlerDetails(scope.row)" style="color: #165dff; cursor: pointer;">

                                    详情
                                </div>
                                <el-dropdown style="margin-left: 10px;" trigger="click">
                                    <span class="el-dropdown-link" style="color: #165dff;">
                                        更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
                                    </span>
                                    <template #dropdown>
                                        <el-dropdown-menu>
                                            <el-dropdown-item disabled @click="onEdit(scope.row)">编辑</el-dropdown-item>
                                            <el-dropdown-item style="color: #165dff; cursor: pointer;"
                                                @click="onRemove(scope.row)">删除</el-dropdown-item>
                                        </el-dropdown-menu>
                                    </template>
                                </el-dropdown>
                            </div>


                        </div>
                    </template>
                </el-table-column>
            </el-table>

        </el-card>
        <createLabel ref="refLabel"></createLabel>
        <showList ref="refShowList" @orderData="getOrderData" :filterOptions="tableDatas" :def="def"></showList>
        <!-- <labelDetails ref="refLabelDetail"></labelDetails> -->

    </div>

</template>


<script setup lang="ts">
import { nextTick, onMounted, ref, onActivated, reactive, onDeactivated } from 'vue'
import { Search, Plus, FolderRemove, ArrowDown, Discount, VideoPlay, Pointer } from '@element-plus/icons-vue'
import createLabel from './components/createLabel.vue'
import showList from './components/showList.vue'
import { ElInput, ElMessage, ElMessageBox } from 'element-plus'
import type { Action } from 'element-plus'
import { addLabelCategory, queryLabelList, modifyLabelCategoryName, delCustomLabel, delLabelCategory, executeRules } from '@/api'
const { push, go } = useRouter();

import { useRouter } from 'vue-router'

const value = ref('')

onActivated(() => {
    initList({})
})

const condition = ref({
    searchKey: "",
    createType: "",
    labelStatus: "",
    latestCalculationStatus: "",
    updateType: ""

})


onDeactivated(() => {
    condition.value.searchKey = "",
        condition.value.createType = "",
        condition.value.labelStatus = "",
        condition.value.latestCalculationStatus = "",
        condition.value.updateType = ""
})

const isShow = ref(false)
const inputValue = ref('')
const options = [
    {
        value: null,
        label: '不限',
    },
    {
        value: 1,
        label: '成功',
    },
    {
        value: 2,
        label: '等待计算',
    },
    {
        value: 3,
        label: '失败',
    }

]
const options1 = [
    {
        value: null,
        label: '不限',
    },
    {
        value: 2,
        label: '手动',
    },
    {
        value: 1,
        label: '例行',
    }
]
const options2 = [
    {
        value: null,
        label: '不限',
    },
    {
        value: 1,
        label: '上线',
    }
    // {
    //     value: '2',
    //     label: '下线',
    // }
]
const options3 = [
    {
        value: null,
        label: '不限',
    },
    {
        value: '规则匹配类',
        label: '规则匹配类',
    },
    {
        value: '统计类',
        label: '统计类',
    },
    {
        value: '挖掘类',
        label: '挖掘类',
    },
]
const getAssetURL = (image: any) => {
    return new URL(`../../../assets/svg/${image}.svg`, import.meta.url).href
}


// 表格数据
const tableData = ref([])
// window.addEventListener('blur', function() {

//     alert('用户离开了页面');
//     // 这里可以执行一些清理工作，比如停止音频播放、取消网络请求等
// });

// 获取初始化列表
const initList = async (item: any) => {

    const res = await queryLabelList(item)
    tableData.value = transformTreeData(res) || []


}

function transformTreeData(data: any) {
    if (data.length == 0) return [];
    const treeData = ref<any>([]);
    for (const item of data) {
        treeData.value.push({
            ...item.data,
            children: transformTreeData(item.children),
        });
    }
    return treeData.value
}



// 选中状态
const checked1 = ref(false)
const inputVisible = ref(false)
const InputRef = ref<InstanceType<typeof ElInput>>()

// const searchKey = ref('')
const selectChangeStatus = (row: any) => {
    initList({ latestCalculationStatus: condition.value.latestCalculationStatus, labelStatus: condition.value.labelStatus, updateType: condition.value.updateType, createType: condition.value.createType, searchKey: condition.value.searchKey })
}
const selectLabelStatus = (row: any) => {
    initList({ latestCalculationStatus: condition.value.latestCalculationStatus, labelStatus: condition.value.labelStatus, updateType: condition.value.updateType, createType: condition.value.createType, searchKey: condition.value.searchKey })
}
const selectupdataMethod = (row: any) => {
    initList({ latestCalculationStatus: condition.value.latestCalculationStatus, labelStatus: condition.value.labelStatus, updateType: condition.value.updateType, createType: condition.value.createType, searchKey: condition.value.searchKey })
}
const selectCreateType = (row: any) => {
    initList({ latestCalculationStatus: condition.value.latestCalculationStatus, labelStatus: condition.value.labelStatus, updateType: condition.value.updateType, createType: condition.value.createType, searchKey: condition.value.searchKey })
}
const handlerSearchKey = (row: any) => {
    initList({ latestCalculationStatus: condition.value.latestCalculationStatus, labelStatus: condition.value.labelStatus, updateType: condition.value.updateType, createType: condition.value.createType, searchKey: condition.value.searchKey })
}
const showInput = (row: any) => {
    inputVisible.value = true
    console.log(!inputVisible.value)
    row.isEditing = true
    // isAdd.value = true
    console.log(row)
    // if (isAdd.value) {

    // }

    // nextTick(() => {
    //     InputRef.value!.input!.focus()
    // })
}


const handleInputConfirm = async (row: any) => {
    console.log(row)
    let params = {
        categoryId: row.id,
        categoryName: row.labelNameCn,
        level: row.level,
        parentId: row.parentId
    }
    modifyLabelCategoryName(params).then((res: any) => {
        initList({})
    }).catch((err: any) => { }).finally(() => {
        initList({})
    })
    row.isEditing = false
    inputVisible.value = false
}
//表格添加一行
const refLabel = ref(null);
const isAdd = ref(false)
const createLabelStatus = ref(false)
const handleCreateLabel = () => {
    if (isAdd.value) {
        handlerTips()
    } else {

        refLabel.value.dialogVisible = true
        isShow.value = true
    }
    // refLabel.value.handleCreateLabel('createLable')

    // createLabelStatus.value = true
}

// 创建类别
const handleCategory = () => {
    if (!isAdd.value) {
        isAdd.value = true
    } else {
        isAdd.value = false
    }

}




// 创建一级类别
let params = {
    categoryName: "",
    level: 0,

}

const levelIndex = ref([])
const list = ref([]);
const handleCreatlevel = async () => {
    if (isAdd.value) {
        handlerTips()
    } else {

        if (tableData.value.length > 0) {
            levelIndex.value = []
            tableData.value.forEach(item => {
                item.isChecked = false
                levelIndex.value.push(Number(item.labelNameCn.slice(5, 6)))

            })
        } else {
            levelIndex.value.push(0)
        }

        const index = findContinuityOrMissing(levelIndex.value)

        if (levelIndex.value.length < 10) {

            // 1.调用接口 向尾部添加一级
            list.value = []
            params.categoryName = `任务一 (${index}) `
            list.value.push({ ...params })
            let res = await addLabelCategory(list.value)
            if (res) {
                initList({})
            }
        } else {
            ElMessage({
                message: '最大只能添加10层',
                type: 'warning',
            });

        }

    }




}
function goBackAndClearStack() {
    if (window.history.length <= 1) {
        push({ path: "/labelManagement" });
        return false;
    } else {
        go(-1);
    }
    // push({ path: "/userPortrait" });
}

// 点击保存
const handleSave = () => {
    isAdd.value = false
    //将当前修改的数据保存到后台
}


const handlerTips = () => {
    ElMessageBox.alert('请先保存类别', '提示', {
        // if you want to disable its autofocus
        // autofocus: false,
        confirmButtonText: '确定',

    })
}

const refShowList = ref(null);
const def = ref([])
//列表类目
const handleList = () => {


    // def.value =  tableDatas.value.map(item => item.label)
    // console.log(def.value)

    if (isAdd.value) {
        handlerTips()
    } else {
        refShowList.value.handleShowList()
    }

}

const list2 = ref([]);
// 向数组末尾 添加下级 点击图标新增下级 { ...params, parentId: row.id, level: row.level + 1 }
let childrenIndex = ref([])
const subordinate = async (row: any) => {
    if (row.children.length > 0) {
        childrenIndex.value = []
        row.children.forEach(item => {
            console.log(item, item.labelNameCn.slice(4, 5))
            childrenIndex.value.push(Number(item.labelNameCn.slice(4, 5)))

        })
    } else {
        // childrenIndex.value = []
        childrenIndex.value.push(0)
    }
    console.log(childrenIndex.value)
    let index = findContinuityOrMissing(childrenIndex.value)
    console.log(index)
    if (childrenIndex.value.length < 10) {
        list2.value = []
        params.categoryName = `任务二(${index})`
        list2.value.push({ ...params, parentId: row.id, level: row.level + 1 })
        let res = await addLabelCategory(list2.value)
        if (res) {
            initList({})
        }
    } else {
        ElMessage({
            message: '最大只能添加10层',
            type: 'warning',
        });
    }


}
const arr = [1, 3, 5, 7, 9, 10]
// arr.push()
// 处理序号问题
function findContinuityOrMissing(arr) {
    // 如果数组为空，直接返回1
    if (arr.length === 0) return 1;

    // 对数组进行排序（对于非递增序列是必要的）
    arr.sort((a, b) => a - b);

    // 遍历数组，查找第一个不连续的位置
    for (let i = 0; i < arr.length - 1; i++) {
        // 如果当前元素与下一个元素的差大于1，则返回当前元素加1
        if (arr[i + 1] - arr[i] > 1) {
            return arr[i] + 1;
        }
    }

    // 如果数组完全连续，则返回最后一个元素加1
    return arr[arr.length - 1] + 1;
}

// 点击删除分类标签
const onRemove = (row: any) => {
    if (isAdd.value) {
        handlerTips()
    } else {

        if (row.level === 0 || row.level === 1) {
            ElMessageBox.confirm(
                '是否确定要删除?',

                {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                }
            )
                .then(() => {
                    delLabelCategory(row.id, row.level).then((res: any) => {
                        // ElMessage({
                        //     type: 'success',
                        //     message: '删除成功',
                        // })
                        initList({})
                    }).catch((err: any) => { })

                })
                .catch(() => {
                    ElMessage({
                        type: 'info',
                        message: '取消删除',
                    })
                })
        } else {


            ElMessageBox.confirm(
                '是否确定要删除?',

                {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                }
            )
                .then(() => {
                    //      delLabelCategory(row.id, row.level).then((res: any) => {
                    //         ElMessage({
                    //             type: 'success',
                    //             message: '删除成功',
                    //         })
                    //     initList({})
                    // }).catch((err: any) => { })
                    delCustomLabel(row.id).then((res: any) => {
                        // ElMessage({
                        //     type: 'success',
                        //     message: '删除成功',
                        // })
                        initList({})
                    }).catch((err: any) => { })
                })
                .catch(() => {
                    ElMessage({
                        type: 'info',
                        message: '取消删除',
                    })
                })
        }
    }


}
// 点击编辑按钮
const onEdit = (row: any) => {
    if (isAdd.value) {
        handlerTips()
    } else {
        push(`/userPortrait/labelManagement/editLabelRules?id=${row.id}`);
    }

}

const onDetail = (row: any) => {
    push(`/userPortrait/labelManagement/labelDetails`);
}
const handlerDetails = (row: any) => {
    // localStorage.setItem('userDetails', JSON.stringify(row));
    console.log("aaaaaaaaaa", row)
    if (isAdd.value) {
        handlerTips()
    } else {
        push(`/userPortrait/labelManagement/labelUserDetails?id=${row.id}&&name=${row.labelNameCn}`);
    }

}

const handlerUpata = async (row: any) => {
    console.log(row.id)
    const params = {
        // createType: row.createType,
        labelId: row.id,
        // labelStatus: row.labelStatus,
        // latestCalculationStatus: row.latestCalculationStatus,
        // updateType: row.updateType
    }
    await executeRules(params).then((res: any) => {
        initList({ latestCalculationStatus: condition.value.latestCalculationStatus, labelStatus: condition.value.labelStatus, updateType: condition.value.updateType, createType: condition.value.createType, searchKey: condition.value.searchKey })
    }).catch((err: any) => { })
}
const tableDatas = ref([
    { label: '覆盖客户数据', value: 'coveredNum' },
    { label: '标签值', value: 'labelValue' },
    { label: '最新计算状态', value: 'latestCalculationStatus' },
    { label: '更新方式', value: 'updateType' },
    { label: '标签状态', value: 'labelStatus' },
    { label: '创建方式', value: 'createType' },
    { label: '创建时间', value: 'createTimeStr' }
])


const getOrderData = (row: any) => {

    tableDatas.value = row

}

</script>
<style lang="scss" scoped>
.fcolor {
    color: #165dff;
}

.point {
    display: inline-block;
    width: 30px;
    height: 10px;
    cursor: pointer;
}

.routine {

    padding-left: 10px;
}

.handler {
    padding-right: 10px;
    color: green;
    cursor: pointer;
}

.btn-tag {

    border: none;
    width: 110px;
   
    margin: 0 15px;

}

.input_value {
    margin: 0 15px;
    width: 80px;
}

.circle {
    display: inline-block;
    width: 8px;
    height: 8px;
    background: green;
    border-radius: 50%;
    margin-right: 5px;
}

.grey {
    display: inline-block;
    width: 8px;
    height: 8px;
    background: #7f8385;
    border-radius: 50%;
    margin-right: 5px;
}
</style>