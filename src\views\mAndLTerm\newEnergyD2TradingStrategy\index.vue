<template>
  <section>
    <el-card class="jt-card" style="margin-bottom: 20px">
      <div class="header">
        <div class="header-title">查询条件</div>
      </div>
      <el-row>
        <el-col :span="9">
          <el-form label-suffix=":">
            <el-row>
              <el-form-item label="交易日期" label-width="80px">
                <el-date-picker
                  v-model="theDateOfTheTransaction"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  :clearable="false"
                  is-range
                  range-separator="-"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  @change="theDateOfTheTransactionChange"
                  @focus="isShow($event)"
                  @visible-change="beforeChange"
                >
                  <template #default="cell">
                    <div class="cell" :class="{ current: cell.isCurrent }">
                      <span class="text">{{ cell.text }}</span>
                      <span v-if="isHoliday(cell)" class="holiday" />
                    </div>
                  </template>
                  <template #range-separator>
                    <div>自定义内容</div>
                  </template>
                </el-date-picker>
              </el-form-item>
            </el-row>
          </el-form>
        </el-col>
        <el-col :span="8"> </el-col>
        <el-col :span="6" :push="3">
          <el-button
            type="primary"
            :icon="Document as any"
            @click="updateTrading()"
          >
            保存
          </el-button>
          <el-button type="primary" :icon="Edit as any" @click="exportButton">
            导出
          </el-button>
        </el-col>
      </el-row>
    </el-card>
    <el-card class="jt-card" v-loading="loading">
      <div class="header">
        <div class="header-title">申报策略</div>
      </div>
      <el-form label-suffix=":" label-width="80px">
        <!--        <el-row>-->
        <!--          <el-form-item label="谷段">-->
        <!--            <el-radio-group v-model="tradingStrategyValley">-->
        <!--              <el-radio label="增持"></el-radio>-->
        <!--              <el-radio label="减持"></el-radio>-->
        <!--            </el-radio-group>-->
        <!--          </el-form-item>-->
        <!--          <el-form-item label="比例(%)">-->
        <!--            <el-input v-model="tradingStrategyRatioValley"></el-input>-->
        <!--          </el-form-item>-->
        <!--          <el-form-item label="峰段">-->
        <!--            <el-radio-group v-model="tradingStrategyPeek">-->
        <!--              <el-radio label="增持"></el-radio>-->
        <!--              <el-radio label="减持"></el-radio>-->
        <!--            </el-radio-group>-->
        <!--          </el-form-item>-->
        <!--          <el-form-item label="比例(%)">-->
        <!--            <el-input v-model="tradingStrategyRatioPeek"></el-input>-->
        <!--          </el-form-item>-->
        <!--        </el-row>-->
        <el-row>
          <el-form-item label="时段">
            <el-select v-model="tradingStrategy" placeholder="请选择"
              :clearable="true" multiple style="width: 300px">
              <el-option value="尖峰"></el-option>
              <el-option value="峰段"></el-option>
              <el-option value="平段"></el-option>
              <el-option value="谷段"></el-option>
            </el-select>
<!--            <el-radio-group v-model="tradingStrategyValley">-->
<!--              <el-radio label="增持"></el-radio>-->
<!--              <el-radio label="减持"></el-radio>-->
<!--            </el-radio-group>-->
          </el-form-item>
          <el-form-item label="比例(%)">
            <el-input-number :controls-position="'right'" :step="1" :precision="3" v-model="tradingStrategyRatio"></el-input-number>
          </el-form-item>
          <el-button
            type="primary"
            style="margin-left: 10px; margin-right: 30px"
            @click="applyButton"
            >应用</el-button
          >
          <el-form label-suffix=":">
            <el-row>
              <el-form-item label-width="60px" label="尖峰">
                <div
                  style="
                    width: 32px;
                    height: 16px;
                    border-radius: 2px;
                    background-color: #fbb4b4;
                  "
                ></div>
              </el-form-item>
              <el-form-item label-width="60px" label="峰段">
                <div
                  style="
                    width: 32px;
                    height: 16px;
                    border-radius: 2px;
                    background-color: #ffdead;
                  "
                ></div>
              </el-form-item>
              <el-form-item label-width="60px" label="平段">
                <div
                  style="
                    width: 32px;
                    height: 16px;
                    border-radius: 2px;
                    background-color: #aef7d8;
                  "
                ></div>
              </el-form-item>
              <el-form-item label-width="60px" label="谷段">
                <div
                  style="
                    width: 32px;
                    height: 16px;
                    border-radius: 2px;
                    background-color: #9ce1ff;
                  "
                ></div>
              </el-form-item>
            </el-row>
          </el-form>
        </el-row>
        <el-row> 电量: &nbsp MWh,&nbsp电价: &nbsp 元/MWh </el-row>
      </el-form>
      <!--	时段表-->
      <hot-table
        style="margin-top: 10px"
        ref="hotTableComponent"
        :settings="settings"
      >
        <hot-column
          width="50px"
          title="序号"
          data="index"
          readOnly="true"
        ></hot-column>
        <hot-column
          title="时段类型"
          data="timeSlot"
          readOnly="true"
        ></hot-column>
        <hot-column
          title="已有合同电量"
          data="existContractElectricity"
        ></hot-column>
        <hot-column title="已有合同电价" data="existContractPrice"></hot-column>
        <!--        <hot-column title="绿电电量" data="greenElectricity"></hot-column>-->
        <!--        <hot-column title="总合同电量" data="totalContractElectricity" readOnly="true"></hot-column>-->
        <hot-column
          title="预测电量"
          data="predictedElectricity"
          readOnly="true"
        ></hot-column>
        <hot-column
          title="交易前偏差电量(公式"
          data="preTradeDeviationPower"
          readOnly="true"
        ></hot-column>
        <hot-column
          title="可卖出电量(90%)"
          data="electricityCanBeSold"
          readOnly="true"
        ></hot-column>
        <hot-column
          title="可买入电量(120%)"
          data="electricityCanBeSold120"
          readOnly="true"
        ></hot-column>
        <hot-column
          title="交易前合同覆盖率(%)(公式"
          data="preTradeContractCoverage"
          readOnly="true"
        ></hot-column>
        <hot-column
          title="拟交易电量(手填"
          data="marketTradingElectricity"
        ></hot-column>
        <hot-column
          title="拟交易电价(手填"
          data="marketTradingPrice"
        ></hot-column>
        <hot-column
          title="绿电交易电量(手填"
          data="greenElectricity"
        ></hot-column>
        <hot-column
          title="绿电交易电价(手填"
          data="greenElectricityTradingPrice"
        ></hot-column>
        <!--        <hot-column title="拟交易电量" data="marketTradingElectricity"></hot-column>-->
        <!--        <hot-column title="拟交易电价" data="paperTradingPrice"></hot-column>-->
        <hot-column
          title="交易完成后合同电量(MWh)"
          data="finishedContractElectricity"
          width="150px"
          readOnly="true"
        ></hot-column>
        <hot-column
          title="交易完成后合同电价"
          data="finishedContractPrice"
          width="140px"
          readOnly="true"
        ></hot-column>
        <hot-column
          title="交易完成后合同覆盖率(%)"
          data="deviationValue"
          readOnly="true"
        ></hot-column>
        <hot-column title="" data="valleyPeakType" readOnly="true"></hot-column>
      </hot-table>
    </el-card>
    <el-row :gutter="20">
      <el-col :span="16">
        <el-card style="margin-top: 20px" class="jt-card">
          <div class="header">
            <div class="header-title">相似日24时段实际用电曲线</div>
          </div>
          <Echarts
            :echartsData="similarDayOptionDeal"
            EWidth="100%"
            EHeight="430px"
            echartId="similar"
          ></Echarts>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card style="margin-top: 20px" class="jt-card">
          <div class="header">
            <div class="header-title">偏差申报策略电量占比</div>
          </div>
          <Echarts
            :echartsData="deviationOptionDeal"
            EWidth="100%"
            EHeight="430px"
            echartId="deviation"
          ></Echarts>
        </el-card>
      </el-col>
    </el-row>
  </section>
</template>

<script setup lang="ts">
import {
  nextTick,
  onActivated,
  onBeforeUnmount,
  onDeactivated,
  onMounted,
  ref
} from "vue";
import dayjs from "dayjs";
import { HotColumn, HotTable } from "@handsontable/vue3";
import { registerAllModules } from "handsontable/registry";
import { HyperFormula } from "hyperformula";
import "handsontable/dist/handsontable.full.css";
import { Document, Edit } from "@element-plus/icons-vue";
import { ElMessage, ElLoading, ElMessageBox, Action } from "element-plus";
// Vue Router导航守卫
import { onBeforeRouteLeave } from "vue-router";
import Handsontable from "handsontable";
import "handsontable/dist/handsontable.full.min.css";
import { echartsConfigBottom } from "@/utils/echartsConfig";
import {
  getD2StrategyDataForPeakValley,
  queryD2StrategyAPI,
  queryMonthDayD2StrategyAPI,
  saveD2StrategyAPI,
  spotPriceForecast
} from "@/api/D2TradingStrategy";
import { columns } from "@/views/customer-management/column";
import { getCustomerListApi } from "@/api/customer-management";
import { aoaToSheetXlsx } from "@/components/Excel";
import { aoaD_2ToSheetXlsx } from "@/components/Excel/src/Export2Excel";
// register Handsontable's modules
registerAllModules();

onActivated(() => {
  // console.log('测试')
  // 交易日期
  theDateOfTheTransaction.value = dayjs(Date())
    .add(2, "day")
    .format("YYYY-MM-DD");
  theDateOfTheTransactionChange();
  queryMonthDayD2Strategy();
});

// 交易日期
const theDateOfTheTransaction = ref(
  dayjs(Date()).add(2, "day").format("YYYY-MM-DD")
);
// 交易场站
const tradingFloor = ref();
// 场站选项
const stationOptions = ref();

const holidays = ref([]);

const isHoliday = ({ dayjs }) => {
  return holidays.value.includes(dayjs.format("YYYY-MM-DD"));
};

const previousDate = ref();
const beforeChange = (val: boolean) => {
  if (val) {
    previousDate.value = theDateOfTheTransaction.value;
  }
};

const tradingStrategyValley = ref("增持");
const tradingStrategyRatioValley = ref(0);
const tradingStrategyPeek = ref("增持");
const tradingStrategyRatioPeek = ref(0);

// 获取焦点 展示日期
async function isShow(e) {
  await nextTick(async () => {
    // 获取焦点
    e.target.focus();
    // 获取当前日期
    // theDateOfTheTransaction.value = dayjs(Date()).format('YYYY-MM-DD')
    await monthChange();
    document
      .querySelectorAll(
        "[aria-label='下个月'],[aria-label='上个月'],[aria-label='后一年'],[aria-label='前一年']"
      )
      .forEach(item =>
        item.addEventListener("click", () => {
          monthChange();
        })
      );
  });
}
// 切换年月后重新调接口
async function monthChange() {
  let year, month;
  // 获取年月
  year = document
    .querySelectorAll(".el-date-picker__header-label")[0]
    .innerHTML.slice(0, 4);
  month = document
    .querySelectorAll(".el-date-picker__header-label")[1]
    .innerHTML.slice(0, -1);
  if (Number(month) < 10) {
    // 10月之前都需要补0
    month = "0" + month;
  }
  const datesYearMonth = year + "-" + month;
  // const { data } = await getWorkDay({
  //   month: this.datesYearMonth
  // })
  // if (data) {
  //   if (data.code === 200) {
  //     this.datesvalue = data.data
  //   }
  // }
  // console.log(datesYearMonth)
  await queryMonthDayD2StrategyAPI(
    dayjs(datesYearMonth).format("YYYY-MM")
  ).then(res => {
    holidays.value = [];
    res.forEach((item: any) => {
      if (item.hasData) {
        holidays.value.push(item.date);
      }
    });
  });
}

// const { infoViewTime: tomorrow } = storeToRefs(useInfoViewStore())
const tableData: any = ref();

// 报价表excel
const hotTableComponent = ref<any>(null);
// const hotInstance = hotTableComponent.value.hotInstance
const settings = ref({
  data: tableData.value,
  columns: [
    {
      data: "index",
      type: "numeric",
      title: "序号",
      width: 30,
      readOnly: true,
      headerClassName: "htTwoLines"
    }, // 将 列设置为 numeric 类型
    {
      data: "timeSlot",
      title: "时段类型",
      readOnly: true,
      headerClassName: "htTwoLines"
    },
    {
      data: "existContractElectricity",
      type: "numeric",
      title: "已有合同电量",
      headerClassName: "htTwoLines",
      width: 55
      // readOnly: true,
    },
    {
      data: "existContractPrice",
      type: "numeric",
      title: "已有合同电价",
      headerClassName: "htTwoLines",
      width: 55
      // readOnly: true,
    },
    // {
    //   data: 'greenElectricity',
    //   type: 'numeric',
    //   title: '绿电电量',
    //   headerClassName: 'htTwoLines'
    // },
    // {
    //   data: 'totalContractElectricity',
    //   type: 'numeric',
    //   title: '总合同电量',
    //   readOnly: true,
    //   headerClassName: 'htTwoLines'
    // },
    {
      data: "predictedElectricity",
      type: "numeric",
      title: "预测电量",
      // renderer: dataBarRenderer,
      // readOnly: true,
      width: 55,
      headerClassName: "htTwoLines"
    },
    {
      data: "preTradeDeviationPower",
      type: "numeric",
      title: "交易前偏差电量",
      readOnly: true,
      width: 65,
      headerClassName: "htTwoLines"
    },
    {
      data: "electricityCanBeSold",
      type: "numeric",
      title: "可卖出电量(90%)",
      readOnly: true,
      width: 70,
      headerClassName: "htTwoLines"
    },
    {
      data: "electricityCanBeSold120",
      type: "numeric",
      title: "可买入电量(120%)",
      readOnly: true,
      width: 75,
      headerClassName: "htTwoLines"
    },
    // {
    //   data: 'tradingDeviationElectricity',
    //   type: 'numeric',
    //   title: '日前现货预测电价',
    //   readOnly: true,
    // },
    // {
    //   data: 'marketTradingElectricity',
    //   type: 'numeric',
    //   title: '拟交易电量',
    //   width: 55,
    //   headerClassName: 'htTwoLines'
    // },
    // { data: 'paperTradingPrice',
    //   type: 'numeric',
    //   title: '拟交易电价',
    //   headerClassName: 'htTwoLines'
    // },
    {
      data: "preTradeContractCoverage",
      type: "text",
      title: "交易前合同覆盖率",
      headerClassName: "htTwoLines",
      width: 80,
      readOnly: true
    },
    {
      data: "marketTradingElectricity",
      type: "numeric",
      title: "拟交易电量",
      headerClassName: "htTwoLines",
      width: 65
    },
    {
      data: "marketTradingPrice",
      type: "numeric",
      title: "拟交易电价",
      headerClassName: "htTwoLines",
      width: 65
    },
    {
      data: "greenElectricity",
      type: "numeric",
      title: "绿电交易电量",
      headerClassName: "htTwoLines",
      width: 65
    },
    {
      data: "greenElectricityTradingPrice",
      type: "numeric",
      title: "绿电交易电价",
      headerClassName: "htTwoLines",
      width: 65
    },
    {
      data: "finishedContractElectricity",
      type: "numeric",
      title: "交易完成后合同电量",
      readOnly: true,
      width: 80,
      headerClassName: "htTwoLines"
    },
    {
      data: "finishedContractPrice",
      type: "numeric",
      title: "交易完成后合同电价",
      readOnly: true,
      width: 80,
      headerClassName: "htTwoLines"
    },
    {
      data: "deviationValue",
      type: "text",
      title: "交易完成后合同覆盖率(%)",
      readOnly: true,
      width: 100,
      headerClassName: "htTwoLines"
    },
    {
      data: "valleyPeakType",
      type: "text",
      title: "时段类型",
      readOnly: true,
      visible: false, // 设置为不可见
      headerClassName: "htTwoLines"
    }
    // ... more columns
  ],
  // enable the `HiddenColumns` plugin
  hiddenColumns: {
    // specify columns hidden by default
    columns: [16]
  },
  fixedRowsBottom: 1,
  licenseKey: "non-commercial-and-evaluation",
  plugins: ["formulas"], // 启用公式插件
  formulas: {
    engine: HyperFormula
  },
  stretchH: "all",
  className: "custom-table",
  rowHeaderHeight: 70, // 设置表头高度为 56
  colWidths: 60, // 单元格宽
  rowHeights: 37, // 单元格高
  headerHeight: 100,
  minRows: 1,
  height: 1000, //表高
  manualColumnResize: true, // 列宽调整
  autoRowSize: true,
  cells: function (row, col, prop) {
    // console.log(row, col, prop)
    const cellProperties: any = {};
    // if (col === 5) {
    //   // 设置拟交易电量的公式为预测电量 - 已有合同电量
    //   cellProperties.formula = '=E' + (row + 1) + ' - C' + (row + 1)
    // } else if (col === 7) {
    //   // 设置交易完成后合同电量的公式为已有合同电量 + 拟交易电量
    //   cellProperties.formula = '=C' + (row + 1) + ' + F' + (row + 1)
    // } else if (col === 9) {
    //   // 设置偏差值的公式为 (交易完成后合同电量 - 已有合同电量) / 已有合同电量
    //   cellProperties.formula =
    //       '=(C' +
    //       (row + 1) +
    //       ' + F' +
    //       (row + 1) +
    //       ' - C' +
    //       (row + 1) +
    //       ') / C' +
    //       (row + 1)
    // }
    // console.log(cellProperties.formula)
    const timeType = hotTableComponent.value.hotInstance.getDataAtCell(row, 16); // 假设第11列是 timeType
    const sumFlag = hotTableComponent.value.hotInstance.getDataAtCell(row, 1); // 假设第2列是 合计
    // console.log(timeType)
    if (sumFlag == "合计") {
      cellProperties.className = "sum-row";
    }
    if (timeType == "1") {
      // 峰
      cellProperties.className = "peak-time-row"; // 设置类名为 color1，可以在样式表中定义对应的颜色
    } else if (timeType == "2") {
      // 平
      cellProperties.className = "off-peak-time-row"; // 设置类名为 color2
    } else if (timeType == "3") {
      // 谷
      cellProperties.className = "valley-time-row"; // 设置类名为 color3
    } else if (timeType == "0") {
      // 尖
      cellProperties.className = "peakily-time-row";
    }
    // console.log(cellProperties)
    return cellProperties;
  },
  // renderer: function (instance, td, row, col, prop, value, cellProperties) {
  //   if (cellProperties.formula) {
  //     // 执行公式计算
  //     var formulaResult =
  //       hotTableComponent.value.hotInstance.getPlugin('formulas')
  //     console.log(formulaResult)
  //     td.textContent = formulaResult
  //   }
  // },
  afterChange: function (changes, source) {
    // console.log(source)
    console.log(changes);
    if (source == "edit") {
      isTableDataFlag.value = true;
      similarDayOption.value.series[0].data = [];
      data.value = [0, 0, 0];
      data1.value = [0, 0, 0];
      data2.value = [0, 0, 0];
      let sum = null;
      let sum1 = null;
      let sum2 = null;
      hotTableComponent.value.hotInstance.getData().forEach((item: any) => {
        console.log(item[2]);
        sum += parseFloat(item[2]) ? parseFloat(item[2]) : 0;
        sum1 += parseFloat(item[9]) ? parseFloat(item[9]) : 0;
        sum2 += parseFloat(item[13]) ? parseFloat(item[13]) : 0;
      });
      hotTableComponent.value.hotInstance.getData().forEach((item: any) => {
        similarDayOption.value.series[0].data.push(item[13] ? item[13] : 0);
        // console.log(item[5],item[8],item[10])
        if (item[16] == "0" || item[16] == "1") {
          // 尖峰和高峰
          data.value[0] += parseFloat(item[2]) ? parseFloat(item[2]) : 0;
          data1.value[0] += parseFloat(item[9]) ? parseFloat(item[9]) : 0;
          data2.value[0] += parseFloat(item[13]) ? parseFloat(item[13]) : 0;
        } else if (item[16] == "2") {
          // 平端
          data.value[1] += parseFloat(item[2]) ? parseFloat(item[2]) : 0;
          data1.value[1] += parseFloat(item[9]) ? parseFloat(item[9]) : 0;
          data2.value[1] += parseFloat(item[13]) ? parseFloat(item[13]) : 0;
        } else if (item[16] == "3") {
          // 低谷
          data.value[2] += parseFloat(item[2]) ? parseFloat(item[2]) : 0;
          data1.value[2] += parseFloat(item[9]) ? parseFloat(item[9]) : 0;
          data2.value[2] += parseFloat(item[13]) ? parseFloat(item[13]) : 0;
        }
      });
      sum = sum / 2;
      sum1 = sum1 / 2;
      sum2 = sum2 / 2;
      similarDayOption.value.series[0].data.pop();
      // console.log(data.value,data1.value,data2.value)
      data.value[0] = ((data.value[0] / sum) * 100).toFixed(2);
      data.value[1] = ((data.value[1] / sum) * 100).toFixed(2);
      data.value[2] = ((data.value[2] / sum) * 100).toFixed(2);
      data1.value[0] = ((data1.value[0] / sum1) * 100).toFixed(2);
      data1.value[1] = ((data1.value[1] / sum1) * 100).toFixed(2);
      data1.value[2] = ((data1.value[2] / sum1) * 100).toFixed(2);
      data2.value[0] = ((data2.value[0] / sum2) * 100).toFixed(2);
      data2.value[1] = ((data2.value[1] / sum2) * 100).toFixed(2);
      data2.value[2] = ((data2.value[2] / sum2) * 100).toFixed(2);
      console.log(data.value, data1.value, data2.value);
      // console.log(similarDayOption.value.series[0].data)
    }
    // console.log(changes)
    // changes.forEach(([row, prop, prevValue, newValue]) => {
    //   if (!isNaN(newValue) && newValue !== '') {
    //     this.getSourceDataAtRow(row)[prop] = parseFloat(newValue)
    //   }
    // })
    // console.log(hotTableComponent.value.hotInstance.getData())
  }
});

const tradingStrategy = ref([])
const tradingStrategyRatio = ref()

const applyButton = async () => {
  const newTableData = [...tableData.value] // 复制原数组
  newTableData.pop()
  const tempTableData = newTableData
  const computerTableData = hotTableComponent.value.hotInstance.getData()
  console.log(tradingStrategy.value, tradingStrategyRatio.value, tempTableData, computerTableData)
  // 尖峰
  const peakScaleValue =
      1 +
      tradingStrategyRatio.value *
      0.01;
  // 谷段
  const valleyScaleValue =
      1 +
      tradingStrategyRatio.value *
      0.01;
  // 平段
  const OffPeakScaleValue =
      1 +
      tradingStrategyRatio.value *
      0.01;
  // 高峰
  const peakSegmentsScaleValue =
      1 +
      tradingStrategyRatio.value *
      0.01;
  tradingStrategy.value.forEach((item: any) => {
    console.log('时段',item)
    if (item == "尖峰") {
      tempTableData.forEach((item1: any,index: any) => {
        if (item1.valleyPeakType !== 0 || computerTableData[item1.index - 1][9] == "") return
        console.log("进入尖峰", item1.valleyPeakType, item1.index, item1.marketTradingElectricity);
          // 尖峰
          tempTableData[item1.index - 1].marketTradingElectricity = (
              computerTableData[item1.index - 1][9] * peakScaleValue
          ).toFixed(3);
      });
    } else if (item == "峰段") {
      tempTableData.forEach((item1: any, index: any) => {
        if (item1.valleyPeakType !== 1 || computerTableData[item1.index - 1][9] == "") return
        console.log("进入高峰", item1.valleyPeakType, item1.index, item1.marketTradingElectricity);
        // 高峰
        tempTableData[item1.index - 1].marketTradingElectricity = (
            computerTableData[item1.index - 1][9] * peakSegmentsScaleValue
        ).toFixed(3);
      });
    } else if (item == "谷段") {
      tempTableData.forEach((item1: any, index: any) => {
        if (item1.valleyPeakType != 3 || computerTableData[item1.index - 1][9] == "") return;
        console.log("进入谷段",item1,item1.valleyPeakType, item1.index, item1.marketTradingElectricity);
        // 谷段
        tempTableData[item1.index - 1].marketTradingElectricity = (
            computerTableData[item1.index - 1][9] * valleyScaleValue
        ).toFixed(3);
      })
    } else if (item == "平段") {
      tempTableData.forEach((item1: any, index: any) => {
        if (item1.valleyPeakType != 2 || computerTableData[item1.index - 1][9] == "") return
        console.log("进入平段", item1.valleyPeakType, item1.index, item1.marketTradingElectricity);
        // 平段
        tempTableData[item1.index - 1].marketTradingElectricity = (
            computerTableData[item1.index - 1][9] * OffPeakScaleValue
        ).toFixed(3);
      })
    }
  })
    hotTableComponent.value.hotInstance.updateSettings({
    data: tempTableData
  });
}

// const applyButton = async () => {
//   // a.	用户可勾选峰段、谷段的增减持策略，根据填写比例计算拟交易电量。
//   // 计算方式为对应的峰谷段总和同电量*增/减持比例（增为正，减为负）
//   // console.log(
//   //   tradingStrategyValley.value,
//   //   tradingStrategyRatioValley.value,
//   //   tradingStrategyPeek.value,
//   //   tradingStrategyRatioPeek.value
//   // );
//   console.log(tradingStrategy.value, tradingStrategyRatio.value)
//   // console.log(tableData.value)
//   const tempHotData = hotTableComponent.value.hotInstance
//     .getData()
//     .slice(0, hotTableComponent.value.hotInstance.getData().length - 1);
//   const tempTableData = tableData.value;
//   // console.log(tempTableData)
//   const peakScaleValue =
//     1 +
//     (tradingStrategyPeek.value == "减持" ? -1 : 1) *
//       tradingStrategyRatioPeek.value *
//       0.01;
//   const valleyScaleValue =
//     1 +
//     (tradingStrategyValley.value == "减持" ? -1 : 1) *
//       tradingStrategyRatioValley.value *
//       0.01;
//   console.log("temp前", tempTableData);
//   console.log("scale值", peakScaleValue, valleyScaleValue);
//   // await tempTableData.forEach((item: any, index: number) => {
//   //   tempHotData.forEach((item2: any, index2: number) => {
//   //     if (item.valleyPeakType == '0' || item.valleyPeakType == '1' && tradingStrategyRatioPeek.value != undefined) {
//   //       console.log('进入峰段', item.valleyPeakType , item.index)
//   //       // 峰段
//   //       tempTableData[item.index].marketTradingElectricity = (item2[9] * peakScaleValue).toFixed(3)
//   //     } else if (item.valleyPeakType == '3' && tradingStrategyRatioValley.value != undefined) {
//   //       console.log('进入谷段', item.valleyPeakType, item.index)
//   //       // 谷段
//   //       tempTableData[item.index].marketTradingElectricity = (item2[9] * valleyScaleValue).toFixed(3)
//   //     }
//   //     // console.log(tempTableData)
//   //   });
//   // })
//   await tempTableData.forEach((item: any, index: number) => {
//     if (
//       item.valleyPeakType == "0" ||
//       (item.valleyPeakType == "1" &&
//         tradingStrategyRatioPeek.value != undefined)
//     ) {
//       console.log(
//         "进入峰段",
//         item.valleyPeakType,
//         item.index,
//         tempTableData[item.index - 1].marketTradingPrice
//       );
//       // 峰段
//       tempTableData[item.index - 1].marketTradingPrice = (
//         tempTableData[item.index - 1].marketTradingPrice * peakScaleValue
//       ).toFixed(3);
//     } else if (
//       item.valleyPeakType == "3" &&
//       tradingStrategyRatioValley.value != undefined
//     ) {
//       console.log("进入谷段", item.valleyPeakType, item.index);
//       // 谷段
//       tempTableData[item.index - 1].marketTradingPrice = (
//         tempTableData[item.index - 1].marketTradingPrice * valleyScaleValue
//       ).toFixed(3);
//     }
//   });
//   console.log(tempTableData);
//   // // 交易情况汇总
//   // tableData.value = tempTableData.push({
//   //   timeSlot: '合计',
//   //   existContractElectricity: `=SUM(C1:C24)`,
//   //   greenElectricity: `=SUM(E1:E24)`,
//   //   totalContractElectricity: `=SUM(F1:F24)`,
//   //   predictedElectricity: `=SUM(G1:G24)`,
//   //   tradingDeviationElectricity: `=SUM(H1:H24)`,
//   //   marketTradingElectricity: `=SUM(I1:I24)`,
//   //   finishedContractElectricity: `=SUM(K1:K24)`,
//   //   deviationValue: `=SUM(M1:M24)`
//   // })
//   // console.log(tempTableData)
//   hotTableComponent.value.hotInstance.updateSettings({
//     data: tempTableData
//   });
// };

onMounted(async () => {
  theDateOfTheTransactionChange();
  // 场站
  // await filterSelect('contract_unit').then((res: any) => {
  //   stationOptions.value = res.map((item: any) => {
  //     return {
  //       label: item.dictItemValue,
  //       value: item.dictItemCode,
  //     }
  //   })
  //   tradingFloor.value = stationOptions.value[0].value
  //   // console.log(stationOptions.value)
  // })
  queryMonthDayD2Strategy();
  // queryStrategyList()
  // peakValleyChart()
  // similarDayChart()
});
onBeforeUnmount(() => {});

// 查询月度每日D-2策略信息是否存在
const queryMonthDayD2Strategy = () => {
  const d2StrategyDataForMonthSearchVO: any = {};
  d2StrategyDataForMonthSearchVO.signedUnitId = tradingFloor.value;
  d2StrategyDataForMonthSearchVO.month = dayjs(
    theDateOfTheTransaction.value
  ).format("YYYY-MM");

  // console.log(dayjs(theDateOfTheTransaction.value).month())
  queryMonthDayD2StrategyAPI(
    dayjs(theDateOfTheTransaction.value).format("YYYY-MM")
  ).then((res: any) => {
    // console.log(res)
    holidays.value = [];
    res.forEach((item: any) => {
      if (item.hasData) {
        holidays.value.push(item.date);
      }
    });
  });
};

const loading = ref(false);
// 查询列表信息
const queryStrategyList = () => {
  tableData.value = [];
  const d2StrategySearchVO: any = {};
  d2StrategySearchVO.signedUnitId = tradingFloor.value;
  d2StrategySearchVO.transactionDate = dayjs(
    theDateOfTheTransaction.value
  ).format("YYYY-MM-DD");
  // loading.value = true`

  queryD2StrategyAPI(
    dayjs(theDateOfTheTransaction.value).format("YYYY-MM-DD")
  ).then((res: any) => {
    // console.log(res)=]]y`
    tableData.value = res?.d2StrategyDetailList;

    tableData.value?.forEach((item: any, index: any) => {
      item.index = `${index + 1}`;
      // 交易前偏差电量
      item.preTradeDeviationPower = `=IF(ISNUMBER(C${item.index}),ROUND(E${item.index}-C${item.index},3),0)`;
      // 可卖出电量(90%)
      item.electricityCanBeSold = `=IF(ISNUMBER(C${item.index}),ROUND(MAX(C${item.index}-E${item.index}*0.9,0),3),"")`;
      // 可买入电量(120%)
      item.electricityCanBeSold120 = `=IF(ISNUMBER(E${item.index}),ROUND(MAX(E${item.index}*1.2-C${item.index},0),3),"")`;
      // 交易前合同覆盖率
      item.preTradeContractCoverage = `=IF(ISNUMBER(C${item.index}),ROUND(C${item.index}/E${item.index}*100,3)&"%","")`;
      // 交易完成后合同电量
      item.finishedContractElectricity = `=IF(ISNUMBER(C${item.index}),ROUND(C${item.index}+J${item.index}+L${item.index},3),"")`;
      // 交易完成后合同电价
      item.finishedContractPrice = `=IF(ISNUMBER(C${item.index}),ROUND((((C${item.index}*D${item.index})+(J${item.index}*K${item.index}))+(L${item.index}*M${item.index})/N${item.index}),3),"")`;
      // 交易完成后合同覆盖率
      item.deviationValue = `=IF(ISNUMBER(C${item.index}),ROUND(N${item.index}/E${item.index}*100,3)&"%","")`;
      // // 拟交易电量(谷段默认173.33,平段默认497.16)
      // if(item.valleyPeakType == '3' && (item.marketTradingElectricity == '' || item.marketTradingElectricity == null)) {
      //   // 谷值
      //   item.marketTradingElectricity = 173.33
      // } else if (item.valleyPeakType == '2' && (item.marketTradingElectricity == '' || item.marketTradingElectricity == null)) {
      //   // 平值
      //   item.marketTradingElectricity = 497.16
      // } else {
      //   // 拟交易电量
      //   item.marketTradingElectricity = `=IF(ISNUMBER(C${item.index}), ROUND(E${item.index}-C${item.index}, 3), "")`
      // }
      // 拟交易电量
      if (item.marketTradingElectricity == "" || item.marketTradingElectricity == null) {
        item.marketTradingElectricity = `=IF(ISNUMBER(C${item.index}), ROUND(E${item.index}-C${item.index}, 3), "")`;
      }

      // 拟交易电价(谷段默认173.33,平段默认497.16)
      if (
        item.valleyPeakType == "3" &&
        (item.marketTradingPrice == "" || item.marketTradingPrice == null)
      ) {
        // 谷值
        item.marketTradingPrice = 173.33;
      } else if (
        item.valleyPeakType == "2" &&
        (item.marketTradingPrice == "" || item.marketTradingPrice == null)
      ) {
        // 平值
        item.marketTradingPrice = 497.16;
      } else if (
        item.valleyPeakType == "1" &&
        (item.marketTradingPrice == "" || item.marketTradingPrice == null)
      ) {
        // 高峰
        item.marketTradingPrice = 1144.82;
        // 拟交易电价
        // item.marketTradingPrice = `=IF(ISNUMBER(C${item.index}), ROUND(E${item.index}-C${item.index}, 3), "")`
      } else if (
        item.valleyPeakType == "0" &&
        (item.marketTradingPrice == "" || item.marketTradingPrice == null)
      ) {
        // 尖峰
        item.marketTradingPrice = 1403.88;
      }
    });

    // 交易情况汇总
    tableData.value.push({
      timeSlot: "合计",
      existContractElectricity: `=SUM(C1:C24)`,
      predictedElectricity: `=SUM(E1:E24)`,
      preTradeDeviationPower: `=SUM(F1:F24)`,
      // marketTradingElectricity: `=SUM(K1:K24)`,
      electricityCanBeSold: `=SUM(G1:G24)`,
      electricityCanBeSold120: `=SUM(H1:H24)`,
      marketTradingElectricity: `=SUM(J1:J24)`,
      greenElectricity: `=SUM(L1:L24)`,
      finishedContractElectricity: `=SUM(N1:N24)`,
      deviationValue: `=IF(ISNUMBER(C24),ROUND(N24/C24*100,3)&"%","")`
    });
    // console.log(tableData.value)
    hotTableComponent.value.hotInstance.updateSettings({
      data: tableData.value
    });

    // tableData.value?.forEach((item: any, index: any) => {
    //   item.index = `${index + 1}`
    //   if (
    //       item.marketTradingElectricity === null ||
    //       item.marketTradingElectricity === ''
    //   ) {
    //     // 拟交易电量
    //     item.marketTradingElectricity = `=IF(ISNUMBER(C${index + 1}), ROUND(G${
    //         index + 1
    //     }-F${index + 1}, 3), "")`;
    //   }
    //   // 交易完成后合同电量
    //   item.finishedContractElectricity = `=IF(ISNUMBER(F${index + 1}), ROUND(F${
    //       index + 1
    //   }+K${index + 1}, 3), "")`;
    //   // 偏差值
    //   // item.deviationValue = `=IF(ISNUMBER(C${index + 1}), ROUND(((K${
    //   //     index + 1
    //   // } - F${index + 1}) / F${index + 1}) * 100, 2) & "%", "")`
    //   // item.deviationValue = `=IF(ISNUMBER(C${index + 1}), ROUND(((K${
    //   //     index + 1
    //   // } - F${index + 1}) / F${index + 1}) * 100, 2), "")`
    //   item.deviationValue = `=IF(ISNUMBER(C${index + 1}), ROUND(((M${
    //       index + 1
    //   }) / G${index + 1}) * 100, 2), "")`
    //   // 总合同电量
    //   item.totalContractElectricity = `=IF(ISNUMBER(C${index + 1}), ROUND(C${
    //       index + 1
    //   } + E${index + 1}, 3), "")`;
    //   // 交易前合同覆盖率(%)
    //   item.tradingDeviationElectricity = `=IF(ISNUMBER(G${index + 1}), ROUND(C${index + 1}/G${index + 1} * 100, 3), "")`;
    //   // item.tradingDeviationElectricity = `=IF(ISNUMBER(C${index + 1}), ROUND(G${
    //   //     index + 1
    //   // }-F${index + 1}, 3), "")`;
    //   // 拟交易电价(谷段默认173.33,平段默认497.16)
    //   if(item.valleyPeakType == '3' && (item.paperTradingPrice == '' || item.paperTradingPrice == null)) {
    //     // 谷值
    //     item.paperTradingPrice = 173.33
    //   } else if (item.valleyPeakType == '2' && (item.paperTradingPrice == '' || item.paperTradingPrice == null)) {
    //     // 平值
    //     item.paperTradingPrice = 497.16
    //   }
    // // 交易完成后合同电价
    //   item.finishedContractPrice = `=IF(ISNUMBER(C${index + 1}), ROUND((((C${index + 1} * D${index + 1}) + (K${index + 1} + L${index + 1})) / F${index + 1}), 3), "")`
    //   // 可卖出电量(90%)
    //   // 计算公式：“可卖出电量（90%）”=max（“总合同电量”-“预测电量”×90%，0）；
    //   item.electricityCanBeSold = `=IF(ISNUMBER(C${index + 1}), ROUND(MAX(F${index + 1} - G${index + 1} * 0.9, 0), 3), "")`
    //   // 可买入电量(120%)“可买入电量（120%）”=max（“预测电量”×120%-“总合同电量”，0）
    //   item.electricityCanBeSold120 = `=IF(ISNUMBER(C${index + 1}), ROUND(MAX(G${index + 1} * 1.2 - F${index + 1}, 0), 3), "")`
    // })
    //
    // // 交易情况汇总
    // tableData.value.push({
    //   timeSlot: '合计',
    //   existContractElectricity: `=SUM(C1:C24)`,
    //   greenElectricity: `=SUM(E1:E24)`,
    //   totalContractElectricity: `=SUM(F1:F24)`,
    //   predictedElectricity: `=SUM(G1:G24)`,
    //   // tradingDeviationElectricity: `=SUM(H1:H24)`,
    //   marketTradingElectricity: `=SUM(K1:K24)`,
    //   finishedContractElectricity: `=SUM(M1:M24)`,
    //   electricityCanBeSold: `=SUM(I1:I24)`,
    //   electricityCanBeSold120: `=SUM(J1:J24)`,
    //   deviationValue: `=IF(ISNUMBER(C25), ROUND((M25 / G25) * 100, 2) & "%", "")`
    // })
    // console.log(tableData.value)
    // hotTableComponent.value.hotInstance.updateSettings({
    //   data: tableData.value,
    // })
    loading.value = false;
  });
  similarDayChart();
  peakValleyChart();
};
const isTableDataFlag = ref(false);
// 交易日期改变
const theDateOfTheTransactionChange = () => {
  // console.log(previousDate.value)
  if (
    dayjs(theDateOfTheTransaction.value).format("YYYY-MM-DD") >=
    dayjs(Date()).format("YYYY-MM-DD")
  ) {
    queryStrategyList();
    updateButtonStatus.value = true;
  } else if (
    dayjs(theDateOfTheTransaction.value).format("YYYY-MM-DD") <
    dayjs(Date()).format()
  ) {
    if (isTableDataFlag.value) {
      ElMessageBox.confirm("表格内容已修改,是否放弃修改过的申报策略?", "警告", {
        distinguishCancelAndClose: true,
        confirmButtonText: "保存",
        cancelButtonText: "放弃",
        type: "warning"
      })
        .then(() => {
          console.log("保存");
          ElMessage({
            type: "info",
            message: "保存"
          });
          // next()
          updateTrading(previousDate.value);
          // queryStrategyList()
          updateButtonStatus.value = false;
        })
        .catch(action => {
          console.log("返回保存", action);
          ElMessage({
            type: action === "cancel" ? "warning" : "info",
            message: action === "cancel" ? "放弃修改" : "返回保存"
          });
          if (action === "cancel") {
            // console.log(to)
            // next()
            queryStrategyList();
            isTableDataFlag.value = false;
            updateButtonStatus.value = false;
          } else {
            theDateOfTheTransaction.value = previousDate.value;
          }
        });
    } else {
      queryStrategyList();
      updateButtonStatus.value = false;
    }
  }
};
// 场站改变
const tradingFloorChange = () => {
  queryStrategyList();
};

const updateButtonStatus = ref(false);
// 新增和保存方法
const updateTrading = (previousDate1 = "undefined") => {
  console.log(previousDate.value);
  console.log("update");
  if (updateButtonStatus.value) {
    const data: any = {};
    // data.signedUnitId = tradingFloor.value
    data.transactionDate =
      previousDate1 !== "undefined"
        ? previousDate1
        : dayjs(theDateOfTheTransaction.value).format("YYYY-MM-DD");
    // data.d2StrategyDetailList = []
    data.d2StrategyDetailList = tableData.value;
    data.d2StrategyDetailList.pop();
    console.log(data.d2StrategyDetailList);
    // console.log(hotTableComponent.value.hotInstance.getSourceData())
    // console.log(hotTableComponent.value.hotInstance.getData())
    const hotData = hotTableComponent.value.hotInstance
      .getData()
      .slice(0, hotTableComponent.value.hotInstance.getData().length - 1);
    console.log(hotData);
    hotData.forEach((item2: any, index2: any) => {
      delete data.d2StrategyDetailList[index2]?.index;
      delete data.d2StrategyDetailList[index2]?.finishedContractElectricity;
      delete data.d2StrategyDetailList[index2]?.finishedContractPrice;
      delete data.d2StrategyDetailList[index2]?.deviationValue;
      // 拟交易电量
      data.d2StrategyDetailList[index2].marketTradingElectricity =
        item2[9].toString();
      data.d2StrategyDetailList[index2].marketTradingElectricity = item2[9];
      // 绿电电量为null处理，设为0
      // data.d2StrategyDetailList[index2].greenElectricity =
      //     item2[4] == null ? 0 : item2[4].toString()
      // console.log(data.d2StrategyDetailList[index2].greenElectricity)
      // 总合同电量
      // data.d2StrategyDetailList[index2].totalContractElectricity =
      //     item2[5].toString()
      // 交易前合同覆盖率(%)
      // data.d2StrategyDetailList[index2].tradingDeviationElectricity =
      //     item2[8].toString()

      // 交易完成后合同电量
      // data.d2StrategyDetailList[index2].finishedContractElectricity =
      //     item2[12].toString()
      // // 交易完成后合同电价
      // data.d2StrategyDetailList[index2].finishedContractPrice =
      //     item2[13].toString()
      // // 偏差值
      // if (item2[15] !== '#DIV/0!') {
      //   data.d2StrategyDetailList[index2].deviationValue = item2[15]
      //       .toString()
      // } else if (item2[15] === '#DIV/0!') {
      //   data.d2StrategyDetailList[index2].deviationValue = '0'
      // } else {
      //   data.d2StrategyDetailList[index2].deviationValue = item2[15]
      // }
      // data.d2StrategyDetailList[index2].deviationValue = item2[9].toString()
    });
    console.log("转换成功后表格数据", data.d2StrategyDetailList);
    saveD2StrategyAPI(data).then((res: any) => {
      if (res !== null) {
        // ElMessage.warning('系统错误，请联系管理员！')
        return;
      } else {
        ElMessage.success("保存成功！");
        isTableDataFlag.value = false;
        queryStrategyList();
      }
    });
  } else {
    ElMessage.warning("历史策略不可修改！");
  }
};

const exportButton = () => {
  if (!tableData.value.length) {
    return ElMessage.info("无可导出数据！");
  }
  const loading = ElLoading.service({ text: "正在下载..." });
  handleExport().then(() => {
    loading.close();
  });
  // exportRadixDecomposePrediction(tableData.value)
  //     .then((data) => {
  //       const blob = new Blob([data])
  //       const url = window.URL.createObjectURL(blob)
  //       const link = document.createElement('a')
  //       link.href = url
  //       link.download = 'D-2新能源交易预测表.xlsx'
  //       link.click()
  //       window.URL.revokeObjectURL(url)
  //       loading.close()
  //     })
  //     .catch(() => {
  //       ElMessage.error('下载失败')
  //       loading.close()
  //     })
};

async function handleExport() {
  const tHeader = settings.value.columns
    .map(i => i.title)
    .filter(item => !["", "操作"].includes(item));
  const filterVal = settings.value.columns
    .map(i => i.data)
    .filter(item => ![undefined].includes(item));
  // const exportTableData = tableData.value.slice(0, tableData.value.length - 1)
  const exportTableData = tableData.value;
  // const hotData = hotTableComponent.value.hotInstance.getData().slice(0,hotTableComponent.value.hotInstance.getData().length-1)
  const hotData = hotTableComponent.value.hotInstance.getData();
  if (exportTableData.length) {
    const list = exportTableData.map(item => {
      return {
        ...item,
        valleyPeakType:
          item.valleyPeakType === 0
            ? "尖"
            : item.valleyPeakType === 1
            ? "峰"
            : item.valleyPeakType == 2
            ? "平"
            : item.valleyPeakType == "3"
            ? "谷"
            : ""
      };
    });
    hotData.forEach((item2: any, index2: any) => {
      list[index2].preTradeDeviationPower = item2[5];
      list[index2].electricityCanBeSold = item2[6];
      list[index2].electricityCanBeSold120 = item2[7];
      list[index2].electricityCanBeSold120 = item2[7];
      list[index2].preTradeContractCoverage = item2[8];
      list[index2].finishedContractElectricity = item2[13];
      list[index2].finishedContractPrice = item2[14];
      list[index2].deviationValue = item2[15];
      list[index2].existContractElectricity = item2[2];
      list[index2].predictedElectricity = item2[4];
      list[index2].marketTradingElectricity = item2[9];
      list[index2].greenElectricity = item2[11];
      list[index2].marketTradingPrice = item2[10];
      list[index2].greenElectricityTradingPrice = item2[12];
    });
    const xlsxData = formatJson(filterVal, list);
    aoaD_2ToSheetXlsx({
      data: xlsxData,
      header: tHeader,
      filename: `D-2新能源交易预测表.xlsx`,
      cellSetup: {
        sizing: [
          { width: 8 },
          { width: 14 },
          { width: 16 },
          { width: 16 },
          { width: 14 },
          { width: 18 },
          { width: 18 },
          { width: 18 },
          { width: 20 },
          { width: 16 },
          { width: 16 },
          { width: 18 },
          { width: 18 },
          { width: 22 },
          { width: 22 },
          { width: 26 }
          // { width: 22 },
        ],
        style: {
          color: "white",
          backgroundColor: "#222",
          font: {
            sz: "12"
          },
          alignment: {
            horizontal: "center",
            vertical: "center",
            wrapText: false
          },
          border: {
            top: { style: "thin" },
            right: { style: "thin" },
            bottom: { style: "thin" },
            left: { style: "thin" }
          },
          fill: {
            fgColor: { rgb: "87CEEB" }
          }
        }
      }
    });
  }
}
// 导出excel数据map方法
function formatJson(filterVal, jsonData) {
  return jsonData.map(v =>
    filterVal.map(j => {
      // null会过滤样式
      return v[j] === null ? " " : v[j];
      // return v[j];
    })
  );
}

// 相似日图表处理
const similarDayChart = () => {
  spotPriceForecast(theDateOfTheTransaction.value).then(res => {
    console.log("相似日", res);
    similarDayOption.value.xAxis.data = [];
    similarDayOption.value.series[0].data = [];
    similarDayOption.value.series[1].data = [];
    similarDayOption.value.series[2].data = [];
    res.forEach((item: any, index: any) => {
      // similarDayOption.value.xAxis.data.push(item.totalContractElectricityForTimeSlotList.map((item2: any)=> {return item2.timeSlot}))
      similarDayOption.value.series[index].name = item.date;
      // similarDayOption.value.series[index].data.push(item.totalContractElectricityForTimeSlotList.totalContractElectricity)
      item.totalContractElectricityForTimeSlotList.forEach((item2: any) => {
        similarDayOption.value.xAxis.data.push(item2.timeSlot + ":00");
        similarDayOption.value.series[index].data.push(
          item2.totalContractElectricity
        );
      });
    });
  });
};

// 峰平谷图表处理
const peakValleyChart = () => {
  getD2StrategyDataForPeakValley(theDateOfTheTransaction.value).then(res => {
    console.log("峰平谷", res);
    data.value = [];
    data1.value = [];
    data2.value = [];
    // deviationOption.value.series[0].data[0].value.push(res.totalContractElecProportionForSummit,res.totalContractElecProportionForFlat,res.totalContractElecProportionForLow)
    // deviationOption.value.series[1].data[0].value.push(res.paperTradingElecProportionForSummit,res.paperTradingElecProportionForFlat,res.paperTradingElecProportionForLow)
    // deviationOption.value.series[2].data[0].value.push(res.finishedElecProportionForSummit,res.finishedElecProportionForFlat,res.finishedElecProportionForLow)
    if (res !== null) {
      data.value.push(
        res.totalContractElecProportionForSummit,
        res.totalContractElecProportionForFlat,
        res.totalContractElecProportionForLow
      );
      data1.value.push(
        res.paperTradingElecProportionForSummit,
        res.paperTradingElecProportionForFlat,
        res.paperTradingElecProportionForLow
      );
      data2.value.push(
        res.finishedElecProportionForSummit,
        res.finishedElecProportionForFlat,
        res.finishedElecProportionForLow
      );
    } else {
      return;
    }

    // echartsConfigBottom(deviationOption.value)
  });
};

// 相似日电量
const similarDayOption = ref({
  confine: true,
  tooltip: {
    trigger: "axis",
    backgroundColor: "rgba(255,255,255,0.90)",
    borderRadius: "4px",
    boxShadow: " 0px 2px 10px 0px rgba(0, 0, 0, 0.16)",
    formatter: function (params: any) {
      let context;
      let content = params.map((item: any) => {
        return `${item.marker}<span>${
          item.seriesName
        }</span>:<span style="margin-left: 10px;float: right">${
          item.value ? item.value : "--"
        }MWh</span><br>`;
      });
      let newContent = "";
      content.forEach((item: any) => {
        newContent = newContent + item;
      });
      context = `<div>${params[0].name}</div><div>${newContent}</div>`;
      return context;
    }
  },
  legend: {
    show: true,
    // data: [
    //   { name: '持仓电量'},
    //   { name: '持仓电量' },
    //   { name: '持仓电量'}
    // ],
    top: "5%"
  },
  grid: {
    top: "18%",
    left: "5%",
    right: "5%",
    bottom: "15%"
  },
  xAxis: {
    type: "category",
    data: [
      "1:00",
      "2:00",
      "3:00",
      "4:00",
      "5:00",
      "6:00",
      "7:00",
      "8:00",
      "9:00",
      "10:00",
      "11:00",
      "12:00",
      "13:00",
      "14:00",
      "15:00",
      "16:00",
      "17:00",
      "18:00",
      "19:00",
      "20:00",
      "21:00",
      "22:00",
      "23:00",
      "24:00"
    ],
    // data: [],
    axisTick: {
      show: false // 将此选项设置为 false 来隐藏 X 轴刻度
    }
  },
  yAxis: {
    position: "left",
    type: "value",
    name: "电量(MWh)",
    nameTextStyle: {
      // y轴name的样式调整
      fontSize: 14
    },
    splitLine: {
      show: true //想要不显示网格线，改为false
    },
    data: []
  },
  // {
  //   position: 'right',
  //   type: 'value',
  //   name: '价格(元/MWh)',
  //   nameTextStyle: {
  //     // y轴name的样式调整
  //     fontSize: 14,
  //   },
  //   splitLine: {
  //     show: false, //想要不显示网格线，改为false
  //   },
  // },
  series: [
    {
      name: "持仓电量",
      data: [],
      type: "line",
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      showSymbol: false, // 不显示折线上的圆点
      symbol: "circle", //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        color: "#254f7a"
      }
    },
    {
      name: "实际发电量",
      data: [],
      type: "line",
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      showSymbol: false, // 不显示折线上的圆点
      symbol: "circle", //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        color: "#029CD4"
      }
    },
    {
      name: "实际发电量",
      data: [],
      type: "line",
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      showSymbol: false, // 不显示折线上的圆点
      symbol: "circle", //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        color: "#58a55c"
      }
    }
  ]
});
const similarDayOptionDeal = echartsConfigBottom(similarDayOption.value);

let data = ref([]);
let data1 = ref([]);
let data2 = ref([]);
const indicatorname = ["峰时段", "平时段", "谷时段"];
const maxdata = [100, 100, 100];

function contains(arrays: any, obj: any) {
  let i = arrays.length;
  while (i--) {
    if (arrays[i] === obj) {
      return i;
    }
  }
  return false;
}

const indicator = [];
for (let i = 0; i < indicatorname.length; i++) {
  indicator.push({
    name: indicatorname[i],
    max: maxdata[i]
  });
}
const deviationOption = ref({
  backgroundColor: "#FFFFFF",
  tooltip: {
    show: true,
    trigger: "item"
    // formatter: function () {
    //   let html = ''
    //   for (let i = 0; i < data.length; i++) {
    //     html += indicatorname[i] + ' : ' + data[i] + '<br>'
    //   }
    //   return html
    // },
    // borderWidth: 0,
  },
  radar: {
    // type: 'gauge',
    radius: "65%", //大小
    nameGap: 40, // 图中工艺等字距离图的距离
    center: ["50%", "55%"], // 图的位置
    name: {
      textStyle: {
        rich: {
          a: {
            fontSize: "12",
            color: "#333333",
            lineHeight: "20",
            fontWeight: "500"
          },
          b: {
            fontSize: "12",
            color: "#666666"
          }
        }
      },
      formatter: function (params: any) {
        const i = contains(indicatorname, params);
        let percent = (data.value[i] / 100) * 100;
        // console.log(percent)
        if (isNaN(percent)) {
          // console.log('nan')
          return "{b|" + params + "}\n" + "{a|" + "-" + "%" + "}";
        } else {
          return (
            "{b|" + params + "}\n" + "{a|" + percent.toFixed(2) + "%" + "}"
          );
        }
      }
    },
    indicator: indicator,
    axisLine: {
      lineStyle: {
        color: "#3299FD30"
      },
      show: true,
      symbolSize: [1, 30],
      symbol: ["none", "rect"],
      symbolOffset: [0, 25]
    },
    splitArea: {
      show: false,
      areaStyle: {
        color: "transparent" // 图表背景的颜色
      }
    },
    splitLine: {
      show: true,
      lineStyle: {
        width: 1,
        color: "#3299FD30" // 设置网格的颜色
      }
    },
    shape: "circle" // 设置网格线为圆形
  },
  legend: {
    show: true,
    type: "scroll",
    data: [
      {
        name: "已有合同电量",
        color: "#0052D9",
        itemStyle: {
          color: "#0052D9"
        }
      },
      {
        name: "拟交易电量",
        color: "#029CD4",
        itemStyle: {
          color: "#029CD4"
        }
      },
      {
        name: "交易完成后合同电量",
        color: "rgba(43, 164, 113, 1)",
        itemStyle: {
          color: "rgba(43, 164, 113, 1)"
        }
      }
    ]
  },
  series: [
    {
      name: "已有合同电量",
      type: "radar",
      symbol: "circle",
      itemStyle: {
        normal: {
          areaStyle: { type: "default" }
        }
      },
      data: [
        {
          symbol: "none",
          symbolSize: 5,
          // value: [],
          value: data,
          areaStyle: { color: "rgba(0, 82, 217, 0.2)" },
          lineStyle: {
            color: "#0052D9",
            width: 4
          }
        }
      ]
    },
    {
      name: "拟交易电量",
      type: "radar",
      symbol: "circle",
      itemStyle: {
        normal: {
          areaStyle: { type: "default" }
        }
      },
      data: [
        {
          symbol: "none",
          symbolSize: 5,
          // value: [],
          value: data1,
          areaStyle: { color: "rgba(2, 156, 212, 0.3)" },
          lineStyle: {
            color: "#029CD4",
            width: 4
          }
        }
      ]
    },
    {
      name: "交易完成后合同电量",
      type: "radar",
      symbol: "circle",
      itemStyle: {
        normal: {
          areaStyle: { type: "default" }
        }
      },
      data: [
        {
          symbol: "none",
          symbolSize: 5,
          // value: [],
          value: data2,
          areaStyle: { color: "rgba(43, 164, 113, 0.3)" },
          lineStyle: {
            color: "rgba(43, 164, 113, 1)",
            width: 4
          }
        }
      ]
    }
  ]
});

const deviationOptionDeal = echartsConfigBottom(deviationOption.value);

// watchEffect(() => {
//   // 监听表格内容的变化
//   // 如果内容发生变化，设置一个变量表示内容是否改变
//   tableDataFlag.value = true
// })

onBeforeRouteLeave((to, from, next) => {
  // console.log('路由离开前')
  if (isTableDataFlag.value) {
    ElMessageBox.confirm("表格内容已修改,是否放弃修改过的申报策略?", "警告", {
      distinguishCancelAndClose: true,
      confirmButtonText: "保存",
      cancelButtonText: "放弃",
      type: "warning"
    })
      .then(() => {
        // console.log('保存')
        ElMessage({
          type: "info",
          message: "保存"
        });
        updateTrading();
        next();
      })
      .catch(action => {
        // console.log('返回保存', action)
        ElMessage({
          type: action === "cancel" ? "warning" : "info",
          message: action === "cancel" ? "放弃修改" : "返回保存"
        });
        if (action === "cancel") {
          // console.log(to)
          isTableDataFlag.value = false;
          next();
        }
      });
  } else {
    next();
  }
});
</script>

<style lang="scss">
td.custom-cell {
  font-size: 14px !important;
  font-weight: bold;
  text-align: center !important;
  vertical-align: middle !important;
}
.custom-table tbody tr:nth-child(odd) th {
  background-color: #d7f1e1;
}
.peak-time-row {
  text-align: center !important;
  vertical-align: middle !important;
  color: #df6200 !important;
  background-color: #ffdead !important; /* 指定高峰时段的背景颜色 */
}

.peakily-time-row {
  text-align: center !important;
  vertical-align: middle !important;
  color: #f61b1b !important;
  background-color: #fbb4b4 !important; /* 指定尖峰时段的背景颜色 */
}

.off-peak-time-row {
  text-align: center !important;
  vertical-align: middle !important;
  color: #116240 !important;
  background-color: #aef7d8 !important; /* 指定平时段的背景颜色 */
}

.valley-time-row {
  text-align: center !important;
  vertical-align: middle !important;
  color: #0561b7 !important;
  background-color: #9ce1ff !important; /* 指定谷时段的背景颜色 */
}
.sum-row {
  text-align: center !important;
  vertical-align: middle !important;
  //color: #116240 !important;
  //background-color: #aef7d8 !important; /* 指定谷时段的背景颜色 */
}

.cell {
  //height: 30px;
  padding: 3px 0;
  box-sizing: border-box;
  .holiday {
    position: absolute;
    width: 6px;
    height: 6px;
    background: var(--el-color-danger);
    border-radius: 50%;
    bottom: -9px;
    left: 50%;
    transform: translateX(-50%);
  }
  .text {
    width: 24px;
    height: 24px;
    display: block;
    margin: 0 auto;
    line-height: 24px;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 50%;
  }
}
.cell.current .text {
  background: #254f7a;
  color: #fff;
}
.htTwoLines {
  height: 50px;
  background-color: #f0f0f0;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  //span {
  //  white-space: pre;       /* 保留空格和换行 */
  //  word-break: break-all;  /* 允许任意字符间断行 */
  //  width: 5ch;             /* 设置宽度为5个字符宽 */
  //  display: inline-block;  /* 使宽度生效 */
  //}
}
</style>
