import dayjs from "dayjs";
import { useUserStore } from "@/store/modules/user";
const userStore = useUserStore();
const contractStatusOptions = userStore.getDictList.find(
  i => i.code === "contractStatus"
)?.items;
// 终止类型
const stopTypeOptions = userStore.getDictList.find(
  i => i.code === "stopType"
)?.items;
export const columns: TableColumnList = [
  {
    label: "序号",
    align: "center",
    width: 80,
    type: "index"
  },
  {
    label: "零售用户",
    prop: "retailUser",
    slot: "name",
    headerSlot: "retailUserHeader",
    searchType: "text"
  },
  {
    label: "套餐名称",
    prop: "packageName",
    headerSlot: "packageNameHeader",
    searchType: "text"
  },
  {
    label: "合同编号",
    prop: "contractNum",
    headerSlot: "contractNumHeader",
    searchType: "text"
  },
  {
    label: "合同电量",
    prop: "contractElectricity",
    // prop: "electricityQty",
    sortable: true,
  },
  {
    label: "合同开始时间",
    sortable: true,
    headerSlot: "startTimetNumHeader",
    prop: "startTime"
    // formatter: ({ startTime }) =>
    //   startTime !== null ? dayjs(Number(startTime)).format("YYYY-MM") : ""
  },
  {
    label: "合同结束时间",
    sortable: true,
    prop: "endTime"
    // formatter: ({ endTime }) =>
    //   endTime !== null ? dayjs(Number(endTime)).format("YYYY-MM") : ""
  },

  // {
  //   label: "合同状态",
  //   prop: "contractStatus",
  //   formatter: ({ contractStatus }) =>
  //     contractStatus !== null
  //       ? contractStatusOptions.find(item => item.value == contractStatus).label
  //       : ""
  // },
  {
    label: "操作",
    width: "180",
    fixed: "right",
    slot: "operation"
  }
];

export const addColumns: TableColumnList = [
  {
    label: "",
    prop: "type",
    align: "center",
    headerAlign: "center"
  },
  {
    label: "1月",
    prop: "month1",
    slot: "month1"
  },
  {
    label: "2月",
    prop: "month2",
    slot: "month2"
  },
  {
    label: "3月",
    prop: "month3",
    slot: "month3"
  },
  {
    label: "4月",
    prop: "month4",
    slot: "month4"
  },
  {
    label: "5月",
    prop: "month5",
    slot: "month5"
  },
  {
    label: "6月",
    prop: "month6",
    slot: "month6"
  },
  {
    label: "7月",
    prop: "month7",
    slot: "month7"
  },
  {
    label: "8月",
    prop: "month8",
    slot: "month8"
  },
  {
    label: "9月",
    prop: "month9",
    slot: "month9"
  },
  {
    label: "10月",
    prop: "month10",
    slot: "month10"
  },
  {
    label: "11月",
    prop: "month11",
    slot: "month11"
  },
  {
    label: "12月",
    prop: "month12",
    slot: "month12"
  }
];

export const detailColumns: TableColumnList = [
  {
    type: "expand",
    slot: "expand"
  },
  {
    label: "序号",
    width: 80,
    type: "index"
  },
  {
    label: "主要变更说明",
    prop: "description"
  },
  {
    label: "变更生效日期",
    prop: "effectiveTime",
    formatter: ({ effectiveTime }) =>
      effectiveTime !== null
        ? dayjs(Number(effectiveTime)).format("YYYY-MM-DD")
        : ""
  },
  {
    label: "变更原因",
    prop: "reason"
  },
  {
    label: "变更备注",
    prop: "remark"
  }
];

export const stopColumns: TableColumnList = [
  {
    label: "序号",
    width: 80,
    type: "index"
  },
  {
    label: "终止类型",
    prop: "stopType",
    formatter: ({ stopType }) =>
      stopType !== null
        ? stopTypeOptions.find(item => item.value == stopType).label
        : ""
  },
  {
    label: "终止原因",
    prop: "reason"
  },
  {
    label: "终止生效日期",
    prop: "effectiveTime",
    formatter: ({ effectiveTime }) =>
      effectiveTime !== null
        ? dayjs(Number(effectiveTime)).format("YYYY-MM-DD")
        : ""
  },
  {
    label: "	创建时间",
    prop: "effectiveTime",
    formatter: ({ createTime }) =>
      createTime !== null ? dayjs(Number(createTime)).format("YYYY-MM-DD") : ""
  }
];
