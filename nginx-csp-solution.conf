# 基于NGINX官方文档的CSP和iframe解决方案
# 解决 "frame-ancestors" 限制问题

server {
    listen 8187;
    server_name ***********;
    
    # 项目根目录
    root /var/www/html/dist;
    index index.html index.htm;
    
    # 主要配置 - 解决CSP frame-ancestors问题
    location / {
        try_files $uri $uri/ /index.html;
        
        # 方案1：完全移除CSP限制（最简单）
        # 不设置任何Content-Security-Policy头部
        
        # 方案2：设置允许所有域名的CSP（推荐）
        add_header Content-Security-Policy "frame-ancestors *" always;
        
        # 方案3：指定特定域名（最安全）
        # add_header Content-Security-Policy "frame-ancestors http://***********:9291 https://***********:9291 http://localhost:* https://localhost:* *" always;
        
        # 设置X-Frame-Options
        add_header X-Frame-Options "ALLOWALL" always;
        
        # CORS配置
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS, HEAD, PATCH" always;
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, Pragma" always;
        add_header Access-Control-Allow-Credentials "true" always;
        add_header Access-Control-Max-Age "86400" always;
        
        # 处理OPTIONS预检请求
        if ($request_method = 'OPTIONS') {
            add_header Content-Security-Policy "frame-ancestors *";
            add_header X-Frame-Options "ALLOWALL";
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS, HEAD, PATCH";
            add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, Pragma";
            add_header Access-Control-Allow-Credentials "true";
            add_header Access-Control-Max-Age "86400";
            add_header Content-Type "text/plain; charset=utf-8";
            add_header Content-Length 0;
            return 204;
        }
    }
    
    # 静态资源配置
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|map|json)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Content-Security-Policy "frame-ancestors *" always;
        add_header X-Frame-Options "ALLOWALL" always;
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept" always;
    }
    
    # API代理配置
    location /api/ {
        proxy_pass http://***********:8187/selling/;
        
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 关键：隐藏后端可能设置的限制性头部
        proxy_hide_header Content-Security-Policy;
        proxy_hide_header X-Frame-Options;
        
        # 重新设置允许的头部
        add_header Content-Security-Policy "frame-ancestors *" always;
        add_header X-Frame-Options "ALLOWALL" always;
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization" always;
        add_header Access-Control-Allow-Credentials "true" always;
        
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        proxy_buffering off;
    }
    
    # 错误页面
    error_page 404 /index.html;
    
    # 日志
    access_log /var/log/nginx/csp-solution-access.log;
    error_log /var/log/nginx/csp-solution-error.log;
}

# 如果需要在不同端口提供无CSP限制的版本
server {
    listen 8189;  # 新端口，专门用于iframe嵌入
    server_name ***********;
    
    root /var/www/html/dist;
    index index.html index.htm;
    
    location / {
        try_files $uri $uri/ /index.html;
        
        # 完全不设置任何CSP头部
        add_header X-Frame-Options "ALLOWALL" always;
        
        # 基本CORS支持
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "*" always;
        add_header Access-Control-Allow-Headers "*" always;
        add_header Access-Control-Allow-Credentials "true" always;
        
        if ($request_method = 'OPTIONS') {
            add_header X-Frame-Options "ALLOWALL";
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "*";
            add_header Access-Control-Allow-Headers "*";
            add_header Content-Length 0;
            return 204;
        }
    }
    
    # 静态资源
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|map|json)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Frame-Options "ALLOWALL" always;
        add_header Access-Control-Allow-Origin "*" always;
    }
    
    error_page 404 /index.html;
}

# 全局配置建议
# 在主nginx.conf的http块中添加：
#
# http {
#     server_tokens off;  # 隐藏nginx版本
#     client_max_body_size 50M;
#     
#     # 默认不设置任何限制性安全头部
#     # 让各个server块自己控制
# }
