import dayjs from "dayjs";
import { useDictOptions } from "@/hooks/dict/useDictOptions";
const { electricityTypeOptions, industryOptions } = useDictOptions();
export const columns = [
  {
    label: "用户名称",
    prop: "name",
    headerSlot: "nameHeader",
    searchType: "text",
    slot: "name",
    headerAlign: "center",
    width: 140
  },
  {
    label: "月平均电量",
    prop: "monthlyAverageElectricity",
    width: 120,
    align: "center",
    headerAlign: "center",
    headerSlot: "monthlyAverageElectricity",
    searchType: "number_range"
  },

  {
    label: "用电性质",
    headerSlot: "electricityTypeHeader",
    searchType: "select",
    align: "center",
    headerAlign: "center",
    prop: "electricalNature",
    formatter: ({ electricalNature }) =>
      ![undefined, null].includes(electricalNature)
        ? electricityTypeOptions.find(item => item.value == electricalNature)
            .label
        : ""
  },
  {
    label: "用户行业",
    prop: "industryId",
    headerSlot: "industryHeader",
    align: "center",
    headerAlign: "center",
    searchType: "select",
    formatter: ({ industryId }) =>
      ![undefined, null].includes(industryId)
        ? industryOptions.find(item => item.value == industryId).label
        : ""
  },
  {
    label: "所在地区",
    align: "center",
    headerAlign: "center",
    searchType: "treeSelect",
    headerSlot: "areaIdHeader",
    prop: "areaName",
    propKey: "areaId"
  },
  {
    label: "用户来源",
    prop: "customerSource",
    align: "center",
    headerSlot: "customerSourceHeader",
    searchType: "select",
    headerAlign: "center",
    slot: "customerSource"
  },
  // {
  //   label: "签约状态",
  //   prop: "isSign",
  //   headerSlot: "statusHeader",
  //   searchType: "select",
  //   align: "center",
  //   headerAlign: "center",
  //   slot: "isSign",
  //   formatter: ({ isSign }) =>
  //     isSign === true? "已签约" : "未签约"
  // },
];
