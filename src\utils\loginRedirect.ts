/**
 * 登录重定向工具函数
 * 用于处理登录前后的页面跳转逻辑
 */

const REDIRECT_PATH_KEY = 'login-redirect-path';

/**
 * 保存当前路径，用于登录后跳转
 * @param path 要保存的路径，默认为当前路径
 */
export function saveRedirectPath(path?: string): void {
  const redirectPath = path || window.location.hash.replace('#', '') || window.location.pathname + window.location.search;
  
  // 不保存登录页面路径
  if (redirectPath === '/login' || redirectPath.includes('/login')) {
    return;
  }
  
  sessionStorage.setItem(REDIRECT_PATH_KEY, redirectPath);
  console.log('保存重定向路径:', redirectPath);
}

/**
 * 获取保存的重定向路径
 * @returns {string | null} 保存的路径或null
 */
export function getRedirectPath(): string | null {
  return sessionStorage.getItem(REDIRECT_PATH_KEY);
}

/**
 * 清除保存的重定向路径
 */
export function clearRedirectPath(): void {
  sessionStorage.removeItem(REDIRECT_PATH_KEY);
  console.log('清除重定向路径');
}

/**
 * 执行登录后跳转
 * @param router Vue Router实例
 * @param defaultPath 默认跳转路径，默认为'/big-screen'
 * @returns {Promise<void>}
 */
export async function executeLoginRedirect(router: any, defaultPath: string = '/big-screen'): Promise<void> {
  const redirectPath = getRedirectPath();
  
  if (redirectPath) {
    clearRedirectPath();
    console.log('登录成功，跳转到原始页面:', redirectPath);
    await router.push(redirectPath);
  } else {
    console.log('登录成功，跳转到默认页面:', defaultPath);
    await router.push(defaultPath);
  }
}

/**
 * 处理需要登录的路由跳转
 * @param router Vue Router实例
 * @param currentPath 当前路径
 */
export function handleLoginRequired(router: any, currentPath?: string): void {
  const path = currentPath || router.currentRoute.value.fullPath;
  saveRedirectPath(path);
  router.replace('/login');
}

/**
 * 检查是否有保存的重定向路径
 * @returns {boolean} 是否有保存的路径
 */
export function hasRedirectPath(): boolean {
  return !!getRedirectPath();
}

/**
 * 获取重定向路径的调试信息
 * @returns {object} 调试信息
 */
export function getRedirectDebugInfo() {
  return {
    hasRedirectPath: hasRedirectPath(),
    redirectPath: getRedirectPath(),
    storageKey: REDIRECT_PATH_KEY,
    currentUrl: window.location.href,
    currentHash: window.location.hash,
    currentPathname: window.location.pathname,
    currentSearch: window.location.search
  };
}
