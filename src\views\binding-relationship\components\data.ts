import dayjs from "dayjs";
export const columns: TableColumnList = [
  {
    label: "序号",
    width: 80,
    type: "index"
  },
  {
    label: "电力用户名称",
    prop: "customElectricityName",
    slot: "customElectricityName"
  },
  {
    label: "居间商名称",
    prop: "customMiddlemanName"
  },
  {
    label: "绑定周期",
    prop: "time",
    formatter: ({ bindStart, bindEnd }) =>
      bindStart && bindEnd
        ? `${dayjs(Number(bindStart)).format("YYYY-MM-DD")} ~ ${dayjs(
            Number(bindEnd)
          ).format("YYYY-MM-DD")}`
        : ""
  },
  {
    label: "绑定状态",
    prop: "status",
    formatter: ({ status }) =>
      status === 0 ? "正常" : status === 1 ? "禁用" : "已删除"
  },
  {
    label: "绑定关系创建日期",
    prop: "createTime",
    formatter: ({ createTime }) =>
      createTime !== null ? dayjs(Number(createTime)).format("YYYY-MM-DD") : ""
  },
  {
    label: "操作",
    width: "120",
    fixed: "right",
    slot: "operation"
  }
];
