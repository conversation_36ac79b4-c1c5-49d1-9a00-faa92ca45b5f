import { request } from '@/utils/request'

// 新增标签类别
export const addLabelCategory = (data: any) => {
  const res = request.post<any>({
    url: '/userPortrait/customLabel/add/labelCategory',
    data,
  })
  return res
}
// 新增用户标签
export const addCustomLabel = (data: any) => {
  const res = request.post<any>({
    url: '/userPortrait/customLabel/add/customLabel',
    data,
  })
  return res
}

// 查询标签类别列表
export const queryLabelList = (data: any) => {
  const res = request.post<any>({
    url: '/userPortrait/customLabel/query/labelList',
    data,
  })
  return res
}

// 修改标签类别名称
export const modifyLabelCategoryName = (data: any) => {
  const res = request.post<any>({
    url: '/userPortrait/customLabel/modifyLabelCategoryName',
    data,
  })
  return res
}



//修改用户标签
export const modifyCustomLabel = (data) => {
  const res = request.post<any>({
    url: `/userPortrait/customLabel/modifyCustomLabel`,
    data
  })
  return res
}





// 删除标签类别

export const delLabelCategory = (categoryId: any,level:any) => {
  console.log(categoryId,level)
  const res = request.get<any>({
    url: `/userPortrait/customLabel/delLabelCategory/${categoryId}?level=${level}`,

  })
  return res
}
export const getLabelSliceList = (id:any) => {

  const res = request.post<any>({
    url: `/userPortrait/customLabel/getLabelSliceList/${id}`,

  })
  return res
}

//删除用户标签
export const delCustomLabel = (labelId: any) => {
  const res = request.get<any>({
    url: `/userPortrait/customLabel/delCustomLabel/${labelId}`,
  })
  return res
}

// 获取用户属性映射列表
export const getUserAttributeMappingList = () => {
  const res = request.post<any>({
    url: '/userPortrait/customLabel/getUserAttributeMappingList',
  })
  return res
}
// 手动执行规则
export const executeRules = (data:any) => {
  const res = request.post<any>({
    url: '/userPortrait/customLabel/executeRules',
    data,
  })
  return res
}
// 新增企业属性映射
export const addEnterpriseAttributeMapping = (data:any) => {
  const res = request.post<any>({
    url: '/userPortrait/customLabel/addEnterpriseAttributeMapping',
    data,
  })
  return res
}
// 客户分页查询操作
export const customLabelQueryPage = (data:any) => {
  const res = request.post<any>({
    url: '/userPortrait/customLabel/queryPage',
    data,
  })
  return res
}
// 客户分页查询操作
export const getStatisticalUserAttributeMappingList = () => {
  const res = request.post<any>({
    url: '/userPortrait/customLabel/getStatisticalUserAttributeMappingList',
  })
  return res
}





// 根据标签Id获取标签详情
export const getLabelInfoById = (id:any) => {
  const res = request.post<any>({
    url: `/userPortrait/customLabel/getLabelInfoById/${id}`
  })
  return res
}
// 根据标签Id获取标签分层详细
export const getLabelSliceInfo = (id:any) => {
  const res = request.post<any>({
    url: `/userPortrait/customLabel/getLabelSliceInfo/${id}`
  })
  return res
}



















// 用户分群


// 新增用户分群
export const addCustomGroup = (data:any) => {
  const res = request.post<any>({
    url: '/userPortrait/customGroup/addCustomGroup',
    data,
  })
  return res
}

//删除用户分群
export const delCustomGroup = (labelId: any) => {
  const res = request.get<any>({
    url: `/userPortrait/customGroup/delCustomGroup/${labelId}`,
  })
  return res
}


// 根据用户分群Id获取用户分群详情
export const getCustomGroupInfoById = (id:any) => {
  const res = request.post<any>({
    url: `/userPortrait/customGroup/getCustomGroupInfoById/${id}`
  })
  return res
}


// 分页查询分群列表
export const queryGroupByPage = (data: any) => {
  const res = request.post<any>({
    url: '/userPortrait/customGroup/queryGroupByPage',
    data,
  })
  return res
}

// 手动执行规则
export const customGroupId = (customGroupId:any) => {
  const res = request.post<any>({
    url: `/userPortrait/customLabel/executeGroupRules/${customGroupId}`,
  })
  return res
}



//修改用户标签
export const modifyGroup = (data: any) => {
  const res = request.post<any>({
    url: '/userPortrait/customGroup/modifyGroup',
    data,
  })
  return res
}
//修改用户标签
export const queryGroupLabelList = () => {
  const res = request.post<any>({
    url: '/userPortrait/customLabel/getLabelList',

   
  })
  return res
}

// 客户分页查询
export const customGroupQuerPages = (data: any) => {
  const res = request.post<any>({
    url: '/userPortrait/customGroup/queryPage',
    data,
  })
  return res
}
// 客户分页查询操作
export const customGroupQueryPage = (data:any) => {
  const res = request.post<any>({
    url: '/userPortrait/customGroup/queryGroupByPage',
    data,
  })
  return res
}