<template>
  <div>

    <el-card class="jt-card">
      <div class="header" style="margin-bottom: 20px">
        <div class="header-title">
          <img :src="getAssetURL('title-arrow')" alt="" />
          查询条件
        </div>
      </div>
      复盘日期：
      <div style="display: inline-block">
        <el-date-picker type='month' @change="dateChange" format="YYYY-MM" value-format="YYYY-MM" v-model="timeDate"
          style="margin-right: 15px" :clearable="false" />
      </div>
      复盘场站：
      <div style="display: inline-block">
        <el-select style="width:100%" v-model="checkStation" placeholder="请选择场站" clearable @change="checkStationChange">
          <el-option v-for="item in checkStationOptions" :key="item.no" :label="item.label" :value="item.value" />
        </el-select>
      </div>
    </el-card>
    <el-card class="jt-card" style="margin-top: 20px">
      <div class="header">
        <div class="header-title">
          <img :src="getAssetURL('title-arrow')" alt="" />
          日均价
        </div>
      </div>

      <div class="table-content" style="margin-top: 20px">
        <div style="display: flex; justify-content: flex-end;margin-bottom: 10px;"> <el-button
            type="primary">导出</el-button></div>
        <el-table :data="tableData" :header-cell-style="{
          background: '#f2f3f5',
          textAlign: 'center',
        }" :cell-style="{ textAlign: 'center' }">
          <el-table-column prop="yearTime" label="电厂/机组名称" align="center" />
          <el-table-column label="发电类型" align="center" prop="seasonType"></el-table-column>
          <el-table-column prop="months" label="结算日期" align="center" />
          <el-table-column prop="peakStartTime" label="上网电量" align="center" />
          <el-table-column prop="summitStartTime" label="场站预测偏差率" align="center" />
          <el-table-column prop="flatSegmentStartTime" label="平台预测偏差率" align="center" />
          <el-table-column prop="lowEbbStartTime" label="日均价含回收（实际合同）" align="center" width="130" />
          <el-table-column prop="lowEbbStartTime" label="日均价含回收（场站预测）" align="center" width="130" />
          <el-table-column prop="lowEbbStartTime" label="日均价含回收（平台预测）" align="center" width="130" />
          <el-table-column prop="lowEbbStartTime" label="日均价含回收（按方案）" align="center" width="130" />
          <el-table-column fixed="right" label="结算明细" align="center">
            <template #default="scope">
              <div @click="onDetail(scope.row)" style="color: #165dff; cursor: pointer">
                详情
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-container">
          <el-pagination :current-page="params.pageNo" :page-sizes="[10, 20, 30, 50, 100]" :page-size="params.pageSize"
            :total="params.total" layout=" ->,total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
        </div>
      </div>
    </el-card>
    <el-dialog width="70%" append-to-body destroy-on-close v-model="dialogVisible" title="结算明细">

      <el-table :data="tableData" :header-cell-style="{
        background: '#f2f3f5',
        textAlign: 'center',
      }" :cell-style="{ textAlign: 'center' }">
        <el-table-column prop="yearTime" label="电厂/机组名称" align="center" />
        <el-table-column label="发电类型" align="center" prop="seasonType"></el-table-column>
        <el-table-column prop="months" label="结算日期" align="center" />
        <el-table-column prop="peakStartTime" label="时段" align="center" />
        <el-table-column prop="summitStartTime" label="中长期合同电费" align="center" />
        <el-table-column prop="flatSegmentStartTime" label="日前偏差电费" align="center" />
        <el-table-column prop="lowEbbStartTime" label="阻塞电费" align="center" />
        <el-table-column prop="lowEbbStartTime" label="实时偏差电费" align="center" />
        <el-table-column prop="lowEbbStartTime" label="偏差回收电费" align="center" />
        <el-table-column prop="lowEbbStartTime" label="电能量电费" align="center" />
        <el-table-column prop="lowEbbStartTime" label="结算均价" align="center" />
        <el-table-column prop="lowEbbStartTime" label="超额回收电费" align="center" />
        <el-table-column prop="lowEbbStartTime" label="结算日均价" align="center" />
        <el-table-column prop="lowEbbStartTime" label="结算日均价（含回收）" align="center" width="120" />
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup lang='ts'>
const getAssetURL = (image: any) => {
  return new URL(`../../../assets/svg/${image}.svg`, import.meta.url).href
}
import { ref, reactive } from 'vue'
const timeDate = ref<any>()
function dateChange() {

}
const checkStation = ref<any>()
function checkStationChange() {

}
const checkStationOptions = ref<any>([])

function onDetail() {
  dialogVisible.value = true
}

const tableData = ref<any>([{
  yearTime: 1
}])
const params = reactive({
  pageSize: 10,
  total: 0,
  pageNo: 1,
})
const handleSizeChange = (val: number) => {
  params.pageSize = val
  // initData()
}

const handleCurrentChange = (val: number) => {
  params.pageNo = val
  // initData()
}

const dialogVisible = ref<any>(false)
</script>

<style lang="scss" scoped>
.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding-top: 20px;
}
</style>