// import "@/utils/sso";
import { getConfig } from "@/config";
import NProgress from "@/utils/progress";
import { sessionKey, type DataInfo } from "@/utils/auth";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";
import { usePermissionStoreHook } from "@/store/modules/permission";
import {
  Router,
  createRouter,
  RouteRecordRaw,
  RouteComponent
} from "vue-router";
import {
  ascending,
  getTopMenu,
  initRouter,
  isOneOfArray,
  getHistoryMode,
  findRouteByPath,
  handleAliveRoute,
  formatTwoStageRoutes,
  formatFlatteningRoutes,
  addPathMatch
} from "./utils";
import { buildHierarchyTree } from "@/utils/tree";
import { isUrl, openLink, storageSession, isAllEmpty } from "@pureadmin/utils";
import { getToken } from "@/utils/auth";
import { useUserStore } from "@/store/modules/user";
import remainingRouter from "./modules/remaining";
import CryptoJS from 'crypto-js'
import {routerArrays} from "@/layout/types";
import { reqLogin, getUserTenant, reqGetVerificationCode, reqExchangeToken } from "@/api/user/index";
import { log } from "console";


/** 自动导入全部静态路由，无需再手动引入！匹配 src/router/modules 目录（任何嵌套级别）中具有 .ts 扩展名的所有文件，除了 remaining.ts 文件
 * 如何匹配所有文件请看：https://github.com/mrmlnc/fast-glob#basic-syntax
 * 如何排除文件请看：https://cn.vitejs.dev/guide/features.html#negative-patterns
 */
const modules: Record<string, any> = import.meta.glob(
  ["./modules/**/*.ts", "!./modules/**/remaining.ts"],
  {
    eager: true
  }
);

/** 原始静态路由（未做任何处理） */
const routes = [];

Object.keys(modules).forEach(key => {
  routes.push(modules[key].default);
});

/** 导出处理后的静态路由（三级及以上的路由全部拍成二级） */
export const constantRoutes: Array<RouteRecordRaw> = formatTwoStageRoutes(
  formatFlatteningRoutes(buildHierarchyTree(ascending(routes.flat(Infinity))))
);

/** 用于渲染菜单，保持原始层级 */
export const constantMenus: Array<RouteComponent> = ascending(
  routes.flat(Infinity)
).concat(...remainingRouter);
/** 不参与菜单的路由 */
export const remainingPaths = Object.keys(remainingRouter).map(v => {
  return remainingRouter[v].path;
});
/** 创建路由实例 */
export const router: Router = createRouter({
  history: getHistoryMode(import.meta.env.VITE_ROUTER_HISTORY),
  routes: constantRoutes.concat(...(remainingRouter as any)),
  strict: true,
  scrollBehavior(to, from, savedPosition) {
    return new Promise(resolve => {
      if (savedPosition) {
        return savedPosition;
      } else {
        if (from.meta.saveSrollTop) {
          const top: number =
            document.documentElement.scrollTop || document.body.scrollTop;
          resolve({ left: 0, top });
        }
      }
    });
  }
});

/** 重置路由 */
export function resetRouter() {
  router.getRoutes().forEach(route => {
    const { name, meta } = route;
    if (name && router.hasRoute(name) && meta?.backstage) {
      router.removeRoute(name);
      router.options.routes = formatTwoStageRoutes(
        formatFlatteningRoutes(
          buildHierarchyTree(ascending(routes.flat(Infinity)))
        )
      );
    }
  });
  usePermissionStoreHook().clearAllCachePage();
}

/** 路由白名单 */
const whiteList = ["/login"];

const { VITE_HIDE_HOME } = import.meta.env;

router.beforeEach(async (to: ToRouteType, _from, next) => {
  // 先解析URL，获取token
  let url = document.URL

  // 检测hide参数，用于系统嵌入模式
  const urlParams = new URLSearchParams(window.location.search)
  const hideParam = urlParams.get('hide')
  console.log(hideParam)
  if (hideParam === 'true') {
    // 将嵌入模式状态存储到localStorage
    localStorage.setItem('system-embedded-mode', 'true')
    // 同时更新store状态
    const { useSettingStoreHook } = await import("@/store/modules/settings")
    useSettingStoreHook().setEmbeddedMode(true)
    console.log('系统嵌入模式已启用')
  }
  
  // OA系统单点登录处理
  if (url.includes('access_system_lock')) {
    try {
      useMultiTagsStoreHook().handleTags("equal", []);
      let access_system_lock = url.split('=')[1]
      if (access_system_lock.includes('#')) {
        access_system_lock = access_system_lock.split('#')[0]
      }
      
      // 清理URL，移除参数
      url = url.split('?access_system_lock')[0]
      window.location.href = url
      
      // 解密用户数据
      const bytes = CryptoJS.AES.decrypt(access_system_lock, 'secret key jtny')
      const decryptedData = JSON.parse(bytes.toString(CryptoJS.enc.Utf8))
      
      // 验证必要字段
      if (!decryptedData.username || !decryptedData.token) {
        console.error('OA单点登录失败：缺少必要字段')
        next({ path: "/login" })
        return
      }
      
      // 设置用户信息和token
      await useUserStore().setUserInfoAndToken(decryptedData, async () => { })
      await useUserStore().userSetToken(decryptedData.token)
      
      console.log('OA单点登录成功：', decryptedData.username)
    } catch (error) {
      console.error('OA单点登录解密失败：', error)
      next({ path: "/login" })
      return
    }
  }

  // 新的单点登录处理 - 模拟登录页流程
  if (url.includes('sso_auto_login')) {
    try {
      const urlParams = new URLSearchParams(window.location.search)
      const username = urlParams.get('username')
      const password = urlParams.get('password')
      const tenantId = urlParams.get('tenantId')
      
      if (!username || !password) {
        console.error('单点登录失败：缺少用户名或密码')
        next({ path: "/login" })
        return
      }

      // 模拟登录流程
      const loginData = { username, password }
      
      // 1. 调用登录接口
      const loginResult = await reqLogin(loginData)
      
      // 2. 获取用户租户信息
      const tenantResult = await getUserTenant(loginResult)
      useUserStore().setLoginUserTenantList(tenantResult.loginUserTenantList)
      useUserStore().setUserId(tenantResult.busId)
      
      // 3. 如果指定了租户ID，直接使用；否则使用第一个租户
      let selectedTenantId = tenantId
      if (!selectedTenantId && tenantResult.loginUserTenantList.length > 0) {
        selectedTenantId = tenantResult.loginUserTenantList[0].tenantId
      }
      
      // 4. 获取验证码
      const verificationData = {
        username,
        password,
        tenantId: selectedTenantId,
        userId: tenantResult.busId
      }
      
      const verificationResult = await reqGetVerificationCode(verificationData)
      
      // 5. 交换token
      const tokenResult = await reqExchangeToken({ code: verificationResult.code })
      
      // 6. 设置token和用户信息
      useUserStore().userSetToken(tokenResult.token)
      useUserStore().userSetLongToken(tokenResult.longToken)
      await useUserStore().stateReqUserInfo()
      
      // 7. 设置权限和路由
      await usePermissionStoreHook().handleWholeMenus([])
      addPathMatch()
      
      // 8. 清理URL参数
      const cleanUrl = window.location.href.split('?')[0]
      window.history.replaceState({}, document.title, cleanUrl)
      
      console.log('单点登录成功：', username)
      
      // 9. 跳转到首页
      next({ path: "/big-screen" })
      return
      
    } catch (error) {
      console.error('单点登录失败：', error)
      next({ path: "/login" })
      return
    }
  }

  if (to.meta?.keepAlive) {
    handleAliveRoute(to, "add");
    // 页面整体刷新和点击标签页刷新
    if (_from.name === undefined || _from.name === "Redirect") {
      handleAliveRoute(to);
    }
  }
  // 刷新页面重新设置字典
  if (getToken() && getToken().accessToken) {
    if (useUserStore().getLastUpdateTime === 0) {
      await useUserStore().setDictList();
    }
  }
  if (useUserStore().token) {
    await useUserStore().stateReqUserInfo()
  }

  const userInfo = storageSession().getItem<DataInfo<number>>(sessionKey);
  NProgress.start();
  const externalLink = isUrl(to?.name as string);
  if (!externalLink) {
    to.matched.some(item => {
      if (!item.meta.title) return "";
      const Title = getConfig().Title;
      if (Title) document.title = `${item.meta.title} | ${Title}`;
      else document.title = item.meta.title as string;
    });
  }
  /** 如果已经登录并存在登录信息后不能跳转到路由白名单，而是继续保持在当前页面 */
  function toCorrectRoute() {
    whiteList.includes(to.fullPath) ? next(_from.fullPath) : next();
  }

  if (userInfo) {
    // 无权限跳转403页面
    if (to.meta?.roles && !isOneOfArray(to.meta?.roles, userInfo?.roles)) {
      next({ path: "/error/403" });
    }
    // 开启隐藏首页后在浏览器地址栏手动输入首页welcome路由则跳转到404页面
    if (VITE_HIDE_HOME === "true" && to.fullPath === "/welcome") {
      next({ path: "/big-screen" });
    }
    if (_from?.name) {
      // name为超链接
      if (externalLink) {
        openLink(to?.name as string);
        NProgress.done();
      } else {
        toCorrectRoute();
      }
    } else {

      // 刷新
      if (
        usePermissionStoreHook().wholeMenus.length === 0 &&
        to.path !== "/login"
      ) {

        await usePermissionStoreHook().handleWholeMenus([]);
        await addPathMatch();
        if (url.includes('access_system_lock') && !useUserStore().hasChange) {
          useUserStore().hasChange = true
          router.push("/customer-management/index");
        }
        if (!useMultiTagsStoreHook().getMultiTagsCache) {
          const { path } = to;
          const route = findRouteByPath(
            path,
            router.options.routes[0].children
          );
          // query、params模式路由传参数的标签页不在此处处理
          if (route && route.meta?.title) {
            useMultiTagsStoreHook().handleTags("push", {
              path: route.path,
              name: route.name,
              meta: route.meta
            });
          }
        }
      }
      toCorrectRoute();
    }
  } else {
    if (to.path !== "/login") {
      if (whiteList.indexOf(to.path) !== -1) {
        next();
      } else {
        next({ path: "/login" });
      }
    } else {
      next();
    }
  }
});

router.afterEach(() => {
  NProgress.done();
});

export default router;
