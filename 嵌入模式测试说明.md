# 系统嵌入模式功能测试说明

## 功能概述

本系统已实现嵌入模式功能，支持通过 URL 参数控制头部和侧边栏的显示/隐藏，适用于将系统嵌入到其他外部系统中的场景。

## 实现的功能

### 1. URL 参数检测

- 系统会自动检测 URL 中的 `hide` 参数
- 当访问 `?hide=true` 时，系统进入嵌入模式
- 嵌入模式状态会自动保存到 localStorage 中

### 2. 界面元素隐藏

在嵌入模式下，以下界面元素将被隐藏：

- **头部导航栏** (navbar)
- **侧边栏菜单** (sidebar)
- **标签页** (tags)
- **水平导航** (horizontal navigation)

### 3. 样式优化

在嵌入模式下，还会自动调整以下样式：

- **移除顶部 padding** (section 的 padding-top)
- **移除 wrapper padding** (.app-main-wrapper 的 padding)
- **移除内容 margin** (.main-content 的 margin)
- **确保内容区域占满整个容器**

### 4. 状态持久化

- 一旦检测到 `hide=true` 参数，状态会保存到 localStorage
- 后续页面刷新和导航都会保持嵌入模式
- 状态存储键名：`system-embedded-mode`

## 测试步骤

### 测试 1：基本嵌入模式启用

1. 启动开发服务器：`npm run dev`
2. 在浏览器中访问：`http://localhost:3000/?hide=true`
3. 验证：
   - 头部导航栏应该被隐藏
   - 左侧边栏应该被隐藏
   - 标签页应该被隐藏
   - 主内容区域应该占满整个屏幕

### 测试 2：状态持久化验证

1. 在嵌入模式下，刷新页面
2. 验证：界面仍然保持嵌入模式（无头部和侧边栏）
3. 导航到其他页面
4. 验证：所有页面都保持嵌入模式

### 测试 3：localStorage 存储验证

1. 在浏览器开发者工具中打开 Application/Storage 标签
2. 查看 localStorage
3. 验证：应该存在键名为 `system-embedded-mode`，值为 `true` 的记录

### 测试 4：正常模式验证

1. 清除 localStorage 中的 `system-embedded-mode` 键
2. 访问不带 `hide` 参数的 URL：`http://localhost:3000/`
3. 验证：
   - 头部导航栏正常显示
   - 左侧边栏正常显示
   - 标签页正常显示

## 技术实现细节

### 1. 路由守卫检测

在 `src/router/index.ts` 的 `beforeEach` 守卫中：

```javascript
// 检测hide参数，用于系统嵌入模式
const urlParams = new URLSearchParams(window.location.search);
const hideParam = urlParams.get("hide");
if (hideParam === "true") {
  // 将嵌入模式状态存储到localStorage
  localStorage.setItem("system-embedded-mode", "true");
  // 同时更新store状态
  const { useSettingStoreHook } = await import("@/store/modules/settings");
  useSettingStoreHook().setEmbeddedMode(true);
  console.log("系统嵌入模式已启用");
}
```

### 2. 状态管理

在 `src/store/modules/settings.ts` 中新增：

- `embeddedMode` 状态
- `getEmbeddedMode` getter
- `getShouldHideSidebar` 综合判断 getter
- `setEmbeddedMode` 和 `initEmbeddedMode` actions

### 3. 布局组件修改

在 `src/layout/index.vue` 中：

- 使用 `getShouldHideSidebar` 替代原来的 `hiddenSideBar`
- 在嵌入模式下隐藏头部组件
- 在嵌入模式下隐藏标签页组件

## 兼容性说明

### 与现有配置的兼容性

- 嵌入模式与现有的 `hiddenSideBar` 配置完全兼容
- `getShouldHideSidebar` getter 会综合判断两种隐藏条件
- 不会影响现有的系统配置和功能

### 重置嵌入模式

如需退出嵌入模式，可以：

1. 清除 localStorage：`localStorage.removeItem('system-embedded-mode')`
2. 或调用 store 方法：`useSettingStoreHook().setEmbeddedMode(false)`

## 使用场景

### 1. iframe 嵌入

```html
<iframe
  src="http://your-system.com/?hide=true"
  width="100%"
  height="600"
></iframe>
```

### 2. 弹窗嵌入

在弹窗或模态框中嵌入系统页面时，使用嵌入模式可以提供更好的用户体验。

### 3. 第三方系统集成

当需要将本系统作为模块集成到其他系统中时，嵌入模式可以确保界面的一致性。

## 注意事项

1. **首次访问**：必须通过带有 `?hide=true` 参数的 URL 首次访问才能启用嵌入模式
2. **状态持久化**：一旦启用，状态会持久保存，需要手动清除才能退出
3. **功能限制**：嵌入模式下某些导航功能可能受限，请根据实际需求调整
4. **样式适配**：在嵌入环境中可能需要额外的 CSS 样式调整

## 故障排除

### 问题 1：嵌入模式未生效

- 检查 URL 参数是否正确：`?hide=true`
- 检查浏览器控制台是否有错误信息
- 检查 localStorage 中是否存在相应记录

### 问题 2：无法退出嵌入模式

- 手动清除 localStorage：`localStorage.removeItem('system-embedded-mode')`
- 刷新页面

### 问题 3：部分界面元素仍然显示

- 检查相关组件是否正确使用了 `getShouldHideSidebar` 或 `embeddedMode` 状态
- 检查组件的条件渲染逻辑

---

_测试文档生成时间: 2025-01-13_  
_功能版本: v1.0_
