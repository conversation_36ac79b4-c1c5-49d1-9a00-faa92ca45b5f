<template>
  <div ref="chartRef" :style="{ height, width }" />
</template>
<script lang="ts">
import {
  defineComponent,
  PropType,
  ref,
  Ref,
  onMounted,
  watch,
  onActivated
} from "vue";
import type { EChartsOption } from "echarts";
import * as echarts from "echarts/core";
import { useAppStoreHook } from "@/store/modules/app";
import { useECharts, delay } from "@pureadmin/utils";
import { getAreaSumApi } from "@/api/customer-management/index";
import { triggerWindowResize } from "@/utils/event";
export default defineComponent({
  props: {
    width: {
      type: String as PropType<string>,
      default: "100%"
    },
    height: {
      type: String as PropType<string>,
      default: "calc(100vh - 78px)"
    },
    series: {
      type: Array as PropType<Array<object>>,
      default: () => []
    }
  },
  emits: ["update"],
  setup(props, { emit }) {
    const chartRef = ref<HTMLDivElement | null>(null);
    const seriesData = ref([]);
    const geoCoordMap = {
      赣州市: [115.30001, 25.82097],
      吉安市: [114.586373, 27.111699],
      上饶市: [117.571185, 28.44442],
      九江市: [115.992811, 29.512034],
      抚州市: [116.358351, 27.68385],
      宜春市: [114.391136, 28.2043],
      南昌市: [115.892151, 28.476493],
      景德镇市: [117.214664, 28.99256],
      萍乡市: [113.852186, 27.322946],
      鹰潭市: [117.193838, 28.038638],
      新余市: [114.660835, 27.710834]
    };
    function convertData(data) {
      const res = [];
      for (var i = 0; i < data.length; i++) {
        const geoCoord = geoCoordMap[data[i].name];
        if (geoCoord) {
          res.push({
            name: data[i].name,
            value: geoCoord.concat(data[i].value)
          });
        }
      }
      return res;
    }
    const tooltipValue = ref();
    const { setOptions, resize } = useECharts(chartRef as Ref<HTMLDivElement>);
    echarts.registerMap("jiangxi", window.mapJSON);
    const getOption = (): EChartsOption => {
      return {
        tooltip: {
          // trigger: "item",
          renderMode: "html",
          triggerOn: "click",
          enterable: true,
          // appendToBody: true,
          backgroundColor: "transparent",
          borderColor: "transparent",
          // alwaysShowContent: true,
          borderWidth: 0,
          padding: 0,
          extraCssText: "box-shadow: none;",
          // position: "right",
          formatter: function (params) {
            tooltipValue.value = params;
            const mapData = filterMapValue(params.name);
            let res =
              '<div onclick="window.handleMapDetail && window.handleMapDetail()" style="box-shadow: none; cursor: pointer;" class="custom-tooltip">' +
              '<h2 style="color:#fff;font-size: 16px;padding-top:38px;text-align:center;">' +
              mapData.index +
              "</h2>" +
              '<p style="margin-top:10px;margin-left:40px;color:#fff;padding:0.1rem 0.1rem;font-size: 14px;">' +
              params.name +
              "</p>" +
              '<p style="color:#fff;margin-left:40px;padding:0.1rem 0.1rem;font-size: 12px;">' +
              "用户数量：" +
              (mapData.customCount || 0) +
              "家</p>" +
              '<p style="color:#fff;margin-left:40px;padding:0.1rem 0.1rem 0.2rem;font-size: 12px;">' +
              "代理电量：" +
              (mapData.sumElectricityQty || 0) +
              " MWh</p>" +
              "</div>";
            return res;
          }
        },
        geo: {
          map: "jiangxi",
          aspectScale: 0.85, // 长宽比
          zoom: 1.05,
          roam: false,
          top: "8%",
          left: "5%",
          right: "5%",
          bottom: "8%",
          itemStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: "#4aa2eb"
                },
                {
                  offset: 1,
                  color: "#4799dc"
                }
              ],
              global: false // 缺省为 false
            },
            borderWidth: 0,
            shadowColor: "#065fcf",
            shadowOffsetX: 5,
            shadowOffsetY: 30
          }
        },
        series: [
          {
            type: "map",
            roam: false,
            label: {
              show: true,
              color: "rgba(255,255,255,.65)"
            },
            itemStyle: {
              areaColor: "#4aa2eb",
              borderWidth: 2,
              borderColor: "#64e1ff"
            },
            emphasis: {
              label: {
                color: "rgba(255,255,255,.65)"
              },
              itemStyle: {
                areaColor: "rgba(20,30,64,0)",
                borderWidth: 1,
                borderColor: "rgba(34,120,255)"
              }
            },
            top: "8%",
            left: "5%",
            right: "5%",
            bottom: "8%",
            zoom: 1.05,
            map: "jiangxi" // 使用
          },
          {
            name: "点",
            type: "scatter",
            coordinateSystem: "geo",
            symbol: "circle",
            // symbol: "image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAARCAYAAAA7bUf6AAAAAXNSR0IArs4c6QAAAxJJREFUOE91lG1o1WUYxn/Xds7e3NrMbNjesm1yqFUOyYWG4AcD7QW2NostKi1nGX44JBl9kag+lIgWpHOBL1DB2sRoaTlUWKAjnDawrdNybLqDS3Jzh714ds7O/87/yTNOks/H57mv38Xz3Nf9iLssM8sACgAvEJQ0ebda3XlgZs8AJcA0cAWIAkVAHhACWiTNJuvmIGaWC2wBjkoK8K2lPgypbnHfQhxWEzNj4S3wRqBV0kACFIeYWdYtF38f7HylGfMs4MH6ctaFIxREYyg3g9HjVzkxPkmga71umplr9qOkQVefgPiBpqJW8JdR9/cEtZ1BwuFZHLcoVbCygJzcbE5+38fBng0aN7P3gE8kmcysAlikVk6/W8hzU1Ns6rrKlCeFDE8K6Qg5xkzUIVyWR3plPqeaetk/+Fr8alWS2lzIFkl7K76x/OoS9hy7REqah5x1D/FozVIKM73QMcDoV+e5EI4SWl1M5plRtnXVKpDQupBGSc3rT9iqeTG2X7xOZEUpvs9W4EvuwOd9jHzdQ3cahKt9dL7Tzj7bwSZJ++cgn3bbG0f7WRNzyNq+nOU15dyfDPklBP7j/BSeIbSxkv6t/XxgddQAHS7EpX35/lmrPxfkhdAM3q1VVL5cTmEypCNIZMfPnJqJEmqsJPDWH3xkdTTEc2Nmb0pqWtlmjzy5gF2dw0zeN4/i5rU8UZT5L2bK7f8ZLv46yEDJPaj4Xg7vforvDFztPhfyKnDEdwC9/jgftvSyKFWk52aRX/0YD7gBav+TseEbBCOzTFcvIdY6hP+3el0zs7clfeFCsoEGQXPVEco2+9i19wLTDng8KXjMkEE05jD7oo+8wAQfHxrjrNXxNDAk6fdE2J6PDxn0LGth8UulbL4+TcXIJE7UwSnIxjs/i6Hz1zhweYSe7kbmA7VuNOYSezv6G4BeiXPL2smM3iR/SQ4laV48/WMMj0cYudTAhBF/8Hpgp6R4ov8zxWa2FigF2iT9ldwdM8tx3QFH0uH/neLEppmlA89CPNZxp9tm7tfwg6Qbd34f/wBjs0gxlxbSuAAAAABJRU5ErkJggg==",
            symbolSize: 24,
            label: {
              show: true,
              formatter: function (params) {
                return params.value[2];
              }
            },
            itemStyle: {
              color: "#1b5a83",
              borderColor: "#46acf0",
              borderWidth: 10
            },
            zlevel: 6,
            data: convertData(props.series)
          }
        ]
      };
    };
    function filterMapValue(name) {
      const item = seriesData.value.find(i => i.areaName === name);
      const index = seriesData.value.findIndex(i => i.areaName === name);
      if (item) {
        return {
          ...item,
          index: index + 1,
          sumElectricityQty:
            item.sumElectricityQty == null ? "" : item.sumElectricityQty
        };
      } else {
        return {
          customCount: 0,
          sumElectricityQty: 0,
          index: "-"
        };
      }
    }
    // 设置全局处理函数
    window.handleMapDetail = handleDetail;

    function handleDetail() {
      if (!tooltipValue.value) {
        console.warn("No tooltip value available");
        return;
      }

      console.log("Map detail clicked:", tooltipValue.value.name);
      const mapData = filterMapValue(tooltipValue.value.name);

      if (!mapData.areaName) {
        console.warn("No area name found for:", tooltipValue.value.name);
        return;
      }

      const obj = {
        areaName: mapData.areaName,
        id: mapData.areaId
      };

      emit("update", obj);
    }

    watch(
      () => useAppStoreHook().getSidebarStatus,
      () => {
        delay(600).then(() => resize());
      }
    );
    watch(
      () => props,
      () => setOptions(getOption() as EChartsOption),
      {
        immediate: true,
        deep: true
      }
    );

    onMounted(async () => {
      const res = await getAreaSumApi();
      seriesData.value = res.data;
      delay(300).then(() => resize());
    });
    onActivated(() => triggerWindowResize());
    return { chartRef };
  }
});
</script>
<style lang="scss" scoped>
:deep .custom-tooltip {
  padding: 0 !important;
  border: none !important;
  background-color: transparent !important;
  background: url("/src/assets/images/BigScreen/tooltip.png") no-repeat;
  background-size: 100% 100%;
  width: 300px;
  height: 160px;
  box-shadow: none;
  cursor: pointer;
}
</style>
