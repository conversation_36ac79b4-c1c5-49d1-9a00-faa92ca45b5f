<template>
    <div>
        <el-dialog width="46%" h v-model="dialogVisible" title="列目显示">
            
             <div class="contant">
                <div class="labelBox" style="margin-right: 50px; padding: 20px;">
                    <el-input v-model="condition.keyWord" style="width: 100%;margin-bottom: 10px;" size="large"
                        placeholder="请输入搜索内容" :prefix-icon="Search" />

                    <div>
                        <el-checkbox-group v-model="checkedList" style="display: flex; flex-direction: column;"
                            @change="handleChange">
                            <el-checkbox v-for="item in filteredOptions" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-checkbox-group>
                    </div>
                </div>
                <div class="labelBox">
                    <div style="padding-left: 20px; padding-top: 15px;">按住可以拖拽排序</div>
                    <el-divider />
                    <el-tree style="max-width: 600px" :allow-drop="allowDrop"  :data="option2"
                        draggable default-expand-all node-key="id"  @node-drop="handleDrop"
                       />
                </div>
             </div>
              
               
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="handlerCancel ">取消</el-button>
                    <el-button type="primary" @click="handlerComfirm">
                        确定
                    </el-button>
                </div>
            </template>
        </el-dialog>

    </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from "vue";
const dialogVisible = ref<boolean>(false);
import { Search } from '@element-plus/icons-vue'
import { watch } from "fs";
const condition = ref({ keyWord: '' });
const props = defineProps({
    filterOptions: {
    type: Array,
    default: () => []
  },
    def: {
    type: Array,
    default: () => []
  },
  
});
const emit = defineEmits(["orderData"]);

let options = [
    { label: '覆盖客户数据', value: 'coveredNum' },
    { label: '标签值', value: 'labelValue' },
    { label: '最新计算状态', value: 'latestCalculationStatus' },
    { label: '标签状态', value: 'labelStatus' },
    { label: '更新方式', value: 'updateType' },
    { label: '创建时间', value: 'createTimeStr' },
    { label: '创建方式', value: 'createType' },
];
let option2 = ref([
    { label: '覆盖客户数据', value: 'coveredNum' },
    { label: '标签值', value: 'labelValue' },
    { label: '最新计算状态', value: 'latestCalculationStatus' },
    { label: '标签状态', value: 'labelStatus' },
    { label: '更新方式', value: 'updateType' },
    { label: '创建时间', value: 'createTimeStr' },
    { label: '创建方式', value: 'createType' },
]);

let checkedList =ref(["覆盖客户数据","创建时间","标签值","最新计算状态","更新方式","标签状态","创建方式","创建方式"])

const handleShowList = () => {
    dialogVisible.value = true;
}
// 计算属性，用于生成过滤后的选项
// 计算属性，用于生成过滤后的选项
const filteredOptions = computed(() => {
    return options.filter(option =>
        option.label.toLowerCase().includes(condition.value.keyWord.toLowerCase())
    );
});

const handleChange = (item:any) => {
    console.log(checkedList.value,item)
    const filteredOptions =   options.filter(option => checkedList.value.includes(option.label))
  
    option2.value =  filteredOptions
    // console.log(option2.value)
}
const handlerIpt = () => {
    // console.log(conditon.value)
}
const allowDrop = (draggingNode: Node, dropNode: Node, type: any) => {
//   console.log(draggingNode, dropNode, type)
  if(type === 'inner'){
    return false
  }
   return true

}

const handleDrop = (before:any, after: any, inner: any) => {

    option2.value = after.parent.data
   
    
}

const handlerCancel = ()=>{
    // console.log('cancel')
    dialogVisible.value = false
}
const handlerComfirm = ()=>{
    // console.log('comfirm')
    emit("orderData", option2.value);
    dialogVisible.value = false
}

onMounted(() => {
   
    // console.log(checkedList.value,filteredOptions.value.map(item => item.value))
    console.log("aa")
   
})
defineExpose({
    handleShowList
});
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__header{
    background: #254F7A;
   
    .el-dialog__title{
        color: white;
    }
}
.contant{
    display: flex;
}
::v-deep .el-dialog__body {
    padding: 30px 30px 30px 50px;
}

.labelBox {
    width: 380px;
    min-height: 460px;
    border: 1px solid #dcdfe6;
}

.el-row {
    margin-bottom: 20px;
}

.el-row:last-child {
    margin-bottom: 0;
}

.el-col {
    border-radius: 4px;
}

.grid-content {
    border-radius: 4px;
    min-height: 36px;
}
</style>