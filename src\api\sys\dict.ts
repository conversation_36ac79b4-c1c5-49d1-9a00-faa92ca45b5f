import { http } from "@/utils/http";
import { baseUrlApi } from "../utils";
import {
  GetDictListResultModel,
  DictResultModel,
  DictPageModel
} from "@/model/dictModel";
import { ResponseDetailModel, BasicResponseParams } from "@/model/baseModel";
// /system/acdiinorty / save;
export const saveSysDictionaryApi = (data: DictResultModel) => {
  return http.request<BasicResponseParams>(
    "post",
    baseUrlApi("system/dictionary/save"),
    {
      data
    }
  );
};
// 分页字典列表
export const getDictListApi = (data: DictPageModel) => {
  return http.request<GetDictListResultModel>(
    "post",
    baseUrlApi("system/dictionary/queryPage"),
    {
      data
    }
  );
};

// 查询所有字典信息
export const getAllDictListApi = () => {
  return http.request<ResponseDetailModel<DictResultModel[]>>(
    "get",
    baseUrlApi("system/dictionary/allData")
  );
};
// 根据id查询
export const getDictDetailApi = (id: string | number) => {
  return http.request<ResponseDetailModel<DictResultModel>>(
    "get",
    baseUrlApi(`system/dictionary/details/${id}`)
  );
};
// 根据id删除
export const delDictByIdApi = (id: string | number) => {
  return http.request<BasicResponseParams>(
    "get",
    baseUrlApi(`system/dictionary/delete/${id}`)
  );
};
