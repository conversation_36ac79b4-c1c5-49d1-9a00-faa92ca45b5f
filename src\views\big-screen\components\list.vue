<template>
  <div>
    <pure-table
      :columns="columns"
      :data="tableData"
      :pagination="pagination"
      @page-size-change="onSizeChange"
      @page-current-change="onCurrentChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from "vue";
import { columns } from "./data";
import type { PaginationProps } from "@pureadmin/table";
import { delay } from "@pureadmin/utils";
import { getCustomerListApi } from "@/api/customer-management/index";
const props = defineProps({
  areaId: {
    type: String as PropType<string>,
    default: undefined
  }
});
const loading = ref(false);
const tableData = ref([]);
const searchInfo = ref({
  name: "",
  customGrade: "",
  areaId: undefined,
  tags: undefined,
  // customIdentity: 1,
  // annualElectricityMin: 0.1,
  pageNo: 1,
  pageSize: 10
});
/** 分页配置 */
const pagination = reactive<PaginationProps>({
  pageSize: 10,
  currentPage: 1,
  pageSizes: [10, 20, 40, 50],
  total: 0,
  align: "right",
  background: true,
  small: false
});
function onSizeChange(val) {
  searchInfo.value.pageSize = val;
  getList();
}
function onCurrentChange(val) {
  searchInfo.value.pageNo = val;
  getList();
}
async function getList() {
  loading.value = true;
  const { data } = await getCustomerListApi(searchInfo.value);
  pagination.total = data ? Number(data.totalCount) : 0;
  tableData.value = data ? data.data : [];
  delay(300).then(() => {
    loading.value = false;
  });
}
onMounted(() => {
  // getList();
});
watch(
  () => props.areaId,
  newVal => {
    searchInfo.value.areaId = newVal;
    tableData.value = [];
    searchInfo.value.pageNo = 1;
    pagination.currentPage = 1;
    pagination.pageSize = 10;
    pagination.total = 0;
    getList();
  },
  { immediate: true }
);
</script>

<style scoped></style>
