import dayjs from "dayjs";
import { useDictOptions } from "@/hooks/dict/useDictOptions";
const { electricityTypeOptions, industryOptions } = useDictOptions();
export const columns: TableColumnList = [
  {
    label: "序号",
    width: 40,
    align: "center",
    type: "index"
  },
  {
    label: "用户名称",
    prop: "name",
    headerSlot: "nameHeader",
    searchType: "text",
    slot: "name",
    headerAlign: "center",
    width: 140
  },
  {
    label: "代理电量",
    prop: "monthlyAverageElectricity",
    width: 120,
    align: "center",
    headerAlign: "center",
    headerSlot: "monthlyAverageElectricity",
    searchType: "number_range"
  },
  {
    label: "代理生效日期",
    prop: "effectiveDate",
    headerSlot: "effectiveDateHeader",
    align: "center",
    headerAlign: "center",
    searchType: "date_range",
    width: 125,
    formatter: ({ effectiveDate }) =>
      !["0", 0, undefined, null].includes(effectiveDate)
        ? dayjs(Number(effectiveDate)).format("YYYY-MM-DD")
        : ""
  },
  {
    label: "代理结束日期",
    prop: "terminationDate",
    headerSlot: "terminationDateHeader",
    align: "center",
    headerAlign: "center",
    searchType: "date_range",
    width: 125,
    formatter: ({ terminationDate }) =>
      !["0", 0, undefined, null].includes(terminationDate)
        ? dayjs(Number(terminationDate)).format("YYYY-MM-DD")
        : ""
  },
  {
    label: "用电性质",
    headerSlot: "electricityTypeHeader",
    searchType: "select",
    align: "center",
    headerAlign: "center",
    prop: "electricalNature",
    formatter: ({ electricalNature }) =>
      ![undefined, null].includes(electricalNature)
        ? electricityTypeOptions.find(item => item.value == electricalNature)
            .label
        : ""
  },
  {
    label: "跟进人",
    prop: "followerName",
    // 表头弹窗搜索字段
    propKey: "followerId",
    align: "center",
    headerAlign: "center",
    headerSlot: "followerNameHeader",
    searchType: "select"
  },
  {
    label: "用户行业",
    prop: "industryId",
    headerSlot: "industryHeader",
    align: "center",
    headerAlign: "center",
    searchType: "select",
    formatter: ({ industryId }) =>
      ![undefined, null].includes(industryId)
        ? industryOptions.find(item => item.value == industryId).label
        : ""
  },
  {
    label: "所在地区",
    align: "center",
    headerAlign: "center",
    searchType: "treeSelect",
    headerSlot: "areaIdHeader",
    prop: "areaName",
    propKey: "areaId"
  },
  {
    label: "用户来源",
    prop: "customerSource",
    align: "center",
    headerSlot: "customerSourceHeader",
    searchType: "select",
    headerAlign: "center",
    slot: "customerSource"
  },
  {
    label: "代理类型",
    prop: "agentType",
    propKey: "customType",
    headerSlot: "customTypeHeader",
    searchType: "select",
    align: "center",
    headerAlign: "center",
    slot: "agentType"
  },
  {
    label: "签约状态",
    prop: "isSign",
    headerSlot: "statusHeader",
    searchType: "select",
    align: "center",
    headerAlign: "center",
    slot: "isSign",
    formatter: ({ isSign }) =>
      isSign === true? "已签约" : "未签约"
  },
  {
    label: "操作",
    width: "140",
    fixed: "right",
    align: "center",
    headerAlign: "center",
    slot: "operation"
  }
];
