<template>
  <div>
    <!-- 四个card平分 -->
    <div style="margin-top: 20px">
      <el-row :gutter="10">
        <el-col :span="8">
          <el-card class="jt-card">
            <div class="jt-card-content">
              <div class="jt-card-content-item">
                <div style="color: #606266; line-height: 22px; font-size: 14px">
                  总营收(元):
                </div>
                <div style="line-height: 24px; font-size: 24px">{{ totalElectricityCost }}</div>
              </div>
              <img :src="getAssetURL('coin')" alt="" />
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="jt-card">
            <div class="jt-card-content">
              <div class="jt-card-content-item">
                <div style="color: #606266; line-height: 22px; font-size: 14px">
                  总度电收入(元/MWh):
                </div>
                <div style="line-height: 24px; font-size: 24px">{{ settlementAveragePrice }}</div>
              </div>
              <img :src="getAssetURL('flag14')" alt="" />
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="jt-card">
            <div class="jt-card-content">
              <div class="jt-card-content-item">
                <div style="color: #606266; line-height: 22px; font-size: 14px">
                  总偏差回收电费(元):
                </div>
                <div style="line-height: 24px; font-size: 24px">{{ testiationRecoveryElectricityCost }}</div>
              </div>
              <img :src="getAssetURL('lingxing14')" alt="" />
            </div>
          </el-card>
        </el-col>
        <!-- <el-col :span="6">
          <el-card class="jt-card">
            <div class="jt-card-content">
              <div class="jt-card-content-item">
                <div style="color: #606266; line-height: 22px; font-size: 14px">
                  总超额回收电费(元):
                </div>
                <div style="line-height: 24px; font-size: 24px"></div>
              </div>
              <img :src="getAssetURL('haunbao14')" alt="" />
            </div>
          </el-card>
        </el-col> -->
      </el-row>
    </div>
    <!-- 整张统计图 -->
    <el-card class="jt-card" style="margin-top: 20px">
      <div class="header" style="margin-bottom: 20px">
        <div class="header-title">
          <img :src="getAssetURL('title-arrow')" alt="" />
          营收总览
        </div>
        <div style="display: flex; align-items: center">
          <el-switch v-model="radio2" :active-action-icon="Postcard" :inactive-action-icon="Clock" style="
              --el-switch-on-color: #dcdfe6;
              --el-switch-off-color: #dcdfe6;
              margin-left: 20px;
            " size="large" />
        </div>
      </div>
      <div v-if="radio2 === true">
        <el-table show-summary :data="tableData" class="table" :cell-style="{ borderRight: 'none', color: '#1D2129' }"
          :header-cell-style="{
        borderColor: '#DCDFE6',
        color: '#1D2129',
        backgroundColor: '#F2F3F5',
      }" border style="width: 100%; margin: 20px 0">
          <el-table-column :prop="multipleDate === '单日' ? 'time' : 'operationDate'"
            :label="multipleDate === '单日' ? '时段' : '日期'" width="180" />
          <el-table-column prop="totalElectricityCost" label="营收" />
          <el-table-column prop="contractElectricityCost" label="中长期合约电费" />
          <el-table-column
            :prop="multipleDate === '单日' ? 'dayaheadTestiationElectricityCost' : 'dayaheadtestiationElectricityCost'"
            label="日前偏差电费" />
          <el-table-column
            :prop="multipleDate === '单日' ? 'testiationElectricityCost' : 'realtimetestiationElectricityCost'"
            label="实时偏差电费" />
          <el-table-column prop="testiationRecoveryElectricityCost" label="偏差回收电费" />
          <el-table-column prop="" label="超额回收费用" />
        </el-table>
      </div>
      <div v-if="radio2 === false">
        <Echarts @instanceFn="instanceFn" :echartsData="optionDeal" EWidth="100%" EHeight="550px"
          echartId="settleSumInfo1"></Echarts>
        <Echarts @instanceFn="instanceFnTwo" :echartsData="optionDeal2" EWidth="100%" EHeight="550px"
          echartId="settleSumInfo2"></Echarts>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { echartsConfigBottom } from '@/utils/echartsConfig'
import { Clock, Postcard } from '@element-plus/icons-vue'
import { ref, watch } from 'vue'
// 获取echarts实例
const instanceOne = ref<any>(null)
const instanceFn = (val: any) => {
  instanceOne.value = val
}
const instanceTwo = ref<any>(null)
const instanceFnTwo = (val: any) => {
  instanceTwo.value = val
}
const getAssetURL = (image: any) => {
  return new URL(`../../../assets/svg/${image}.svg`, import.meta.url).href
}
const props = defineProps({
  allData: {
    default: {}
  },
  totalInfo: {
    default: {}
  },
  multipleDate: {
  },
})
// 总营收
const totalElectricityCost = ref<any>()
// 总度电收入
const settlementAveragePrice = ref<any>()
// 总偏差回收费用
const testiationRecoveryElectricityCost = ref<any>()
// 总火电超额回收费用
// const  = ref<any>()
const radio2 = ref(false)
watch(
  () => [props.allData, props.totalInfo, props.multipleDate],
  (newVal: any) => {
    option.value.xAxis.data = []
    option2.value.xAxis.data = []
    option.value.series[0].data = []
    option.value.series[1].data = []
    option2.value.series[0].data = []
    option2.value.series[1].data = []
    option2.value.series[2].data = []
    option2.value.series[3].data = []
    tableData.value = []
    totalElectricityCost.value = newVal[1]?.totalElectricityCost
    settlementAveragePrice.value = newVal[1]?.settlementAveragePrice
    testiationRecoveryElectricityCost.value = newVal[1]?.testiationRecoveryElectricityCost
    if (newVal[2] === '单日') {
      option.value.xAxis.data = newVal[0]?.map((item: any) => item.time)
      option2.value.xAxis.data = newVal[0]?.map((item: any) => item.time)
      option.value.series[0].data = newVal[0]?.map((item: any) => item.settlementAveragePrice)
      option.value.series[1].data = newVal[0]?.map((item: any) => item.totalElectricityCost)
      option2.value.series[0].data = newVal[0]?.map((item: any) => item.contractElectricityCost)
      option2.value.series[1].data = newVal[0]?.map((item: any) => item.dayaheadTestiationElectricityCost)
      option2.value.series[2].data = newVal[0]?.map((item: any) => item.testiationElectricityCost)
      option2.value.series[3].data = newVal[0]?.map((item: any) => item.testiationRecoveryElectricityCost)
      // option2.value.series[4].data = newVal[0]?.map((item: any) => )
    } else {
      option.value.xAxis.data = newVal[0]?.map((item: any) => item.operationDate)
      option2.value.xAxis.data = newVal[0]?.map((item: any) => item.operationDate)
      option.value.series[0].data = newVal[0]?.map((item: any) => item.settlementAveragePrice)
      option.value.series[1].data = newVal[0]?.map((item: any) => item.totalElectricityCost)
      option2.value.series[0].data = newVal[0]?.map((item: any) => item.contractElectricityCost)
      option2.value.series[1].data = newVal[0]?.map((item: any) => item.dayaheadtestiationElectricityCost)
      option2.value.series[2].data = newVal[0]?.map((item: any) => item.realtimetestiationElectricityCost)
      option2.value.series[3].data = newVal[0]?.map((item: any) => item.testiationRecoveryElectricityCost)
      // option2.value.series[4].data = newVal[0]?.map((item: any) => )
    }
    let table = newVal[0]
    tableData.value = table
  },
  {
    deep: true,
  },
)
// test
const tableData = ref<any>([])
const option = ref<any>({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      crossStyle: {
        color: '#999',
      },
    },
    formatter: function (params: any) {
      const dataIndex = option2.value.xAxis.data.findIndex(
        (x: any) => x === params[0].name,
      )
      instanceTwo.value.dispatchAction({
        type: 'showTip',
        seriesIndex: 0,
        dataIndex,
      })
      let context
      let content = params.map((item: any) => {
        if (item.seriesName === '度电收入') {
          return `${item.marker}<span>${item.seriesName
            }</span>:<span style="margin-left: 10px;float: right">${item.value ? item.value : '--'
            }元/MWh</span><br>`
        } else {
          return `${item.marker}<span>${item.seriesName
            }</span>:<span style="margin-left: 10px;float: right">${item.value ? item.value : '--'
            }元</span><br>`
        }
      })
      let newContent = ''
      content.forEach((item: any) => {
        newContent = newContent + item
      })
      context = `<div>${params[0].name}</div><div>${newContent}</div>`
      return context
    },
  },
  xAxis:
  {
    type: 'category',
    data: [],
    axisPointer: {
      type: 'shadow',
    },
  },
  yAxis: [
    {
      type: 'value',
      name: '电费（元）',
    },
    {
      type: 'value',
      name: '电价（元/MWh）',
    },
  ],
  series: [
    {
      name: '度电收入',
      yAxisIndex: 1, //设置y轴归属
      type: 'bar',
      tooltip: {
        valueFormatter: function (value: number) {
          return (value || '--') + ' 元/MWh'
        },
      },
      data: [],
    },
    {
      name: '营收',
      type: 'line',
      symbol: 'none',
      itemStyle: {
        normal: {
          color: '#68CEA2', //改变折线点的颜色
          lineStyle: {
            color: '#68CEA2', //改变折线颜色
          },
        },
      },
      tooltip: {
        valueFormatter: function (value: number) {
          return (value || '--') + ' 元'
        },
      },
      data: []
    },
  ],
})
const optionDeal = echartsConfigBottom(option.value)
const option2 = ref({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      crossStyle: {
        color: '#999',
      },
    },
    formatter: function (params: any) {
      const dataIndex = option.value.xAxis.data.findIndex(
        (x: any) => x === params[0].name,
      )
      instanceOne.value.dispatchAction({
        type: 'showTip',
        seriesIndex: 0,
        dataIndex,
      })
      let context
      let content = params.map((item: any) => {
        console.log(item.marker)
        return `${item.marker}<span>${item.seriesName
          }</span>:<span style="margin-left: 10px;float: right">${item.value ? item.value : '--'
          }元</span><br>`
      })
      let newContent = ''
      content.forEach((item: any) => {
        newContent = newContent + item
      })
      context = `<div>${params[0].name}</div><div>${newContent}</div>`
      return context
    },
  },
  xAxis:
  {
    type: 'category',
    data: [],
    axisPointer: {
      type: 'shadow',
    },
  },
  yAxis: [
    {
      type: 'value',
      name: '电费（元）',
    }
  ],
  series: [
    {
      name: '中长期合约电费',
      stack: 'aa',
      type: 'bar',
      tooltip: {
        valueFormatter: function (value: number) {
          return (value || '--') + ' 元'
        },
      },
      data: [],
    },
    {
      name: '日前偏差电费',
      stack: 'aa',
      type: 'bar',
      tooltip: {
        valueFormatter: function (value: number) {
          return (value || '--') + ' 元'
        },
      },
      data: [],
    },
    {
      name: '实时偏差电费',
      stack: 'aa',
      type: 'bar',
      tooltip: {
        valueFormatter: function (value: number) {
          return (value || '--') + ' 元'
        },
      },
      data: [],
    },
    {
      name: '偏差回收电费',
      stack: 'aa',
      type: 'bar',
      tooltip: {
        valueFormatter: function (value: number) {
          return (value || '--') + ' 元'
        },
      },
      data: [],
    },
    {
      name: '超额回收费用',
      stack: 'aa',
      type: 'bar',
      tooltip: {
        valueFormatter: function (value: number) {
          return (value || '--') + ' 元'
        },
      },
      data: [],
    },
  ],
})
const optionDeal2 = echartsConfigBottom(option2.value)
</script>

<style scoped lang="scss">
.jt-card-content {
  display: flex;
  justify-content: space-between;
  margin: 20px;

  .jt-card-content-item {
    height: 60px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
}
</style>
