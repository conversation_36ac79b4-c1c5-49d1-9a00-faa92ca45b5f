<template>
  <div>
    <el-dialog v-model="dialogVisible" :title="title" width="70%">
     
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="dialogVisible = false">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { ref } from "vue";
export default {
  props: {
    title: {
      type: String,
      default:'添加'
    }
  },
  setup() {
    const dialogVisible = ref<boolean>(false)
    return {
      dialogVisible
    };
  }
};
</script>

<style lang="scss" scoped></style>
