<template>
  <div class="container">
    <div v-if="loadState === 'loaded'" id="excelPreview" ref="excelContainerRef"></div>
    <span v-else-if="loadState === 'loading'">文件加载中...</span>
  </div>
</template>

<script setup lang="ts">
import {ref, nextTick, defineExpose, defineProps} from 'vue'
import zhCN from 'x-data-spreadsheet/src/locale/zh-cn'
import Spreadsheet from 'x-data-spreadsheet'
import * as XLSX from 'xlsx'
import {ElLoading, ElMessage} from "element-plus";
import {downloadMarketDataTemplateAPI} from "@/api/upload";
Spreadsheet.locale('zh-cn', zhCN)
// 备用，不可修改宽高，可直接传入arraybuffer
// import ExcelViewer from 'excel-viewer'

const props = defineProps({
  // 文件id
  fileId: String,
})

let xs: any = null
const excelContainerRef = ref()
const totalRows = ref(3)
const loadState = ref('loading')

// 直接传入文件数据
function initShow(blob) {
  nextTick(() => {
    importExcel(blob)
  })
}

function show(row) {
  // 模板预览
  nextTick(() => {
    const loading = ElLoading.service({ text: '正在下载...' })
    console.log(row)
    const apiFunctions: any = {
      'import/dayahead/t-node-result-data':
          '/import/dayahead/export-t-node-result-data',
      'disclose/import': '/disclose/exportDiscloseInfoTemplate',
      'import/dayahead/t-positive-negative-data':
          '/import/dayahead/export-t-positive-negative-data',
      'import/dayahead/t-block-section-data':
          '/import/dayahead/export-t-block-section-data',
      '/TDayaheadMarketclearResult/importDayaheadMarketclearResult':
          '/TDayaheadMarketclearResult/exportDayaheadMarketclearResult',
      '/TDayaheadFmMarketclearResult/importDayaheadMarketclearResult':
          '/TDayaheadFmMarketclearResult/exportDayaheadMarketclearResult',
      importElectricityInfoData: '/exportRealElectricityInfoData',
      importNodeResultChokeData: '/exportNodeResultChokeData',
      importTransmitElectricityChokeData: '/exportTransmitElectricityChokeData',
      importInterconnectionPlanData: '/exportInterconnectionPlanData',
      '/TRealtimeMaketclearResult/importRealtimeMarketclearResult':
          '/exportRealtimeMarketclearResult',
      '/TRealtimeFmMarketclearResult/importRealtimeFmMarketclearResult':
          '/exportRealtimeFmMarketclearResult',
      importRealityData: '/downloadRealityData',
      importpivotalTangentElectric: '/downloadPivotalTangentElectric',
      importActualElectircControl: '/downloadActualElectircControl',
      importShadowPriceData: '/downloadShadowPrice',
      '/TDayOnlineElectricityQuantity/importDayOnlineElectricityQuantity':
          '/TDayOnlineElectricityQuantity/download',
      '/production-info/import': '/production-info/download',
      '/unitOffer/importSettlementData': '/unitOffer/download',
      '/contracts/importContractEnergyDetail': '/contracts/download',
      'spotReport/import': '/spotReport/download',
      '/tradingInstitution/importMarketUnitContractInfo':
          '/tradingInstitution/exportMarketUnitContractInfo',
      '/import/dayahead/importSpotClearData': '/import/dayahead/exportSpotClearData',
      '/importSpotClearData': '/exportSpotClearData',
      '/disclose/importDailyGenerationReport': '/disclose/exportDailyGenerationReportTemplate'
    }

    // const requestAPI = apiFunctions[row.url]
    const requestAPI = row.downloadUrl

    if (!requestAPI) {
      ElMessage.warning('暂不支持下载模板')
      loading.close()
      return
    }

    downloadMarketDataTemplateAPI(requestAPI)
        .then((data: any) => {
          // const blob = new Blob([data])
          importExcel(data)
          // const url = window.URL.createObjectURL(blob)
          // const link = document.createElement('a')
          // link.href = url
          // link.download = `${row.tableChName}导入模板.xlsx`
          // link.click()
          // window.URL.revokeObjectURL(url)
          loading.close()
        })
        .catch(() => {
          ElMessage.error('下载失败')
          loading.close()
        })
    // xxx.filePreview(props.fileId).then(res => {
    //   importExcel(res.data)
    // })
  })
}
// 导入数据
function importExcel(file) {
  loadState.value = 'loading'
  let reader = new FileReader()
  reader.onload = e => {
    let data = e.target.result
    let fixedData = fixData(data)
    let workbook = XLSX.read(btoa(fixedData), { type: 'base64' })
    let sheetData = stox(workbook)
    loadState.value = 'loaded'
    // 初始化xs
    nextTick(() => {
      init()
      xs.loadData(sheetData)
      console.log(workbook,sheetData)
    })
  }
  reader.readAsArrayBuffer(file)
  // return workbook
}
// 初始化
function init() {
  xs = new Spreadsheet('#excelPreview', {
    mode: 'read',
    showToolbar: false,
    showGrid: true,
    showContextmenu: false,
    showBottomBar: true,
    view: {
      height: () => {
        if (excelContainerRef.value) {
          return excelContainerRef.value.offsetHeight
        } else {
          return
        }
      },
      width: () => {
        if (excelContainerRef.value) {
          return excelContainerRef.value.offsetWidth
        } else {
          return
        }
      },
    },
    style: {
      bgcolor: '#f2f2f2',
      align: 'left',
      valign: 'middle',
      textwrap: false,
      strike: false,
      underline: false,
      color: '#0b0b0b',
      font: {
        name: 'Helvetica',
        size: 11,
        bold: false,
        italic: false,
      },
    },
    row: {
      len: totalRows.value,
      height: 28,
    },
    col: {
      len: 100,
      width: 150,
      indexWidth: 60,
      minWidth: 60,
    },
  })
}

// 处理数据
function fixData(data) {
  let o = '',
    l = 0,
    w = 10240
  for (; l < data.byteLength / w; ++l)
    o += String.fromCharCode.apply(null, new Uint8Array(data.slice(l * w, l * w + w)))
  o += String.fromCharCode.apply(null, new Uint8Array(data.slice(l * w)))
  return o
}

// sheet转换为x-sheet格式
function stox(wb) {
  console.log(wb)
  let out = []
  wb.SheetNames.forEach(name => {
    let o = { name: name, rows: {}, merges: [] }
    let ws = wb.Sheets[name]
    // ws[!ref]= "A1:BF2"
    let aoa
    if(props.fileId == '数据预览') {
      aoa = XLSX.utils.sheet_to_json(ws, { raw: true, header: 1, defval: '', })
    } else if (props.fileId == '模板预览') {
      aoa = XLSX.utils.sheet_to_json(ws, { raw: true, header: 1, defval: '', range: "A1:CV2" })
    } else if (props.fileId == '数据预览合同明细') {
      aoa = XLSX.utils.sheet_to_json(ws, { raw: true, header: 1, defval: '', range: "A1:AY200" })
    } else if (props.fileId == '数据预览24时段上网电量') {
      aoa = XLSX.utils.sheet_to_json(ws, { raw: true, header: 1, defval: '', range: "A1:AY20" })
    } else if (props.fileId == '调度披露日发电情况') {
      aoa = XLSX.utils.sheet_to_json(ws, { raw: true, header: 1, defval: '', range: "A1:E500" })
    }
    console.log('toJson', aoa, ws)
    aoa?.forEach((r, i) => {
      let cells = {}
      r.forEach((c, j) => {
        cells[j] = { text: c }
      })
      o.rows[i] = { cells: cells }
    })
    // 获取表格行数，下方空20行
    totalRows.value = Object.keys(o.rows).length + 20
    if (totalRows.value < 100) {
      totalRows.value = 20
    }
    // 设置合并单元格
    if (ws['!merges']) {
      ws['!merges'].forEach(merge => {
        /** merge = {
         *  s: {c: 0, r: 15}
         *  e: {c: 15, r: 15}
         * }
         */
          // 修改 cell 中 merge [合并行数,合并列数]
        let cell = o.rows[merge.s.r].cells[merge.s.c]

        //无内容单元格处理
        if (!cell) {
          cell = { text: '' }
        }
        cell.merge = [merge.e.r - merge.s.r, merge.e.c - merge.s.c]
        o.rows[merge.s.r].cells[merge.s.c] = cell

        // 修改 merges
        o.merges.push(XLSX.utils.encode_range(merge))
      })
    }

    out.push(o)
  })
  return out
}

// 导出excel
function exportExcel() {
  let new_wb = xtos(xs.getData())
  XLSX.writeFile(new_wb, 'SheetJS.xlsx')
}

// x-sheet转换为sheet格式
function xtos(sdata) {
  let out = XLSX.utils.book_new()
  sdata.forEach(xws => {
    let aoa = [[]]
    let rowObj = xws.rows
    for (let ri = 0; ri < rowObj.len; ++ri) {
      let row = rowObj[ri]
      if (!row) continue
      aoa[ri] = []
      Object.keys(row.cells).forEach(k => {
        let idx = +k
        if (isNaN(idx)) return
        aoa[ri][idx] = row.cells[k].text
      })
    }
    let ws = XLSX.utils.aoa_to_sheet(aoa)

    /** 读取在线中的合并单元格，并写入导出的数据中
     * merges: Array(19)
     0: "A16:P16"
     1: "A17:P17"
     2: "O2:P2"
     3: "F2:G2"
     */
    ws['!merges'] = []
    xws.merges.forEach(merge => {
      ws['!merges'].push(XLSX.utils.decode_range(merge))
    })

    XLSX.utils.book_append_sheet(out, ws, xws.name)
  })
  return out
}

defineExpose({
  initShow,
  show,
})
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  height: 650px;
  display: flex;
  align-items: center;
  justify-content: center;
  #excelPreview {
    width: 100%;
    height: 100%;
  }
  .ant-spin-text {
    margin-top: 10px;
  }
}
:deep(.x-spreadsheet-scrollbar) {
  background-color: #435384 !important;
}
</style>