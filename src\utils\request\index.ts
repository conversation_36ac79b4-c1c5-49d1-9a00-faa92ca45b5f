import { ElMessage } from 'element-plus'
import { merge, isString } from 'lodash'
import type { InternalAxiosRequestConfig } from 'axios'
import type { AxiosTransform, CreateAxiosOptions } from './AxiosTransform'
import { VAxios } from './Axios'
import proxy from '@/config/proxy'
import { joinTimestamp, formatRequestDate, setObjToUrlParams } from './utils'
import { ContentTypeEnum } from '@/constants'
import 'element-plus/es/components/message/style/css'
import { useUserStore } from '@/store/modules/user'
import router from '@/router'
// import { da } from 'element-plus/es/locale/index.js'
const env = import.meta.env.MODE || 'development'
// 如果是mock模式 或 没启用直连代理 就不配置host 会走本地Mock拦截 或 Vite 代理
const host = env === 'mock' || !proxy.isRequestProxy ? '' : proxy[env].host

// 数据处理，方便区分多种处理方式
const transform: AxiosTransform = {
  transformRequestHook: async (res, options) => {
    const { isTransformResponse, isReturnNativeResponse } = options
    const method = res.config.method?.toLowerCase()
    if (res.status === 204 || method === 'put' || method === 'patch') {
      return res
    }
    if (isReturnNativeResponse) {
      return res
    }
    if (!isTransformResponse) {
      return res.data
    }
    const { data } = res
    if (!data) {
      throw new Error('请求接口错误')
    }
    const { code, message } = data
    const hasSuccess =
      (data && (code == 200 || code == 0)) || res.config.responseType === 'blob'
    if (hasSuccess) {
      if (message && message !== '操作成功！') {
        if (message === '用户未登录，请先登录之后再进行操作！') {
          await useUserStore().userLogoutTimeout(() => {
            router.replace('/login')
          })
          ElMessage.info('令牌过期')
          throw new Error(`用户未登陆，请先登陆之后再进行尝试": ${code}`)
        } else {
          ElMessage.info(message)
        }
      }
      return Reflect.has(data, 'data') ? data.data : data
    }
    if (code == 600) {
      ElMessage.warning(message)
      throw new Error(`业务异常，请确认操作是否正确: ${code}`)
    }
    if (code == 502) {
      ElMessage.error('错误的网关，请联系管理员进行处理')
      throw new Error(`错误的网关，请联系管理员进行处理: ${code}`)
    }
    if (code == 500) {
      ElMessage.error('系统异常，请联系管理员进行处理')
      // throw new Error(`系统异常，请连续管理员进行处理": ${code}`)
    }
    if (code == 200001) {
      ElMessage.warning(`${message}`)
    }
    if (code == 408) {
      ElMessage.error(message)
      // throw new Error(`请求超时，请连续管理员进行处理": ${code}`)
    }
    if (code == 403) {
      ElMessage.error('用户未授权，请联系管理员进行授权')
      await useUserStore().userLogoutTimeout(() => {
        router.replace('/login')
      })
      throw new Error(`用户未授权，请联系管理员进行授权": ${code}`)
    }
    if (code == 401) {
      ElMessage.error('用户未登陆，请先登陆之后再进行尝试')
      await useUserStore().userLogoutTimeout(() => {
        router.replace('/login')
      })
      throw new Error(`用户未登陆，请先登陆之后再进行尝试": ${code}`)
    }
    if (code == 209) {
      ElMessage.info(message)
    }
    if (code == 200) {
      if (message && message !== '操作成功！') {
        ElMessage.info(message)
      }
      return data.data
    }
    throw new Error(`请求接口错误, 错误码: ${code}`)
  },

  // 请求前处理配置
  beforeRequestHook: (config, options) => {
    const {
      apiUrl,
      isJoinPrefix,
      urlPrefix,
      joinParamsToUrl,
      formatDate,
      joinTime = true,
    } = options
    // 添加接口前缀
    if (isJoinPrefix && urlPrefix && isString(urlPrefix)) {
      config.url = `${urlPrefix}${config.url}`
    }

    // 将baseUrl拼接
    if (apiUrl && isString(apiUrl)) {
      config.url = `${apiUrl}${config.url}`
    }
    const params = config.params || {}
    const data = config.data || false

    if (formatDate && data && !isString(data)) {
      formatRequestDate(data)
    }
    if (config.method?.toUpperCase() === 'GET') {
      if (!isString(params)) {
        // 给 get 请求加上时间戳参数，避免从缓存中拿数据。
        config.params = Object.assign(
          params || {},
          joinTimestamp(joinTime, false),
        )
      } else {
        // 兼容restful风格
        config.url = `${config.url + params}${joinTimestamp(joinTime, true)}`
        config.params = undefined
      }
    } else if (!isString(params)) {
      if (formatDate) {
        formatRequestDate(params)
      }
      if (
        Reflect.has(config, 'data') &&
        config.data &&
        (Object.keys(config.data).length > 0 || data instanceof FormData)
      ) {
        config.data = data
        config.params = params
      } else {
        // 非GET请求如果没有提供data，则将params视为data
        config.data = params
        config.params = undefined
      }
      if (joinParamsToUrl) {
        config.url = setObjToUrlParams(config.url as string, {
          ...config.params,
          ...config.data,
        })
      }
    } else {
      // 兼容restful风格
      config.url += params
      config.params = undefined
    }
    return config
  },

  // 请求拦截器处理
  requestInterceptors: (config, options) => {
    // 请求之前处理config
    const { token } = useUserStore()
    const { longToken } = useUserStore()
    if (token && (config as Recordable)?.requestOptions?.withToken !== false) {
      ; (config as Recordable).headers.token = token
        ; (config as Recordable).headers.longToken = longToken
        ; (config as Recordable).headers.Authorization =
          options.authenticationScheme
            ? `${options.authenticationScheme} ${token}`
            : token
    }
    return config as InternalAxiosRequestConfig
  },

  // 响应拦截器处理
  responseInterceptors: (res) => {
    if (res.data.code == '600') {
      ElMessage.warning(res.data.message)
    }
    if (res.data.code == '200') {
      if (res.data.message && res.data.message !== '操作成功！' && res.data.message !== '操作成功') {
        ElMessage.info(res.data.message)
      }
    }
    if (res.headers.authorization) {
      // console.log('刷新Token', res.headers, res.config)
      useUserStore().userSetToken(res.headers.authorization)
          let relativeUrl:string | undefined= ''
          // 使用相对路径
          if (env == 'development') {
            res.config.url = res.config?.url?.replace(/^\/api/, '')
            relativeUrl = res.config?.url?.replace(/^\/api/, '');
          } else {
            res.config.url = res.config?.url?.replace(/^\/selling/, '')
            relativeUrl = res.config?.url?.replace(/^\/selling/, '');
          }
          // 重新发送请求
          request.request({ ...res.config, url: relativeUrl });
        }
    return res
  },

  // 响应错误处理
  responseInterceptorsCatch: (error: any) => {
    const { config, response } = error
    if (!response) {
      ElMessage.error('请求错误!请检查控制台')
    }
    if (response.status === 404) {
      ElMessage.error('请求资源不存在!请检查请求路径🥹')
    }
    if (response?.status === 401) {
      // ElMessage.error({message:'您的登录已过期，请重新登录！', grouping: true})
      // token 过期不提醒，直接跳转到登录页面
      useUserStore().userLogoutTimeout(() => {
        router.replace('/login')
      })
    }
    if (response.status === 500) {
      ElMessage.error('服务器错误!请找后端处理😁')
    }
    if (response?.status === 600) {
      if (response.data?.errorCode == 600) {
        let messages = JSON.parse(response.data?.message)?.errorMessage
        // console.log(messages)
        ElMessage.info(messages)
      }
    }
    if (!config || !config.requestOptions.retry) return Promise.reject(error)

    config.retryCount = config.retryCount || 0

    if (config.retryCount >= config.requestOptions.retry.count)
      return Promise.reject(error)

    config.retryCount += 1

    const backoff = new Promise((resolve) => {
      setTimeout(() => {
        resolve(config)
      }, config.requestOptions.retry.delay || 1)
    })
    config.headers = { ...config.headers, 'Content-Type': ContentTypeEnum.Json }
    return backoff.then((config: any) => request.request(config))
  },
}

function createAxios(opt?: Partial<CreateAxiosOptions>) {
  return new VAxios(
    merge(
      <CreateAxiosOptions>{
        // authenticationScheme: 'Bearer',
        timeout: 10 * 60 * 1000,
        withCredentials: true,
        headers: { 'Content-Type': ContentTypeEnum.Json },
        transform,
        requestOptions: {
          apiUrl: host,
          // 是否自动添加接口前缀
          isJoinPrefix: true,
          // 接口前缀
          urlPrefix: env === 'development' ? '/api' : '/selling',
          // urlPrefix: '/selling',
          // 是否返回原生响应头 比如：需要获取响应头时使用该属性
          isReturnNativeResponse: false,
          // 需要对返回数据进行处理
          isTransformResponse: true,
          // post请求的时候添加参数到url
          joinParamsToUrl: false,
          // 格式化提交参数时间
          formatDate: true,
          // 是否加入时间戳
          joinTime: true,
          // 忽略重复请求
          ignoreRepeatRequest: true,
          // 是否携带token
          withToken: true,
        },
      },
      opt || {},
    ),
  )
}
export const request = createAxios()
