<template>
    <div>
        <div style="display: flex; justify-content: space-between;">
          
               
                <div style="line-height: 60px;">{{ name }} 用户明细</div>
                <el-button style="margin: 20px 30px 10px 20px;" @click="goBackAndClearStack">返回</el-button>
           
         

        </div>
     
      
        <pure-table :columns="columns" border stripe :loading="loading" :data="tableData" :pagination="pagination"
        @page-size-change="onSizeChange" @page-current-change="onCurrentChange"
        @selection-change="handleSelectionChange">
       
        <!-- <template #nameHeader>
          <table-head-popover :time-slot="timeSlot" :column="columns[0]" />
        </template>
        <template #monthlyAverageElectricity>
          <table-head-popover :column="columns[1]" />
        </template>
        <template #electricityTypeHeader>
          <table-head-popover 
            :filter-options="electricityTypeOptions" :column="columns[2]" />
        </template>
        <template #industryHeader>
          <table-head-popover  :filter-options="industryOptions"
            :column="columns[3]" />
        </template>
        <template #areaIdHeader>
          <table-head-popover   :filter-options="treeData"
            :column="columns[4]" />
        </template>
      
        <template #customerSourceHeader>
          <table-head-popover :time-slot="timeSlot" @tableUpdate="handleTableUpdate"
            :filter-options="customerSourceOptions" :column="columns[5]" />
        </template> -->
        <template #customerSource="{ row }">
          <div>
            {{ filterDictText(row.customerSource, customerSourceOptions) }}
          </div>
        </template>
    
    </pure-table>
        <!-- <div class="pagenation">
            <el-pagination v-model:currentPage="condition.pageNo" v-model:page-size="condition.pageSize"
                :page-sizes="[5, 10, 15, 20]" :disabled="false" :background="false"
                layout="total, prev, pager, next, sizes" :total="condition.total" @change="handleChange"
                @current-change="handleChange" />
        </div> -->
        <showList ref="refShowList"></showList>
    </div>
</template>

<script setup lang="ts">
import { nextTick, onMounted, reactive, ref } from 'vue'
import showList from './components/showList.vue'
import { useRouter,useRoute } from "vue-router";
import { columns } from "./column";
import { usePowerCustomer } from "@/hooks/customer/usePowerCustomer"
import TableHeadPopover from "@/components/Core/TableHeadPopover/index.vue";
import { customGroupQuerPages } from '@/api'

const refShowList = ref(null);
const { push, go} = useRouter();
const route = useRoute();
const handleList = () => {
    refShowList.value.handleShowList()
}

let condition = reactive({
  pageNo: 1,
  pageSize: 10,
  total: 0,
});

  const {filterDictText,treeData,getList,getUserList,industryOptions,customerSourceOptions,electricityTypeOptions,handleSelectionChange,loading, timeSlot,getCityTreeData,handleTableUpdate} = usePowerCustomer(false);
console.log('tableData')
const name = ref("");
function goBackAndClearStack() {
    if (window.history.length <= 1) {
        push({ path: "/userGrouping" });
        return false;
    } else {
        go(-1);
    }
    // push({ path: "/userPortrait" });
}
const tableData = ref([])
const pagination = reactive<any>({
    pageSize: 10,
    currentPage: 1,
    pageSizes: [10, 20, 40, 50],
    total: 0,
    align: "right",
    background: true,
    small: false
  });
  const cond = ref({
    pageNo: 1,
    pageSize: 10,
    total: 0
  })
const getDetailsList = async () => {
    const id = route.query.id;
    name.value = String(route.query.name) 
    const res = await customGroupQuerPages({groupId:id,pageNo:cond.value.pageNo,pageSize:cond.value.pageSize})
    console.log("aaa",name,res,res.totalCount)

  pagination.total = Number(res.totalCount);
    tableData.value = res.data;
 
}
function onCurrentChange(val) {
  console.log("当前页码：", val);
   cond.value.pageNo = val
   getDetailsList()
}
function onSizeChange(val) {
   cond.value.pageSize = val
   console.log("当前页码：", val);
   getDetailsList()
}
onMounted(() => {
    getDetailsList()
    getCityTreeData();
    getUserList();
    getList();

})

// defineExpose({
//     getDetailsList
// });

</script>

<style scoped lang="scss">
.pagenation {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
</style>