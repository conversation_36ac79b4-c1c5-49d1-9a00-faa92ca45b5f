import { request } from '@/utils/request'

// 日前价格
export const getDayAheadData = (data: any) => {
  const res = request.post<any>({
    url: '/pricescene/getDayAheadData',
    data,
  })
  return res
}
// 日内价格
export const getRealTimeData = (data: any) => {
  const res = request.post<any>({
    url: '/pricescene/getRealTimeData',
    data,
  })
  return res
}
// 日前日内差
export const getDeviationData = (data: any) => {
  const res = request.post<any>({
    url: '/pricescene/getDeviationData',
    data,
  })
  return res
}
// 导入日前预测价格
export const importDayaheadForcastData = (data: any, dateStr: any) => {
  const res = request.post<any>({
    url: `/pricescene/importDayaheadForcastData?dateStr=${dateStr}`,
    data, headers: {
      'Content-Type': 'multipart/form-data',
    }
  },

  )
  return res
}
// 导入日内预测价格
export const importRealtimeForcastData = (data: any, dateStr: any) => {
  const res = request.post<any>({
    url: `/pricescene/importRealtimeForcastData?dateStr=${dateStr}`,
    data, headers: {
      'Content-Type': 'multipart/form-data',
    }
  },

  )
  return res
}
// 市场供需详情
export const marketSupplyDemand = (data: any) => {
  const res = request.post<any>({
    url: '/pricescene/marketSupplyDemand',
    data,
  })
  return res
}


// 实际预测电量导入
export const importLoadPowerForcastData = (data: any, dateStr: any) => {
  const res = request.post<any>({
    url: `/simulationscheme/importLoadPowerForcastData?dateStr=${dateStr}`,
    data, headers: {
      'Content-Type': 'multipart/form-data',
    }
  },

  )
  return res
}
// 预测电量申报策略导入
export const importLoadpowerApplyData = (data: any, dateStr: any) => {
  const res = request.post<any>({
    url: `/simulationscheme/importLoadpowerApplyData?dateStr=${dateStr}`,
    data, headers: {
      'Content-Type': 'multipart/form-data',
    }
  },

  )
  return res
}
// 获取实际电量预测
export const getElectricalVoltageForcast = (data: any) => {
  const res = request.post<any>({
    url: '/simulationscheme/getElectricalVoltageForcast',
    data,
  })
  return res
}
// 获取电量预测申报策略
export const getElectricalVoltageApply = (data: any) => {
  const res = request.post<any>({
    url: '/simulationscheme/getElectricalVoltageApply',
    data,
  })
  return res
}

// 支出统计
export const payoutStatis = (data: any) => {
  const res = request.post<any>({
    url: '/simulationscheme/payoutStatis',
    data,
  })
  return res
}
// 支出明细
export const pyaoutDetail = (data: any) => {
  const res = request.post<any>({
    url: '/simulationscheme/pyaoutDetail',
    data,
  })
  return res
}
// 量价统计
export const quantitypriceStatis = (data: any) => {
  const res = request.post<any>({
    url: '/simulationscheme/quantitypriceStatis',
    data,
  })
  return res
}
// 量价明细
export const quantitypriceDetail = (data: any) => {
  const res = request.post<any>({
    url: '/simulationscheme/quantitypriceDetail',
    data,
  })
  return res
}