# 反向代理解决方案 - 最可靠的方法
# 通过代理完全控制响应头部

server {
    listen 8188;  # 使用新端口避免冲突
    server_name ***********;
    
    # 反向代理到您的原始服务
    location / {
        # 代理到原始服务器
        proxy_pass http://***********:8187;
        
        # 基本代理头部
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 关键：隐藏原始服务器的限制性头部
        proxy_hide_header X-Frame-Options;
        proxy_hide_header Content-Security-Policy;
        proxy_hide_header X-Content-Type-Options;
        proxy_hide_header X-XSS-Protection;
        
        # 重新设置允许iframe嵌入的头部
        add_header X-Frame-Options "ALLOWALL" always;
        
        # 完全开放的CORS配置
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS, HEAD, PATCH" always;
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, Pragma, X-Forwarded-For" always;
        add_header Access-Control-Allow-Credentials "true" always;
        add_header Access-Control-Max-Age "86400" always;
        
        # 处理OPTIONS预检请求
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS, HEAD, PATCH";
            add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, Pragma, X-Forwarded-For";
            add_header Access-Control-Allow-Credentials "true";
            add_header Access-Control-Max-Age "86400";
            add_header Content-Type "text/plain; charset=utf-8";
            add_header Content-Length 0;
            return 204;
        }
        
        # 代理配置
        proxy_buffering off;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 支持WebSocket（如果需要）
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    # 日志配置
    access_log /var/log/nginx/proxy-access.log;
    error_log /var/log/nginx/proxy-error.log;
}

# 可选：在原端口提供重定向
server {
    listen 8187;
    server_name ***********;
    
    # 重定向到代理端口
    return 301 http://$server_name:8188$request_uri;
}
