<template>
    <div>
        <el-card class="jt-card" style="margin-top: 20px">
            <div class="header" style="margin-bottom: 20px">
                <div class="header-title">
                    <img src="@/assets/svg/title-arrow.svg" alt="" />
                    查询条件
                </div>
            </div>
            <!-- <MyTab v-model="active" @change="searchConditionChange" :tabs="['代理销售费', '偏差费用']" style="margin-bottom: 15px">
        </MyTab> -->
            <div class="footers">
                <div class="footers-left">
                    套餐名称:
                    <!-- 空格占位 -->
                    &nbsp; &nbsp;
                    <el-input v-model="condition.packageName" style="width: 200px" maxlength="12" placeholder="套餐名称" />
                    &nbsp; &nbsp;&nbsp; 套餐类型: &nbsp; &nbsp;
                    <el-input v-model="condition.packageType" style="width: 200px" maxlength="12" placeholder="套餐类型" />

                </div>

            </div>
            <div class="footers">
                <div class="footers-left">
                    套餐状态:
                    <!-- 空格占位 -->
                    &nbsp; &nbsp;
                    <el-checkbox-group v-model="condition.checkList">
                        <el-checkbox :label="0" size="large">未上架</el-checkbox>
                        <el-checkbox :label="1" size="large">已上架</el-checkbox>

                    </el-checkbox-group>


                </div>

            </div>
            <div class="footers">
                <div class="app-btn-group">
                    <el-button class="filter-item" type="primary" @click="getList">查询</el-button>
                    <el-button class="filter-item" @click="handleReset">重置</el-button>
                </div>

            </div>
        </el-card>

        <el-card class="jt-card" style="margin-top: 20px; position: relative" v-if="active === '代理销售费'">
            <div class="header" style="margin-bottom: 20px;">
                <el-button type="primary" @click="handlerAdd">
                    <b>新增套餐</b>
                </el-button>
                <el-popover placement="bottom" :width="180" trigger="hover" v>
                    <template #reference>
                        <el-button style="margin-left: 20px" type="primary">
                            <b>批量管理</b>
                        </el-button>
                    </template>
                    <div style="display: flex; flex-direction: column;justify-content: center;align-items: center;">
                        <el-upload style="margin-bottom: 5px;" :http-request="tradingCenterUploadFunction" :show-file-list="false"
                            class="upload" action="" :on-change="tradingCenterUploadFile">
                            <el-button :icon="Upload" type="primary">批量导入</el-button>
                        </el-upload>

                        <el-button :icon="Download" type="primary" @click="exportButton"
                            style="width: 110px;">批量导出</el-button>

                    </div>
                </el-popover>
            </div>

            <!-- 写一个表格 -->
            <el-table :data="tableData1" :cell-style="{ borderRight: 'none', color: '#1D2129' }" :header-cell-style="{
                borderColor: '#DCDFE6',
                color: '#1D2129',
                backgroundColor: '#F2F3F5'
            }" border style="width: 100%; overflow: auto">
                <!-- 序号 -->
                <el-table-column type="index" label="序号" width="80"></el-table-column>
                <el-table-column prop="packageName" label="套餐名称">
                    <template #default="scope">
                        <span @click="handlerDeTails(scope.row)" style=" color: #165dff; cursor: pointer">{{
                            scope.row.packageName }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="packageType" label="套餐类型"></el-table-column>
                <el-table-column prop="packageDescription" label="套餐描述"></el-table-column>
                <el-table-column prop="argumentSet" label="参数设置"></el-table-column>
                <el-table-column prop="status" label="状态">
                    <template #default="scope">
                        <span v-if="scope.row.status === 1"
                            style="display: inline-block; width: 6px;height: 6px;background-color:green;border-radius: 50%;margin-right: 8px;"></span>
                        <span v-if="scope.row.status === 0"
                            style="display: inline-block; width: 6px;height: 6px;background-color:red;border-radius: 50%;margin-right: 8px;"></span>
                        <span style="color: green;" v-if="scope.row.status === 1">已上架</span>
                        <span style="color: red;" v-if="scope.row.status === 0">未上架</span>

                    </template>

                </el-table-column>




                <el-table-column prop="ispackage" label="操作">
                    <template #default="scope">
                        <div style="display: flex">
                            <div style="margin-right: 15px; display: flex; 
                  align-items: center; color: #165dff; cursor: pointer" @click="handlerEdit(scope.row)">
                                <img :src="getAssetURL('edit')" style="margin-right: 2px" />
                                编辑
                            </div>
                            <!-- 
                            <div v-if="scope.row.status === 1" @click="hanlderDown(scope.row)" style="color: orange; cursor: pointer;margin-right: 15px;">
                                下架
                            </div> -->
                            <div @click="hanlderDown(scope.row)"
                                style="color: orange; cursor: pointer;margin-right: 15px;">
                                <span v-if="scope.row.status === 1">下架</span>
                                <span v-if="scope.row.status === 0">上架</span>
                            </div>
                            <div @click="onRemove(scope.row)" style="margin-right: 15px;display: flex;
                  align-items: center; color: #f53f3f; cursor: pointer">
                                <img :src="getAssetURL('delete')" style="margin-right: 2px" />
                                删除
                            </div>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination v-if="tableData1 && tableData1.length" v-model:current-page="electricRateCurrentPage1"
                v-model:page-size="electricRatePageSize1" :page-sizes="[10, 20, 50, 100]"
                layout="->,total, prev, pager, next,sizes" :total="electricRateTotal1"
                @size-change="electricRateChange1" @current-change="electricRateCurrentChange1" />

        </el-card>

    </div>
</template>

<script setup lang="ts">
import { echartsConfigBottom, echartsConfig } from "@/utils/echartsConfig";
import { ref, onMounted, watch, onActivated, onDeactivated } from "vue";
import { Upload, Search, Download } from "@element-plus/icons-vue";
import dayjs from "dayjs"; // 引入dayjs
import { ElLoading, ElMessage, ElMessageBox } from "element-plus";
import {
    importPriceDeclarationForm, //导入价格申报单
    importDeviationCostForm, //导入偏差费用表
    importUserElectricityForm, //导入用户电量表
    importBatch, //代理销售费
    exportBatch, //点击查询
    deviationQueryPage,
    agencySaveAgencySaleFee,
    deviationSaveDeviationCost, queryPageSalePackage, deleteBatch, onOrDownShelf

} from "@/api";
import { useRoute, useRouter } from "vue-router";
import { any } from "vue-types";
// #region 第一个table
const timeDate = ref(dayjs().format("YYYY-MM"));
const active = ref("代理销售费");
const fileList = ref<any>([]);

const { push, go } = useRouter();

const route = useRoute();

function tradingCenterUploadFunction() { }








const getAssetURL = (image: any) => {
    return new URL(`../../../assets/svg/${image}.svg`, import.meta.url).href
}



const handlerAdd = (row: any) => {


    push(`/ratail/setMeal/add`);
}
const handlerEdit = (row: any) => {
    // localStorage.setItem("row",JSON.stringify(row) );
    push(`/ratail/setMeal/edit?id=${row.id}`);
}



const ids = ref<any>([]);

const onRemove = async (row: any) => {
    ElMessageBox.confirm(
        '是否确定要删除?',
        
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    )
        .then(() => {
            ids.value.push(row.id);
            deleteBatch(ids.value).then(() => {
                ElMessage({
                    type: 'success',
                    message: '删除成功',
                })
                agencyQueryPageFn()
            })

        })
        .catch(() => {
            ElMessage({
                type: 'info',
                message: '取消删除',
            })
        })



}

const hanlderDown = async (row: any) => {
    // const status;
    // if(row.status === 1){

    // }
    console.log(row.status)
    onOrDownShelf({ id: row.id, status: row.status === 1 ? 0 : 1 }).then(res => {
        ElMessage.success('操作成功')
        agencyQueryPageFn()
    })
}






const firstForm = ref({
    one: "",
    two: "",
    three: "",
    four: "",
    five: ""
});
const tableData1 = ref([]);
onMounted(() => {
    agencyQueryPageFn();
});
onActivated(() => {
    condition.value.packageName= ""
    condition.value.packageType = ""
    condition.value.checkList = [0, 1]
    agencyQueryPageFn();
})
onDeactivated(() => {
    condition.value.checkList = [0, 1]

 })
// 当前页
const electricRateCurrentPage1 = ref<any>(1);
// 当前显示条数
const electricRatePageSize1 = ref<any>(10);
// 总页数
const electricRateTotal1 = ref<any>(20);

const condition = ref<any>({
    packageName: "",
    packageType: "",
    checkList: [0, 1]
});
function agencyQueryPageFn() {
    if (!electricRateCurrentPage1.value) electricRateCurrentPage1.value = 1;
    if (!electricRatePageSize1.value) electricRatePageSize1.value = 10;
    queryPageSalePackage({
        pageNo: electricRateCurrentPage1.value,
        pageSize: electricRatePageSize1.value,
        packageName: condition.value.packageName,
        packageType: condition.value.packageType,
        status: condition.value.checkList
    }).then(res => {
        tableData1.value = res.data;

        electricRateCurrentPage1.value = res.pageNo;
        electricRatePageSize1.value = res.pageSize;
        electricRateTotal1.value = +res.totalCount;
    });
}




const handleReset = () => {
    condition.value.packageName= ""
    condition.value.packageType = ""
    condition.value.checkList = [0, 1]
    electricRateCurrentPage1.value = 1
    electricRatePageSize1.value = 10;
    agencyQueryPageFn()


}


const getList = () => {
    agencyQueryPageFn()

}

const option1 = ref({
    xAxis: {
        type: "category",
        axisLabel: {
            interval: 0,
            formatter: function (value) {
                //x轴的文字改为竖版显示
                var str = value.split("");
                return str.join("\n");
            }
        },
        data: []
    },
    yAxis: [
        {
            type: "value",
            name: "套餐代理销售费",
            min: 0,
            axisLabel: {
                formatter: "{value} 元"
            }
        },
        {
            type: "value",
            name: "当前套餐代理销售费",
            min: 0,
            axisLabel: {
                formatter: "{value} 元"
            }
        }
    ],
    series: [
        {
            name: "套餐代理销售费",
            data: [],
            type: "bar"
            // 在后面加上单位元
        },
        {
            name: "当前套餐代理销售费",
            type: "line",
            yAxisIndex: 1,
            data: []
        }
    ]
});
echartsConfig(option1.value);



// 上传函数
const handleUpload1 = () => {
    if (fileList.value.length === 0) {
        ElMessage.warning("未选择文件");
    } else {
        const formData = new FormData();
        formData.append("file", fileList.value[0].raw);
        importPriceDeclarationForm(formData)
            .then(() => {
                ElMessage.success("上传成功");
                // 更新数据
            })
            .catch((e: any) => {
                console.log(e);
            });
    }
};
const tradingCenterUploadFile = (file: any) => {
    const fileExtension = file.name.split(".").pop();
    const size = file.size / 1024 / 1024;
    if (fileExtension !== "xlsx" && fileExtension !== "xls") {
        ElMessage.warning("只能上传excel文件");
    }
    if (size > 10) {
        ElMessage.warning("文件大小不得超过10M");
    } else {
        fileList.value = [file];
        handleUpload2();
    }
};
function handleUpload2() {
    if (fileList.value.length === 0) {
        ElMessage.warning("未选择文件");
    } else {
        const formData = new FormData();
        formData.append("file", fileList.value[0].raw);
        importBatch(formData)
            .then((res) => {
                console.log(res);
                ElMessage.success("上传成功");
                agencyQueryPageFn()
                // 更新数据
            })
            .catch();
    }
}
function electricRateChange1() {
    agencyQueryPageFn();
}
function electricRateCurrentChange1() {
    agencyQueryPageFn();
}
function agencySaveAgencySaleFeeFn() {
    ElMessageBox.alert("是否要保存并更新数据？(请谨慎操作)", "提示", {
        confirmButtonText: "确认",
        callback: (action: any) => {
            if (action === "confirm") {
                agencySaveAgencySaleFee({
                    month: timeDate.value,
                    pageNo: electricRateCurrentPage1.value,
                    pageSize: electricRatePageSize1.value,
                    retailUserNameLike: inputValue.value,
                    regularTradingCeilingPrice: firstForm.value.one,
                    regularTradingAveragePrice: firstForm.value.two,
                    wholesaleMarketCeilingPrice: firstForm.value.three,
                    stateGridAgencyPrice: firstForm.value.four,
                    gatdfRegularTradingPrice: firstForm.value.five
                }).then(() => {
                    agencyQueryPageFn();
                    ElMessage.success("保存并更新数据成功");
                });
            }
        }
    });
}
// #endregion

// #region 第二个table
const secondForm = ref({
    one: "",
    two: "",
    three: "",
    four: "",
    five: ""
});
function deviationSaveDeviationCostFn() {
    ElMessageBox.alert("是否要保存并更新数据？(请谨慎操作)", "提示", {
        confirmButtonText: "确认",
        callback: (action: any) => {
            if (action === "confirm") {
                deviationSaveDeviationCost({
                    month: timeDate.value,
                    pageNo: electricRateCurrentPage2.value,
                    pageSize: electricRatePageSize2.value,
                    retailUserNameLike: inputValue.value,
                    totalElectricityConsumption: secondForm.value.one,
                    differentialElectricityFee: secondForm.value.two
                }).then(() => {
                    deviationQueryPageFn();
                    ElMessage.success("保存并更新数据成功");
                });
            }
        }
    });
}
function tradingCenterUploadFile3(file: any) {
    const fileExtension = file.name.split(".").pop();
    const size = file.size / 1024 / 1024;
    if (fileExtension !== "xlsx" && fileExtension !== "xls") {
        ElMessage.warning("只能上传excel文件");
    } else if (size > 10) {
        ElMessage.warning("文件大小不得超过10M");
    } else {
        fileList.value = [file];
        handleUpload3();
    }
}
function handleUpload3() {
    if (fileList.value.length === 0) {
        ElMessage.warning("未选择文件");
    } else {
        const formData = new FormData();
        formData.append("file", fileList.value[0].raw);
        importDeviationCostForm(formData)
            .then(() => {
                ElMessage.success("上传成功");
                // 更新数据
            })
            .catch();
    }
}
const secondTable = ref([]);
// 当前页
const electricRateCurrentPage2 = ref<any>(1);
// 当前显示条数
const electricRatePageSize2 = ref<any>(10);
// 总页数
const electricRateTotal2 = ref<any>(20);
function deviationQueryPageFn() {
    deviationQueryPage({
        month: timeDate.value,
        pageNo: electricRateCurrentPage2.value,
        pageSize: electricRatePageSize2.value,
        retailUserNameLike: inputValue.value
    }).then(res => {
        console.log("res", res);
        secondTable.value = res.data;
        electricRateCurrentPage2.value = res.pageNo;
        electricRatePageSize2.value = res.pageSize;
        electricRateTotal2.value = +res.totalCount;
    });
}
function searchConditionChange(val) {
    console.log("搜索条件", active.value, val);
    if (val === "偏差费用") {
        deviationQueryPageFn();
    } else {
        agencyQueryPageFn();
    }
}

function electricRateChange2(val) {
    electricRatePageSize2.value = val;
    deviationQueryPageFn();
}
function electricRateCurrentChange2(val) {
    electricRateCurrentPage2.value = val;
    deviationQueryPageFn();
}

// #endregion
const inputValue = ref("");
function searchWatch() {
    if (active.value === "代理销售费") {
        agencyQueryPageFn();
    } else if (active.value === "偏差费用") {
        deviationQueryPageFn();
    }
}

const exportButton = () => {
    if (!tableData1.value.length) {
        return ElMessage.info('无可导出数据！')
    }
    console.log("tableData1", condition.value.checkList);
    const loading = ElLoading.service({ text: '正在下载...' })
    exportBatch({
        packageName: condition.value.packageName,
        packageType: condition.value.packageType,
        pageNo: electricRatePageSize1.value,
        pageSize: electricRatePageSize1.value,
        pageNo: electricRateCurrentPage1.value,
        status: condition.value.checkList,


    })
        .then((data) => {
            const blob = new Blob([data])
            const url = window.URL.createObjectURL(blob)
            const link = document.createElement('a')
            link.href = url
            link.download = '售电套餐.xlsx'
            link.click()
            window.URL.revokeObjectURL(url)
            loading.close()
        })
        .catch(() => {
            ElMessage.error('下载失败')
            loading.close()
        })
}
// 跳转到详情页面
const handlerDeTails = (row) => {

    console.log("row", row);
    push(`/ratail/setMeal/details?id=${row.id}`);
};
</script>

<style scoped lang="scss">
.footers {
    display: flex;
    justify-content: space-between;

    .footers-left {
        display: flex;
        align-items: center;
        height: 40px;
        line-height: 40px;
    }

    .footers-right {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        height: 40px;
        line-height: 40px;

        el-button {
            margin-left: 10px;
        }
    }
}
</style>