<template>
  <section>
    <div class="app-content-container container-h">
      <el-tabs @tab-click="tabClick">
        <template v-for="(item, index) of list" :key="item.key">
          <el-tab-pane :label="item.name" :lazy="true">
            <component
              ref="detailRef"
              :selected-value="selected"
              v-if="selected == index"
              :is="item.component"
            />
          </el-tab-pane>
        </template>
      </el-tabs>
    </div>
  </section>
</template>

<script lang="ts">
import { ref } from "vue";
import Board from "./components/Board.vue";
export default {
  name: "OpportunityManagement",
  components: {
    Board
  },
  setup() {
    const selected = ref<number>(0);
    const detailRef = ref(null);
    const list = [
      {
        name: "全部",
        component: Board
      },
      // {
      //   name: "赢单看板",
      //   component: Board
      // },
      // {
      //   name: "丢单看板",
      //   component: Board
      // }
    ];
    function tabClick(row) {
      selected.value = Number(row.index);
    }
    return {
      tabClick,
      selected,
      list,
      detailRef
    };
  }
};
</script>

<style lang="scss" scoped></style>
