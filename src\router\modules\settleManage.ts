export default {
  path: '/settleManage',
  meta: {
    icon: "material-symbols:supervisor-account-rounded",
    title: '批发结算管理',
    rank: 6
  },
  redirect: '/settleManage/dayMonClean',
  children: [
    {
      path: '/settleManage/dayMonClean',
      component: () => import('@/views/settleManage/dayMonClean/index.vue'),
      name: 'dayMonClean',
      meta: {
        title: '日清月结',
        icon: '',
      },
    },
    {
      path: '/settleManage/settleSum',
      component: () => import('@/views/settleManage/settleSum/index.vue'),
      name: 'settleSum',
      meta: {
        title: '结算概览',
        icon: '',
      },
    },
    // {
    //   path: '/settleManage/secCheck',
    //   component: () => import('@/views/settleManage/secCheck/index.vue'),
    //   name: 'secCheck',
    //   meta: {
    //     title: '复盘分析',
    //     icon: '',
    //   },
    // },
    // {
    //   path:'/settleManage/monthSum',
    //   component: () => import('@/views/settleManage/monthSum/index.vue'),
    //   name: 'monthSum',
    //   meta:{
    //     title:'月度结算',
    //     icon:'',
    //   }
    // },
    // {
    //   path:'/settleManage/setMeal',
    //   component: () => import('@/views/settleManage/setMeal/index.vue'),
    //   name: 'setMeal',
    //   meta:{
    //     title:'套餐定制',
    //     icon:'',
    //   }
    // },
    // {
    //   path:'/settleManage/setMeal/edit',
    //   component: () => import('@/views/settleManage/setMeal/component/edit.vue'),
    //   name: 'edit',
    //   meta:{
    //     title:'套餐编辑',
    //     icon:'',
    //     showLink: false
    //   }
    // },
    // {
    //   path:'/settleManage/setMeal/add',
    //   component: () => import('@/views/settleManage/setMeal/component/add.vue'),
    //   name: 'add',
    //   meta:{
    //     title:'添加套餐',
    //     icon:'',
    //     showLink: false
    //   }
    // },
    // {
    //   path:'/settleManage/setMeal/details',
    //   component: () => import('@/views/settleManage/setMeal/component/details.vue'),
    //   name: 'details',
    //   meta:{
    //     title:'查看详情',
    //     icon:'',
    //     showLink: false
    //   }
    // },
  ],
};
