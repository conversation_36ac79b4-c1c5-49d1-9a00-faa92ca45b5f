<template>
  <el-upload
    class="upload-demo"
    action="string"
    :http-request="uploadFile"
    :limit="1"
    :show-file-list="false"
    :on-success="handleUploadSuccess"
    :on-error="handleUploadError"
    accept=".xlsx,.xls,.csv"
    :isShow="showUp"
    v-if="showUp"
  >
    <el-button size="small" type="primary" :icon="Upload">上传文件</el-button>
  </el-upload>
  <el-button type="primary" :icon="Loading" v-else size="small">
    上传中
  </el-button>
</template>

<script setup lang="ts">
import { reqUploadForm } from '@/api/upload/index'
import { ElNotification } from 'element-plus'
import { h, ref } from 'vue'
// import { useLayOutSettingStore } from '@/store'
import { Loading, Upload } from '@element-plus/icons-vue'
const emits = defineEmits(['upSuccessCallback'])
// const viewStore = useLayOutSettingStore()
const $props = defineProps(['address', 'date', 'dateUrl', 'schemeName'])
const showUp = ref(true)
const uploadFile = async (params: any) => {
  // 可以在上传之前就行文件类型和大小处理
  showUp.value = false
  let formData = new FormData()
  let myFile = params.file
  let imgSize = Number(myFile.size / 1024 / 1024)
  const fileSuffix = myFile.name.substring(myFile.name.lastIndexOf('.') + 1)
  const whiteList = ['csv', 'xls', 'xlsx']
  if (whiteList.indexOf(fileSuffix) === -1) {
    ElNotification({
      title: '导入失败',
      message: h('i', { style: 'color: red' }, '不支持该文件类型'),
    })
    return Promise.reject('不支持该文件类型')
  }
  if (imgSize > 30) {
    ElNotification({
      title: '导入失败',
      message: h('i', { style: 'color: red' }, '超过限制大小30m'),
    })
    return Promise.reject('超过限制大小30m')
  }
  formData.append('file', params.file)
  formData.append('type', 'head')
  if ($props.date) {
    formData.append('dataDate', $props.date)
  }
  if (
    $props.address == null ||
    $props.address == undefined ||
    $props.address == ''
  ) {
    ElNotification({
      title: '导入失败',
      message: h('i', { style: 'color: red' }, '请传入上传地址'),
    })
    return Promise.reject('请传入上传地址')
  }
  // viewStore.upLoading = true
  try {
    const res = await reqUploadForm(
      formData,
      $props.address,
      $props.dateUrl,
      null,
      $props.schemeName,
      $props.date,
    )
    // if (res == true) {
    //   viewStore.upLoading = false
    //   ElNotification({
    //     title: '导入成功',
    //     message: h('i', { style: 'color: teal' }, '已导入excel表格'),
    //   })
    //   emits('upSuccessCallback')
    // } else {
    //   viewStore.upLoading = false
    //   ElNotification({
    //     title: '导入失败',
    //     message: h('i', { style: 'color: red' }, '导入失败'),
    //   })
    //   return Promise.resolve('上传失败!')
    // }
    // viewStore.upLoading = false
    emits('upSuccessCallback')
  } catch (err) {
    // viewStore.upLoading = false
    return Promise.reject(`内容不符!${err}`)
  }
}
const handleUploadSuccess = () => {
  showUp.value = true
  //文件上传成功后的操作
  return
}
const handleUploadError = () => {
  //文件上传失败后的操作
  showUp.value = true
  return
}
</script>

<style scoped lang="scss"></style>
