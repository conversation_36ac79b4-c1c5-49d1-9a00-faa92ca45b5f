import { ref, getCurrentInstance } from "vue";
import type { UploadUserFile } from "element-plus";
import { getFileListApi, delFileApi,deleteImage } from "@/api/user";
import { ElMessage } from "element-plus";
import { getToken } from "@/utils/auth";
import { useUserStore } from '@/store/modules/user'
const userStore = useUserStore()

export function useFileAction() {
  // 上传请求头
  const header = ref({
    Authorization: getToken().accessToken,
    token: userStore.token,
    longToken: userStore.longToken
  });
  const { BaseUrl } =
    getCurrentInstance().appContext.config.globalProperties.$config;
  const requestUrl= process.env.NODE_ENV === "development"
      ? `/api`
      : `/selling`;
  // console.log(requestUrl)
  const actionUrl = `${requestUrl}/system/annex/upload`;
  const actionUr2 = `${requestUrl}/system/annex/uploadImage`;
  // console.log("actionUrl",actionUrl)
  const fileList = ref<UploadUserFile[]>([]);
  // 最大文件大小（10MB）
  const maxSize = 10 * 1024 * 1024;
  // 查询附件
  async function getFileList(type, id) {
    const file = await getFileListApi(type, id);
    const fileArr: any[] = [];
    if (file.data.length) {
      fileList.value = [];
      file.data.forEach(item => {
        fileArr.push({
          status: "done",
          name: item.fileName,
          uid: item.id,
          url: `${requestUrl}/system/annex/download?id=` + item.id
        });
      });
      fileList.value = fileArr;
    } else {
      fileList.value = [];
    }
  }
  // 下载附件
  async function handleDownload(data) {
    const url = data.url || requestUrl + "/" + data.response.data.downloadUrl
    if (url) {
      let fileName = data.name;
      fetch(url, {
              method: 'GET',
              headers: new Headers({
                  'Authorization': getToken().accessToken
              })
          })
         .then(res => res.blob())
         .then(data => {
              const blobUrl = window.URL.createObjectURL(data);
              const a = document.createElement('a');
              a.download = fileName;
              a.href = blobUrl;
              a.click();
      });
    }
  }
  // 删除附件
  async function handleRemove(data) {
    await delFileApi(data.uid);
  }
  // 删除附件2
  async function handleRemove2(data) {
    await deleteImage(data.uid);
  }
  // 上传文件大小限制
  function beforeUpload(file) {
    return new Promise((resolve, reject) => {
      if (file.size <= maxSize) {
        resolve(file);
      } else {
        ElMessage({
          message: "文件大小超过限制",
          type: "error"
        });
        reject();
      }
    });
  }
  return {
    getFileList,
    handleRemove,
    handleRemove2,
    handleDownload,
    actionUrl,
    actionUr2,
    beforeUpload,
    header,
    BaseUrl,
    fileList
  };
}
