<template>
  <section>
    <el-card class="jt-card">
      <div class="header">
        <MyTab v-model="contractAdministrationActive" @change="contractAdministrationChange"
          :tabs="['合约详情', '合约总览']">
        </MyTab>
<!--        <MyTab v-model="contractAdministrationActive" @change="contractAdministrationChange"-->
<!--          :tabs="['合约详情', '合约总览', '电量分解库']">-->
<!--        </MyTab>-->
      </div>
    </el-card>

    <div v-if="contractAdministrationActive === '合约详情'" style="margin-top: 20px">
      <el-card class="jt-card">
        <div class="header">
          <el-input style="width: 16%" v-model="detail_contractlName" placeholder="合约名称查询">
            <template #append>
              <el-button @click="detail_contractlNameChange" type="primary" :icon="Search" />
            </template>
          </el-input>
          <el-select clearable @change="detail_tradingCycleChange" style="margin-right: 10px; width: 13%"
            class="singleSelect" v-model="detail_tradePeriodId" placeholder="交易周期">
            <el-option v-for="item in tradingCycleOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-select clearable @change="detail_contractTypeChange" style="margin-right: 10px; width: 13%"
            class="singleSelect" v-model="detail_contractType" placeholder="合约类型">
            <el-option v-for="item in contractTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-select clearable @change="detail_meansExchangeChange" style="margin-right: 10px; width: 13%"
            class="singleSelect" v-model="detail_tradeModeId" placeholder="交易方式">
            <el-option v-for="item in meansExchangeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <div style="display: flex; align-items: center">
            交易日期：
            <el-date-picker :clearable="true" @change="detail_contractDateChange" style="width: 228px"
              range-separator="至" format="YYYY-MM-DD" value-format="YYYY-MM-DD" v-model="detail_contractDate"
              type="daterange" start-placeholder="开始日期" end-placeholder="结束日期" />
          </div>
          <el-button @click="detail_reset" type="success" style="margin-right: 10px">
            重置
          </el-button>
          <el-upload class="daoru" style="margin-left: 10px" action="" :http-request="detailUploadFunction"
            :on-change="detailguideInto">
            <el-button :icon="Upload" type="primary">导入</el-button>
          </el-upload>
        </div>
      </el-card>
      <el-card class="jt-card" style="margin-top: 20px">
        <el-table stripe :header-cell-style="{
          borderColor: '#DCDFE6',
          color: '#1D2129',
          backgroundColor: '#F2F3F5',
        }" :data="detail_contractTableData" border style="width: 100%; margin: 20px 0" height="440">
          <el-table-column type="index" label="序号" width="60" />
          <el-table-column prop="contractName" label="合约名称" show-overflow-tooltip />
          <el-table-column prop="signedUnitName" label="交易机组" />
          <el-table-column prop="contractTypeName" label="合约类型" />
          <el-table-column prop="contractSequenceName" label="交易序列名称" />
          <el-table-column prop="transactionObject" label="交易对象" />
          <el-table-column prop="tradePeriodName" label="交易周期" />
          <el-table-column prop="tradeModeName" label="交易方式" />
          <el-table-column prop="subjectDate" label="标的日期" />
          <el-table-column prop="contractEnergy" label="交易电量" />
          <el-table-column prop="contractAvgPrice" label="交易均价" />
          <el-table-column label="操作" width="180">
            <template #default="scope">
              <div style="display: flex; justify-content: space-evenly">
                <div @click="detail_detailClick(scope.row)" style="
                  cursor: pointer;
                  color: #165dff;
                  font-size: 15px;
                  display: flex;
                  align-items: center;
                ">
                  <img style="margin-right: 5px; width: 15px" src="@/assets/svg/Edit_light.svg" alt="" />
                  详情
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination v-model:current-page="detail_contractCurrentPage" v-model:page-size="detail_contractPageSize"
          :page-sizes="[10, 20, 50, 100]" layout="->,total, prev, pager, next,sizes" :total="detail_contractTotal"
          @size-change="detail_contractHandleSizeChange" @current-change="detail_contractHandleCurrentChange" />
      </el-card>
      <!-- 合约详情弹窗 -->
      <el-dialog class="dialog" v-model="detail_Visible" title="合约电量与电价" style="width: 50%">
        <div>
          <Echarts :echartsData="contract_detailOptionDeal" EWidth="100%" EHeight="250px"
            echartId="contract_detail_Option">
          </Echarts>
        </div>

        <template #footer>
          <span class="dialog-footer">
            <el-button type="primary" @click="detail_VisibleConfirm">
              确认
            </el-button>
          </span>
        </template>
      </el-dialog>
    </div>

    <div v-if="contractAdministrationActive === '合约总览'" style="margin-top: 20px">
      <el-card class="jt-card">
        <div class="header">
          <el-input style="width: 16%" v-model="contractlName" placeholder="合约名称查询">
            <template #append>
              <el-button @click="contractlNameChange" type="primary" :icon="Search" />
            </template>
          </el-input>
          <el-select clearable @change="tradingCycleChange" style="margin-right: 10px; width: 13%" class="singleSelect"
            v-model="tradePeriodName" placeholder="交易周期">
            <el-option v-for="item in tradingCycleOptions" :key="item.value" :label="item.label" :value="item.label" />
          </el-select>
          <!-- <el-select @change="meansExchangeChange" style="margin-right: 10px; width: 11%" class="singleSelect"
          v-model="tradeModeName" placeholder="交易方式">
          <el-option v-for="item in meansExchangeOptions" :key="item.value" :label="item.label" :value="item.label" />
        </el-select> -->
          <el-select clearable @change="tradeCategoryChange" style="margin-right: 10px; width: 13%" class="singleSelect"
            v-model="tradeCommodityName" placeholder="交易品种">
            <el-option v-for="item in tradeCategoryOptions" :key="item.value" :label="item.label" :value="item.label" />
          </el-select>
          <el-select clearable @change="decompositionStateChange" style="margin-right: 10px; width: 13%"
            class="singleSelect" v-model="decompositionStatusName" placeholder="分解状态">
            <el-option v-for="item in decompositionStateOptions" :key="item.value" :label="item.label"
              :value="item.label" />
          </el-select>
          <div style="display: flex; align-items: center;">
            合约开始时间：
            <el-date-picker @change="contractDateChange" style="width: 228px" range-separator="至" format="YYYY-MM-DD"
              value-format="YYYY-MM-DD" v-model="contractDate" type="daterange" start-placeholder="开始日期"
              end-placeholder="结束日期" />
          </div>
        </div>
        <div class="header" style="margin-top: 20px">
          <div>
            <el-button @click="addContracts" type="primary" :icon="DocumentRemove">
              新增合约
            </el-button>
            <el-button class="no-border" @click="DownloadContracts" type="success" :icon="Bottom">
              下载模板
            </el-button>
          </div>
          <div style="display: flex">
            <el-button class="no-border" @click="reset" type="success" style="margin-right: 10px">
              重置
            </el-button>
            <el-upload class="daoru" style="margin-right: 10px" action="" :http-request="UploadFunction"
              :on-change="guideInto">
              <el-button :icon="Upload" type="primary">导入</el-button>
            </el-upload>
            <el-button @click="Download" type="primary" :icon="Edit">
              导出
            </el-button>
          </div>
        </div>

        <el-table class="table-test" :header-cell-style="{
          borderColor: '#DCDFE6',
          color: '#1D2129',
          backgroundColor: '#F2F3F5',
        }" :data="contractTableData" border style="width: 100%; margin: 20px 0" height="440">
          <el-table-column type="index" label="序号" width="60" />
          <el-table-column prop="contractNo" label="合约编号" />
          <el-table-column prop="contractName" label="合约名称" />
          <el-table-column prop="tradeCommodityName" label="交易品种" />
          <el-table-column prop="tradePeriodName" label="交易周期" />
          <el-table-column prop="contractEnergy" label="合约电量(MWh)" width="135" />
          <el-table-column prop="contractAvgPrice" label="合约电价(元/MWh)" width="155" />
          <el-table-column prop="partyTwo" label="交易对象" />
          <el-table-column prop="decompositionStatusName" label="分解状态" />
          <el-table-column prop="startTime" label="开始时间" />
          <el-table-column prop="endTime" label="结束时间" />
          <el-table-column label="操作" width="180">
            <template #default="scope">
              <div style="display: flex; justify-content: space-between">
                <div @click="editClick(scope.row)" style="
                  cursor: pointer;
                  color: #254f7a;
                  display: flex;
                  align-items: center;
                ">
                  <img style="margin-right: 5px; width: 15px" :src="getAssetURL('Edit_light')" alt="" />
                  编辑
                </div>
                <div @click="triangleClick(scope.row)" style="
                  cursor: pointer;
                  color: #ff7d00;
                  display: flex;
                  align-items: center;
                ">
                  <img style="margin-right: 5px; width: 15px" :src="getAssetURL('triangle')" alt="" />
                  分解
                </div>
                <div @click="closeClick(scope.row)" style="
                  cursor: pointer;
                  color: #f53f3f;
                  display: flex;
                  align-items: center;
                ">
                  <img style="margin-right: 5px; width: 15px" :src="getAssetURL('close')" alt="" />
                  删除
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination v-model:current-page="contractCurrentPage" v-model:page-size="contractPageSize"
          :page-sizes="[10, 20, 50, 100]" layout="->,total, prev, pager, next,sizes" :total="contractTotal"
          @size-change="contractHandleSizeChange" @current-change="contractHandleCurrentChange" />
      </el-card>

      <!-- 编辑合约弹窗 -->
      <el-dialog class="dialog" v-model="editVisible" title="基本信息" style="width: 80%">
        <div>
          <el-form :rules="rules" ref="resetFormData" label-suffix=":" :model="contractForm" label-width="120px">
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="grid-content ep-bg-purple">
                  <el-form-item label="合约编号" prop="contractNo">
                    <el-input v-model="contractForm.contractNo"></el-input>
                  </el-form-item>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="grid-content ep-bg-purple">
                  <el-form-item label="合约名称" prop="contractName">
                    <el-input v-model="contractForm.contractName"></el-input>
                  </el-form-item>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="grid-content ep-bg-purple">
                  <el-form-item label="合约时间" prop="startAndEndTime">
                    <el-date-picker @change="yearValueChange" @calendar-change="yearPanelChange"
                      :disabled-date="yearLimit" v-if="contractForm.tradePeriodName == '年' ||
                        contractForm.tradePeriodName == null
                      " format="YYYY-MM-DD" value-format="YYYY-MM-DD" v-model="contractForm.startAndEndTime" :clearable="false"
                      range-separator="至" type="daterange" start-placeholder="开始日期" end-placeholder="结束日期" />
                    <el-date-picker @change="monthValueChange" @calendar-change="monthPanelChange"
                      :disabled-date="monthLimit" v-if="contractForm.tradePeriodName == '月'" format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD" v-model="contractForm.startAndEndTime" :clearable="false"
                      range-separator="至" type="daterange" start-placeholder="开始日期" end-placeholder="结束日期" />
                    <el-date-picker @change="dateValueChange" @calendar-change="datePanelChange"
                      :disabled-date="dateLimit" v-if="contractForm.tradePeriodName == '日'" format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD" v-model="contractForm.startAndEndTime" :clearable="false"
                      range-separator="至" type="daterange" start-placeholder="开始日期" end-placeholder="结束日期" />
                  </el-form-item>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <!-- <el-col :span="8">
              <div class="grid-content ep-bg-purple">
                <el-form-item label="结束时间" prop="endTime">
                  <el-date-picker
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    v-model="contractForm.endTime"
                    :clearable="false"
                  />
                </el-form-item>
              </div>
            </el-col> -->
              <el-col :span="8">
                <div class="grid-content ep-bg-purple">
                  <el-form-item label="己方">
                    <el-input v-model="contractForm.partyOne"></el-input>
                  </el-form-item>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="grid-content ep-bg-purple">
                  <el-form-item label="交易对象">
                    <el-input v-model="contractForm.partyTwo"></el-input>
                  </el-form-item>
                </div>
              </el-col><el-col :span="8">
                <div class="grid-content ep-bg-purple">
                  <el-form-item label="交易方向">
                    <el-select class="singleSelect" v-model="contractForm.tradeDirectionName">
                      <el-option v-for="item in tradingDirectionOptions" :key="item.value" :label="item.label"
                        :value="item.label" />
                    </el-select>
                  </el-form-item>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">

              <el-col :span="8">
                <div class="grid-content ep-bg-purple">
                  <el-form-item label="交易品种">
                    <el-select class="singleSelect" v-model="contractForm.tradeCommodityName">
                      <el-option v-for="item in tradeCategoryOptions" :key="item.value" :label="item.label"
                        :value="item.label" />
                    </el-select>
                  </el-form-item>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="grid-content ep-bg-purple">
                  <el-form-item label="交易方式">
                    <el-select class="singleSelect" v-model="contractForm.tradeModeName">
                      <el-option v-for="item in meansExchangeOptions" :key="item.value" :label="item.label"
                        :value="item.label" />
                    </el-select>
                  </el-form-item>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="grid-content ep-bg-purple">
                  <el-form-item label="交易周期" prop="tradePeriodName">
                    <el-select class="singleSelect" v-model="contractForm.tradePeriodName"
                      @change="tradePeriodNameChange">
                      <el-option v-for="item in tradingCycleOptions" :key="item.value" :label="item.label"
                        :value="item.label" />
                    </el-select>
                  </el-form-item>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="grid-content ep-bg-purple">
                  <el-form-item label="电量类型" prop="energyTypeName">
                    <!-- <el-input v-model="contractForm.energyType"></el-input> -->
                    <el-select class="singleSelect" v-model="contractForm.energyTypeName">
                      <el-option v-for="item in energyTypeOptions" :key="item.value" :label="item.label"
                        :value="item.label" />
                    </el-select>
                  </el-form-item>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="grid-content ep-bg-purple">
                  <el-form-item label="结算类型">
                    <el-select class="singleSelect" v-model="contractForm.settlementTypeName">
                      <el-option v-for="item in settlementTypeFormOptions" :key="item.value" :label="item.label"
                        :value="item.label" />
                    </el-select>
                  </el-form-item>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <!-- <el-col :span="8">
              <div class="grid-content ep-bg-purple">
                <el-form-item label="分解状态">
                  <el-select class="singleSelect" v-model="contractForm.decompositionStatusName">
                    <el-option v-for="item in decompositionStateOptions" :key="item.value" :label="item.label"
                      :value="item.label" />
                  </el-select>
                </el-form-item>
              </div>
            </el-col> -->
              <el-col :span="8">
                <div class="grid-content ep-bg-purple">
                  <el-form-item label="成交时间">
                    <el-date-picker format="YYYY-MM-DD" value-format="YYYY-MM-DD" v-model="contractForm.dealTime"
                      :clearable="false" />
                  </el-form-item>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="grid-content ep-bg-purple">
                  <el-form-item label="备注">
                    <el-input v-model="contractForm.note"></el-input>
                  </el-form-item>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="grid-content ep-bg-purple">
                  <el-form-item label="合约电量(MWh)">
                    <el-input @input="(v: any) => (
                      (contractForm.contractEnergy = v
                        .replace(/[^\d]/g, '')
                        .replace(/^[eE]/, '')
                        .replace(/[eE][+\-]?[0-9]*/, '')),
                      contractForm.contractEnergy.length > 10
                        ? (contractForm.contractEnergy =
                          contractForm.contractEnergy.slice(0, 10))
                        : (contractForm.contractEnergy =
                          contractForm.contractEnergy)
                    )
                      " v-model="contractForm.contractEnergy"></el-input>
                  </el-form-item>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="grid-content ep-bg-purple">
                  <el-form-item label="合约电价(元/MWh)" label-width="150">
                    <el-input @input="(v: any) => (
                      (contractForm.contractAvgPrice = v
                        .replace(/[^\d]/g, '')
                        .replace(/^[eE]/, '')
                        .replace(/[eE][+\-]?[0-9]*/, '')),
                      contractForm.contractAvgPrice.length > 10
                        ? (contractForm.contractAvgPrice =
                          contractForm.contractAvgPrice.slice(0, 10))
                        : (contractForm.contractAvgPrice =
                          contractForm.contractAvgPrice)
                    )
                      " v-model="contractForm.contractAvgPrice"></el-input>
                  </el-form-item>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="grid-content ep-bg-purple">
                  <el-form-item label="合约附件">
                    <el-upload :on-remove="removeFile" :on-preview="openFile" v-model:file-list="fileList"
                      class="upload-demo" action="" :on-change="uploadAttachment" :http-request="UploadFunction"
                      multiple>
                      <el-button :icon="Upload" type="primary">
                        点击上传
                      </el-button>
                    </el-upload>
                  </el-form-item>
                </div>
              </el-col>
              <el-col :span="16">
                <div class="grid-content ep-bg-purple">
                  <el-form-item label="其他">
                    <el-input :rows="8" v-model="contractForm.otherDetails" type="textarea" />
                  </el-form-item>
                </div>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button class="no-border" type="info" @click="cancleVisibleConfirm">取消</el-button>
            <el-button type="primary" @click="editVisibleConfirm(resetFormData)">
              确认
            </el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 分解合约弹窗 -->
      <el-dialog class="triangleDialog" v-model="triangleVisible" title="电量分解" style="width: 50%">
        <div>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="grid-content ep-bg-purple">
                <div class="wrap">
                  <div class="top">
                    <div class="left">合约名称</div>
                    <div class="right">{{ rowData.contractName }}</div>
                  </div>
                  <div class="bottom">
                    <div class="left">开始时间</div>
                    <div class="right">{{ rowData.startTime }}</div>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="grid-content ep-bg-purple">
                <div class="wrap">
                  <!-- <div class="top">
                    <div class="left">签约机组</div>
                    <div class="right">{{ rowData.signedUnitName }}</div>
                  </div> -->
                  <div class="bottom justone">
                    <div class="left">结束时间</div>
                    <div class="right">{{ rowData.endTime }}</div>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="grid-content ep-bg-purple">
                <div class="wrap">
                  <div class="top">
                    <div class="left">合约电量</div>
                    <div class="right">{{ rowData.contractEnergy }}</div>
                  </div>
                  <div class="bottom">
                    <div class="left">合约电价</div>
                    <div class="right">{{ rowData.contractAvgPrice }}</div>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>

          <div class="triangleTitle">
            <span>分解曲线</span>
            <div style="display: flex; align-items: center">
              <div style="display: flex; margin-right: 10px">
                <MyTab v-model="timeTypeActive" @change="timeTypeChange" :tabs="timeTypeTab"></MyTab>
              </div>
              <el-date-picker :disabled-date="fenjieMonthLimit" @change="fenjieMonthChange" type="month"
                v-if="timeTypeActive === '月分日'" style="width: 150px; margin-right: 10px" format="YYYY-MM"
                value-format="YYYY-MM" v-model="monthPickerData" placeholder="请选择月份" />
              <el-date-picker :disabled-date="fenjieDateLimit" @change="fenjieDateChange" type="date"
                v-if="timeTypeActive === '日分时'" style="width: 150px; margin-right: 10px" format="YYYY-MM-DD"
                value-format="YYYY-MM-DD" v-model="datePickerData" placeholder="请选择日期" />
              <div style="display: flex">
                <div style="width: 70px">分解方案：</div>
                <el-select @change="triangleSchemeChange" class="singleSelect" v-model="triangleScheme">
                  <el-option v-for="item in triangleSchemeOptions" :key="item.value" :label="item.label"
                    :value="item.value" />
                </el-select>
              </div>
            </div>
          </div>

          <Echarts :echartsData="triangleOptionDeal" EWidth="100%" EHeight="250px" echartId="triangleOption"></Echarts>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <!-- <el-button type="success" @click="triangleVisible = false">
            导出
          </el-button> -->
            <el-button type="primary" @click="triangleVisibleConfirm">
              确认
            </el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 删除合约弹窗 -->
      <el-dialog v-model="closeVisible" title="提示" style="width: 30%">
        <div>
          <div style="
            text-align: center;
            line-height: 200px;
            height: 200px;
            font-size: 16px;
          ">
            是否确认删除？
          </div>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button class="no-border" type="info" @click="closeVisible = false">取消</el-button>
            <el-button type="primary" @click="closeVisibleConfirm">
              确认
            </el-button>
          </span>
        </template>
      </el-dialog>
    </div>

    <div v-if="contractAdministrationActive === '电量分解库'" style="margin-top: 20px">
      <el-card class="jt-card">
        <div style="display: flex; align-items: center">
          <MyTab v-model="coulometricDecompositionBankActive" @change="coulometricDecompositionBankChange"
            :tabs="['年分月', '月分日', '日分时']"></MyTab>
        </div>

        <div class="box-wrap">
          <div class="box" @click="addScheme" style="
            cursor: pointer;
            background-color: #f2f3f5;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
          ">
            <img width="20" :src="getAssetURL('plus')" alt="" />
            <span style="margin-top: 10px;font-size: 16px;">添加分解曲线方案</span>
          </div>
          <div class="box">
            <div class="box-header">
              <span>均分方案</span>
              <div class="box-header-icon">
                <img @click="junfenEditScheme" width="16" :src="getAssetURL('Edit_light')" alt="" />
              </div>
            </div>
            <Echarts :echartsData="equipartitionSchemeOptionDeal" EWidth="100%" EHeight="180px"
              echartId="equipartitionScheme"></Echarts>
          </div>

          <div class="box" v-for="(item, index) in getProgrammeContentInfoList" :key="index">
            <div class="box-header">
              <span>{{ item.name }}</span>
              <div class="box-header-icon">
                <img @click="EditScheme(item)" width="16" :src="getAssetURL('Edit_light')" alt="" />
                <img @click="deleteScheme(item)" width="14" :src="getAssetURL('trash-2')" alt="" />
              </div>
            </div>
            <Echarts :echartsData="item.equipartitionSchemeOptionDeal" EWidth="100%" EHeight="180px"
              :echartId="item.id">
            </Echarts>
          </div>
        </div>
      </el-card>

      <!-- 均分方案弹窗 -->
      <el-dialog class="schemeDialog" v-model="schemeVisible" :title="schemeTitle" style="width: 50%">
        <div>
          <div class="scheme-title">
            <div style="width: 10%">方案名称：</div>
            <el-input style="width: 30%" v-model="schemeName" :disabled="averageFlag" />
          </div>
          <div style="margin-top: 20px">
            <span style="width: 10%">分解权重：</span>
            <div class="weight-table" v-if="coulometricDecompositionBankActive === '年分月'">
              <div class="weight-table-head">
                <div v-for="(item, index) in 8" :key="index">
                  {{ item % 2 === 0 ? '权重' : '月份' }}
                </div>
              </div>
              <div class="weight-table-body">
                <div class="monthArray" style="
                  display: flex;
                  flex-wrap: wrap;
                  height: 168px;
                  line-height: 56px;
                  border: 1px solid #dcdfe6;
                  border-top: none;
                ">
                  <template v-for="item in monthArray" :key="item">
                    <div style="width: 12.5%; text-align: center">
                      {{ item.name }}
                    </div>
                    <div style="width: 12.5%; text-align: center">
                      <el-input :disabled="averageFlag" @input="(v: any) => (
                        (item.programmeWeight = v
                          .replace(/[^\d]/g, '')
                          .replace(/^[eE]/, '')
                          .replace(/[eE][+\-]?[0-9]*/, '')),
                        item.programmeWeight.length > 10
                          ? (item.programmeWeight =
                            item.programmeWeight.slice(0, 10))
                          : (item.programmeWeight = item.programmeWeight)
                      )
                        " style="width: 80%" v-model="item.programmeWeight"></el-input>
                    </div>
                  </template>
                </div>

                <Echarts :echartsData="monthArrayOption" EWidth="100%" EHeight="180px" echartId="monthArray"></Echarts>
              </div>
            </div>
            <div class="weight-table" v-if="coulometricDecompositionBankActive === '月分日'">
              <div class="weight-table-head">
                <div v-for="(item, index) in 8" :key="index">
                  {{ item % 2 === 0 ? '权重' : '日期' }}
                </div>
              </div>
              <div class="weight-table-body" style="
                height: 168px;
                overflow-y: auto;
                border: 1px solid #dcdfe6;
                border-top: none;
              ">
                <div class="dateArray" style="display: flex; flex-wrap: wrap; line-height: 56px">
                  <template v-for="item in dateArray" :key="item">
                    <div style="width: 12.5%; text-align: center">
                      {{ item.name }}
                    </div>
                    <div style="width: 12.5%; text-align: center">
                      <el-input :disabled="averageFlag" @input="(v: any) => (
                        (item.programmeWeight = v
                          .replace(/[^\d]/g, '')
                          .replace(/^[eE]/, '')
                          .replace(/[eE][+\-]?[0-9]*/, '')),
                        item.programmeWeight.length > 10
                          ? (item.programmeWeight =
                            item.programmeWeight.slice(0, 10))
                          : (item.programmeWeight = item.programmeWeight)
                      )
                        " style="width: 80%" v-model="item.programmeWeight"></el-input>
                    </div>
                  </template>
                </div>
              </div>
              <Echarts :echartsData="dateArrayOption" EWidth="100%" EHeight="180px" echartId="dateArray"></Echarts>
            </div>
            <div class="weight-table" v-if="coulometricDecompositionBankActive === '日分时'">
              <div class="weight-table-head">
                <div v-for="(item, index) in 8" :key="index">
                  {{ item % 2 === 0 ? '权重' : '时间' }}
                </div>
              </div>
              <div class="weight-table-body" style="
                height: 168px;
                overflow-y: auto;
                border: 1px solid #dcdfe6;
                border-top: none;
              ">
                <div class="timeArray" style="display: flex; flex-wrap: wrap; line-height: 56px">
                  <template v-for="item in itemArray" :key="item">
                    <div style="width: 12.5%; text-align: center">
                      {{ item.name }}
                    </div>
                    <div style="width: 12.5%; text-align: center">
                      <el-input :disabled="averageFlag" @input="(v: any) => (
                        (item.programmeWeight = v
                          .replace(/[^\d]/g, '')
                          .replace(/^[eE]/, '')
                          .replace(/[eE][+\-]?[0-9]*/, '')),
                        item.programmeWeight.length > 10
                          ? (item.programmeWeight =
                            item.programmeWeight.slice(0, 10))
                          : (item.programmeWeight = item.programmeWeight)
                      )
                        " style="width: 80%" v-model="item.programmeWeight"></el-input>
                    </div>
                  </template>
                </div>
              </div>

              <Echarts :echartsData="timeArrayOption" EWidth="100%" EHeight="180px" echartId="timeArray"></Echarts>
            </div>
          </div>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button class="no-border" type="info" @click="schemeVisibleCancel">取消</el-button>
            <el-button type="primary" @click="schemeVisibleConfirm">
              确认
            </el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, watch } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { ElLoading, ElMessage } from 'element-plus'
import { Upload, DocumentRemove, Bottom, Edit } from '@element-plus/icons-vue'
import { echartsConfig } from '@/utils/echartsConfig'
import { filterSelect } from '@/utils/fillterData'
import dayjs from 'dayjs' // 引入dayjs
import { useUserStore } from '@/store/modules/user'

import {
  contractsQueryPage, //分页查询合约信息
  contractsAdd, //新增合约信息
  contractsUpdate, //修改合约信息
  contractsDelete, //删除合约信息
  contractseEnergyDecompose, //电量分解
  contractseDownload, //下载合约模板
  contractseImport, //导入合约
  attachmentUpload, //上传附件
  attachmentGetList, //根据id查询附件列表
  contractseDownloadExcel, //导出excel
  getProgrammeList, //查询方案列表
  getProgrammeContentInfo, //查询方案信息
  programmeAdd, //新增方案信息
  programmeUpdate, //修改方案信息
  programmeDelete, //删除方案信息
  attachmentDownload, //下载附件
  getDecomposeInfo, //获取分解信息
  addDecompose, //新增分解信息
  updateDecompose, //修改分解信息
  contractDetails, //分页查询合约明细信息
  queryDecomposeByContractId, //根据合约id查询分解信息
  importContractEnergyDetail, //导入合约明细信息
} from '@/api'


//#region 合约详情

// 导入
let detailfileList = ref<any>()
function detailUploadFunction() { }
function detailguideInto(file: any) {
  const fileExtension = file.name.split('.').pop()
  const size = file.size / 1024 / 1024
  if (fileExtension !== 'xlsx' && fileExtension !== 'xls') {
    ElMessage.warning('只能上传excel文件')
    detailfileList.value = []
    return
  }
  if (size > 10) {
    ElMessage.warning('文件大小不得超过10M')
    detailfileList.value = []
    return
  }
  detailfileList.value = [file]
  detailhandleUpload()
}

// 上传函数
const detailhandleUpload = () => {
  if (detailfileList.value.length === 0) {
    ElMessage.warning('未选择文件')
  } else {
    const formData = new FormData()
    formData.append('file', detailfileList.value[0].raw)
    importContractEnergyDetail(formData)
      .then(() => {
        // ElMessage.success('上传成功')
        contractDetailsInfo()
      })
      .catch((e) => {
        console.log(e)
      })
  }
}

// 分页查询合约明细信息
async function contractDetailsInfo() {
  const res = await contractDetails({
    contractName: detail_contractlName.value || null,
    tradePeriodId: detail_tradePeriodId.value || null,
    contractTypeId: detail_contractType.value || null,
    tradeModeId: detail_tradeModeId.value || null,
    startTime: detail_contractDate.value && detail_contractDate.value[0],
    endTime: detail_contractDate.value && detail_contractDate.value[1],
    pageNo: detail_contractCurrentPage.value,
    pageSize: detail_contractPageSize.value,
  })
  detail_contractTableData.value = res.data
  detail_contractTotal.value = Number(res.totalCount)
}

// 分页
const detail_contractCurrentPage = ref<any>(1)
const detail_contractPageSize = ref<any>(10)
const detail_contractTotal = ref<any>(1)

function detail_contractHandleSizeChange() {
  contractDetailsInfo()
}
function detail_contractHandleCurrentChange() {
  contractDetailsInfo()
}

const contract_detailOption = ref<any>({
  legend: {
    show: false,
  },
  confine: true,
  tooltip: {
    trigger: 'axis',
    backgroundColor: 'rgba(255,255,255,0.90)',
    borderRadius: '4px',
    boxShadow: ' 0px 2px 10px 0px rgba(0, 0, 0, 0.16)',
    formatter: function (params: any) {
      let context
      let content = params.map((item: any) => {
        if (item.seriesName === '电量') {
          return `${item.marker}<span>${item.seriesName
            }</span>:<span style="margin-left: 10px;float: right">${item.value ? item.value : '--'
            }MWh</span><br>`
        }
        if (item.seriesName === '电价') {
          return `${item.marker}<span>${item.seriesName
            }</span>:<span style="margin-left: 10px;float: right">${item.value ? item.value : '--'
            }元/MWh</span><br>`
        }
      })
      let newContent = ''
      content.forEach((item: any) => {
        newContent = newContent + item
      })
      context = `<div>${params[0].name}</div><div>${newContent}</div>`
      return context
    },
  },
  grid: {
    top: '18%',
    left: '8%',
    right: '8%',
    bottom: '8%',
  },
  xAxis: {
    type: 'category',
    data: [1, 1, 1, 1, 1],
    axisTick: {
      show: false, // 将此选项设置为 false 来隐藏 X 轴刻度
    },
  },
  yAxis: [
    {
      position: 'left',
      type: 'value',
      name: '电量(MWh)',
      nameTextStyle: {
        // y轴name的样式调整
        fontSize: 14,
      },
      splitLine: {
        show: false, //想要不显示网格线，改为false
      },
    },
    {
      position: 'right',
      type: 'value',
      name: '电价(元/MWh)',
      nameTextStyle: {
        // y轴name的样式调整
        fontSize: 14,
      },
      splitLine: {
        show: false, //想要不显示网格线，改为false
      },
    },
  ],
  series: [
    {
      name: '电量',
      data: [1, 1, 1, 1, 1],
      type: 'bar',
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        color: '#254F7A',
      },
    },
    {
      name: '电价',
      data: [1, 1, 1, 1, 1],
      type: 'line',
      yAxisIndex: 1,
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        color: '#029CD4',
      },
      lineStyle: {
        color: '#029CD4',
      },
    },
  ],
})


const contract_detailOptionDeal = echartsConfig(contract_detailOption.value)

// 详情弹窗
async function detail_detailClick(data: any) {
  detail_Visible.value = true
  contract_detailOption.value.series[0].data = []
  contract_detailOption.value.series[1].data = []
  const res = await queryDecomposeByContractId(data.id)
  contract_detailOption.value.xAxis.data = res.map((item: any) => item.time)
  contract_detailOption.value.series[0].data = res.map(
    (item: any) => item.energy,
  )
  contract_detailOption.value.series[1].data = res.map(
    (item: any) => item.price,
  )
}
const detail_Visible = ref<any>(false)
function detail_VisibleConfirm() {
  detail_Visible.value = false
}
// 合约名称
const detail_contractlName = ref<any>()
function detail_contractlNameChange() {
  contractDetailsInfo()
}

// 交易周期
const detail_tradePeriodId = ref<any>()
// 交易周期下拉框
function detail_tradingCycleChange() {
  contractDetailsInfo()
}

// 交易方式
const detail_tradeModeId = ref<any>()
// 交易方式下拉框
function detail_meansExchangeChange() {
  contractDetailsInfo()
}

// 交易日期
const detail_contractDate = ref<any>([
  dayjs().add(2, 'day').format('YYYY-MM-DD'),
  dayjs().add(2, 'day').format('YYYY-MM-DD'),
])
// 交易日期改变
function detail_contractDateChange() {
  contractDetailsInfo()
}

// 合约类型
const detail_contractType = ref<any>()
// 合约类型下拉框
const contractTypeOptions = ref<any>([])
function detail_contractTypeChange() {
  contractDetailsInfo()
}

// 合约详情表格
const detail_contractTableData = ref<any>([])

// 重置按钮
function detail_reset() {
  detail_contractlName.value = null
  detail_tradePeriodId.value = null
  detail_tradeModeId.value = null
  detail_contractType.value = null
  detail_contractDate.value = [
    dayjs().add(2, 'day').format('YYYY-MM-DD'),
    dayjs().add(2, 'day').format('YYYY-MM-DD'),
  ]
  contractDetailsInfo()
}

//#endregion

//#region 合约总览
const getAssetURL = (image: any) => {
  return new URL(`../../../assets/svg/${image}.svg`, import.meta.url).href
}
// 年周期时间限制
let yearFirst = ref<any>('')
function yearPanelChange(date) {
  yearFirst.value = date[0]
}
function yearLimit(time: any) {
  return (
    time > dayjs(yearFirst.value).endOf('year') ||
    time < dayjs(yearFirst.value).startOf('year')
  )
}
function yearValueChange() {
  yearFirst.value = ''
}
// 月周期时间限制
let monthFirst = ref<any>('')
function monthPanelChange(date) {
  monthFirst.value = date[0]
}
function monthLimit(time: any) {
  return (
    time > dayjs(monthFirst.value).endOf('month') ||
    time < dayjs(monthFirst.value).startOf('month')
  )
}
function monthValueChange() {
  monthFirst.value = ''
}
// 日周期时间限制
let dateFirst = ref<any>('')
function datePanelChange(date) {
  dateFirst.value = date[0]
}
function dateLimit(time: any) {
  return time > dayjs(dateFirst.value) || time < dayjs(dateFirst.value)
}
function dateValueChange() {
  dateFirst.value = ''
}
// 分解月周期时间限制
function fenjieMonthLimit(time: any) {
  return (
    time > dayjs(rowData.value.endTime) || time < dayjs(rowData.value.startTime)
  )
}
// 分解ri周期时间限制
function fenjieDateLimit(time: any) {
  return (
    time > dayjs(rowData.value.endTime) || time < dayjs(rowData.value.startTime)
  )
}
// 分解弹窗月份改变
async function fenjieMonthChange() {
  await getDecomposeInfoData()
  getcontractseEnergyDecompose()
}

// 表单校验
const resetFormData = ref<any>(null)
const rules = ref({
  contractName: [
    { required: true, message: '请输入合约名称', trigger: 'blur' },
  ],
  energyTypeName: [
    { required: true, message: '请选择电量类型', trigger: 'blur' },
  ],
  tradePeriodName: [
    { required: true, message: '请选择交易周期', trigger: 'blur' },
  ],
  contractNo: [{ required: true, message: '请输入合约编号', trigger: 'blur' }],
  startAndEndTime: [
    { required: true, message: '请选择开始时间', trigger: 'blur' },
  ],
})

let addOrEdit = ref<any>(null)

// 新增合约
function addContracts() {
  fileList.value = []
  addOrEdit.value = 'add'
  editVisible.value = true
  const keys = Object.keys(contractForm.value)
  keys.forEach((item: any) => (contractForm.value[item] = null))
  nextTick(() => {
    resetFormData.value.clearValidate()
  })
}
// 下载模板
function DownloadContracts() {
  const loading = ElLoading.service({ text: '正在下载...' })
  contractseDownload()
    .then((data) => {
      const blob = new Blob([data])
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = '合约模板.xlsx'
      link.click()
      window.URL.revokeObjectURL(url)
      loading.close()
    })
    .catch(() => {
      ElMessage.error('下载失败')
      loading.close()
    })
}
// 重置
function reset() {
  contractlName.value = null
  tradePeriodName.value = null
  tradeCommodityName.value = null
  decompositionStatusName.value = null
  contractDate.value = [
    dayjs().startOf('year').format('YYYY-MM-DD'),
    dayjs().format('YYYY-MM-DD'),
  ]
  getContractsQueryPage()
}
// 导入
function UploadFunction() { }
function guideInto(file: any) {
  const fileExtension = file.name.split('.').pop()
  const size = file.size / 1024 / 1024
  if (fileExtension !== 'xlsx' && fileExtension !== 'xls') {
    ElMessage.warning('只能上传excel文件')
    fileList.value = []
    return
  }
  if (size > 10) {
    ElMessage.warning('文件大小不得超过10M')
    fileList.value = []
    return
  }
  fileList.value = [file]
  handleUpload()
}

// 上传函数
const handleUpload = () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('未选择文件')
  } else {
    const formData = new FormData()
    formData.append('file', fileList.value[0].raw)
    contractseImport(formData)
      .then(() => {
        // ElMessage.success('上传成功')
        getContractsQueryPage()
      })
      .catch((e) => {
        console.log(e)
      })
  }
}

// 导出数据
function Download() {
  const loading = ElLoading.service({ text: '正在下载...' })
  contractseDownloadExcel({
    contractStartTime: contractDate.value[0],
    contractEndTime: contractDate.value[1],
    contractName: contractlName.value,
    decompositionStatusId: decompositionStatusName.value
      ? decompositionStateOptions.value.filter(
        (item: any) => item.label == decompositionStatusName.value,
      )[0].value
      : null,
    pageNo: contractCurrentPage.value,
    pageSize: contractPageSize.value,
    tradeCommodityId: tradeCommodityName.value
      ? tradeCategoryOptions.value.filter(
        (item: any) => item.label == tradeCommodityName.value,
      )[0].value
      : null,
    // tradeModeId: tradeModeName.value
    //   ? meansExchangeOptions.value.filter(
    //     (item: any) => item.label == tradeModeName.value,
    //   )[0].value
    //   : null,
    tradePeriodId: tradePeriodName.value
      ? tradingCycleOptions.value.filter(
        (item: any) => item.label == tradePeriodName.value,
      )[0].value
      : null,
  })
    .then((data) => {
      const blob = new Blob([data])
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = '合约.xlsx'
      link.click()
      window.URL.revokeObjectURL(url)
      loading.close()
    })
    .catch(() => {
      ElMessage.error('下载失败')
      loading.close()
    })
}

// 最顶部tab切换
const contractAdministrationActive = ref('合约详情')
function contractAdministrationChange(val: any) {
  nextTick(() => {
    if (val === '电量分解库') {
      getProgrammeListData()
    }
    if (val === '合约总览') {
      getContractsQueryPage()
    }
    if (val === '合约详情') {
      contractDetailsInfo()
    }
  })

}
// 合约名称
const contractlName = ref()
function contractlNameChange() {
  getContractsQueryPage()
}


// 交易周期
const tradePeriodName = ref(null)
function tradingCycleChange() {
  getContractsQueryPage()
}
const tradingCycleOptions = ref<any>([])

// 电量类型
const energyTypeOptions = ref<any>([])

// 交易方式
// const tradeModeName = ref(null)
// function meansExchangeChange() {
//   getContractsQueryPage()
// }
const meansExchangeOptions = ref<any>([])
// 交易品种
const tradeCommodityName = ref(null)
function tradeCategoryChange() {
  getContractsQueryPage()
}
const tradeCategoryOptions = ref<any>([])
// 分解状态
const decompositionStatusName = ref(null)
function decompositionStateChange() {
  getContractsQueryPage()
}
const decompositionStateOptions = ref<any>([])
// 合约开始时间
const contractDate = ref([
  dayjs().startOf('year').format('YYYY-MM-DD'),
  dayjs().format('YYYY-MM-DD'),
])
// 合约总览表格
const contractTableData = ref<any>([
  {
    dateTime: 1,
  },
])
function contractDateChange() {
  getContractsQueryPage()
}
// 合约总览表格分页
// 当前页
const contractCurrentPage = ref(1)
// 当前显示条数
const contractPageSize = ref(10)
// 总页数
const contractTotal = ref()
// 当前显示条数改变
const contractHandleSizeChange = () => {
  getContractsQueryPage()
}
// 当前页改变
const contractHandleCurrentChange = () => {
  getContractsQueryPage()
}

let rowData = ref()
// 编辑弹窗
async function editClick(data: any) {
  console.log(`output->rowData.value`, data)
  fileList.value = []
  rowData.value = data
  addOrEdit.value = 'edit'
  editVisible.value = true
  contractForm.value = {
    ...data,
    startAndEndTime: [data.startTime, data.endTime],
  }
  if (!data.contractAttachmentId) return
  const res = await attachmentGetList(data.contractAttachmentId)
  fileList.value = res
  attachmentIdList.value = res?.map((item: any) => item.id)
  console.log(`output->fileList.value`, fileList.value)
}

const editVisible = ref<any>()
async function editVisibleConfirm(formEl: any) {
  if (!formEl) return
  await formEl.validate(async (valid: any, fields: any) => {
    if (valid) {
      if (addOrEdit.value == 'add') {
        let payload = {
          status: 0,
          contractName: contractForm.value.contractName,
          startTime: contractForm.value.startAndEndTime[0],
          endTime: contractForm.value.startAndEndTime[1],
          partyOne: contractForm.value.partyOne,
          partyTwo: contractForm.value.partyTwo,
          energyTypeName: contractForm.value.energyTypeName,
          dealTime: contractForm.value.dealTime,
          note: contractForm.value.note,
          contractEnergy: contractForm.value.contractEnergy,
          contractAvgPrice: contractForm.value.contractAvgPrice,
          otherDetails: contractForm.value.otherDetails,
          contractNo: contractForm.value.contractNo,

          tradeDirectionName: contractForm.value.tradeDirectionName,
          tradeCommodityName: contractForm.value.tradeCommodityName,
          tradeModeName: contractForm.value.tradeModeName,
          tradePeriodName: contractForm.value.tradePeriodName,
          settlementTypeName: contractForm.value.settlementTypeName,
          decompositionStatusName: '未分解',

          tradeDirectionId: contractForm.value.tradeDirectionName
            ? tradingDirectionOptions.value.filter(
              (item: any) =>
                item.label == contractForm.value.tradeDirectionName,
            )[0]?.value
            : null,
          energyTypeId: contractForm.value.energyTypeName
            ? energyTypeOptions.value.filter(
              (item: any) => item.label == contractForm.value.energyTypeName,
            )[0]?.value
            : null,
          tradeCommodityId: contractForm.value.tradeCommodityName
            ? tradeCategoryOptions.value.filter(
              (item: any) =>
                item.label == contractForm.value.tradeCommodityName,
            )[0]?.value
            : null,
          tradeModeId: contractForm.value.tradeModeName
            ? meansExchangeOptions.value.filter(
              (item: any) => item.label == contractForm.value.tradeModeName,
            )[0]?.value
            : null,
          tradePeriodId: contractForm.value.tradePeriodName
            ? tradingCycleOptions.value.filter(
              (item: any) => item.label == contractForm.value.tradePeriodName,
            )[0]?.value
            : null,
          settlementTypeId: contractForm.value.settlementTypeName
            ? settlementTypeFormOptions.value.filter(
              (item: any) =>
                item.label == contractForm.value.settlementTypeName,
            )[0]?.value
            : null,
          decompositionStatusId: contractForm.value.decompositionStatusName
            ? decompositionStateOptions.value.filter(
              (item: any) => item.label == '未分解',
            )[0]?.value
            : null,

          contractAttachmentId: attachmentIdList.value.join(','),
        }
        console.log(`output->payload`, payload)
        await contractsAdd(payload)
        getContractsQueryPage()
        fileList.value = []
        editVisible.value = false
      } else {
        console.log(`output->rowData.value`, rowData.value)
        let payload = {
          id: rowData.value.id,
          contractName: contractForm.value.contractName,
          startTime: contractForm.value.startAndEndTime[0],
          endTime: contractForm.value.startAndEndTime[1],
          partyOne: contractForm.value.partyOne,
          partyTwo: contractForm.value.partyTwo,
          energyTypeName: contractForm.value.energyTypeName,
          dealTime: contractForm.value.dealTime,
          note: contractForm.value.note,
          contractEnergy: contractForm.value.contractEnergy,
          contractAvgPrice: contractForm.value.contractAvgPrice,
          otherDetails: contractForm.value.otherDetails,
          contractNo: contractForm.value.contractNo,

          tradeDirectionName: contractForm.value.tradeDirectionName,
          tradeCommodityName: contractForm.value.tradeCommodityName,
          tradeModeName: contractForm.value.tradeModeName,
          tradePeriodName: contractForm.value.tradePeriodName,
          settlementTypeName: contractForm.value.settlementTypeName,
          decompositionStatusName: contractForm.value.decompositionStatusName,
          // @ts-ignore
          tradeDirectionId: contractForm.value.tradeDirectionName
            ? tradingDirectionOptions.value.filter(
              (item: any) =>
                item.label == contractForm.value.tradeDirectionName,
            )[0]?.value
            : null,
          energyTypeId: contractForm.value.energyTypeName
            ? energyTypeOptions.value.filter(
              (item: any) => item.label == contractForm.value.energyTypeName,
            )[0]?.value
            : null,
          tradeCommodityId: contractForm.value.tradeCommodityName
            ? tradeCategoryOptions.value.filter(
              (item: any) =>
                item.label == contractForm.value.tradeCommodityName,
            )[0]?.value
            : null,
          tradeModeId: contractForm.value.tradeModeName
            ? meansExchangeOptions.value.filter(
              (item: any) => item.label == contractForm.value.tradeModeName,
            )[0]?.value
            : null,
          tradePeriodId: contractForm.value.tradePeriodName
            ? tradingCycleOptions.value.filter(
              (item: any) => item.label == contractForm.value.tradePeriodName,
            )[0]?.value
            : null,
          settlementTypeId: contractForm.value.settlementTypeName
            ? settlementTypeFormOptions.value.filter(
              (item: any) =>
                item.label == contractForm.value.settlementTypeName,
            )[0]?.value
            : null,
          decompositionStatusId: contractForm.value.decompositionStatusName
            ? decompositionStateOptions.value.filter(
              (item: any) => item.label == contractForm.value.decompositionStatusName,
            )[0]?.value
            : null,

          contractAttachmentId: attachmentIdList.value.join(','),
        }
        await contractsUpdate(payload)
        getContractsQueryPage()
        fileList.value = []
        editVisible.value = false
      }
    } else {
      console.log('error submit!', fields)
    }
  })
}
function cancleVisibleConfirm() {
  fileList.value = []
  editVisible.value = false
}


const typeObj: any = {
  年分月: 1,
  月分日: 2,
  日分时: 3,
}
// 获取电量分解图表
async function getcontractseEnergyDecompose() {
  let startTime, endTime
  if (timeTypeActive.value === '年分月') {
    startTime = rowData.value.startTime
    endTime = rowData.value.endTime
  }
  if (timeTypeActive.value === '月分日') {
    if (
      rowData.value.startTime.includes(monthPickerData.value) &&
      rowData.value.endTime.includes(monthPickerData.value)
    ) {
      startTime = rowData.value.startTime
      endTime = rowData.value.endTime
    } else if (rowData.value.startTime.includes(monthPickerData.value)) {
      startTime = rowData.value.startTime
      endTime = dayjs(rowData.value.startTime)
        .endOf('month')
        .format('YYYY-MM-DD')
      console.log(`output->开始时间`, '开始时间', startTime, endTime)
    } else if (rowData.value.endTime.includes(monthPickerData.value)) {
      startTime = dayjs(rowData.value.endTime)
        .startOf('month')
        .format('YYYY-MM-DD')
      endTime = rowData.value.endTime
      console.log(`output->结束时间`, '结束时间', startTime, endTime)
    } else {
      startTime = dayjs(monthPickerData.value)
        .startOf('month')
        .format('YYYY-MM-DD')
      endTime = dayjs(monthPickerData.value).endOf('month').format('YYYY-MM-DD')
    }
  }
  if (timeTypeActive.value === '日分时') {
    startTime = datePickerData.value
    endTime = datePickerData.value
  }
  console.log(triangleScheme.value)
  const res = await contractseEnergyDecompose({
    startTime: startTime,
    endTime: endTime,
    energy: rowData.value.contractEnergy,
    programmeId: triangleScheme.value,
    type: typeObj[timeTypeActive.value],
  })
  const res1 = Object.entries(res)
  res1.sort((a: any, b: any) => {
    return a[0].slice(0, -1) - b[0].slice(0, -1)
  })
  triangleOption.value.xAxis.data = res1.map((item: any) => item[0])
  triangleOption.value.series[0].data = res1.map((item: any) => item[1])
}

// 分解弹窗tab
const timeTypeTabObj: any = {
  1: ['年分月', '月分日', '日分时'],
  2: ['月分日', '日分时'],
  3: ['日分时'],
}
const timeTypeActive = ref()
function timeTypeChange() {
  nextTick(async () => {
    if (timeTypeActive.value === '年分月') {
      await getProgrammeListInfo()
      await getDecomposeInfoData()
      getcontractseEnergyDecompose()
    }
    if (timeTypeActive.value === '月分日') {
      await getProgrammeListInfo()
      await getDecomposeInfoData()
      getcontractseEnergyDecompose()
    }
    if (timeTypeActive.value === '日分时') {
      await getProgrammeListInfo()
      await getDecomposeInfoData()
      getcontractseEnergyDecompose()
    }
  })
}

// 分解弹窗日期改变
async function fenjieDateChange() {
  await getDecomposeInfoData()
  getcontractseEnergyDecompose()
}
const timeTypeTab = ref<any>()

// 月分日月份选择
const monthPickerData = ref<any>()
// 日分时月份选择
const datePickerData = ref<any>()

// 获取方案信息
async function getProgrammeListInfo() {
  const ProgrammeList = await getProgrammeList({
    type: typeObj[timeTypeActive.value],
    unitId: rowData.value.signedUnitId,
  })
  let array: any

  array = ProgrammeList?.map((item: any) => {
    return {
      label: item.name,
      value: item.id,
    }
  })
  array.unshift({
    label: '均分法',
    value: -1,
  })
  triangleSchemeOptions.value = array
  triangleScheme.value = triangleSchemeOptions.value[0].value
}
// 分解弹窗
async function triangleClick(data: any) {
  yearResolve.value = false
  monthResolve.value = false
  dateResolve.value = false
  rowData.value = data
  datePickerData.value = rowData.value.endTime
  monthPickerData.value = rowData.value.endTime.slice(0, 7)
  timeTypeTab.value = timeTypeTabObj[rowData.value.tradePeriodId]
  timeTypeActive.value = timeTypeTab.value[0]

  await getProgrammeListInfo()
  await getDecomposeInfoData()
  getcontractseEnergyDecompose()

  triangleVisible.value = true
}

let isAddProgramme = ref(false)
// 分解方案Id
let resolveId = ref(null)
//根据条件查询分解信息
async function getDecomposeInfoData() {
  console.log(`output->timeTypeActive.value`, timeTypeActive.value)
  let decomposeDate
  if (typeObj[timeTypeActive.value] == 1) {
    decomposeDate = rowData.value.startTime.slice(0, 4)
  }
  if (typeObj[timeTypeActive.value] == 2) {
    decomposeDate = monthPickerData.value
  }
  if (typeObj[timeTypeActive.value] == 3) {
    decomposeDate = datePickerData.value
  }
  const res = await getDecomposeInfo({
    contractId: rowData.value.id,
    type: typeObj[timeTypeActive.value],
    decomposeDate,
  })
  if (res && res.programmeId) {
    triangleScheme.value = res.programmeId
    resolveId.value = res.id
    isAddProgramme.value = false
    if (typeObj[timeTypeActive.value] == 1) {
      yearResolve.value = true
    }
    if (typeObj[timeTypeActive.value] == 2) {
      monthResolve.value = true
    }
    if (typeObj[timeTypeActive.value] == 3) {
      dateResolve.value = true
    }
  } else {
    isAddProgramme.value = true
  }
  console.log(`output->isAddProgramme.value`, isAddProgramme.value)
}
const triangleVisible = ref<any>()
let yearResolve = ref(false)
let monthResolve = ref(false)
let dateResolve = ref(false)
async function triangleVisibleConfirm() {
  let decomposeDate
  if (typeObj[timeTypeActive.value] == 1) {
    yearResolve.value = true
    decomposeDate = rowData.value.startTime.slice(0, 4)
  }
  if (typeObj[timeTypeActive.value] == 2) {
    if (!yearResolve.value) {
      return ElMessage.info('请先分解年分月')
    } else {
      monthResolve.value = true
      decomposeDate = monthPickerData.value
    }
  }
  if (typeObj[timeTypeActive.value] == 3) {
    if (!monthResolve.value) {
      return ElMessage.info('请先分解月分日')
    } else {
      dateResolve.value = true
      decomposeDate = datePickerData.value
    }
  }
  if (!rowData.value.contractEnergy) {
    return ElMessage.info('该合同没有电量')
  }
  if (isAddProgramme.value) {
    await addDecompose({
      contractId: rowData.value.id,
      programmeId: triangleScheme.value,
      type: typeObj[timeTypeActive.value],
      decomposeDate,
    })
  } else {
    await updateDecompose({
      id: resolveId.value,
      contractId: rowData.value.id,
      programmeId: triangleScheme.value,
      type: typeObj[timeTypeActive.value],
      decomposeDate,
    })
  }
  console.log(`output->rowData.value`, rowData.value)
  let payload = {
    id: rowData.value.id,
    contractName: rowData.value.contractName,
    startTime: rowData.value.startTime,
    endTime: rowData.value.endTime,
    partyOne: rowData.value.partyOne,
    partyTwo: rowData.value.partyTwo,
    energyTypeName: rowData.value.energyTypeName,
    dealTime: rowData.value.dealTime,
    note: rowData.value.note,
    contractEnergy: rowData.value.contractEnergy,
    contractAvgPrice: rowData.value.contractAvgPrice,
    otherDetails: rowData.value.otherDetails,
    contractNo: rowData.value.contractNo,

    tradeDirectionName: rowData.value.tradeDirectionName,
    tradeCommodityName: rowData.value.tradeCommodityName,
    tradeModeName: rowData.value.tradeModeName,
    tradePeriodName: rowData.value.tradePeriodName,
    settlementTypeName: rowData.value.settlementTypeName,
    decompositionStatusName: '已分解',
    // @ts-ignore
    tradeDirectionId: rowData.value.tradeDirectionName
      ? tradingDirectionOptions.value.filter(
        (item: any) => item.label == rowData.value.tradeDirectionName,
      )[0]?.value
      : null,
    energyTypeId: rowData.value.energyTypeName
      ? energyTypeOptions.value.filter(
        (item: any) => item.label == contractForm.value.energyTypeName,
      )[0]?.value
      : null,
    tradeCommodityId: rowData.value.tradeCommodityName
      ? tradeCategoryOptions.value.filter(
        (item: any) => item.label == rowData.value.tradeCommodityName,
      )[0]?.value
      : null,
    tradeModeId: rowData.value.tradeModeName
      ? meansExchangeOptions.value.filter(
        (item: any) => item.label == rowData.value.tradeModeName,
      )[0]?.value
      : null,
    tradePeriodId: rowData.value.tradePeriodName
      ? tradingCycleOptions.value.filter(
        (item: any) => item.label == rowData.value.tradePeriodName,
      )[0]?.value
      : null,
    settlementTypeId: rowData.value.settlementTypeName
      ? settlementTypeFormOptions.value.filter(
        (item: any) => item.label == rowData.value.settlementTypeName,
      )[0]?.value
      : null,
    decompositionStatusId: rowData.value.decompositionStatusName
      ? decompositionStateOptions.value.filter(
        (item: any) => item.label == '已分解',
      )[0]?.value
      : null,

    contractAttachmentId: attachmentIdList.value.join(','),
  }
  await contractsUpdate(payload)
  getContractsQueryPage()
  fileList.value = []
  triangleVisible.value = false
}
// 删除弹窗
function closeClick(data: any) {
  closeVisible.value = true
  rowData.value = data
}
const closeVisible = ref<any>()
async function closeVisibleConfirm() {
  const loading = ElLoading.service({ text: '正在删除...' })
  await contractsDelete(rowData.value)
  getContractsQueryPage()
  loading.close()
  closeVisible.value = false
}

// 弹窗表单
const contractForm = ref<any>({
  contractName: null,
  startAndEndTime: null,
  partyOne: null,
  partyTwo: null,
  tradeDirectionId: null,
  tradeCommodityId: null,
  tradeModeId: null,
  tradePeriodId: null,
  energyTypeId: null,
  signedUnitId: null,
  settlementTypeId: null,
  dealTime: null,
  note: null,
  contractEnergy: null,
  contractAvgPrice: null,
  otherDetails: null,
  decompositionState: null,

  energyTypeName: null,
  tradeDirectionName: null,
  tradeCommodityName: null,
  tradeModeName: null,
  tradePeriodName: null,
  settlementTypeName: null,
  decompositionStatusName: null,
  contractNo: null,
})
// 交易方向下拉框
const tradingDirectionOptions = ref<any>([])
// 结算类型下拉框
const settlementTypeFormOptions = ref<any>([])

const fileList = ref<any>([
  // {
  //   name: '流程.pdf',
  // },
  // {
  //   name: 'element.svg',
  // },
])

// 上传附件
function uploadAttachment(file: any, files: any) {
  const size = file.size / 1024 / 1024
  if (size > 100) {
    ElMessage.warning('文件大小不得超过100M')
    fileList.value = []
    return
  }
  fileList.value = files
  handleuploadAttachment()
}

// 下载附件
function openFile(e: any) {
  const loading = ElLoading.service({ text: '正在下载...' })
  attachmentDownload({
    name: e.name,
  })
    .then((data) => {
      const blob = new Blob([data])
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = e.name
      link.click()
      window.URL.revokeObjectURL(url)
      loading.close()
    })
    .catch(() => {
      ElMessage.error('下载失败')
      loading.close()
    })
}

const attachmentIdList = ref<any>([])

// 删除附件
// @ts-ignore
function removeFile(file: any, fileList: any) {
  attachmentIdList.value = fileList.map((item: any) => item.id)
  console.log(`output->attachmentIdList.value `, attachmentIdList.value)
}

// 上传函数
const handleuploadAttachment = () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('未选择文件')
  } else {
    const formData = new FormData()
    formData.append('file', fileList.value[fileList.value.length - 1].raw)
    attachmentUpload(formData)
      .then((res: any) => {
        // ElMessage.success('上传成功')
        attachmentIdList.value.push(res.id)
        fileList.value[fileList.value.length - 1] = res
      })
      .catch((e) => {
        console.log(e)
      })
  }
}

// 电量分解
// 时间
// 分解方案
const triangleScheme = ref()
const triangleSchemeOptions = ref<any>([])
function triangleSchemeChange() {
  getcontractseEnergyDecompose()
}
// 分解图表
const triangleOption = ref<any>({
  legend: {
    show: false,
  },
  confine: true,
  tooltip: {
    trigger: 'axis',
    backgroundColor: 'rgba(255,255,255,0.90)',
    borderRadius: '4px',
    boxShadow: ' 0px 2px 10px 0px rgba(0, 0, 0, 0.16)',
    formatter: function (params: any) {
      let context
      let content = params.map((item: any) => {
        if (item.seriesName === '电量') {
          return `${item.marker}<span>${item.seriesName
            }</span>:<span style="margin-left: 10px;float: right">${item.value ? item.value : '--'
            }MWh</span><br>`
        }
        if (item.seriesName === '电价') {
          return `${item.marker}<span>${item.seriesName
            }</span>:<span style="margin-left: 10px;float: right">${item.value ? item.value : '--'
            }元/MWh</span><br>`
        }
      })
      let newContent = ''
      content.forEach((item: any) => {
        newContent = newContent + item
      })
      context = `<div>${params[0].name}</div><div>${newContent}</div>`
      return context
    },
  },
  grid: {
    top: '18%',
    left: '8%',
    right: '8%',
    bottom: '8%',
  },
  xAxis: {
    type: 'category',
    data: [],
    axisTick: {
      show: false, // 将此选项设置为 false 来隐藏 X 轴刻度
    },
  },
  yAxis: [
    {
      position: 'left',
      type: 'value',
      name: '电量(MWh)',
      nameTextStyle: {
        // y轴name的样式调整
        fontSize: 14,
      },
      splitLine: {
        show: false, //想要不显示网格线，改为false
      },
    },
    // {
    //   position: 'right',
    //   type: 'value',
    //   name: '电价(元/MWh)',
    //   nameTextStyle: {
    //     // y轴name的样式调整
    //     fontSize: 14,
    //   },
    //   splitLine: {
    //     show: false, //想要不显示网格线，改为false
    //   },
    // }
  ],
  series: [
    {
      name: '电量',
      data: [],
      type: 'bar',
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        color: '#254f7a',
      },
    },
    // {
    //   name: '电价',
    //   data: [],
    //   type: 'line',
    //   yAxisIndex: 1,
    //   smooth: true, //关键点，为true是不支持虚线，实线就用true
    //   showSymbol: false, // 不显示折线上的圆点
    //   symbol: 'circle', //设定为实心点
    //   symbolSize: 8, //设定实心点的大小
    //   itemStyle: {
    //     color: '#029CD4',
    //   },
    //   lineStyle: {
    //     color: '#029CD4'
    //   }
    // }
  ],
})
const triangleOptionDeal = echartsConfig(triangleOption.value)

//#endregion

//#region 电量分解库
// 顶部tab
const coulometricDecompositionBankActive = ref('年分月')
function coulometricDecompositionBankChange() {
  nextTick(() => {
    getProgrammeContentInfoList.value = []
    getProgrammeListData()
  })
}

// 均分方案图表
const equipartitionSchemeOptionDeal = ref({
  legend: {
    show: false,
  },
  confine: true,
  tooltip: {
    trigger: 'axis',
    backgroundColor: 'rgba(255,255,255,0.90)',
    borderRadius: '4px',
    boxShadow: ' 0px 2px 10px 0px rgba(0, 0, 0, 0.16)',
  },
  grid: {
    top: '18%',
    left: '12%',
    right: '5%',
    bottom: '10%',
  },
  xAxis: {
    type: 'category',
    data: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
    axisTick: {
      show: false, // 将此选项设置为 false 来隐藏 X 轴刻度
    },
  },
  yAxis: [
    {
      position: 'left',
      type: 'value',
      name: '权重',
      nameTextStyle: {
        // y轴name的样式调整
        fontSize: 14,
      },
      splitLine: {
        show: false, //想要不显示网格线，改为false
      },
    },
  ],
  series: [
    {
      name: '权重',
      data: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
      type: 'bar',
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        color: '#254f7a',
      },
    },
  ],
})
// 均分方案弹窗
const schemeVisible = ref(false)
function schemeVisibleCancel() {
  monthArrayOption.value.xAxis.data = []
  monthArrayOption.value.series[0].data = []
  monthArray.value.forEach((item: any) => {
    item.programmeWeight = null
  })
  dateArrayOption.value.xAxis.data = []
  dateArrayOption.value.series[0].data = []
  dateArray.value.forEach((item: any) => {
    item.programmeWeight = null
  })
  timeArrayOption.value.xAxis.data = []
  timeArrayOption.value.series[0].data = []
  itemArray.value.forEach((item: any) => {
    item.programmeWeight = null
  })
  schemeVisible.value = false
  schemeName.value = null
}
const schemeVisibleConfirm = async () => {
  const obj = {
    年分月: 1,
    月分日: 2,
    日分时: 3,
  }
  if (schemeTitle.value === '添加曲线') {
    if (!schemeName.value) {
      return ElMessage.info('请输入方案名称')
    }
    if (coulometricDecompositionBankActive.value === '年分月') {
      const flag = monthArray.value.every((item: any) => !item.programmeWeight)
      if (flag) {
        return ElMessage.info('请至少输入一个权重')
      }
      const programmeContentVOList = monthArray.value.map((item: any) => ({
        programmeWeight: item.programmeWeight,
        programmeDate: item.programmeDate,
        status: 0,
      }))
      await programmeAdd({
        name: schemeName.value,
        type: obj[coulometricDecompositionBankActive.value],
        status: 0,
        programmeContentVOList: programmeContentVOList
      })
      getProgrammeListData()
      schemeVisible.value = false
    }
    if (coulometricDecompositionBankActive.value === '月分日') {
      const flag = dateArray.value.every((item: any) => !item.programmeWeight)
      if (flag) {
        return ElMessage.info('请至少输入一个权重')
      }
      const programmeContentVOList = dateArray.value.map((item: any) => ({
        programmeWeight: item.programmeWeight,
        programmeDate: item.programmeDate,
        status: 0,
      }))
      await programmeAdd({
        name: schemeName.value,
        type: obj[coulometricDecompositionBankActive.value],
        status: 0,
        programmeContentVOList: programmeContentVOList
      })
      getProgrammeListData()
      schemeVisible.value = false
    }
    if (coulometricDecompositionBankActive.value === '日分时') {
      const flag = itemArray.value.every((item: any) => !item.programmeWeight)
      if (flag) {
        return ElMessage.info('请至少输入一个权重')
      }
      const programmeContentVOList = itemArray.value.map((item: any) => ({
        programmeWeight: item.programmeWeight,
        programmeDate: item.programmeDate,
        status: 0,
      }))
      await programmeAdd({
        name: schemeName.value,
        type: obj[coulometricDecompositionBankActive.value],
        status: 0,
        programmeContentVOList: programmeContentVOList
      })
      getProgrammeListData()
      schemeVisible.value = false
    }
  } else {
    if (schemeName.value === '均分方案') {
      schemeVisible.value = false
    } else {
      if (coulometricDecompositionBankActive.value === '年分月') {
        const flag = monthArray.value.every(
          (item: any) => !item.programmeWeight,
        )
        if (flag) {
          return ElMessage.info('请至少输入一个权重')
        }
        await programmeUpdate({
          id: monthArray.value[0].parentId,
          name: schemeName.value,
          type: obj[coulometricDecompositionBankActive.value],
          status: 0,
          programmeContentVOList: monthArray.value
        })
        getProgrammeListData()
        schemeVisible.value = false
      }
      if (coulometricDecompositionBankActive.value === '月分日') {
        const flag = dateArray.value.every((item: any) => !item.programmeWeight)
        if (flag) {
          return ElMessage.info('请至少输入一个权重')
        }
        await programmeUpdate({
          id: dateArray.value[0].parentId,
          name: schemeName.value,
          type: obj[coulometricDecompositionBankActive.value],
          status: 0,
          programmeContentVOList: dateArray.value
        })
        getProgrammeListData()
        schemeVisible.value = false
      }
      if (coulometricDecompositionBankActive.value === '日分时') {
        const flag = itemArray.value.every((item: any) => !item.programmeWeight)
        if (flag) {
          return ElMessage.info('请至少输入一个权重')
        }
        await programmeUpdate({
          id: itemArray.value[0].parentId,
          name: schemeName.value,
          type: obj[coulometricDecompositionBankActive.value],
          status: 0,
          programmeContentVOList: itemArray.value
        })
        getProgrammeListData()
        schemeVisible.value = false
      }
    }
  }
}
const schemeTitle = ref()

// 添加方案
function addScheme() {
  schemeTitle.value = '添加曲线'

  monthArrayOption.value.xAxis.data = []
  monthArrayOption.value.series[0].data = []
  monthArray.value.forEach((item: any) => {
    item.programmeWeight = null
  })
  dateArrayOption.value.xAxis.data = []
  dateArrayOption.value.series[0].data = []
  dateArray.value.forEach((item: any) => {
    item.programmeWeight = null
  })
  timeArrayOption.value.xAxis.data = []
  timeArrayOption.value.series[0].data = []
  itemArray.value.forEach((item: any) => {
    item.programmeWeight = null
  })
  schemeName.value = null

  schemeVisible.value = true
  averageFlag.value = false
}
const averageFlag = ref(false)
// 编辑方案
function junfenEditScheme() {
  averageFlag.value = true
  schemeTitle.value = '编辑曲线'
  schemeVisible.value = true
  schemeName.value = '均分方案'
  monthArray.value = monthArray.value.map((item: any) => {
    return {
      ...item,
      programmeWeight: 1,
    }
  })
  monthArrayOption.value.xAxis.data = monthArray.value.map(
    (item: any) => item.name,
  )
  monthArrayOption.value.series[0].data = monthArray.value.map(() => 1)

  dateArray.value = dateArray.value.map((item: any) => {
    return {
      ...item,
      programmeWeight: 1,
    }
  })
  dateArrayOption.value.xAxis.data = dateArray.value.map(
    (item: any) => item.name,
  )
  dateArrayOption.value.series[0].data = dateArray.value.map(() => 1)

  itemArray.value = itemArray.value.map((item: any) => {
    return {
      ...item,
      programmeWeight: 1,
    }
  })
  timeArrayOption.value.xAxis.data = itemArray.value.map(
    (item: any) => item.name,
  )
  timeArrayOption.value.series[0].data = itemArray.value.map(() => 1)
}
// 方案名称
const schemeName = ref()
// 年分月输入框
// @ts-ignore
const monthArray = ref<any>(Array.from({ length: 12 }, (v, k) => k + 1))
monthArray.value = monthArray.value.map((item: any) => {
  return {
    name: `${item}月`,
    programmeWeight: null,
    programmeDate: item,
  }
})
// 月分日输入框
// @ts-ignore
const dateArray = ref<any>(Array.from({ length: 31 }, (v, k) => k + 1))
dateArray.value = dateArray.value.map((item: any) => {
  return {
    name: item,
    programmeWeight: null,
    programmeDate: item,
  }
})
// 日分时输入框
// @ts-ignore
const itemArray = ref<any>(Array.from({ length: 24 }, (v, k) => k + 1))
itemArray.value = itemArray.value.map((item: any) => {
  return {
    name: `${item >= 10 ? item : '0' + item}:00`,
    programmeWeight: null,
    programmeDate: item,
  }
})

// 电量分解库图表 年分月
const monthArrayOption = ref({
  legend: {
    show: false,
  },
  confine: true,
  tooltip: {
    trigger: 'axis',
    backgroundColor: 'rgba(255,255,255,0.90)',
    borderRadius: '4px',
    boxShadow: ' 0px 2px 10px 0px rgba(0, 0, 0, 0.16)',
  },
  grid: {
    top: '18%',
    left: '10%',
    right: '5%',
    bottom: '10%',
  },
  xAxis: {
    type: 'category',
    data: [],
    axisTick: {
      show: false, // 将此选项设置为 false 来隐藏 X 轴刻度
    },
  },
  yAxis: [
    {
      position: 'left',
      type: 'value',
      // name: '权重',
      nameTextStyle: {
        // y轴name的样式调整
        fontSize: 14,
      },
      splitLine: {
        show: false, //想要不显示网格线，改为false
      },
    },
  ],
  series: [
    {
      name: '权重',
      data: [],
      type: 'bar',
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        color: '#254f7a',
      },
    },
  ],
})
// 电量分解库图表 月分日
const dateArrayOption = ref({
  legend: {
    show: false,
  },
  confine: true,
  tooltip: {
    trigger: 'axis',
    backgroundColor: 'rgba(255,255,255,0.90)',
    borderRadius: '4px',
    boxShadow: ' 0px 2px 10px 0px rgba(0, 0, 0, 0.16)',
  },
  grid: {
    top: '18%',
    left: '10%',
    right: '5%',
    bottom: '10%',
  },
  xAxis: {
    type: 'category',
    data: [],
    axisTick: {
      show: false, // 将此选项设置为 false 来隐藏 X 轴刻度
    },
  },
  yAxis: [
    {
      position: 'left',
      type: 'value',
      // name: '权重',
      nameTextStyle: {
        // y轴name的样式调整
        fontSize: 14,
      },
      splitLine: {
        show: false, //想要不显示网格线，改为false
      },
    },
  ],
  series: [
    {
      name: '权重',
      data: [],
      type: 'bar',
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        color: '#254f7a',
      },
    },
  ],
})
// 电量分解库图表 日分时
const timeArrayOption = ref({
  legend: {
    show: false,
  },
  confine: true,
  tooltip: {
    trigger: 'axis',
    backgroundColor: 'rgba(255,255,255,0.90)',
    borderRadius: '4px',
    boxShadow: ' 0px 2px 10px 0px rgba(0, 0, 0, 0.16)',
  },
  grid: {
    top: '18%',
    left: '10%',
    right: '5%',
    bottom: '10%',
  },
  xAxis: {
    type: 'category',
    data: [],
    axisTick: {
      show: false, // 将此选项设置为 false 来隐藏 X 轴刻度
    },
  },
  yAxis: [
    {
      position: 'left',
      type: 'value',
      // name: '权重',
      nameTextStyle: {
        // y轴name的样式调整
        fontSize: 14,
      },
      splitLine: {
        show: false, //想要不显示网格线，改为false
      },
    },
  ],
  series: [
    {
      name: '权重',
      data: [],
      type: 'bar',
      smooth: true, //关键点，为true是不支持虚线，实线就用true
      showSymbol: false, // 不显示折线上的圆点
      symbol: 'circle', //设定为实心点
      symbolSize: 8, //设定实心点的大小
      itemStyle: {
        color: '#254f7a',
      },
    },
  ],
})

//#endregion

// 获取查询合约信息
async function getContractsQueryPage() {
  const res = await contractsQueryPage({
    contractStartTime: contractDate.value[0],
    contractEndTime: contractDate.value[1],
    contractName: contractlName.value,
    decompositionStatusId: decompositionStatusName.value
      ? decompositionStateOptions.value.filter(
        (item: any) => item.label == decompositionStatusName.value,
      )[0]?.value
      : null,
    pageNo: contractCurrentPage.value,
    pageSize: contractPageSize.value,
    tradeCommodityId: tradeCommodityName.value
      ? tradeCategoryOptions.value.filter(
        (item: any) => item.label == tradeCommodityName.value,
      )[0]?.value
      : null,
    // tradeModeId: tradeModeName.value
    //   ? meansExchangeOptions.value.filter(
    //     (item: any) => item.label == tradeModeName.value,
    //   )[0]?.value
    //   : null,
    tradePeriodId: tradePeriodName.value
      ? tradingCycleOptions.value.filter(
        (item: any) => item.label == tradePeriodName.value,
      )[0]?.value
      : null,
  })
  contractTableData.value = res.data
  contractTotal.value = res.totalCount
}

// 方案信息数组
const getProgrammeContentInfoList = ref<any>([])

// 获取分解方案
async function getProgrammeListData() {
  const obj: any = {
    年分月: 1,
    月分日: 2,
    日分时: 3,
  }
  const res = await getProgrammeContentInfo({
    type: obj[coulometricDecompositionBankActive.value]
  })
  console.log(`output->res`, res)
  getProgrammeContentInfoList.value = res?.map((item: any) => {
    return {
      ...item,
      equipartitionSchemeOptionDeal: {
        legend: {
          show: false,
        },
        confine: true,
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(255,255,255,0.90)',
          borderRadius: '4px',
          boxShadow: ' 0px 2px 10px 0px rgba(0, 0, 0, 0.16)',
        },
        grid: {
          top: '18%',
          left: '12%',
          right: '5%',
          bottom: '10%',
        },
        xAxis: {
          type: 'category',
          data: item.programmeContentVOList.map(
            (item: any) => item.programmeDate,
          ),
          axisTick: {
            show: false, // 将此选项设置为 false 来隐藏 X 轴刻度
          },
        },
        yAxis: [
          {
            position: 'left',
            type: 'value',
            name: '权重',
            nameTextStyle: {
              // y轴name的样式调整
              fontSize: 14,
            },
            splitLine: {
              show: false, //想要不显示网格线，改为false
            },
          },
        ],
        series: [
          {
            name: '权重',
            data: item.programmeContentVOList.map(
              (item: any) => item.programmeWeight,
            ),
            type: 'bar',
            smooth: true, //关键点，为true是不支持虚线，实线就用true
            showSymbol: false, // 不显示折线上的圆点
            symbol: 'circle', //设定为实心点
            symbolSize: 8, //设定实心点的大小
            itemStyle: {
              color: '#254f7a',
            },
          },
        ],
      },
    }
  })
}

// 编辑方案
function EditScheme(data: any) {
  console.log(`output->data`, data)

  averageFlag.value = false
  console.log(`output->data`, data)
  schemeTitle.value = '编辑曲线'
  schemeVisible.value = true
  schemeName.value = data.name
  if (coulometricDecompositionBankActive.value === '年分月') {
    monthArray.value = data.programmeContentVOList.map((item: any) => {
      return {
        ...item,
        name: `${item.programmeDate}月`,
        programmeDate: item.programmeDate,
        parentId: data.id,
      }
    })
    monthArrayOption.value.xAxis.data = data.programmeContentVOList.map(
      (item: any) => {
        return item.programmeDate
      },
    )
    monthArrayOption.value.series[0].data = data.programmeContentVOList.map(
      (item: any) => item.programmeWeight,
    )
  }

  if (coulometricDecompositionBankActive.value === '月分日') {
    dateArray.value = data.programmeContentVOList.map((item: any) => {
      return {
        ...item,
        name: item.programmeDate,
        programmeDate: item.programmeDate,
        parentId: data.id,
      }
    })
    dateArrayOption.value.xAxis.data = data.programmeContentVOList.map(
      (item: any) => item.programmeDate,
    )
    dateArrayOption.value.series[0].data = data.programmeContentVOList.map(
      (item: any) => item.programmeWeight,
    )
  }

  if (coulometricDecompositionBankActive.value === '日分时') {
    itemArray.value = data.programmeContentVOList.map((item: any) => {
      return {
        ...item,
        name: `${item.programmeDate >= 10
          ? item.programmeDate
          : '0' + item.programmeDate
          }:00`,
        programmeDate: item.programmeDate,
        parentId: data.id,
      }
    })
    timeArrayOption.value.xAxis.data = data.programmeContentVOList.map(
      (item: any) => item.programmeDate,
    )
    timeArrayOption.value.series[0].data = data.programmeContentVOList.map(
      (item: any) => item.programmeWeight,
    )
  }
}
// 删除方案
async function deleteScheme(data: any) {
  programmeDelete
  const obj = {
    年分月: 1,
    月分日: 2,
    日分时: 3,
  }
  monthArray.value = data.programmeContentVOList.map((item: any) => {
    return {
      ...item,
      name: `${item.programmeDate}月`,
      programmeDate: item.programmeDate,
      parentId: data.id,
    }
  })
  dateArray.value = data.programmeContentVOList.map((item: any) => {
    return {
      ...item,
      name: item.programmeDate,
      programmeDate: item.programmeDate,
      parentId: data.id,
    }
  })
  itemArray.value = data.programmeContentVOList.map((item: any) => {
    return {
      ...item,
      name: `${item.programmeDate >= 10 ? item.programmeDate : '0' + item.programmeDate
        }:00`,
      programmeDate: item.programmeDate,
      parentId: data.id,
    }
  })
  if (coulometricDecompositionBankActive.value === '年分月') {
    await programmeDelete({
      id: monthArray.value[0].parentId,
      name: schemeName.value,
      type: obj[coulometricDecompositionBankActive.value],
      status: 0,
      programmeContentVOList: monthArray.value
    })
    getProgrammeListData()
  }
  if (coulometricDecompositionBankActive.value === '月分日') {
    await programmeDelete({
      id: dateArray.value[0].parentId,
      name: schemeName.value,
      type: obj[coulometricDecompositionBankActive.value],
      status: 0,
      programmeContentVOList: dateArray.value
    })
    getProgrammeListData()
  }
  if (coulometricDecompositionBankActive.value === '日分时') {
    await programmeDelete({
      id: itemArray.value[0].parentId,
      name: schemeName.value,
      type: obj[coulometricDecompositionBankActive.value],
      status: 0,
      programmeContentVOList: itemArray.value
    })
    getProgrammeListData()
  }
}

// 交易周期改变重置合约时间
function tradePeriodNameChange() {
  contractForm.value.startAndEndTime = null
}

watch(
  monthArray,
  (value) => {
    monthArrayOption.value.xAxis.data = value.map((item: any) => item.name)
    monthArrayOption.value.series[0].data = value.map(
      (item: any) => item.programmeWeight,
    )
  },
  {
    deep: true,
  },
)
watch(
  dateArray,
  (value) => {
    dateArrayOption.value.xAxis.data = value.map((item: any) => item.name)
    dateArrayOption.value.series[0].data = value.map(
      (item: any) => item.programmeWeight,
    )
  },
  {
    deep: true,
  },
)
watch(
  itemArray,
  (value) => {
    timeArrayOption.value.xAxis.data = value.map((item: any) => item.name)
    timeArrayOption.value.series[0].data = value.map(
      (item: any) => item.programmeWeight,
    )
  },
  {
    deep: true,
  },
)

onMounted(async () => {
  let useStore = useUserStore()
  await useStore.setDictionaryPa()
  contractDetailsInfo()
  await filterSelect('交易周期').then((res: any) => {
    tradingCycleOptions.value = res?.map((item: any) => {
      return {
        label: item.dictItemValue,
        value: item.dictItemCode,
      }
    })
  })
  await filterSelect('交易方式').then((res: any) => {
    meansExchangeOptions.value = res?.map((item: any) => {
      return {
        label: item.dictItemValue,
        value: item.dictItemCode,
      }
    })
  })
  await filterSelect('交易品种').then((res: any) => {
    tradeCategoryOptions.value = res?.map((item: any) => {
      return {
        label: item.dictItemValue,
        value: item.dictItemCode,
      }
    })
  })
  await filterSelect('分解状态').then((res: any) => {
    decompositionStateOptions.value = res?.map((item: any) => {
      return {
        label: item.dictItemValue,
        value: item.dictItemCode,
      }
    })
  })
  await filterSelect('结算类型').then((res: any) => {
    settlementTypeFormOptions.value = res?.map((item: any) => {
      return {
        label: item.dictItemValue,
        value: item.dictItemCode,
      }
    })
  })
  await filterSelect('交易方向').then((res: any) => {
    tradingDirectionOptions.value = res?.map((item: any) => {
      return {
        label: item.dictItemValue,
        value: item.dictItemCode,
      }
    })
  })
  await filterSelect('电量类型').then((res: any) => {
    energyTypeOptions.value = res?.map((item: any) => {
      return {
        label: item.dictItemValue,
        value: item.dictItemCode,
      }
    })
  })
})
</script>

<style lang="scss" scoped>
:deep(.singleSelect.el-select .el-input__wrapper) {
  background-color: #f2f3f5 !important;
}

:deep(.el-date-editor) {
  background-color: #f2f3f5 !important;
}

:deep(.singleSelect.el-select .el-input__suffix-inner) {
  border-left: 1px solid #e5e6eb;
}

:deep(.el-input-group__append) {
  background-color: #254f7a;

  .el-icon {
    color: #fff;
  }
}

//el-card头部样式
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .header-title {
    padding: 0px 10px;
    border-left: 3px solid #c3d7f0;
    color: #303133;
    /* 文字/18加粗 */
    font-size: 18px;
    font-weight: 700;
    line-height: 22px;
  }
}

// 修改el-card阴影
.jt-card {
  box-shadow: 0px 2px 22px 0px rgba(0, 0, 0, 0.06) !important;
  border-radius: 6px !important;
  border: none !important;
}

:deep(.daoru) {
  .el-upload-list.el-upload-list--text {
    display: none;
  }
}

.weight-table {
  margin-top: 20px;

  &-head {
    display: flex;
    font-size: 16px;
    font-weight: 700;
    color: #1d2129;
    border: 1px solid #dcdfe6;

    div {
      width: 12.5%;
      height: 56px;
      line-height: 56px;
      text-align: center;
      background-color: #f2f3f5;
      border-right: 1px solid #dcdfe6;

      &:last-child {
        border: none;
      }
    }
  }

  .monthArray,
  .timeArray {
    &>div {
      border-bottom: 1px solid #dcdfe6;
    }

    &>div:nth-last-child(-n + 8) {
      border-bottom: none;
    }
  }

  .dateArray {
    &>div {
      border-bottom: 1px solid #dcdfe6;
    }

    &>div:nth-last-child(-n + 6) {
      border-bottom: none;
    }
  }
}

.scheme-title {
  display: flex;
  width: 100%;
  font-size: 14px;
  align-items: center;
}

.schemeDialog {
  .dialog-footer {
    display: flex;
    justify-content: center;
  }
}

.triangleDialog {
  .dialog-footer {
    display: flex;
    justify-content: center;
  }
}

.triangleTitle {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  &>span {
    color: #1d2129;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
  }

  &>div {
    display: flex;
    justify-content: space-between;

    &>div {
      display: flex;
      align-items: center;
    }
  }
}

.wrap {
  width: 100%;
  display: flex;
  flex-direction: column;

  .top,
  .bottom {
    display: flex;

    div {
      font-size: 14px;
      width: 120px;
      height: 48px;
      line-height: 48px;
      text-align: center;
      border: 1px solid #dcdfe6;

      &.left {
        font-size: 16px;
        font-weight: 700;
        color: #1d2129;
        background-color: #f2f3f5;
      }
    }

    &.justone {
      div {
        height: 96px;
        line-height: 96px;
      }
    }
  }
}

:deep(.el-overlay-dialog) {
  .el-dialog {
    width: 900px;

    .el-dialog__header {
      border-bottom: 1px solid #e5e6eb;
      margin-right: 0;

      span {
        color: #1d2129;
        text-align: center;
        /* 文字/16加粗 */
        font-size: 16px;
        font-weight: 700;
        line-height: 22px;
        /* 137.5% */
      }
    }

    .el-dialog__body {
      padding: 20px;

      .el-button {
        border: 1px solid #254f7a;
      }

      .el-checkbox-group {
        display: inline-block;
      }
    }

    .el-dialog__footer {
      border-top: 1px solid #e5e6eb;
    }
  }
}

:deep(.dialog) {
  .el-upload-list__item .el-icon--close-tip {
    display: none !important;
  }

  .el-date-editor.el-input {
    width: 100%;
  }

  .el-textarea__inner {
    background-color: #f2f3f5 !important;
  }

  .upload-demo {
    width: 100%;

    .el-upload-list__item {
      background-color: #f2f3f5 !important;
    }
  }

  .el-input {
    .el-input__wrapper {
      background-color: #f2f3f5 !important;
    }
  }

  .el-select.singleSelect {
    width: 100% !important;
  }

  .dialog-footer {
    display: flex;
    justify-content: center;
  }

  .el-date-editor {
    background-color: #f2f3f5 !important;
  }
}

.box-wrap {
  display: flex;
  flex-wrap: wrap;
  // justify-content: center;

  .box {
    margin-top: 20px;
    margin-right: 40px;
    width: 31%;
    height: 264px;
    border: 1px solid #dcdfe6;

    &:nth-child(3n) {
      margin-right: 0;
    }

    .box-header {
      padding: 16px 20px;
      position: relative;
      text-align: center;
      color: #1d2129;
      font-size: 14px;

      &-icon {
        position: absolute;
        right: 20px;
        top: 16px;

        img {
          cursor: pointer;
        }

        img:nth-child(1) {
          margin-right: 15px;
        }
      }
    }
  }
}

.box-header-icon {
  display: flex;
}
</style>
