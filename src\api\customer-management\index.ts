import { http } from "@/utils/http";
import { baseUrlApi } from "../utils";
import { request } from '@/utils/request'
import {
  ResponseDetailModel,
  BasicResponseParams,
  BasicPageParams
} from "@/model/baseModel";
import {
  CustomerPageModel,
  CustomerResultModel,
  GetCustomerListResultModel,
  CustomerContactListModel,
  CustomerAccountListModel,
  GetElectricityAccountListResultModel,
  ElectricityAccountPageModel,
  CustomerBindListModel,
  GetBindListResultModel,
  GetFollowListResultModel,
  CustomerFollowListModel,
  FollowListPageModel,
  CustomerValueAddedListModel,
  CustomerValueAddedListResultModel,
  ValueAddedListPageModel,
  CustomerOpportunityListModel,
  GetCustomerOpportunityListResultModel,
  CustomerOpportunityListPageModel,
  GetCustomerContractModelListResultModel,
  CustomerContractModel,
  CustomerContractListPageModel,
  GETContractTotalModel,
  CustomerChangeListModel,
  StopContractListModel,
  GetUserCountModel,
  GetSaleUserByIdModel,
  GetOpportunityTotalModel,
  GetContractSumModel,
  GetGradeCountModel,
  GetElectricitySumModel,
  CustomerPowerListPageModel,
  GetCustomMeterListResultModel,
  CustomerPortraitLoadModel,
  CustomerPortraitLoadResultModel,
  CustomerSumResultModel,
  AreaSumResultModel
} from "@/model/customerModel";
export const saveCustomerDataApi = (data: CustomerResultModel) => {
  return http.request<BasicResponseParams>(
    "post",
    baseUrlApi("custom/basic/save"),
    {
      data
    }
  );
};

// 列表
export const getCustomerListApi = (data: CustomerPageModel) => {
  return http.request<GetCustomerListResultModel>(
    "post",
    baseUrlApi("custom/basic/queryPage"),
    {
      data
    }
  );
};

// 根据id查询
export const getCustomerDetailApi = (id: string | number) => {
  return http.request<ResponseDetailModel<CustomerResultModel>>(
    "get",
    baseUrlApi(`custom/basic/details/${id}`)
  );
};

// 根据id删除
export const delCustomerByIdApi = (id: string | number) => {
  return http.request<BasicResponseParams>(
    "get",
    baseUrlApi(`custom/basic/delete/${id}`)
  );
};

export const saveContactDataApi = (data: CustomerContactListModel) => {
  return http.request<BasicResponseParams>(
    "post",
    baseUrlApi("custom/contact/save"),
    {
      data
    }
  );
};

// 根据id查询
export const getContactrDetailApi = (id: string | number) => {
  return http.request<ResponseDetailModel<CustomerContactListModel>>(
    "get",
    baseUrlApi(`custom/contact/details/${id}`)
  );
};

// 根据id删除
export const delContactByIdApi = (id: string | number) => {
  return http.request<BasicResponseParams>(
    "get",
    baseUrlApi(`custom/contact/delete/${id}`)
  );
};

// 用电户号
export const saveElectricityAccountApi = (data: CustomerAccountListModel) => {
  return http.request<BasicResponseParams>(
    "post",
    baseUrlApi("custom/account/save"),
    {
      data
    }
  );
};
export const editElectricityAccountApi = (data: CustomerAccountListModel) => {
  return http.request<BasicResponseParams>(
    "post",
    baseUrlApi("custom/account/modify"),
    {
      data
    }
  );
};

// 列表
export const getElectricityAccountListApi = (
  data: ElectricityAccountPageModel
) => {
  return http.request<GetElectricityAccountListResultModel>(
    "post",
    baseUrlApi("custom/account/queryMeterByPage"),
    {
      data
    }
  );
};

// 根据id查询
export const getElectricityAccountDetailApi = (id: string | number) => {
  return http.request<ResponseDetailModel<CustomerAccountListModel>>(
    "get",
    baseUrlApi(`custom/account/detailsMeter/${id}`)
  );
};

// 根据id删除
export const delElectricityAccountByIdApi = (id: string | number) => {
  return http.request<BasicResponseParams>(
    "get",
    baseUrlApi(`custom/account/delete/${id}`)
  );
};

// 绑定关系
export const saveBindRelationshipApi = (data: CustomerBindListModel) => {
  return http.request<BasicResponseParams>(
    "post",
    baseUrlApi("custom/bind/save"),
    {
      data
    }
  );
};

// 列表
export const getBindRelationshipListApi = data => {
  return http.request<GetBindListResultModel>(
    "post",
    baseUrlApi("custom/bind/queryPage"),
    {
      data
    }
  );
};

// 根据id查询
export const getBindRelationshipDetailApi = (id: string | number) => {
  return http.request<ResponseDetailModel<CustomerBindListModel>>(
    "get",
    baseUrlApi(`custom/bind/details/${id}`)
  );
};

// 根据id删除
export const delBindRelationshipByIdApi = (id: string | number) => {
  return http.request<BasicResponseParams>(
    "get",
    baseUrlApi(`custom/bind/delete/${id}`)
  );
};

// 跟进记录管理保存
export const saveFollowRecordApi = (data: CustomerFollowListModel) => {
  return http.request<BasicResponseParams>(
    "post",
    baseUrlApi("custom/follow/save"),
    {
      data
    }
  );
};

// 列表
export const getFollowRecordListApi = (data: FollowListPageModel) => {
  return http.request<GetFollowListResultModel>(
    "post",
    baseUrlApi("custom/follow/queryPage"),
    {
      data
    }
  );
};

// 根据id查询
export const getFollowRecordByIdApi = (id: string | number) => {
  return http.request<ResponseDetailModel<CustomerFollowListModel>>(
    "get",
    baseUrlApi(`custom/follow/details/${id}`)
  );
};

// 根据id删除
export const delFollowRecordApi = (id: string | number) => {
  return http.request<BasicResponseParams>(
    "get",
    baseUrlApi(`custom/follow/delete/${id}`)
  );
};

// 增值记录管理保存
export const saveValueAddedApi = (data: CustomerValueAddedListModel) => {
  return http.request<BasicResponseParams>(
    "post",
    baseUrlApi("custom/valueAdded/save"),
    {
      data
    }
  );
};

// 列表
export const getValueAddedListApi = (data: ValueAddedListPageModel) => {
  return http.request<CustomerValueAddedListResultModel>(
    "post",
    baseUrlApi("custom/valueAdded/queryPage"),
    {
      data
    }
  );
};

// 根据id查询
export const getValueAddedByIdApi = (id: string | number) => {
  return http.request<ResponseDetailModel<CustomerValueAddedListModel>>(
    "get",
    baseUrlApi(`custom/valueAdded/details/${id}`)
  );
};

// 根据id删除
export const delValueAddedApi = (id: string | number) => {
  return http.request<BasicResponseParams>(
    "get",
    baseUrlApi(`custom/valueAdded/delete/${id}`)
  );
};

// 商机管理保存
export const saveOpportunityApi = (data: CustomerOpportunityListModel) => {
  return http.request<BasicResponseParams>(
    "post",
    baseUrlApi("custom/opportunity/save"),
    {
      data
    }
  );
};

// 商机管理分页查询
export const getOpportunityListApi = (
  data: CustomerOpportunityListPageModel
) => {
  return http.request<GetCustomerOpportunityListResultModel>(
    "post",
    baseUrlApi("custom/opportunity/queryPage"),
    {
      data
    }
  );
};

// 根据id查询
export const getOpportunityByIdApi = (id: string | number) => {
  return http.request<ResponseDetailModel<CustomerOpportunityListModel>>(
    "get",
    baseUrlApi(`custom/opportunity/details/${id}`)
  );
};

// 根据id删除
export const delOpportunityApi = (id: string | number) => {
  return http.request<BasicResponseParams>(
    "get",
    baseUrlApi(`custom/opportunity/delete/${id}`)
  );
};

// 合同管理
export const getContractListApi = (data: CustomerContractListPageModel) => {
  return http.request<GetCustomerContractModelListResultModel>(
    "post",
    baseUrlApi("custom/contractNew/queryPage"),
    {
      data
    }
  );
};

// 合同数量统计
export const getContractTotalApi = () => {
  return http.request<ResponseDetailModel<GETContractTotalModel>>(
    "get",
    baseUrlApi(`custom/contract/getTotal`)
  );
};

// 合同保存操作
export const saveContractApi = (data: CustomerContractModel) => {
  return http.request<BasicResponseParams>(
    "post",
    baseUrlApi("custom/contract/save"),
    {
      data
    }
  );
};

// 合同变更操作
export const changeContractApi = (data: CustomerChangeListModel) => {
  return http.request<BasicResponseParams>(
    "post",
    baseUrlApi("custom/contract/change"),
    {
      data
    }
  );
};

// 合同终止操作
export const stopContractApi = (data: StopContractListModel) => {
  return http.request<BasicResponseParams>(
    "post",
    baseUrlApi("custom/contract/stop"),
    {
      data
    }
  );
};

// 根据id查询
export const getContractByIdApi = (id: string | number) => {
  return http.request<ResponseDetailModel<CustomerContractModel>>(
    "get",
    baseUrlApi(`custom/contract/details/${id}`)
  );
};

// 根据id删除
export const delContractApi = (id: string | number) => {
  return http.request<BasicResponseParams>(
    "get",
    baseUrlApi(`custom/contract/delete/${id}`)
  );
};

// 营销人员列表查询
// export const getSaleListApi = () => {
//   return http.request<any>("post", baseUrlApi("custom/sale/queryPage"));
// };

// 销售人员区域数量统计
export const getSaleUserCountApi = () => {
  return http.request<ResponseDetailModel<GetUserCountModel[]>>(
    "get",
    baseUrlApi(`custom/sale/getUserCount`)
  );
};

// 通过用户id查询区域ID列表
export const getSaleUserByIdApi = (id: string | number) => {
  return http.request<ResponseDetailModel<GetSaleUserByIdModel>>(
    "get",
    baseUrlApi(`custom/sale/getByUserId/${id}`)
  );
};

// 批量保存营销人员区域配置
export const saveSaleUserApi = (data: GetSaleUserByIdModel) => {
  return http.request<BasicResponseParams>(
    "post",
    baseUrlApi("custom/sale/batchSave"),
    {
      data
    }
  );
};

// 商机转化漏斗
export const getOpportunityTotalApi = () => {
  return http.request<ResponseDetailModel<GetOpportunityTotalModel[]>>(
    "get",
    baseUrlApi(`custom/opportunity/getTotal`)
  );
};

// 年度销售排行榜
export const getContractSumApi = () => {
  return http.request<ResponseDetailModel<GetContractSumModel[]>>(
    "get",
    baseUrlApi(`custom/contract/getSum`)
  );
};

// 客户分级统计
export const getGradeCountApi = () => {
  return http.request<ResponseDetailModel<GetGradeCountModel[]>>(
    "get",
    baseUrlApi(`custom/basic/getGradeCount`)
  );
};

// 客户经理维护客户电量统计
export const getElectricitySumApi = () => {
  return http.request<ResponseDetailModel<GetElectricitySumModel[]>>(
    "get",
    baseUrlApi(`custom/basic/getElectricitySum`)
  );
};

// 根据区域ID获取关联用户ID;
export const getAreaUserIdApi = (id: string | number) => {
  return http.request<ResponseDetailModel<string>>(
    "get",
    baseUrlApi(`custom/sale/getUserId/${id}`)
  );
};
// 表计电量管理
export const getCustomMeterPowerListApi = (
  data: CustomerPowerListPageModel
) => {
  return http.request<GetCustomMeterListResultModel>(
    "post",
    baseUrlApi("custom/power/queryPage"),
    {
      data
    }
  );
};
// 表计电量曲线
export const getCustomMeterPowerLineApi = data => {
  return http.request<GetCustomMeterListResultModel>(
    "post",
    baseUrlApi("custom/power/getLine"),
    {
      data
    }
  );
};
// 客户画像负荷及波动情况;
export const getPortraitLoadApi = (data: CustomerPortraitLoadModel) => {
  return http.request<CustomerPortraitLoadResultModel>(
    "post",
    baseUrlApi("custom/basic/getPortraitLoad"),
    {
      data
    }
  );
};
// 大用户排行榜;
export const getCustomerSumApi = () => {
  return http.request<CustomerSumResultModel>(
    "get",
    baseUrlApi(`custom/contract/getCustomSum`)
  );
};
// 按地区统计签约量;
export const getAreaSumApi = () => {
  return http.request<AreaSumResultModel>(
    "get",
    baseUrlApi(`custom/contract/getAreaSum`)
  );
};
// 按地区统计签约量;
export const getYearTotalApi = year => {
  return http.request<any>(
    "get",
    baseUrlApi(`custom/contract/getYearTotal/${year}`)
  );
};
// 按年统计客户签约情况（大屏）;
export const getHomeYearTotalApi = year => {
  return http.request<any>(
    "get",
    baseUrlApi(`custom/contract/getHomeYearTotal/${year}`)
  );
};
// 客户画像结算信息;
export const getPortraitSettlementApi = (data) => {
  return http.request<any>(
    "post",
    baseUrlApi("custom/basic/getPortraitSettlement"),
    {
      data
    }
  );
};
// 商机转化漏斗（大屏）
export const getTotalTop4Api = () => {
  return http.request<ResponseDetailModel<GetOpportunityTotalModel[]>>(
    "get",
    baseUrlApi(`custom/opportunity/getTotalTop4`)
  );
};
// 通过客户id查询计量点管理
export const getPortraitMeterListApi = (id:string) => {
  return http.request<any>(
    "get",
    baseUrlApi(`custom/basic/getPortraitMeterList/${id}`)
  );
};
// 大屏结算收益
export const getScreenSettlementApi = (data) => {
  return http.request<any>(
    "post",
    baseUrlApi(`custom/basic/getScreenSettlement`),
    {
      data
    }
  );
};
// 表计电量曲线查询;
export const getCustomPowerApi = data => {
  return http.request<any>("post", baseUrlApi(`custom/power/query`), {
    data
  });
};

// 用户标签分组统计
export const getTagGroupApi = (data?) => {
  return http.request<any>("post", baseUrlApi(`custom/basic/getTagGroup`), {
    data
  });
};




// 获取分页数据列表
export const getContractPageList = (data) => {
  return http.request<any>(
    "post",
    baseUrlApi("custom/contractNew/queryPageContract"),
    {
      data
    }
  );
};


//合同数据新增
export const addContractData = (data) => {
  return http.request<any>(
    "post",
    baseUrlApi("custom/contractNew/contractDataAdd"),
    {
      data
    }
  );
};
//合同数据导入
// export const importContractData = (data) => {
//   return http.request<any>(
//     "post",
//     baseUrlApi("custom/contractNew/importContractData"),
//     {
//       data
//     }
//   );
// };

export const importContractData = (formData: any) => {
  return request.post({
    url: '/custom/contractNew/importContractData',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}
//合同数据修改
export const contractDataUpdate = (data) => {
  return http.request<any>(
    "post",
    baseUrlApi("custom/contractNew/contractDataUpdate"),
    {
      data
    }
  );
};
//合同数据删除
export const getPortraitMeterList = (id:any) => {
  return http.request<any>(
    "get",
    baseUrlApi(`custom/contractNew/contractDataDelete/${id}`)
   
  );
};
//总计
export const getContractNewTotal = () => {
  return http.request<any>(
    "get",
    baseUrlApi(`/custom/contractNew/getTotal`)
   
  );
};

// 用电户号模板下载
export const downloadElectrictyTemplateAPI = () => {
  return request.post<any>(
    {
      url: '/custom/account/exportAccountTemplate',
      responseType: 'blob',
    },
    {
      isTransformResponse: false,
    }
  );
};

// 用电户号导入
export const importElectrictyDataAPI = (formData: any) => {
  return request.post<any>(
    {
      url: '/custom/account/importAccountInfoData',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
    {
      isTransformResponse: false,
    }
  );
};

// 导出合同数据分页查询（合同管理）结果
export const exportContractInfoList = (data: any) => {
  return request.post<any>(
    {
      url: '/custom/contractNew/exportContractInfoList',
      data: data,
      responseType: 'blob',
    },
    {
      isTransformResponse: false,
    }
  );
};

// 下载合同管理模板
export const exportContractTemplate = () => {
  return request.post<any>(
    {
      url: '/custom/contractNew/exportContractTemplate',
      responseType: 'blob',
    },
    {
      isTransformResponse: false,
    }
  );
};