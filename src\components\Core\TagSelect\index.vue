<template>
  <section>
    <div>
      <div>用电规模</div>
      <div>
        <el-check-tag style="margin-right: 8px" v-for="(item, index) in electricityConsumptionOptions"
          :checked="item.checked" :key="index" @change="onChangeMap.electricityScaleChange(item)">
          {{ item.label }}
        </el-check-tag>
      </div>
    </div>
    <div class="mt-[20px]">
      <div>生产类型</div>
      <div>
        <el-check-tag style="margin-right: 8px" v-for="(item, index) in productTypeOptions" :key="index"
          :checked="item.checked" @change="onChangeMap.productTypeChange(item)">{{ item.label }}</el-check-tag>
      </div>
    </div>
    <div class="mt-[20px]">
      <div>用电特征</div>
      <div>
        <el-check-tag style="margin-right: 8px" v-for="(item, index) in electricityCharacteristicsOptions" :key="index"
          :checked="item.checked" @change="onChangeMap.electricityCharacteristicChange(item)">{{ item.label
          }}</el-check-tag>
      </div>
    </div>
    <div class="mt-[20px]">
      <div>合同期限</div>
      <div>
        <el-check-tag style="margin-right: 8px" v-for="(item, index) in contractPeriodOptions" :key="index"
          :checked="item.checked" @change="onChangeMap.contractPeriodChange(item)">{{ item.label }}</el-check-tag>
      </div>
    </div>
    <div class="mt-[20px]">
      <div>套餐类型</div>
      <div>
        <el-check-tag style="margin-right: 8px" v-for="(item, index) in packageTypeOptions" :key="index"
          :checked="item.checked" @change="onChangeMap.packageTypChange(item)">{{ item.label }}</el-check-tag>
      </div>
    </div>
    <div class="mt-[20px]">
      <div>套餐名称</div>
      <div>
        <el-check-tag style="margin-right: 8px" v-for="(item, index) in packageNameOptions" :key="index"
          :checked="item.checked" @change="onChangeMap.packageNameChange(item)">{{ item.label }}</el-check-tag>
      </div>
    </div>
    <div class="mt-[20px]">
      <div>偏差特点</div>
      <div>
        <el-check-tag style="margin-right: 8px" v-for="(item, index) in deviationCharacteristicsOptions" :key="index"
          :checked="item.checked" @change="onChangeMap.deviationCharacteristicsChange(item)">{{ item.label
          }}</el-check-tag>
      </div>
    </div>
    <div class="flex justify-end mt-[40px]">
      <el-button type="primary" @click="onClearSelect">全部不选</el-button>
      <el-button type="primary" @click="handleConfirm">确认选择</el-button>
    </div>
  </section>
</template>

<script setup lang="ts">
import { useUserStore } from "@/store/modules/user";
import { ref, watch } from "vue";
import { DictItemsModel } from "@/model/dictModel";
type SelectTagsType = {
  customId?: number;
  id?: number;
  tagType: string;
  tagValueId: number;
  tagName?: string
};
type DictListType = DictItemsModel & {
  checked: boolean
}
defineOptions({
  name: "TagSelect"
});
const emit = defineEmits(["change"]);
const props = defineProps({
  selectTags: {
    type: Array<SelectTagsType>,
    default: () => []
  }
});
const userStore = useUserStore();
// 用电规模
const electricityConsumptionOptions = ref(
  userStore.getDictList
    .find(i => i.code === "electricityConsumptionScale")
    ?.items.map(item => {
      return {
        ...item,
        checked: false
      };
    })
);
// 生产类型
const productTypeOptions = ref(
  userStore.getDictList
    .find(i => i.code === "productType")
    ?.items.map(item => {
      return {
        ...item,
        checked: false
      };
    })
);
const electricityCharacteristicsOptions = ref(
  userStore.getDictList
    .find(i => i.code === "electricityConsumptionCharacteristics")
    ?.items.map(item => {
      return {
        ...item,
        checked: false
      };
    })
);
const contractPeriodOptions = ref(
  userStore.getDictList
    .find(i => i.code === "contractPeriod")
    ?.items.map(item => {
      return {
        ...item,
        checked: false
      };
    })
);
const packageTypeOptions = ref(
  userStore.getDictList
    .find(i => i.code === "packageType")
    ?.items.map(item => {
      return {
        ...item,
        checked: false
      };
    })
);
const packageNameOptions = ref(
  userStore.getDictList
    .find(i => i.code === "packageName")
    ?.items.map(item => {
      return {
        ...item,
        checked: false
      };
    })
);
const deviationCharacteristicsOptions = ref(
  userStore.getDictList
    .find(i => i.code === "deviationCharacteristics")
    ?.items.map(item => {
      return {
        ...item,
        checked: false
      };
    })
);
// 标签点击变化事件，用来标记是否选中
const onChangeMap = ref({
  electricityScaleChange: item => {
    item.checked = !item.checked;
  },
  productTypeChange: item => {
    item.checked = !item.checked;
  },
  electricityCharacteristicChange: item => {
    item.checked = !item.checked;
  },
  contractPeriodChange: item => {
    item.checked = !item.checked;
  },
  packageTypChange: item => {
    item.checked = !item.checked;
  },
  packageNameChange: item => {
    item.checked = !item.checked;
  },
  deviationCharacteristicsChange: item => {
    item.checked = !item.checked;
  }
});
function handleConfirm() {
  // 过滤已选中的标签
  const array = [
    ...electricityConsumptionOptions.value
      .filter(item => item.checked === true)
      .map(item => {
        return {
          tagType: "electricityConsumptionScale",
          tagValueId: Number(item.value)
        };
      }),
    ...productTypeOptions.value
      .filter(item => item.checked === true)
      .map(item => {
        return {
          tagType: "productType",
          tagValueId: Number(item.value)
        };
      }),
    ...electricityCharacteristicsOptions.value
      .filter(item => item.checked === true)
      .map(item => {
        return {
          tagType: "electricityConsumptionCharacteristics",
          tagValueId: Number(item.value)
        };
      }),
    ...contractPeriodOptions.value
      .filter(item => item.checked === true)
      .map(item => {
        return {
          tagType: "contractPeriod",
          tagValueId: Number(item.value)
        };
      }),
    ...packageTypeOptions.value
      .filter(item => item.checked === true)
      .map(item => {
        return {
          tagType: "packageType",
          tagValueId: Number(item.value)
        };
      }),
    ...packageNameOptions.value
      .filter(item => item.checked === true)
      .map(item => {
        return {
          tagType: "packageName",
          tagValueId: Number(item.value)
        };
      }),
    ...deviationCharacteristicsOptions.value
      .filter(item => item.checked === true)
      .map(item => {
        return {
          tagType: "deviationCharacteristics",
          tagValueId: Number(item.value)
        };
      })
  ];
  emit("change", array);
}
function filterCheckedTag(target: DictListType[], current: SelectTagsType[]) {
  if (Array.isArray(current) && current.length) {
    target.forEach(item => {
      const element = current.find(i => i.tagName === item.label)
      if (element)
        item.checked = true
    })
  }
}
function onClearSelect() {
  electricityConsumptionOptions.value.forEach(item => {
    item.checked = false
  })
  productTypeOptions.value.forEach(item => {
    item.checked = false
  })
  electricityCharacteristicsOptions.value.forEach(item => {
    item.checked = false
  })
  contractPeriodOptions.value.forEach(item => {
    item.checked = false
  })
  packageTypeOptions.value.forEach(item => {
    item.checked = false
  })
  packageNameOptions.value.forEach(item => {
    item.checked = false
  })
  deviationCharacteristicsOptions.value.forEach(item => {
    item.checked = false
  })
   emit("change", []);
}
watch(() => props.selectTags, (newVal) => {
  console.log(newVal,'newVal');
  
  filterCheckedTag(electricityConsumptionOptions.value, newVal)
  filterCheckedTag(productTypeOptions.value, newVal)
  filterCheckedTag(electricityCharacteristicsOptions.value, newVal)
  filterCheckedTag(contractPeriodOptions.value, newVal)
  filterCheckedTag(packageTypeOptions.value, newVal)
  filterCheckedTag(packageNameOptions.value, newVal)
  filterCheckedTag(deviationCharacteristicsOptions.value, newVal)
}, { immediate: true, deep: true })
</script>

<style lang="scss" scoped></style>
