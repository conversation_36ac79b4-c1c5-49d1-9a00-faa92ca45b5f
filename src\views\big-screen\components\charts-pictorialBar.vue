<template>
  <div ref="chartRef" :style="{ height, width }" />
</template>

<script setup lang="ts">
import {
  ref,
  computed,
  onMounted,
  watch,
  type Ref,
  PropType,
  onActivated
} from "vue";
import { useAppStoreHook } from "@/store/modules/app";
import {
  delay,
  useDark,
  useECharts,
  type EchartOptions
} from "@pureadmin/utils";
import * as echarts from "echarts/core";
import type { EChartsOption } from "echarts";
import { MarkPointComponent, MarkLineComponent } from "echarts/components";
import { triggerWindowResize } from "@/utils/event";
echarts.use([MarkPointComponent, MarkLineComponent]);
const emit = defineEmits(["update"]);
const props = defineProps({
  height: {
    type: String as PropType<string>,
    default: "100%"
  },
  width: {
    type: String as PropType<string>,
    default: "100%"
  },
  xData: {
    type: Array as PropType<string[] | number[]>,
    default: () => []
  },
  yData: {
    type: Array as PropType<string[] | number[]>,
    default: () => []
  },
  yAxisName1: {
    type: String as PropType<string>,
    default: ""
  },
  yAxisName2: {
    type: String as PropType<string>,
    default: ""
  },
  series: {
    type: Array as PropType<Array<object>>,
    default: () => []
  },
  legendData: {
    type: Array as PropType<Array<object>>,
    default: () => []
  }
});
const { isDark } = useDark();

const theme: EchartOptions["theme"] = computed(() => {
  return isDark.value ? "dark" : "light";
});

const chartRef = ref<HTMLDivElement | null>(null);
const { setOptions, resize } = useECharts(chartRef as Ref<HTMLDivElement>, {
  theme
});

const getOption = (): EChartsOption => {
  return {
    color: [
      "#63caff",
      "#49beff",
      "#03387a",
      "#03387a",
      "#03387a",
      "#6c93ee",
      "#a9abff",
      "#f7a23f",
      "#27bae7",
      "#ff6d9d",
      "#cb79ff",
      "#f95b5a",
      "#ccaf27",
      "#38b99c",
      "#93d0ff",
      "#bd74e0",
      "#fd77da",
      "#dea700"
    ],
    tooltip: {
      // show: false
    },
    grid: {
      top: "12%",
      left: "15%",
      right: "10%",
      bottom: "20%"
    },
    legend: {
      show: true,
      itemHeight: 8,
      itemWidth: 13,
      selectedMode: false,
      data: props.legendData
    },
    xAxis: {
      type: "category",
      boundaryGap: [0, 0.01],
      axisLine: {
        show: true,
        lineStyle: {
          color: "#456192"
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: "#fff",
        opacity: 0.8
      },
      splitLine: {
        show: false
      },
      data: new Array(12).fill(item => item).map((_, index) => `${index + 1}月`)
    },
    yAxis: [
      {
        name: props.yAxisName1,
        type: "value",
        scale:true,
        axisLine: {
          show: true,
          lineStyle: {
            color: "#456192"
          }
        },
        axisLabel: {
          color: "#fff",
          opacity: 0.8
        },
        axisTick: {
          show: false
        },
        splitLine: {
          lineStyle: {
            type: "dashed",
            color: "#456192"
          }
        }
      },
      {
        name: props.yAxisName2,
        type: "value",
        scale: false,
        axisLine: {
          show: true,
          lineStyle: {
            color: "#456192"
          }
        },
        axisLabel: {
          color: "#fff",
          opacity: 0.8
        },
        axisTick: {
          show: false
        },
        splitLine: {
          lineStyle: {
            type: "dashed",
            color: "#456192"
          }
        }
      }
    ],
    series: props.series
  };
};

watch(
  () => useAppStoreHook().getSidebarStatus,
  () => {
    delay(600).then(() => resize());
  }
);

watch(
  () => props,
  () => setOptions(getOption() as EChartsOption, false, { notMerge: true }),
  {
    immediate: true,
    deep: true
  }
);
onMounted(() => {
  delay(300).then(() => resize());
});
onActivated(() => triggerWindowResize());
</script>
