export default {
  path: '/dataManage',
  name: 'dataManage',
  meta: {
    title: '数据管理',
    icon: "material-symbols:supervisor-account-rounded",
    rank: 8
  },
  redirect: '/dataManage/peakFlatValley',
  children: [
    {
      path: '/dataManage/peakFlatValley',
      component: () => import('@/views/dataManage/peakFlatValley/index.vue'),
      name: 'peakFlatValley',
      meta: {
        title: '峰平谷',
        icon: '',
      },
    },
    {
      path: "/dataManage/screenDataManage",
      name: "screenDataManage",
      component: () => import("@/views/dataManage/screenDataManage/index.vue"),
      meta: {
        // keepAlive: true,
        title: "大屏数据管理"
        //icon: "ant-design:user-outlined"
      }
    },
    {
      path: '/dataManage/marketData',
      component: () => import('@/views/marketData/index.vue'),
      name: 'marketData',
      meta: {
        title: '市场数据',
        icon: '',
      },
    },
  ],
};